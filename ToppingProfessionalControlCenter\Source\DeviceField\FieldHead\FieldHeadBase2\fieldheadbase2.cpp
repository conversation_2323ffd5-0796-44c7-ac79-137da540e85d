#include "globalfont.h"
#include "fieldheadbase2.h"


FieldHeadBase2::FieldHeadBase2(QWidget* parent)
    : QWidget(parent)
{
    mLabelLogo.setParent(this);
    mLabelSampleRate.setParent(this);
    mLabelBufferSize.setParent(this);
    mComboBoxSampleRate.setParent(this);
    mComboBoxBufferSize.setParent(this);
    mBattery.setParent(this);
    mPushButtonSettings.setParent(this);
    QString style;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/TPLogo.png);"
            "}";
    mLabelLogo.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   color: rgb(166, 166, 166);"
            "}";
    mLabelSampleRate.setStyleSheet(style);
    mLabelBufferSize.setStyleSheet(style);
    mLabelSampleRate.setText("SampleRate");
    mLabelBufferSize.setText("BufferSize");
    mComboBoxSampleRate.setName("SampleRate");
    mComboBoxBufferSize.setName("BufferSize");
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/Settings.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid transparent;"
            "   border-radius: 2px;"
            "}";
    mPushButtonSettings.setStyleSheet(style);
    connect(&mComboBoxSampleRate, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mComboBoxAll_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    connect(&mComboBoxBufferSize, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mComboBoxAll_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    connect(&mPushButtonSettings, SIGNAL(clicked()), this, SLOT(in_mPushButtonAll_clicked()), Qt::UniqueConnection);
    mLabelSampleRate.hide();
    mLabelBufferSize.hide();
    mComboBoxSampleRate.hide();
    mComboBoxBufferSize.hide();
    mBattery.hide();
    mPushButtonSettings.hide();
}
FieldHeadBase2::~FieldHeadBase2()
{

}


// override
void FieldHeadBase2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int x=width();
    int w=0, h=0, hRatio=0;
    int spacing=height() / 10;
    float hPixelPerRatio=height() / 100.0;
    // Logo
    mLabelLogo.setGeometry(height() * 0.4, hPixelPerRatio * 2, height() * 8, height());
    // PushButtonSettings
    hRatio = 80;
    h = hPixelPerRatio * hRatio;
    w = h;
    x -= (spacing * 4 + w);
    mPushButtonSettings.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mBattery
    hRatio = 45;
    h = hPixelPerRatio * hRatio;
    w = h * 2;
    x -= (spacing * 5 + w);
    mBattery.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mComboBoxBufferSize
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    w = h * 3.3;
    x -= (spacing * 12 + w);
    mComboBoxBufferSize.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mLabelBufferSize
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, h));
    mLabelBufferSize.setFont(mFont);
    w = GLBFHandle.getSuitableWidth(mFont, mLabelBufferSize.text(), h);
    x -= (spacing * 2 + w);
    mLabelBufferSize.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mComboBoxSampleRate
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    w = h * 4;
    x -= (spacing * 12 + w);
    mComboBoxSampleRate.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mLabelSampleRate
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, h));
    mLabelSampleRate.setFont(mFont);
    w = GLBFHandle.getSuitableWidth(mFont, mLabelSampleRate.text(), h);
    x -= (spacing * 2 + w);
    mLabelSampleRate.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
}
void FieldHeadBase2::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
}
void FieldHeadBase2::drawBG(QPainter *painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(mColorBG));
    painter->drawRect(rect());
    painter->restore();
}


// slot
void FieldHeadBase2::in_mPushButtonAll_clicked()
{
    QPushButton* button=qobject_cast<QPushButton*>(sender());
    if(button == &mPushButtonSettings)
    {
        emit attributeChanged("SystemSettings", "Clicked", "1");
    }
}
void FieldHeadBase2::in_mComboBoxAll_attributeChanged(QString objectName, QString attribute, QString value)
{
    emit attributeChanged(objectName, attribute, value);
}


// setter & getter
FieldHeadBase2& FieldHeadBase2::setFont(QFont font)
{
    mFont = font;
    mComboBoxSampleRate.setFont(mFont);
    mComboBoxBufferSize.setFont(mFont);
    mBattery.setFont(mFont);
    return *this;
}
FieldHeadBase2& FieldHeadBase2::setFieldColor(QColor color)
{
    mColorBG = color;
    update();
    return *this;
}
FieldHeadBase2& FieldHeadBase2::setLanguage(QString language)
{
    mLanguage = language;
    if(mLanguage == "Chinese")
    {
        mLabelSampleRate.setText("采样率");
        mLabelBufferSize.setText("缓冲区大小");
    }
    else
    {
        mLabelSampleRate.setText("Sample Rate");
        mLabelBufferSize.setText("Buffer Size");
    }
    return *this;
}
FieldHeadBase2& FieldHeadBase2::setBatteryValue(int value)
{
    mBattery.setValue(value);
    return *this;
}
FieldHeadBase2& FieldHeadBase2::setBatteryCharging(bool charging)
{
    mBattery.setCharging(charging);
    return *this;
}
FieldHeadBase2& FieldHeadBase2::modifySampleRateList(QVector<unsigned int> list, unsigned int defaultItem)
{
    QVector<QString> listItem;
    for(auto element : list)
    {
        listItem << QString::number(element);
    }
    mComboBoxSampleRate.modifyItemList(listItem, QString::number(defaultItem));
    return *this;
}
FieldHeadBase2& FieldHeadBase2::modifyBufferSizeList(QVector<unsigned int> list, unsigned int defaultItem)
{
    QVector<QString> listItem;
    for(auto element : list)
    {
        listItem << QString::number(element);
    }
    mComboBoxBufferSize.modifyItemList(listItem, QString::number(defaultItem));
    return *this;
}


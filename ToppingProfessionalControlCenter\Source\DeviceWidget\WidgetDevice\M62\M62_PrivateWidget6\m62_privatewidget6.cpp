#include "M62_PrivateWidget6.h"
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QPainter>
#include <QSvgRenderer>
#include <QRandomGenerator>
#include <QTimerEvent>

M62_PrivateWidget6::M62_PrivateWidget6(QWidget *parent) : QWidget(parent), pix(originalWidth, originalHeight) {
    originalElements = parseSvgJson();
    resize(originalWidth, originalHeight);
    startTimer(50);
}

QList<M62_PrivateWidget6::SvgCircle> M62_PrivateWidget6::parseSvgJson() {
    QList<SvgCircle> circles;
    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (doc.isNull()) {
        return circles;
    }

    QJsonObject root = doc.object();
    QJsonArray groups = root["groups"].toArray();
    for (const QJsonValue &groupVal : groups) {
        QJsonArray elements = groupVal.toObject()["elements"].toArray();
        for (const QJsonValue &elementVal : elements) {
            QJsonObject element = elementVal.toObject();
            circles.append({
                element["x"].toDouble(),
                element["y"].toDouble(),
                element["width"].toDouble(),
                element["height"].toDouble()
            });
        }
    }
    return circles;
}

QList<M62_PrivateWidget6::SvgCircle> M62_PrivateWidget6::scaleElements(const QList<SvgCircle> &elements) {
    QList<SvgCircle> scaled;
    const qreal scaleX = width() / originalWidth;
    const qreal scaleY = height() / originalHeight;

    for (const auto &element : elements) {
        scaled.append({
            element.x * scaleX,
            element.y * scaleY,
            element.width * scaleX,
            element.height * scaleY
        });
    }
    return scaled;
}

void M62_PrivateWidget6::resizeEvent(QResizeEvent *event) {
    QWidget::resizeEvent(event);
    ScaleElements = scaleElements(originalElements);
    pix = pix.scaled(width(), height(), Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
    update();
}

void M62_PrivateWidget6::paintEvent(QPaintEvent *event) {
    QWidget::paintEvent(event);

    pix.fill(Qt::transparent);
    QPainter painter;
    painter.begin(&pix);
    painter.setRenderHints(
        QPainter::Antialiasing | 
        QPainter::TextAntialiasing | 
        QPainter::SmoothPixmapTransform
    );
    QString svgPath = ":/Icon/M62Device.svg";
    QSvgRenderer renderer(svgPath);
    if(renderer.isValid()){
        QRectF targetRect = QRectF(0, 0, width(), height());
        renderer.render(&painter, targetRect);
    }
    painter.setBrush(Qt::red);
    for (; startIndex < randomNum; ++startIndex) {
        const auto &circle = ScaleElements[startIndex];
        painter.drawEllipse(QPointF(circle.x, circle.y), circle.width / 2, circle.height / 2);
    }
    painter.end();

    painter.begin(this);
    painter.drawPixmap(rect(), pix);
    painter.end();
}

void M62_PrivateWidget6::timerEvent(QTimerEvent *event) {
    QWidget::timerEvent(event);
    startIndex = startIndex % ((ScaleElements.size()-1)/2);
    randomNum = QRandomGenerator::global()->generate() % ScaleElements.size();
    update();
}

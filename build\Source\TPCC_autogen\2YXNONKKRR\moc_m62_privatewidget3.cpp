/****************************************************************************
** Meta object code from reading C++ file 'm62_privatewidget3.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget3/m62_privatewidget3.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'm62_privatewidget3.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN18M62_PrivateWidget3E_t {};
} // unnamed namespace

template <> constexpr inline auto M62_PrivateWidget3::qt_create_metaobjectdata<qt_meta_tag_ZN18M62_PrivateWidget3E_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "M62_PrivateWidget3",
        "in_widgetDial_valueChanged",
        "",
        "value",
        "in_widgetDialDecay_valueChanged",
        "in_sliderInput1_valueChanged",
        "in_sliderInput2_valueChanged",
        "in_sliderAux_valueChanged",
        "in_sliderBluetooth_valueChanged",
        "in_sliderOtg_valueChanged"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'in_widgetDial_valueChanged'
        QtMocHelpers::SlotData<void(float)>(1, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Float, 3 },
        }}),
        // Slot 'in_widgetDialDecay_valueChanged'
        QtMocHelpers::SlotData<void(float)>(4, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Float, 3 },
        }}),
        // Slot 'in_sliderInput1_valueChanged'
        QtMocHelpers::SlotData<void(int)>(5, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 3 },
        }}),
        // Slot 'in_sliderInput2_valueChanged'
        QtMocHelpers::SlotData<void(int)>(6, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 3 },
        }}),
        // Slot 'in_sliderAux_valueChanged'
        QtMocHelpers::SlotData<void(int)>(7, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 3 },
        }}),
        // Slot 'in_sliderBluetooth_valueChanged'
        QtMocHelpers::SlotData<void(int)>(8, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 3 },
        }}),
        // Slot 'in_sliderOtg_valueChanged'
        QtMocHelpers::SlotData<void(int)>(9, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 3 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<M62_PrivateWidget3, qt_meta_tag_ZN18M62_PrivateWidget3E_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject M62_PrivateWidget3::staticMetaObject = { {
    QMetaObject::SuperData::link<EffectBase::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18M62_PrivateWidget3E_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18M62_PrivateWidget3E_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN18M62_PrivateWidget3E_t>.metaTypes,
    nullptr
} };

void M62_PrivateWidget3::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<M62_PrivateWidget3 *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->in_widgetDial_valueChanged((*reinterpret_cast< std::add_pointer_t<float>>(_a[1]))); break;
        case 1: _t->in_widgetDialDecay_valueChanged((*reinterpret_cast< std::add_pointer_t<float>>(_a[1]))); break;
        case 2: _t->in_sliderInput1_valueChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 3: _t->in_sliderInput2_valueChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 4: _t->in_sliderAux_valueChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 5: _t->in_sliderBluetooth_valueChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 6: _t->in_sliderOtg_valueChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject *M62_PrivateWidget3::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *M62_PrivateWidget3::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18M62_PrivateWidget3E_t>.strings))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "WorkspaceObserver"))
        return static_cast< WorkspaceObserver*>(this);
    if (!strcmp(_clname, "AppSettingsObserver"))
        return static_cast< AppSettingsObserver*>(this);
    return EffectBase::qt_metacast(_clname);
}

int M62_PrivateWidget3::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = EffectBase::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 7;
    }
    return _id;
}
QT_WARNING_POP

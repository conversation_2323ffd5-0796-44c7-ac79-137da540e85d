#include "batterys1m1.h"
#include "batterydrawstrategy.h"
#include <QPainter>
#include <QPainterPath>

BatteryS1M1::BatteryS1M1(QWidget* parent)
    : QWidget(parent)
{
    resize(90, 50);

    mDrawStrategy = std::make_unique<ContinuousDrawStrategy>(this);
    mDrawStrategy->setUpdateCallback([this]() {
        update();
    });
}

BatteryS1M1::~BatteryS1M1()
{

}

void BatteryS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int width = rect().width();
    int height = rect().height();

    double bodyWidth = width * 0.85;
    int margin = 0;
    mRectBattery = QRectF(margin, margin, bodyWidth - margin, height - 2 * margin);
}

void BatteryS1M1::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}

void BatteryS1M1::drawBG(QPainter* painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(mCharging ? mColorGrooveCharging : mColorGrooveNormal));
    painter->drawRect(rect());
    painter->restore();
}

void BatteryS1M1::drawElement(QPainter* painter)
{
    painter->save();

    QPainterPath batteryPath;
    qreal outRectRadius = mRectBattery.height() * 0.25;
    batteryPath.addRoundedRect(mRectBattery, outRectRadius, outRectRadius);
    qreal innerMargin = 0.15 * mRectBattery.height();
    QRectF innerRoundRect = mRectBattery.adjusted(innerMargin, innerMargin, -innerMargin, -innerMargin);
    qreal innerRectRadius = innerRoundRect.height() * 0.15;
    batteryPath.addRoundedRect(innerRoundRect, innerRectRadius, innerRectRadius);
    painter->setPen(Qt::NoPen);
    QColor brush = mColorShellNormal;
    if(!mCharging){
        if(mValue <= mLowValue) {
            brush = mLowColorShellNormal;
        } else if(mValue <= mWarningValue) {
            brush =mWarningColorShellNormal;
        }
    }
    painter->setBrush(brush);

    painter->drawPath(batteryPath);

    QRectF headRect(innerRoundRect.right(),
                   mRectBattery.center().y() - mRectBattery.height()/6,
                   mRectBattery.height() * 0.3,
                   mRectBattery.height()/3);
    qreal headRadius = headRect.height() * 0.3;
    painter->drawRoundedRect(headRect, headRadius, headRadius);

    qreal powerMargin = 0.15 * innerRoundRect.height();
    QRectF powerRect = innerRoundRect.adjusted(powerMargin, powerMargin, -powerMargin, -powerMargin);
    if (mDrawStrategy) {
        BatteryColors colors = getBatteryColors();

        mDrawStrategy->drawPower(painter, powerRect, mValue, mLowValue,  mCharging, colors);

        if(mShowChargingIcon && mCharging){
            mDrawStrategy->drawLightning(painter, powerRect, colors.lightning);
        }

        if(mShowValueText){
            QColor textColor = mCharging ? colors.textCharging : colors.textNormal;
            mDrawStrategy->drawText(painter, mRectBattery, mValue, mFont, textColor);
        }
    }

    painter->restore();
}

BatteryS1M1& BatteryS1M1::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setValue(int value)
{
    value = qBound(0, value, 100);
    if (mValue != value) {
        mValue = value;
        update();
    }
    return *this;
}

BatteryS1M1& BatteryS1M1::setCharging(bool charging)
{
    if(mCharging != charging) {
        mCharging = charging;

        if (mDrawStrategy) {
            if (mCharging) {
                mDrawStrategy->startChargingAnimation();
            } else {
                mDrawStrategy->stopChargingAnimation();
            }
        }
        update();
    }
    return *this;
}

BatteryS1M1& BatteryS1M1::setLowColorShellNormal(QColor color)
{
    mLowColorShellNormal = color;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setWarningColorShellNormal(QColor color)
{
    mWarningColorShellNormal = color;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setColorShellNormal(QColor color)
{
    mColorShellNormal = color;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setColorGrooveNormal(QColor color)
{
    mColorGrooveNormal = color;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setColorGrooveCharging(QColor color)
{
    mColorGrooveCharging = color;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setColorLightning(QColor color)
{
    mColorLightning= color;
    update();
    return *this;
}


BatteryS1M1& BatteryS1M1::setColorElectricQuantityNormal(QColor color)
{
    mColorElectricQuantityNormal = color;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setColorElectricQuantityCharging(QColor color)
{
    mColorElectricQuantityCharging = color;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setColorTextNormal(QColor color)
{
    mColorTextNormal = color;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setColorTextCharging(QColor color)
{
    mColorTextCharging = color;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setIsShowChargingIcon(bool status)
{
    mShowChargingIcon = status;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setIsShowText(bool status)
{
    mShowValueText = status;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setLowValue(int value)
{
    mLowValue = value;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setWarningValue(int value)
{
    mWarningValue = value;
    update();
    return *this;
}

BatteryS1M1& BatteryS1M1::setDrawStrategy(std::unique_ptr<BatteryDrawStrategy> strategy)
{
    if (strategy) {
        if (mDrawStrategy && mCharging) {
            mDrawStrategy->stopChargingAnimation();
        }

        mDrawStrategy = std::move(strategy);
        mDrawStrategy->setUpdateCallback([this]() {
            update();
        });

        if (mCharging) {
            mDrawStrategy->startChargingAnimation();
        }

        update();
    }
    return *this;
}

BatteryColors BatteryS1M1::getBatteryColors() const
{
    BatteryColors colors;
    colors.electricQuantityNormal = mColorElectricQuantityNormal;
    colors.electricQuantityCharging = mColorElectricQuantityCharging;
    colors.lightning = mColorLightning;
    colors.textNormal = mColorTextNormal;
    colors.textCharging = mColorTextCharging;
    return colors;
}
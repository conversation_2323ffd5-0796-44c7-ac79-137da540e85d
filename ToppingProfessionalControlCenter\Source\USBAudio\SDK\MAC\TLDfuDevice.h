/************************************************************************

    Description:
        TLDFU device
        Encapsulation of a TLDfuDeviceHandle

    Author(s):
        <PERSON><PERSON><PERSON> Eberhardt
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __TLDfuDevice_h__
#define __TLDfuDevice_h__


class TLDfuDevice
{
public:

    //
    // Constructor
    //
    TLDfuDevice()
            {
                // empty
            }


    //
    // Destructor
    //
    ~TLDfuDevice()
            {
                // make sure current handle is closed (ignore possibly error)
                CloseDevice();
            }

    // disable copy and move operations
    TLDfuDevice(const TLDfuDevice&) = delete;
    TLDfuDevice& operator=(const TLDfuDevice&) = delete;
    TLDfuDevice(TLDfuDevice&&) = delete;
    TLDfuDevice& operator=(TLDfuDevice&&) = delete;

/////////////////////////////////////////
// Interface
//
public:

    bool
    IsValid() const
            {
                return ( mHandle != TLDFU_INVALID_HANDLE );
            }

    void
    AttachHandle(
        TLDfuDeviceHandle handle
        )
            {
                // make sure current handle is closed (ignore possibly error)
                CloseDevice();

                mHandle = handle;
            }


    TLDfuDeviceHandle
    DetachHandle()
            {
               TLDfuDeviceHandle h = mHandle;
               mHandle = TLDFU_INVALID_HANDLE;
               return h;
            }


    TLSTATUS
    OpenDevice(
        const TLDfuEnumerator& enumerator,
        unsigned int deviceIndex,
        unsigned int flags = 0
        )
            {
                // make sure current handle is closed (ignore possibly error)
                CloseDevice();
                
                return TLDFU_OpenDevice(
                                enumerator.Handle(),
                                deviceIndex,
                                &mHandle,
                                flags
                                );
            }


    TLSTATUS
    CloseDevice()
            {
                TLSTATUS st = TLSTATUS_SUCCESS;
                
                if ( TLDFU_INVALID_HANDLE != mHandle ) {
                    st = TLDFU_CloseDevice(mHandle);
                    mHandle = TLDFU_INVALID_HANDLE;
                }

                return st;
            }

    bool
    IsOpen() const
            { 
                return (TLDFU_INVALID_HANDLE != mHandle);
            }


    TLSTATUS
    CheckDeviceConnection()
            {
                return TLDFU_CheckDeviceConnection(mHandle);
            }


    TLSTATUS
    GetDevicePropertyUint(
        TLDfuDeviceProperty propertyId,
        unsigned int& propertyValue
        )
            {
                return TLDFU_GetDevicePropertyUint(
                                mHandle,
                                propertyId,
                                &propertyValue
                                );
            }

    
    TLSTATUS
    GetDevicePropertyString(
        TLDfuDeviceProperty propertyId,
        T_UNICHAR* stringBuffer,
        unsigned int stringBufferMaxItems
        )
            {
                return TLDFU_GetDevicePropertyString(
                                mHandle,
                                propertyId,
                                stringBuffer,
                                stringBufferMaxItems
                                );
            }


    TLSTATUS
    GetTargetImagePropertyUint(
        unsigned int targetId,
        TLDfuImageProperty propertyId,
        unsigned int& propertyValue,
        unsigned int flags = 0
        )
            {
                return TLDFU_GetTargetImagePropertyUint(
                                mHandle,
                                targetId,
                                propertyId,
                                &propertyValue,
                                flags
                                );
            }

    TLSTATUS
    GetTargetImagePropertyUint64(
        unsigned int targetId,
        TLDfuImageProperty propertyId,
        unsigned long long& propertyValue,
        unsigned int flags = 0
        )
            {
                return TLDFU_GetTargetImagePropertyUint64(
                                mHandle,
                                targetId,
                                propertyId,
                                &propertyValue,
                                flags
                                );
            }

    TLSTATUS
    GetTargetImagePropertyString(
        unsigned int targetId,
        TLDfuImageProperty propertyId,
        T_UNICHAR* stringBuffer,
        unsigned int stringBufferMaxItems,
        unsigned int flags = 0
        )
            {
                return TLDFU_GetTargetImagePropertyString(
                                mHandle,
                                targetId,
                                propertyId,
                                stringBuffer,
                                stringBufferMaxItems,
                                flags
                                );
            }


    TLSTATUS
    InterfaceVendorInRequest(
        unsigned int bRequest,     
        unsigned int wValue,
        void* buffer,                   // out
        unsigned int bufferSize,
        unsigned int* bytesTransferred = nullptr,  // out, optional
        TLTimeoutInterval timeout = 5000
        )
            {
                return TLDFU_InterfaceVendorInRequest(
                                mHandle,
                                bRequest,
                                wValue,
                                buffer,
                                bufferSize,
                                bytesTransferred,
                                timeout
                                );
            }


    TLSTATUS
    InterfaceVendorOutRequest(
        unsigned int bRequest,     
        unsigned int wValue,
        const void* data,                // in, optional
        unsigned int dataLength,
        unsigned int* bytesTransferred = nullptr,  // out, optional
        TLTimeoutInterval timeout = 5000
        )
            {
                return TLDFU_InterfaceVendorOutRequest(
                                mHandle,
                                bRequest,
                                wValue,
                                data,
                                dataLength,
                                bytesTransferred,
                                timeout
                                );
            }


    TLSTATUS
    RebootDevice(
        unsigned int runMode,
        unsigned int flags = 0
        )
            {
                return TLDFU_RebootDevice(
                                mHandle,
                                runMode,
                                flags
                                );
            }


    TLSTATUS
    StartUpgrade(
        const TLDfuImage& image,
        unsigned int targetId,
        unsigned int flags = 0
        )
            {
                return TLDFU_StartUpgrade(
                            mHandle,
                            image.Handle(),
                            targetId,
                            flags
                            );
            }


    TLSTATUS
    GetUpgradeStatus(
        TLDfuUpgradeState& upgradeState,
        unsigned long long* currentBytes,   // optional, can be nullptr
        unsigned long long* totalBytes,     // optional, can be nullptr
        TLSTATUS* completionStatus          // optional, can be nullptr
        )
            {
                return TLDFU_GetUpgradeStatus(
                            mHandle,
                            &upgradeState,
                            currentBytes,
                            totalBytes,
                            completionStatus
                            );
            }


    // convenient helper struct
    struct UpgradeStatus
    {
        TLDfuUpgradeState upgradeState;
        unsigned long long currentBytes;
        unsigned long long totalBytes;
        TLSTATUS completionStatus;
    };
    
    TLSTATUS
    GetUpgradeStatus(
        UpgradeStatus& status
        )
            {
                return GetUpgradeStatus(
                            status.upgradeState,
                            &status.currentBytes,
                            &status.totalBytes,
                            &status.completionStatus
                            );
            }


    TLSTATUS
    FinishUpgrade(
        unsigned int flags = 0
        )
            {
                return TLDFU_FinishUpgrade(
                                mHandle,
                                flags
                                );
            }



    TLSTATUS
    StartReadout(
        unsigned int targetId,
        unsigned int flags = 0
        )
            {
                return TLDFU_StartReadout(
                            mHandle,
                            targetId,
                            flags
                            );
            }


    TLSTATUS
    GetReadoutStatus(
        TLDfuReadoutState& readoutState,
        unsigned long long* currentBytes,   // optional, can be nullptr
        TLSTATUS* completionStatus          // optional, can be nullptr
        )
            {
                return TLDFU_GetReadoutStatus(
                            mHandle,
                            &readoutState,
                            currentBytes,
                            completionStatus
                            );
            }


    // convenient helper struct
    struct ReadoutStatus
    {
        TLDfuReadoutState readoutState;
        unsigned long long currentBytes;
        TLSTATUS completionStatus;
    };
    
    TLSTATUS
    GetReadoutStatus(
        ReadoutStatus& status
        )
            {
                return GetReadoutStatus(
                            status.readoutState,
                            &status.currentBytes,
                            &status.completionStatus
                            );
            }


    TLSTATUS
    FinishReadout(
        TLDfuImage& image,
        unsigned int flags = 0
        )
            {
                TLDfuImageHandle h = TLDFU_INVALID_HANDLE;
                TLSTATUS st = TLDFU_FinishReadout(
                                mHandle,
                                &h,
                                flags
                                );
                image.AttachHandle(h);
                return st;
            }


    //
    // Access to the encapsulated handle.
    //
    TLDfuDeviceHandle
    Handle() const
            {
                return mHandle;
            }


/////////////////////////////////////////
// Implementation
//
protected:



////////////////////////////////////////
// Data
//
protected:

    // handle of the device
    TLDfuDeviceHandle mHandle {TLDFU_INVALID_HANDLE};
};


#endif 

/*** EOF ***/

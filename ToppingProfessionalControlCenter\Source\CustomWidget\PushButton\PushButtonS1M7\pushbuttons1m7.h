#ifndef PUSHBUTTONS1M7_H
#define PUSHBUTTONS1M7_H


#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M7 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M7(QWidget* parent=nullptr);
    ~PushButtonS1M7();
    enum ButtonID
    {
        buttonMUTE=0
    };
    PushButtonS1M7& setFont(QFont font);
    PushButtonS1M7& setPushButtonWeightWidth(int weight);
    PushButtonS1M7& setPushButtonStateMUTE(bool state);
    PushButtonS1M7& setPushButtonClickedMUTE(bool state);
    bool getPushButtonStateMUTE();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateMUTE=false;
    QPushButton mPushButtonMUTE;
    int mWeightWidth=40;
    int mRadius=0;
private slots:
    void in_mPushButtonMUTE_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M7_H


#ifndef FIELDMIXERS1M1_H
#define FIELDMIXERS1M1_H


#include <QHash>
#include <QWidget>
#include <QVector>
#include <QString>
#include <QVariantList>

#include "mixerbase.h"
#include "workspace.h"
#include "appsettings.h"
#include "fieldmixerbase1.h"


class FieldMixerS1M1 : public FieldMixerBase1, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldMixerS1M1(QWidget* parent=nullptr, QString name="");
    ~FieldMixerS1M1();
    FieldMixerS1M1& setName(QString name);
    FieldMixerS1M1& modifyMixerList(QVector<MixerInfo> list);
    FieldMixerS1M1& modifyWidgetList(QVector<MixerBase*> list);
    FieldMixerS1M1& setVisibleListDefault(QString mixer, QVector<MixerBase*> list);
protected:
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    int mSoloState=0;
    QString mMixer;
    QVector<QString> mMixerList;
    QHash<QString, int> mOriginSoloState;
    QHash<QString, QString> mOriginVisibleList;
    QVector<MixerBase*> mWidgetList;
    QMap<QString, QVariantList*> mVisibleListDefault;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
    void in_widgetList_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDMIXERS1M1_H


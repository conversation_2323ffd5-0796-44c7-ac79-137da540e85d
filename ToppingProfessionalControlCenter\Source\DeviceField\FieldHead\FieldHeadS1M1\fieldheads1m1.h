#ifndef FIELDHEADS1M1_H
#define FIELDHEADS1M1_H


#include "appsettings.h"
#include "fieldheadbase1.h"


class FieldHeadS1M1 : public FieldHeadBase1, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldHeadS1M1(QWidget* parent=nullptr, QString name="");
    ~FieldHeadS1M1();
    FieldHeadS1M1& setName(QString name);
protected:
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDHEADS1M1_H


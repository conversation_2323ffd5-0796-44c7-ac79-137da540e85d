#include "globalfont.h"
#include "messageboxs1m1.h"


MessageBoxS1M1_WidgetS1M1::MessageBoxS1M1_WidgetS1M1(QWidget* parent)
    : QWidget(parent)
{
    mLabelTop.setParent(this);
    mLabelMiddle.setParent(this);
    mLabelBottom.setParent(this);
    mPushButtonYes.setParent(this);
    mPushButtonNo.setParent(this);
    mLabelTop.setAlignment(Qt::AlignCenter);
    mLabelMiddle.setAlignment(Qt::AlignCenter);
    mLabelBottom.setAlignment(Qt::AlignCenter);
    setColorTextTop(QColor(0, 0, 0));
    setColorTextMiddle(QColor(0, 0, 0));
    setColorTextBottom(QColor(0, 0, 0));
    setColorTextButtonYes(QColor(0, 0, 0), QColor(166, 166, 166));
    setColorTextButtonNo(QColor(0, 0, 0), QColor(166, 166, 166));
    connect(&mPushButtonYes, SIGNAL(clicked()), this, SLOT(in_mPushButtonAll_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonNo, SIGNAL(clicked()), this, SLOT(in_mPushButtonAll_clicked()), Qt::UniqueConnection);
}


// override
void MessageBoxS1M1_WidgetS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1 = wPixelPerRatio * 10;
    int wSpace2 = wPixelPerRatio * 20;
    int wButton = (size().width() - wSpace1 * 2 - wSpace2) / 2;
    int xButtonYes = wSpace1;
    int xButtonNo = xButtonYes + wButton + wSpace2;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hSpace1 = hPixelPerRatio * 10;
    int hSpace2 = hPixelPerRatio * 5;
    int hItem = (size().height() - hSpace1 * 2 - hSpace2 * 3) / 4;
    mLabelTop.setGeometry(0, hSpace1, size().width(), hItem);
    mLabelMiddle.setGeometry(0, hSpace1 + hItem + hSpace2, size().width(), hItem);
    mLabelBottom.setGeometry(0, hSpace1 + hItem * 2 + hSpace2 * 2, size().width(), hItem);
    mPushButtonYes.setGeometry(xButtonYes, hSpace1 + hItem * 3 + hSpace2 * 3, wButton, hItem);
    mPushButtonNo.setGeometry(xButtonNo, hSpace1 + hItem * 3 + hSpace2 * 3, wButton, hItem);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, hItem));
    mLabelTop.setFont(mFont);
    mLabelMiddle.setFont(mFont);
    mLabelBottom.setFont(mFont);
    mPushButtonYes.setFont(mFont);
    mPushButtonNo.setFont(mFont);
}


// slot
void MessageBoxS1M1_WidgetS1M1::in_mPushButtonAll_clicked()
{
    QPushButton* button=qobject_cast<QPushButton*>(sender());
    if(button == &mPushButtonYes)
    {
        emit buttonClicked("Yes");
    }
    else if(button == &mPushButtonNo)
    {
        emit buttonClicked("No");
    }
}


// setter & getter
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setTextTop(QString text)
{
    mLabelTop.setText(text);
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setTextMiddle(QString text)
{
    mLabelMiddle.setText(text);
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setTextBottom(QString text)
{
    mLabelBottom.setText(text);
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setTextButtonYes(QString text)
{
    mPushButtonYes.setText(text);
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setTextButtonNo(QString text)
{
    mPushButtonNo.setText(text);
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setColorTextTop(QColor color)
{
    QString style;
    style = QString("QLabel {"
                    "   color: %1;"
                    "   background-color: transparent;"
                    "}").arg(color.name());
    mLabelTop.setStyleSheet(style);
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setColorTextMiddle(QColor color)
{
    QString style;
    style = QString("QLabel {"
                    "   color: %1;"
                    "   background-color: transparent;"
                    "}").arg(color.name());
    mLabelMiddle.setStyleSheet(style);
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setColorTextBottom(QColor color)
{
    QString style;
    style = QString("QLabel {"
                    "   color: %1;"
                    "   background-color: transparent;"
                    "}").arg(color.name());
    mLabelBottom.setStyleSheet(style);
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setColorTextButtonYes(QColor color, QColor colorBG)
{
    QString style;
    style = QString("QPushButton {"
                    "   color: %1;"
                    "   background-color: %2;"
                    "}"
                    "QPushButton:hover {"
                    "   border: 2px solid gray;"
                    "   border-radius: 3px;"
                    "}").arg(color.name()).arg(colorBG.name());
    mPushButtonYes.setStyleSheet(style);
    return *this;
}
MessageBoxS1M1_WidgetS1M1& MessageBoxS1M1_WidgetS1M1::setColorTextButtonNo(QColor color, QColor colorBG)
{
    QString style;
    style = QString("QPushButton {"
                    "   color: %1;"
                    "   background-color: %2;"
                    "}"
                    "QPushButton:hover {"
                    "   border: 2px solid gray;"
                    "   border-radius: 3px;"
                    "}").arg(color.name()).arg(colorBG.name());
    mPushButtonNo.setStyleSheet(style);
    return *this;
}






MessageBoxS1M1::MessageBoxS1M1(FramelessWindow* parent)
    : FramelessWindow(parent)
{
    mWidget.setParent(this);
    setCentralWidget(&mWidget);
    setHRatio(2, 10);
    setColorTitle(QColor(0, 0, 0), QColor(166, 166, 166));
    setColorBody(QColor(204, 204, 204));
    connect(&mWidget, SIGNAL(buttonClicked(QString)), this, SLOT(in_mWidget_buttonClicked(QString)), Qt::UniqueConnection);
}


// slot
void MessageBoxS1M1::in_mWidget_buttonClicked(QString button)
{
    if(button == "Yes")
    {
        done(Yes);
    }
    else if(button == "No")
    {
        done(No);
    }
}


// setter & getter
MessageBoxS1M1& MessageBoxS1M1::setFont(QFont font)
{
    mWidget.setFont(font);
    FramelessWindow::setFont(font);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setTextTop(QString text)
{
    mWidget.setTextTop(text);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setTextMiddle(QString text)
{
    mWidget.setTextMiddle(text);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setTextBottom(QString text)
{
    mWidget.setTextBottom(text);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setTextButtonYes(QString text)
{
    mWidget.setTextButtonYes(text);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setTextButtonNo(QString text)
{
    mWidget.setTextButtonNo(text);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setColorTitle(QColor color, QColor colorBG)
{
    setTitleColor(color);
    setTitleBackground(colorBG);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setColorBody(QColor color)
{
    QString style;
    style = QString("QWidget {"
                    "   background-color: %1;"
                    "}").arg(color.name());
    setStyleSheet(style);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setColorTextTop(QColor color)
{
    mWidget.setColorTextTop(color);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setColorTextMiddle(QColor color)
{
    mWidget.setColorTextMiddle(color);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setColorTextBottom(QColor color)
{
    mWidget.setColorTextBottom(color);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setColorTextButtonYes(QColor color, QColor colorBG)
{
    mWidget.setColorTextButtonYes(color, colorBG);
    return *this;
}
MessageBoxS1M1& MessageBoxS1M1::setColorTextButtonNo(QColor color, QColor colorBG)
{
    mWidget.setColorTextButtonNo(color, colorBG);
    return *this;
}


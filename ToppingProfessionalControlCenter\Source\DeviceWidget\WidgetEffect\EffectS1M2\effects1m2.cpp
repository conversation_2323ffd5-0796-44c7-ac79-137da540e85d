#include "effects1m2.h"
#include "globalfont.h"
#include "ui_effects1m2.h"


EffectS1M2::EffectS1M2(QWidget* parent, QString name)
    : EffectBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::EffectS1M2)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "   color:rgb(161,161,161);"
            "}";
    ui->frameLeft->setStyleSheet(style);
    ui->frameLeft->raise();
    style = "QFrame {"
            "   border-radius: 8px;"
            "   color:rgb(64, 63, 63);"
            "}";
    ui->frameRight->setStyleSheet(style);
    ui->frameRight->lower();
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 5px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetVolumeMeter->setWidthRatio(23, 30, 6, 35);
    ui->widgetVolumeMeter->setScaleLineHidden(true);
    ui->widgetDial->showCircle(false).setRange(0, 99);
    ui->widgetDialDecay->showCircle(false);
    ui->widgetDialDecay->setColorBG(Qt::transparent);
    ui->widgetDialDecay->setColorCircleBG({204,204,204});
    ui->widgetDialRoom->showCircle(false);
    ui->widgetDialRoom->setColorBG(Qt::transparent);
    ui->widgetDialRoom->setColorCircleBG({204,204,204});
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup1, SIGNAL(buttonStateChanged(PushButtonS1M5::ButtonID, bool)), this, SLOT(in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M5::ButtonID, bool)), Qt::UniqueConnection);
    connect(ui->widgetDial, SIGNAL(valueChanged(float)), this, SLOT(in_widgetDial_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetDialDecay, SIGNAL(valueChanged(float)), this, SLOT(in_widgetDialDecay_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetDialRoom, SIGNAL(valueChanged(float)), this, SLOT(in_widgetDialRoom_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup2, SIGNAL(buttonStateChanged(PushButtonS1M7::ButtonID, bool)), this, SLOT(in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M7::ButtonID, bool)), Qt::UniqueConnection);
}
EffectS1M2::~EffectS1M2()
{
    delete ui;
}


// override
bool EffectS1M2::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void EffectS1M2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);

    int leftWidth = -1, rightWidth = -1, frameRightOffset = -1;
    if(mDisplayMode==expand)
    {
        frameRightOffset = 11;
        leftWidth = width() / 2;
        rightWidth = width() / 2;
        int navigationWidth = qMax(height(), minimumHeight()) / ((float) minimumHeight()) * 88 - leftWidth;
        ui->pushButtonNavigation->setFixedWidth(navigationWidth);
    }
    else if(mDisplayMode==collapse)
    {
        frameRightOffset = 0;
        leftWidth = qMax(height(), minimumHeight()) / ((float) minimumHeight()) * 160 /2 ;
        rightWidth = width()-leftWidth;
        ui->pushButtonNavigation->setFixedWidth(rightWidth);
    }
    // W
    float wPixelPerRatio=leftWidth / 100.0;
    int wSpace1=wPixelPerRatio * 13;
    int wMeter=wPixelPerRatio * 32;
    int wSpace2=wPixelPerRatio * 6;
    int wButtonGroup1=wPixelPerRatio * 42;
    int xMeter=wSpace1;
    int xButtonGroup1=wSpace1 + wMeter + wSpace2;
    // H
    float hPixelPerRatio = height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 0;
    int hMeter=hPixelPerRatio * 58;
    int hSpace2=hPixelPerRatio * 1;
    int hDial=hPixelPerRatio * 18;
    int hSpace3=hPixelPerRatio * 4;
    int hButtonGroup2=hPixelPerRatio * 6;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->frameLeft->setGeometry(0,0, leftWidth, height());
    ui->lineEdit->setGeometry(0, 0, leftWidth, hLineEdit);
    ui->pushButtonClose->setGeometry(leftWidth - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetVolumeMeter->setGeometry(xMeter, hLineEdit + hSpace1, wMeter, hMeter);
    ui->widgetPushButtonGroup1->setGeometry(xButtonGroup1, hLineEdit + hSpace1, wButtonGroup1, hMeter);
    ui->widgetPushButtonGroup2->setGeometry(0, hLineEdit + hSpace1 + hMeter + hSpace2 + hDial + hSpace3, leftWidth, hButtonGroup2);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.04;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);

    int labelHeight = hPixelPerRatio * 6;
    int diameter = qMin(leftWidth, hDial);
    int labelTopOffset = 5;
    QRect dialRect(0, hLineEdit + hSpace1 + hMeter + hSpace2, leftWidth, hDial);
    ui->widgetDial->setGeometry(dialRect);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->widgetDial->height()));
    ui->widgetDial->setFont(mFont);
    ui->labelDry->setGeometry(0, dialRect.bottom()-labelTopOffset, (leftWidth-diameter)/2, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelDry->height()));
    ui->labelDry->setFont(mFont);
    ui->labelWet->setGeometry((leftWidth-diameter)/2+diameter, dialRect.bottom()-labelTopOffset, (leftWidth-diameter)/2, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelWet->height()));
    ui->labelWet->setFont(mFont);

    int rightRemoveButtonWidth = rightWidth-ui->pushButtonNavigation->width();
    ui->frameRight->setGeometry(leftWidth-frameRightOffset,0, rightWidth+frameRightOffset, height());
    ui->pushButtonNavigation->setFixedHeight(15*height() / 100.0);
    ui->pushButtonNavigation->move(frameRightOffset+rightRemoveButtonWidth, 8*hPixelPerRatio);

    QRect roomRect(frameRightOffset, hLineEdit + hSpace1 + 40, rightRemoveButtonWidth, hDial);
    ui->widgetDialRoom->setGeometry(roomRect);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->widgetDialRoom->height()));
    ui->widgetDialRoom->setFont(mFont);
    ui->labelRoom->setGeometry(frameRightOffset, roomRect.top()-2*labelHeight, rightRemoveButtonWidth, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelRoom->height()));
    ui->labelRoom->setFont(mFont);

    ui->labelSize->setGeometry(frameRightOffset, roomRect.top()-labelHeight, rightRemoveButtonWidth, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelSize->height()));
    ui->labelSize->setFont(mFont);

    ui->labelSmall->setGeometry(frameRightOffset, roomRect.bottom()-labelTopOffset, rightRemoveButtonWidth/2, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelSmall->height()));
    ui->labelSmall->setFont(mFont);

    ui->labelLarge->setGeometry(frameRightOffset+rightRemoveButtonWidth/2, roomRect.bottom()-labelTopOffset, rightRemoveButtonWidth/2, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelLarge->height()));
    ui->labelLarge->setFont(mFont);

    QRect decayRect(frameRightOffset, hLineEdit + hSpace1 + hMeter + hSpace2, rightWidth-frameRightOffset, hDial);
    ui->widgetDialDecay->setGeometry(decayRect);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->widgetDialDecay->height()));
    ui->widgetDialDecay->setFont(mFont);
    ui->labelDecay->setGeometry(frameRightOffset, decayRect.top()-2*labelHeight, rightRemoveButtonWidth, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelDecay->height()));
    ui->labelDecay->setFont(mFont);

    ui->labelRate->setGeometry(frameRightOffset, decayRect.top()-labelHeight, rightRemoveButtonWidth, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelRate->height()));
    ui->labelRate->setFont(mFont);

    ui->labelMin->setGeometry(frameRightOffset, decayRect.bottom()-labelTopOffset, (rightRemoveButtonWidth-diameter)/2, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelMin->height()));
    ui->labelMin->setFont(mFont);

    ui->labelMax->setGeometry(frameRightOffset+(rightRemoveButtonWidth-diameter)/2+diameter, decayRect.bottom()-labelTopOffset, (rightRemoveButtonWidth-diameter)/2, labelHeight);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelMax->height()));
    ui->labelMax->setFont(mFont);
}
void EffectS1M2::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setPen(Qt::NoPen);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setBrush(QColor(128, 128, 128));
    int frameRightOffset = -1;
    if(mDisplayMode==expand)
    {
        frameRightOffset = 11;
    }
    else if(mDisplayMode==collapse)
    {
        frameRightOffset = 0;
    }
    QRect rightRect(width()/2-frameRightOffset,0,width()/2+frameRightOffset-ui->pushButtonNavigation->width(),height());
    painter.drawRoundedRect(rightRect,8,8);
}
void EffectS1M2::updateAttribute()
{
    if(isWidgetReady())
    {
        if(isWidgetEnable())
        {
            if(mPreReverbChannel != WorkspaceObserver::value("ReverbChannel").toString())
            {
                mPreReverbChannel = WorkspaceObserver::value("ReverbChannel").toString();
                emit attributeChanged(this->objectName(), "ReverbChannel", mPreReverbChannel);
            }
            if(mPreReverbType != WorkspaceObserver::value("ReverbType").toString())
            {
                mPreReverbType = WorkspaceObserver::value("ReverbType").toString();
                emit attributeChanged(this->objectName(), "ReverbType", mPreReverbType);
            }
            if(mPreDryWet != WorkspaceObserver::value("DryWet").toFloat())
            {
                mPreDryWet = WorkspaceObserver::value("DryWet").toFloat();
                emit attributeChanged(this->objectName(), "DryWet", QString::number(mPreDryWet));
            }
            if(mPreMute != static_cast<int>(WorkspaceObserver::value("MUTE").toBool()))
            {
                mPreMute = static_cast<int>(WorkspaceObserver::value("MUTE").toBool());
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMute));
            }
            if(mPreRoom != static_cast<int>(WorkspaceObserver::value("Room").toFloat()))
            {
                mPreRoom = static_cast<int>(WorkspaceObserver::value("Room").toFloat());
                emit attributeChanged(this->objectName(), "Room", QString::number(mPreRoom));
            }
            if(mPreDecay != static_cast<int>(WorkspaceObserver::value("Decay").toFloat()))
            {
                mPreDecay = static_cast<int>(WorkspaceObserver::value("Decay").toFloat());
                emit attributeChanged(this->objectName(), "Decay", QString::number(mPreDecay));
            }
        }
        else
        {
            if(mPreMute != static_cast<int>(true))
            {
                mPreMute = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMute));
            }
        }
    }
}
void EffectS1M2::loadSettings()
{
    mPreReverbChannel = "";
    mPreReverbType = "";
    mPreDryWet = -2147483648;
    mPreMute = -2147483648;
    mPreRoom = -2147483648;
    mPreDecay = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("ReverbChannel", "NC");
        WorkspaceObserver::setValue("ReverbType", "OFF");
        WorkspaceObserver::setValue("DryWet", 0);
        WorkspaceObserver::setValue("Room", 0);
        WorkspaceObserver::setValue("Decay", 0);
        WorkspaceObserver::setValue("MUTE", false);
        WorkspaceObserver::setValue("NavigationDisplayMode", (int)collapse);
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    if(WorkspaceObserver::value("ReverbChannel").toString() == "NC")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC(true);
    }
    else if(WorkspaceObserver::value("ReverbChannel").toString() == "IN1")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN1(true);
    }
    else if(WorkspaceObserver::value("ReverbChannel").toString() == "IN2")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN2(true);
    }
    else if(WorkspaceObserver::value("ReverbChannel").toString() == "IN12")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN12(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC(true);
    }
    if(WorkspaceObserver::value("ReverbType").toString() == "OFF")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateOFF(true);
    }
    else if(WorkspaceObserver::value("ReverbType").toString() == "STU")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateSTU(true);
    }
    else if(WorkspaceObserver::value("ReverbType").toString() == "LIVE")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateLIVE(true);
    }
    else if(WorkspaceObserver::value("ReverbType").toString() == "HALL")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateHALL(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateOFF(true);
    }
    NavigationDisplayMode navigationDisplayMode = (NavigationDisplayMode)WorkspaceObserver::value("NavigationDisplayMode").toInt();
    setNavigationDisplayMode(navigationDisplayMode);
    ui->widgetDial->setValue(WorkspaceObserver::value("DryWet").toFloat());
    ui->widgetDialRoom->setValue(WorkspaceObserver::value("Room").toFloat());
    ui->widgetDialDecay->setValue(WorkspaceObserver::value("Decay").toFloat());
    ui->widgetPushButtonGroup2->setPushButtonStateMUTE(WorkspaceObserver::value("MUTE").toBool());
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_ReverbChannel", WorkspaceObserver::value("ReverbChannel").toString());
        emit attributeChanged(this->objectName(), "Save_ReverbType", WorkspaceObserver::value("ReverbType").toString());
        emit attributeChanged(this->objectName(), "Save_DryWet", WorkspaceObserver::value("DryWet").toString());
        emit attributeChanged(this->objectName(), "Save_Room", WorkspaceObserver::value("Room").toString());
        emit attributeChanged(this->objectName(), "Save_Decay", WorkspaceObserver::value("Decay").toString());
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
        emit attributeChanged(this->objectName(), "Save_NavigationDisplayMode", WorkspaceObserver::value("NavigationDisplayMode").toString());
    }
    setWidgetReady(true);
    updateAttribute();
}
void EffectS1M2::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}

void EffectS1M2::setNavigationDisplayMode(NavigationDisplayMode mode)
{
    if(mDisplayMode == mode)
        return;

    bool visible = false;
    QString style;
    if(mode==invalid){
        if(mDisplayMode==collapse)
            mDisplayMode=expand;
        else if(mDisplayMode==expand)
            mDisplayMode=collapse;
    }else{
        mDisplayMode = mode;
    }

    if(mDisplayMode==expand)
    {
        setMinimumWidth(160);
        visible=true;
        style = "QPushButton {"
                "border-image: url(:/Icon/NavigationButton.png);"
                "}";
    }
    else if(mDisplayMode==collapse)
    {
        //int maxHeight = qMax(height(), minimumHeight());
        //double needMinWidth = (maxHeight / (static_cast<double>(minimumHeight())) * minimumWidth() / 2.0 + ui->pushButtonNavigation->width()) * minimumHeight() / maxHeight;
        setMinimumWidth(88);
        visible=false;
        style = "QPushButton {"
                "border-image: url(:/Icon/NavigationButton.png);"
                "}";
    }
    ui->pushButtonNavigation->setStyleSheet(style);
    ui->labelRoom->setVisible(visible);
    ui->labelSize->setVisible(visible);
    ui->labelSmall->setVisible(visible);
    ui->labelLarge->setVisible(visible);
    ui->labelDecay->setVisible(visible);
    ui->labelRate->setVisible(visible);
    ui->labelMin->setVisible(visible);
    ui->labelMax->setVisible(visible);
    ui->widgetDialRoom->setVisible(visible);
    ui->widgetDialDecay->setVisible(visible);
    emit attributeChanged(getChannelName(), "Resize", "");
    resizeEvent(nullptr);
}

// slot
void EffectS1M2::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void EffectS1M2::in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M5::ButtonID button, bool state)
{
    Q_UNUSED(state);
    switch(button)
    {
        case PushButtonS1M5::buttonNC:
            save("ReverbChannel", "NC");
            break;
        case PushButtonS1M5::buttonIN1:
            save("ReverbChannel", "IN1");
            break;
        case PushButtonS1M5::buttonIN2:
            save("ReverbChannel", "IN2");
            break;
        case PushButtonS1M5::buttonIN12:
            save("ReverbChannel", "IN12");
            break;
        case PushButtonS1M5::buttonOFF:
            save("ReverbType", "OFF");
            break;
        case PushButtonS1M5::buttonSTU:
            save("ReverbType", "STU");
            break;
        case PushButtonS1M5::buttonLIVE:
            save("ReverbType", "LIVE");
            break;
        case PushButtonS1M5::buttonHALL:
            save("ReverbType", "HALL");
            break;
        default:
            break;
    }
    updateAttribute();
}
void EffectS1M2::in_widgetDial_valueChanged(float value)
{
    save("DryWet", value);
    updateAttribute();
}

void EffectS1M2::in_widgetDialRoom_valueChanged(float value)
{
    save("Room", value);
    updateAttribute();
}

void EffectS1M2::in_widgetDialDecay_valueChanged(float value)
{
    save("Decay", value);
    updateAttribute();
}
void EffectS1M2::in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M7::ButtonID button, bool state)
{
    switch(button)
    {
        case PushButtonS1M7::buttonMUTE:
            save("MUTE", state);
            updateAttribute();
            break;
        default:
            break;
    }
}
void EffectS1M2::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void EffectS1M2::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void EffectS1M2::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}


// setter & getter
void EffectS1M2::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
EffectS1M2& EffectS1M2::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
EffectS1M2& EffectS1M2::setFont(QFont font)
{
    mFont = font;
    ui->widgetVolumeMeter->setFont(font);
    ui->widgetPushButtonGroup1->setFont(font);
    ui->widgetDial->setFont(font);
    ui->widgetPushButtonGroup2->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
EffectS1M2& EffectS1M2::setVolumeMeter(int value)
{
    ui->widgetVolumeMeter->setValue(value);
    return *this;
}

EffectS1M2& EffectS1M2::setVolumeMeterClear()
{
    ui->widgetVolumeMeter->setMeterClear();
    return *this;
}

EffectS1M2& EffectS1M2::setVolumeMeterSlip()
{
    ui->widgetVolumeMeter->setMeterSlip();
    return *this;
}

EffectS1M2 &EffectS1M2::setDialTexts(const QStringList &texts)
{
    if(texts.size()!=10)
        return *this;
    ui->labelDry->setText(texts.at(0));
    ui->labelWet->setText(texts.at(1));
    ui->labelRoom->setText(texts.at(2));
    ui->labelSize->setText(texts.at(3));
    ui->labelSmall->setText(texts.at(4));
    ui->labelLarge->setText(texts.at(5));
    ui->labelDecay->setText(texts.at(6));
    ui->labelRate->setText(texts.at(7));
    ui->labelMin->setText(texts.at(8));
    ui->labelMax->setText(texts.at(9));
    return *this;
}


void EffectS1M2::on_pushButtonNavigation_clicked()
{
    setNavigationDisplayMode();
    save("NavigationDisplayMode", (int)mDisplayMode);
}

EffectS1M2& EffectS1M2::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}
EffectS1M2& EffectS1M2::setValueReverbChannel(QString channel)
{
    mPreReverbChannel = channel;
    if(mPreReverbChannel == "NC")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC(true);
    }
    else if(mPreReverbChannel == "IN1")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN1(true);
    }
    else if(mPreReverbChannel == "IN2")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN2(true);
    }
    else if(mPreReverbChannel == "IN12")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN12(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC(true);
    }
    save("ReverbChannel", mPreReverbChannel);
    return *this;
}
EffectS1M2& EffectS1M2::setValueReverbType(QString type)
{
    mPreReverbType = type;
    if(mPreReverbType == "OFF")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateOFF(true);
    }
    else if(mPreReverbType == "STU")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateSTU(true);
    }
    else if(mPreReverbType == "LIVE")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateLIVE(true);
    }
    else if(mPreReverbType == "HALL")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateHALL(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateOFF(true);
    }
    save("ReverbType", mPreReverbType);
    return *this;
}
EffectS1M2& EffectS1M2::setValueDryWet(float value)
{
    mPreDryWet = value;
    ui->widgetDial->setValue(mPreDryWet);
    save("DryWet", mPreDryWet);
    return *this;
}
EffectS1M2& EffectS1M2::setValueRoom(float value)
{
    mPreRoom = value;
    ui->widgetDialRoom->setValue(mPreRoom);
    save("Room", mPreRoom);
    return *this;
}
EffectS1M2& EffectS1M2::setValueDecay(float value)
{
    mPreDecay = value;
    ui->widgetDialDecay->setValue(mPreDecay);
    save("Decay", mPreDecay);
    return *this;
}
EffectS1M2& EffectS1M2::setValueMUTE(bool state)
{
    mPreMute = state;
    ui->widgetPushButtonGroup2->setPushButtonStateMUTE(mPreMute);
    save("MUTE", mPreMute);
    return *this;
}


#include <QDebug>
#include <QtCore/qhash.h>

#include "usbaudioapi.h"

USBAudioAPI USBAudioAPI::mInstance;

#ifdef Q_OS_WIN
#if (USB_AUDIO_SERIES == SERIES_PROFESSIONAL)
TCHAR strGUID[]={'{','E','9','7','E','1','5','0','8','-','2','A','6','6','-','4','D','F','8','-','B','7','A','7','-','3','2','D','6','E','5','F','C','B','A','4','6','}','\0'};
#elif (USB_AUDIO_SERIES == SERIES_DECODER)
TCHAR strGUID[]={'{','3','C','E','D','0','A','0','E','-','6','7','3','A','-','4','E','4','9','-','8','7','7','7','-','F','5','0','D','B','1','3','5','0','1','3','6','}','\0'};
#else
TCHAR strGUID[];
#endif


USBAudioAPI::USBAudioAPI()
{
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    mMixer=new TUsbAudioMixer(mAPI);
#endif
}
USBAudioAPI::~USBAudioAPI()
{
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    delete mMixer;
#endif
}


bool USBAudioAPI::init()
{
    mAPI.LoadByGUID(strGUID);
    unsigned int apiVersion = mAPI.TUSBAUDIO_GetApiVersion();
    qInfo("USBAudioAPI::Init //usb audio version API[%u.%02u] SDK[%u.%02u]", apiVersion >> 16, apiVersion & 0xFFFF, TUSBAUDIO_API_VERSION_MJ, TUSBAUDIO_API_VERSION_MN);
    if(!mAPI.TUSBAUDIO_CheckApiVersion(TUSBAUDIO_API_VERSION_MJ, TUSBAUDIO_API_VERSION_MN))
    {
        qWarning() << "USBAudioAPI::Init //failed to check api version";
        return false;
    }
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    mMixer->SetMaxChannelCounts(1024, 1024, 1024, 1024, 1024, 1024);
#endif
    return true;
}


// getter
unsigned int USBAudioAPI::getNumberOfAvailableDevice()
{
    if(mAPI.TUSBAUDIO_EnumerateDevices() != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getNumberOfAvailableDevice //failed to enumerate devices";
    }
    return mAPI.TUSBAUDIO_GetDeviceCount();
}
QVector<TUsbAudioDeviceProperties> USBAudioAPI::getPropertiesOfAllDevice()
{
    unsigned int deviceCount=getNumberOfAvailableDevice();
    TUsbAudioHandle deviceHandle;
    TUsbAudioDeviceProperties deviceProperties;
    QVector<TUsbAudioDeviceProperties> devicePropertiesVector;
    for(unsigned int i=0;i<deviceCount;i++)
    {
        if(mAPI.TUSBAUDIO_OpenDeviceByIndex(i, &deviceHandle) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::getPropertiesOfAllDevice //failed to open device by index";
            continue;
        }
        if(mAPI.TUSBAUDIO_GetDeviceProperties(deviceHandle, &deviceProperties) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::getPropertiesOfAllDevice //failed to get device properties";
            continue;
        }
        devicePropertiesVector.push_back(deviceProperties);
        if(QString::fromWCharArray(deviceProperties.productString) == mDeviceName)
        {
            mDeviceHandle = deviceHandle;
#if (USB_AUDIO_MIXER_SUPPORT == 1)
            mMixer->DetachFromDevice();
            mMixer->AttachToDevice(mDeviceHandle);
#if (USB_AUDIO_LEVEL_METER_SUPPORT == 1)
            mMixer->EnableLevelMeters();
#endif
#endif
            continue;
        }
        if(mAPI.TUSBAUDIO_CloseDevice(deviceHandle) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::getPropertiesOfAllDevice //failed to close device";
            continue;
        }
    }
    return devicePropertiesVector;
}
QVector<QString> USBAudioAPI::getNameOfAllDevice()
{
    unsigned int deviceCount=getNumberOfAvailableDevice();
    TUsbAudioHandle deviceHandle;
    TUsbAudioDeviceProperties deviceProperties;
    QVector<QString> deviceNameVector;
    for(unsigned int i=0;i<deviceCount;i++)
    {
        if(mAPI.TUSBAUDIO_OpenDeviceByIndex(i, &deviceHandle) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::getNameOfAllDevice //failed to open device by index";
            continue;
        }
        if(mAPI.TUSBAUDIO_GetDeviceProperties(deviceHandle, &deviceProperties) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::getNameOfAllDevice //failed to get device properties";
            continue;
        }
        deviceNameVector.push_back(QString::fromWCharArray(deviceProperties.productString));
        if(QString::fromWCharArray(deviceProperties.productString) == mDeviceName)
        {
            mDeviceHandle = deviceHandle;
#if (USB_AUDIO_MIXER_SUPPORT == 1)
            mMixer->DetachFromDevice();
            mMixer->AttachToDevice(mDeviceHandle);
#if (USB_AUDIO_LEVEL_METER_SUPPORT == 1)
            mMixer->EnableLevelMeters();
#endif
#endif
            continue;
        }
        if(mAPI.TUSBAUDIO_CloseDevice(deviceHandle) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::getNameOfAllDevice //failed to close device";
            continue;
        }
    }
    return deviceNameVector;
}
QVector<unsigned int> USBAudioAPI::getSampleRateOfActiveDeviceSupported()
{
    QVector<unsigned int> supportedSampleRateVector;
    unsigned int supportedSampleRateBuffer[64];
    unsigned int supportedSampleRateNumber=0;
    if(mAPI.TUSBAUDIO_GetSupportedSampleRates(mDeviceHandle, 64, supportedSampleRateBuffer, &supportedSampleRateNumber) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getSampleRateOfActiveDeviceSupported //failed to get supported sample rates";
        return supportedSampleRateVector;
    }
    for(unsigned int i=0;i<supportedSampleRateNumber;i++)
    {
        supportedSampleRateVector.push_back(supportedSampleRateBuffer[i]);
    }
    return supportedSampleRateVector;
}
unsigned int USBAudioAPI::getSampleRateOfActiveDevice()
{
    unsigned int currentSampleRate=0;
    if(mAPI.TUSBAUDIO_GetCurrentSampleRate(mDeviceHandle, &currentSampleRate) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getSampleRateOfActiveDevice //failed to get current sample rate";
        return 0;
    }
    return currentSampleRate;
}
QVector<unsigned int> USBAudioAPI::getBufferSizeOfActiveDeviceSupported()
{
    TUsbAudioASIOInstanceDetails detailsOfASIOInstance;
    TUsbAudioASIOInstanceInfo infoOfASIOInstance;
    QVector<unsigned int> supportedBufferSizeVector;
    if(mAPI.TUSBAUDIO_GetASIOInstanceDetails(0, &detailsOfASIOInstance) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getBufferSizeOfActiveDeviceSupported //failed to get asio instance details";
        return supportedBufferSizeVector;
    }
    if(mAPI.TUSBAUDIO_GetASIOInstanceInfo(detailsOfASIOInstance.asioInstanceId, &infoOfASIOInstance) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getBufferSizeOfActiveDeviceSupported //failed to get asio instance info";
        return supportedBufferSizeVector;
    }
    for(unsigned int i=0;i<infoOfASIOInstance.supportedSizesCount;i++)
    {
        supportedBufferSizeVector.push_back(infoOfASIOInstance.supportedSizes[i]);
    }
    return supportedBufferSizeVector;
}
unsigned int USBAudioAPI::getBufferSizeOfActiveDevice()
{
    TUsbAudioASIOInstanceDetails detailsOfASIOInstance;
    TUsbAudioASIOInstanceInfo infoOfASIOInstance;
    if(mAPI.TUSBAUDIO_GetASIOInstanceDetails(0, &detailsOfASIOInstance) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getBufferSizeOfActiveDevice //failed to get asio instance details";
        return 0;
    }
    if(mAPI.TUSBAUDIO_GetASIOInstanceInfo(detailsOfASIOInstance.asioInstanceId, &infoOfASIOInstance) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getBufferSizeOfActiveDevice //failed to get asio instance info";
        return 0;
    }
    return infoOfASIOInstance.preferredSize;
}
bool USBAudioAPI::getSafeModeOfActiveDevice()
{
    TUsbAudioASIOInstanceDetails detailsOfASIOInstance;
    TUsbAudioASIOInstanceInfo infoOfASIOInstance;
    if(mAPI.TUSBAUDIO_GetASIOInstanceDetails(0, &detailsOfASIOInstance) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getSafeModeOfActiveDevice //failed to get asio instance details";
        return false;
    }
    if(mAPI.TUSBAUDIO_GetASIOInstanceInfo(detailsOfASIOInstance.asioInstanceId, &infoOfASIOInstance) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getSafeModeOfActiveDevice //failed to get asio instance info";
        return false;
    }
    if(infoOfASIOInstance.options)
    {
        return true;
    }
    return false;
}
unsigned int USBAudioAPI::getDFUBytesTotal()
{
    DFUStatus statusDFU;
    if(mAPI.TUSBAUDIO_GetDfuStatus(&statusDFU.statusDfuProc, &statusDFU.currentBytes, &statusDFU.totalBytes, &statusDFU.statusCompletion) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getDFUBytesTotal //failed to get dfu status";
        return 0;
    }
    return statusDFU.totalBytes;
}
unsigned int USBAudioAPI::getDFUBytesCurrent()
{
    DFUStatus statusDFU;
    if(mAPI.TUSBAUDIO_GetDfuStatus(&statusDFU.statusDfuProc, &statusDFU.currentBytes, &statusDFU.totalBytes, &statusDFU.statusCompletion) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getDFUBytesCurrent //failed to get dfu status";
        return 0;
    }
    return statusDFU.currentBytes;
}
unsigned int USBAudioAPI::getDFUPercentageRemain()
{
    DFUStatus statusDFU;
    if(mAPI.TUSBAUDIO_GetDfuStatus(&statusDFU.statusDfuProc, &statusDFU.currentBytes, &statusDFU.totalBytes, &statusDFU.statusCompletion) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getDFUPercentageRemain //failed to get dfu status";
        return 0;
    }
    if(statusDFU.statusDfuProc == DfuProcState_Failed)
    {
        qWarning() << "USBAudioAPI::getDFUPercentageRemain //dfu failed";
        return 0;
    }
    if(statusDFU.totalBytes == 0)
    {
        qWarning() << "USBAudioAPI::getDFUPercentageRemain //dfu status totalBytes is 0";
        return 0;
    }
    unsigned int percentage = ((statusDFU.totalBytes - statusDFU.currentBytes) * 100 / statusDFU.totalBytes);
    if(percentage <= 1)
    {
        percentage = 1;
    }
    else if(percentage >= 100)
    {
        percentage = 100;
    }
    return percentage;
}
unsigned int USBAudioAPI::getDFUPercentageComplete()
{
    DFUStatus statusDFU;
    if(mAPI.TUSBAUDIO_GetDfuStatus(&statusDFU.statusDfuProc, &statusDFU.currentBytes, &statusDFU.totalBytes, &statusDFU.statusCompletion) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getDFUPercentageComplete //failed to get dfu status";
        return 0;
    }
    if(statusDFU.statusDfuProc == DfuProcState_Failed)
    {
        qWarning() << "USBAudioAPI::getDFUPercentageComplete //dfu failed";
        return 0;
    }
    if(statusDFU.totalBytes == 0)
    {
        qWarning() << "USBAudioAPI::getDFUPercentageComplete //dfu status totalBytes is 0";
        return 0;
    }
    unsigned int percentage = (statusDFU.currentBytes * 100 / statusDFU.totalBytes);
    if(percentage <= 1)
    {
        percentage = 1;
    }
    else if(percentage >= 100)
    {
        percentage = 100;
    }
    return percentage;
}
#if (USB_AUDIO_MIXER_SUPPORT == 1)
int USBAudioAPI::getNodeGain(unsigned int matrix_X, unsigned int matrix_Y)
{
    return mMixer->GetNodeGain(mNodeMatrix.at(matrix_X - 1)[matrix_Y - 1]);
}
unsigned int USBAudioAPI::getNodeGainPercent(unsigned int matrix_X, unsigned int matrix_Y)
{
    return TUsbAudioMixer::GainToPercent(mMixer->GetNodeGain(mNodeMatrix.at(matrix_X - 1)[matrix_Y - 1]));
}
double USBAudioAPI::getNodeGainLinear(unsigned int matrix_X, unsigned int matrix_Y)
{
    return TUsbAudioMixer::GainToLinear(mMixer->GetNodeGain(mNodeMatrix.at(matrix_X - 1)[matrix_Y - 1]));
}
double USBAudioAPI::getNodeGainLog(unsigned int matrix_X, unsigned int matrix_Y)
{
    return TUsbAudioMixer::GainToLog(mMixer->GetNodeGain(mNodeMatrix.at(matrix_X - 1)[matrix_Y - 1]));
}
int USBAudioAPI::getOutputGain(unsigned int matrix_X)
{
    return mMixer->GetOutputGain(mNodeMatrix.at(matrix_X - 1)[0].outputChannel);
}
unsigned int USBAudioAPI::getOutputGainPercent(unsigned int matrix_X)
{
    return TUsbAudioMixer::GainToPercent(mMixer->GetOutputGain(mNodeMatrix.at(matrix_X - 1)[0].outputChannel));
}
double USBAudioAPI::getOutputGainLinear(unsigned int matrix_X)
{
    return TUsbAudioMixer::GainToLinear(mMixer->GetOutputGain(mNodeMatrix.at(matrix_X - 1)[0].outputChannel));
}
double USBAudioAPI::getOutputGainLog(unsigned int matrix_X)
{
    return TUsbAudioMixer::GainToLog(mMixer->GetOutputGain(mNodeMatrix.at(matrix_X - 1)[0].outputChannel));
}
#if (USB_AUDIO_LEVEL_METER_SUPPORT == 1)
short USBAudioAPI::getLevelMeterOfChannelOutput(unsigned int matrix_X)
{
    if(mMixer->UpdateLevelMeterData() != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getLevelMeterOfChannelOutput //failed to update level meter data";
        return -1024;
    }
    return mMixer->QueryLevelMeter(mNodeMatrix.at(matrix_X - 1)[0].outputChannel) / 10;
}
QVector<short> USBAudioAPI::getLevelMeterOfChannelOutput()
{
    QVector<short> vectorLevel;
    std::vector<TUsbAudioMixer::Level> vector;
    if(mMixer->UpdateLevelMeterData() != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getLevelMeterOfChannelOutput //failed to update level meter data";
        return vectorLevel;
    }
    vector = mMixer->QueryLevelMeters(TUsbAudioMixer::Pin::DeviceOutput);
    for(std::vector<short>::iterator it=vector.begin();it!=vector.end();++it)
    {
        vectorLevel.push_back((*it) / 10);
    }
    vector = mMixer->QueryLevelMeters(TUsbAudioMixer::Pin::AppRecording);
    for(std::vector<short>::iterator it=vector.begin();it!=vector.end();++it)
    {
        vectorLevel.push_back((*it) / 10);
    }
    vector = mMixer->QueryLevelMeters(TUsbAudioMixer::Pin::AppRecordingVirt);
    for(std::vector<short>::iterator it=vector.begin();it!=vector.end();++it)
    {
        vectorLevel.push_back((*it) / 10);
    }
    return vectorLevel;
}
short USBAudioAPI::getLevelMeterOfChannelInput(unsigned int matrix_Y)
{
    if(mMixer->UpdateLevelMeterData() != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getLevelMeterOfChannelInput //failed to update level meter data";
        return -1024;
    }
    return mMixer->QueryLevelMeter(mNodeMatrix.at(0)[matrix_Y - 1].inputChannel) / 10;
}
QVector<short> USBAudioAPI::getLevelMeterOfChannelInput()
{
    QVector<short> vectorLevel;
    std::vector<TUsbAudioMixer::Level> vector;
    if(mMixer->UpdateLevelMeterData() != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::getLevelMeterOfChannelInput //failed to update level meter data";
        return vectorLevel;
    }
    vector = mMixer->QueryLevelMeters(TUsbAudioMixer::Pin::AppPlayback);
    for(std::vector<short>::iterator it=vector.begin();it!=vector.end();++it)
    {
        vectorLevel.push_back((*it) / 10);
    }
    vector = mMixer->QueryLevelMeters(TUsbAudioMixer::Pin::DeviceInput);
    for(std::vector<short>::iterator it=vector.begin();it!=vector.end();++it)
    {
        vectorLevel.push_back((*it) / 10);
    }
    vector = mMixer->QueryLevelMeters(TUsbAudioMixer::Pin::AppPlaybackVirt);
    for(std::vector<short>::iterator it=vector.begin();it!=vector.end();++it)
    {
        vectorLevel.push_back((*it) / 10);
    }
    return vectorLevel;
}
#endif
#endif


// setter
USBAudioAPI& USBAudioAPI::setDeviceToReset()
{
    mDeviceName = "None";
    if(mAPI.TUSBAUDIO_CloseDevice(mDeviceHandle) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::setDeviceToReset //failed to close device";
    }
    return *this;
}
bool USBAudioAPI::setDeviceToActiveByName(QString deviceName)
{
    unsigned int deviceCounter=0;
    QVector<TUsbAudioDeviceProperties> devicePropertiesVector=getPropertiesOfAllDevice();
    for(QVector<TUsbAudioDeviceProperties>::iterator it=devicePropertiesVector.begin();it!=devicePropertiesVector.end();++it)
    {
        if(QString::fromWCharArray(it->productString) == deviceName)
        {
            if(mAPI.TUSBAUDIO_OpenDeviceByIndex(deviceCounter, &mDeviceHandle) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDeviceToActiveByName //failed to open device by index";
                return false;
            }
            if(mAPI.TUSBAUDIO_SetPreferredASIODevice(mDeviceHandle, 0) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDeviceToActiveByName //failed to set preferred asio device";
            }
            if(mAPI.TUSBAUDIO_CloseDevice(mDeviceHandle) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDeviceToActiveByName //failed to close device";
                return false;
            }
            if(mAPI.TUSBAUDIO_OpenDeviceByIndex(deviceCounter, &mDeviceHandle) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDeviceToActiveByName //failed to open device by index";
                return false;
            }
            mDeviceName = QString::fromWCharArray(it->productString);
#if (USB_AUDIO_MIXER_SUPPORT == 1)
            mMixer->DetachFromDevice();
            mMixer->AttachToDevice(mDeviceHandle);
#if (USB_AUDIO_LEVEL_METER_SUPPORT == 1)
            mMixer->EnableLevelMeters();
#endif
            setMixerNodeAttachToActiveDevice();
#endif
            return true;
        }
        deviceCounter++;
    }
    qWarning() << "USBAudioAPI::setDeviceToActiveByName //invalid device name";
    return false;
}
bool USBAudioAPI::setDeviceToActiveByID(unsigned int deviceVendorId, unsigned int deviceProductId)
{
    unsigned int deviceCounter=0;
    QVector<TUsbAudioDeviceProperties> devicePropertiesVector=getPropertiesOfAllDevice();

    for(QVector<TUsbAudioDeviceProperties>::iterator it=devicePropertiesVector.begin();it!=devicePropertiesVector.end();++it)
    {
        if(it->usbVendorId == deviceVendorId && it->usbProductId == deviceProductId)
        {
            if(mAPI.TUSBAUDIO_OpenDeviceByIndex(deviceCounter, &mDeviceHandle) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDeviceToActiveByID //failed to open device by index";
                return false;
            }
            if(mAPI.TUSBAUDIO_SetPreferredASIODevice(mDeviceHandle, 0) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDeviceToActiveByID //failed to set preferred asio device";
            }
            if(mAPI.TUSBAUDIO_CloseDevice(mDeviceHandle) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDeviceToActiveByID //failed to close device";
                return false;
            }
            if(mAPI.TUSBAUDIO_OpenDeviceByIndex(deviceCounter, &mDeviceHandle) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDeviceToActiveByID //failed to open device by index";
                return false;
            }
            mDeviceName = QString::fromWCharArray(it->productString);
#if (USB_AUDIO_MIXER_SUPPORT == 1)
            mMixer->DetachFromDevice();
            mMixer->AttachToDevice(mDeviceHandle);
#if (USB_AUDIO_LEVEL_METER_SUPPORT == 1)
            mMixer->EnableLevelMeters();
#endif
            setMixerNodeAttachToActiveDevice();
#endif
            return true;
        }
        deviceCounter++;
    }
    qWarning() << "USBAudioAPI::setDeviceToActiveByID //invalid device VID/PID";
    return false;
}
bool USBAudioAPI::setSampleRateOfActiveDevice(unsigned int sampleRate)
{
    QVector<unsigned int> supportedSampleRateVector=getSampleRateOfActiveDeviceSupported();
    if(supportedSampleRateVector.contains(sampleRate))
    {
        if(mAPI.TUSBAUDIO_SetSampleRate(mDeviceHandle, sampleRate) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::setSampleRateOfActiveDevice //failed to set sample rate";
            return false;
        }
        return true;
    }
    return false;
}
bool USBAudioAPI::setBufferSizeOfActiveDevice(unsigned int bufferSize)
{
    QVector<unsigned int> supportedBufferSizeVector=getBufferSizeOfActiveDeviceSupported();
    TUsbAudioASIOInstanceDetails detailsOfASIOInstance;
    TUsbAudioASIOInstanceInfo infoOfASIOInstance;
    if(supportedBufferSizeVector.contains(bufferSize))
    {
        if(mAPI.TUSBAUDIO_GetASIOInstanceDetails(0, &detailsOfASIOInstance) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::setBufferSizeOfActiveDevice //failed to get asio instance details";
            return false;
        }
        if(mAPI.TUSBAUDIO_GetASIOInstanceInfo(detailsOfASIOInstance.asioInstanceId, &infoOfASIOInstance) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::setBufferSizeOfActiveDevice //failed to get asio instance info";
            return false;
        }
        if(mAPI.TUSBAUDIO_SetASIOBufferPreferredSize(detailsOfASIOInstance.asioInstanceId, infoOfASIOInstance.referenceSampleRate, bufferSize, infoOfASIOInstance.options) != TSTATUS_SUCCESS)
        {
            qWarning() << "USBAudioAPI::setBufferSizeOfActiveDevice //failed to set asio buffer preferred size";
            return false;
        }
        return true;
    }
    return false;
}
bool USBAudioAPI::setSafeModeOfActiveDevice(bool safeMode)
{
    TUsbAudioASIOInstanceDetails detailsOfASIOInstance;
    TUsbAudioASIOInstanceInfo infoOfASIOInstance;
    if(mAPI.TUSBAUDIO_GetASIOInstanceDetails(0, &detailsOfASIOInstance) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::setSafeModeOfActiveDevice //failed to get asio instance details";
        return false;
    }
    if(mAPI.TUSBAUDIO_GetASIOInstanceInfo(detailsOfASIOInstance.asioInstanceId, &infoOfASIOInstance) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::setSafeModeOfActiveDevice //failed to get asio instance info";
        return false;
    }
    if(mAPI.TUSBAUDIO_SetASIOBufferPreferredSize(detailsOfASIOInstance.asioInstanceId, infoOfASIOInstance.referenceSampleRate, infoOfASIOInstance.preferredSize, safeMode ? TUSBAUDIO_ASIO_OPT_SAFE_MODE : 0) != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::setSafeModeOfActiveDevice //failed to set asio buffer preferred size";
        return false;
    }
    return true;
}
unsigned int USBAudioAPI::setDFUStart(QString path)
{
    if(mDeviceName.isEmpty())
    {
        qWarning() << "USBAudioAPI::setDFUStart //no device connected";
    }
    else
    {
        unsigned int deviceCounter=0;
        unsigned int imageSize=0;
        QVector<TUsbAudioDeviceProperties> devicePropertiesVector=getPropertiesOfAllDevice();
        for(QVector<TUsbAudioDeviceProperties>::iterator it=devicePropertiesVector.begin();it!=devicePropertiesVector.end();++it)
        {
            if(QString::fromWCharArray(it->productString) == mDeviceName)
            {
                if(mAPI.TUSBAUDIO_LoadFirmwareImageFromFile(reinterpret_cast<TCHAR*>(const_cast<ushort*>(path.utf16())), deviceCounter) != TSTATUS_SUCCESS)
                {
                    qWarning() << "USBAudioAPI::setDFUStart //failed to load firmware image from file";
                    return 0;
                }
                if(mAPI.TUSBAUDIO_GetFirmwareImageSize(&imageSize, 0) != TSTATUS_SUCCESS)
                {
                    qWarning() << "USBAudioAPI::setDFUStart //failed to get firmware image size";
                    return 0;
                }
                if(mAPI.TUSBAUDIO_StartDfuDownload(deviceCounter, 0, 0) != TSTATUS_SUCCESS)
                {
                    qWarning() << "USBAudioAPI::setDFUStart //failed to start dfu download";
                    mAPI.TUSBAUDIO_UnloadFirmwareImage();
                    return 0;
                }
                return imageSize;
            }
            deviceCounter++;
        }
    }
    return 0;
}
unsigned int USBAudioAPI::setDFUStartByName(QString path, QString deviceName)
{
    unsigned int deviceCounter=0;
    unsigned int imageSize=0;
    QVector<TUsbAudioDeviceProperties> devicePropertiesVector=getPropertiesOfAllDevice();
    for(QVector<TUsbAudioDeviceProperties>::iterator it=devicePropertiesVector.begin();it!=devicePropertiesVector.end();++it)
    {
        if(QString::fromWCharArray(it->productString) == deviceName)
        {
            if(mAPI.TUSBAUDIO_LoadFirmwareImageFromFile(reinterpret_cast<TCHAR*>(const_cast<ushort*>(path.utf16())), deviceCounter) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDFUStartByName //failed to load firmware image from file";
                return 0;
            }
            if(mAPI.TUSBAUDIO_GetFirmwareImageSize(&imageSize, 0) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDFUStartByName //failed to get firmware image size";
                return 0;
            }
            if(mAPI.TUSBAUDIO_StartDfuDownload(deviceCounter, 0, 0) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDFUStartByName //failed to start dfu download";
                mAPI.TUSBAUDIO_UnloadFirmwareImage();
                return 0;
            }
            return imageSize;
        }
        deviceCounter++;
    }
    return 0;
}
unsigned int USBAudioAPI::setDFUStartByID(QString path, unsigned int deviceVendorId, unsigned int deviceProductId)
{
    unsigned int deviceCounter=0;
    unsigned int imageSize=0;
    QVector<TUsbAudioDeviceProperties> devicePropertiesVector=getPropertiesOfAllDevice();
    for(QVector<TUsbAudioDeviceProperties>::iterator it=devicePropertiesVector.begin();it!=devicePropertiesVector.end();++it)
    {
        if(it->usbVendorId == deviceVendorId && it->usbProductId == deviceProductId)
        {
            if(mAPI.TUSBAUDIO_LoadFirmwareImageFromFile(reinterpret_cast<TCHAR*>(const_cast<ushort*>(path.utf16())), deviceCounter) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDFUStartByID //failed to load firmware image from file";
                return 0;
            }
            if(mAPI.TUSBAUDIO_GetFirmwareImageSize(&imageSize, 0) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDFUStartByID //failed to get firmware image size";
                return 0;
            }
            if(mAPI.TUSBAUDIO_StartDfuDownload(deviceCounter, 0, 0) != TSTATUS_SUCCESS)
            {
                qWarning() << "USBAudioAPI::setDFUStartByID //failed to start dfu download";
                mAPI.TUSBAUDIO_UnloadFirmwareImage();
                return 0;
            }
            return imageSize;
        }
        deviceCounter++;
    }
    return 0;
}
bool USBAudioAPI::setDFUTerminate()
{
    if(mAPI.TUSBAUDIO_EndDfuProc() != TSTATUS_SUCCESS)
    {
        qWarning() << "USBAudioAPI::setDFUTerminate //failed to end dfu proc";
        return false;
    }
    mAPI.TUSBAUDIO_UnloadFirmwareImage();
    return true;
}
#if (USB_AUDIO_MIXER_SUPPORT == 1)
USBAudioAPI& USBAudioAPI::setNodeGain(unsigned int matrix_X, unsigned int matrix_Y, int gain)
{
    mMixer->SetNodeGain(mNodeMatrix.at(matrix_X - 1)[matrix_Y - 1], gain);
    return *this;
}
USBAudioAPI& USBAudioAPI::setNodeGainPercent(unsigned int matrix_X, unsigned int matrix_Y, unsigned int gain)
{
    mMixer->SetNodeGain(mNodeMatrix.at(matrix_X - 1)[matrix_Y - 1], TUsbAudioMixer::PercentToGain(gain));
    return *this;
}
USBAudioAPI& USBAudioAPI::setNodeGainLinear(unsigned int matrix_X, unsigned int matrix_Y, double gain)
{
    mMixer->SetNodeGain(mNodeMatrix.at(matrix_X - 1)[matrix_Y - 1], TUsbAudioMixer::LinearToGain(gain));
    return *this;
}
USBAudioAPI& USBAudioAPI::setNodeGainLog(unsigned int matrix_X, unsigned int matrix_Y, double gain)
{
    mMixer->SetNodeGain(mNodeMatrix.at(matrix_X - 1)[matrix_Y - 1], TUsbAudioMixer::LogToGain(gain));
    return *this;
}
USBAudioAPI& USBAudioAPI::setOutputGain(unsigned int matrix_X, int gain)
{
    mMixer->SetOutputGain(mNodeMatrix.at(matrix_X - 1)[0].outputChannel, gain);
    return *this;
}
USBAudioAPI& USBAudioAPI::setOutputGainPercent(unsigned int matrix_X, unsigned int gain)
{
    mMixer->SetOutputGain(mNodeMatrix.at(matrix_X - 1)[0].outputChannel, TUsbAudioMixer::PercentToGain(gain));
    return *this;
}
USBAudioAPI& USBAudioAPI::setOutputGainLinear(unsigned int matrix_X, double gain)
{
    mMixer->SetOutputGain(mNodeMatrix.at(matrix_X - 1)[0].outputChannel, TUsbAudioMixer::LinearToGain(gain));
    return *this;
}
USBAudioAPI& USBAudioAPI::setOutputGainLog(unsigned int matrix_X, double gain)
{
    mMixer->SetOutputGain(mNodeMatrix.at(matrix_X - 1)[0].outputChannel, TUsbAudioMixer::LogToGain(gain));
    return *this;
}
void USBAudioAPI::setMixerNodeAttachToActiveDevice()
{
    QMap<int, TUsbAudioMixer::Node> nodeMap;
    TUsbAudioMixer::Channel channelIn;
    TUsbAudioMixer::Channel channelOut;
    TUsbAudioMixer::Node node;
    unsigned int mixNodesPerLine=mMixer->ChannelCountAppPlayback() + mMixer->ChannelCountDeviceInput() + mMixer->ChannelCountAppPlaybackVirt();
    unsigned int mixLines = mMixer->ChannelCountDeviceOutput() + mMixer->ChannelCountAppRecording() + mMixer->ChannelCountAppRecordingVirt();
    mNodeMatrix.clear();
    for(unsigned int i=0;i<mixLines;i++)
    {
        if(i >= (mMixer->ChannelCountDeviceOutput() + mMixer->ChannelCountAppRecording()))
        {
            channelOut.pin   = TUsbAudioMixer::AppRecordingVirt;
            channelOut.index = i - (mMixer->ChannelCountDeviceOutput() + mMixer->ChannelCountAppRecording());
        }
        else if(i >= mMixer->ChannelCountDeviceOutput())
        {
            channelOut.pin   = TUsbAudioMixer::AppRecording;
            channelOut.index = i - mMixer->ChannelCountDeviceOutput();
        }
        else
        {
            channelOut.pin   = TUsbAudioMixer::DeviceOutput;
            channelOut.index = i;
        }
        nodeMap.clear();
        for(unsigned int j=0;j<mixNodesPerLine;j++)
        {
            if(j >= (mMixer->ChannelCountAppPlayback() + mMixer->ChannelCountDeviceInput()))
            {
                channelIn.pin   = TUsbAudioMixer::AppPlaybackVirt;
                channelIn.index = j - (mMixer->ChannelCountAppPlayback() + mMixer->ChannelCountDeviceInput());
            }
            else if(j >= mMixer->ChannelCountAppPlayback())
            {
                channelIn.pin   = TUsbAudioMixer::DeviceInput;
                channelIn.index = j - mMixer->ChannelCountAppPlayback();
            }
            else
            {
                channelIn.pin   = TUsbAudioMixer::AppPlayback;
                channelIn.index = j;
            }
            node.inputChannel  = channelIn;
            node.outputChannel = channelOut;
            nodeMap.insert(j, node);
        }
        mNodeMatrix.push_back(nodeMap);
    }
}
#endif


// modify
USBAudioAPI& USBAudioAPI::modifyDevicePIDList(QVector<QPair<QString, QString>> list)
{
    mDevicePIDList = list;
    return *this;
}


// is
bool USBAudioAPI::isDeviceDuplicate(QVector<QString>& deviceNameList)
{
    bool result=false;
    QVector<QString> deviceNameVector=getNameOfAllDevice();
    QMap<QString, int> deviceNameMap;
    deviceNameList.clear();
    for(auto it=deviceNameVector.constBegin();it!=deviceNameVector.constEnd();++it)
    {
        deviceNameMap[*it]++;
    }
    for(auto it=deviceNameMap.constBegin();it!=deviceNameMap.constEnd();++it)
    {
        if(it.value() > 1)
        {
            deviceNameList.append(it.key());
            result = true;
        }
    }
    return result;
}
bool USBAudioAPI::isDeviceOnline()
{
    return getSampleRateOfActiveDevice() ? true : false;
}


// check
bool USBAudioAPI::checkDriver()
{
    if(mAPI.TUSBAUDIO_GetApiVersion())
    {
        return true;
    }
    else
    {
        return false;
    }
}
bool USBAudioAPI::checkApiVersion()
{
    if(mAPI.TUSBAUDIO_CheckApiVersion(TUSBAUDIO_API_VERSION_MJ, TUSBAUDIO_API_VERSION_MN))
    {
        return true;
    }
    else
    {
        return false;
    }
}


// show
USBAudioAPI& USBAudioAPI::showPropertiesOfAllDevice()
{
    unsigned int deviceCounter=0;
    QVector<TUsbAudioDeviceProperties> devicePropertiesVector=getPropertiesOfAllDevice();
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showPropertiesOfAllDevice //show list below";
    qInfo() << "Index\tDevice\tVID\tPID\tREV\tSerial\t\tDFU supported\tUSB mode";
    for(QVector<TUsbAudioDeviceProperties>::iterator it=devicePropertiesVector.begin();it!=devicePropertiesVector.end();++it)
    {
        qInfo("%02u\t%s\t0x%04X\t0x%04X\t0x%04X\t%12s\t%s\t\t%s",
              deviceCounter,
              qPrintable(QString::fromWCharArray(it->productString)),
              it->usbVendorId,
              it->usbProductId,
              it->usbRevisionId,
              it->serialNumberString,
              (0 != (it->flags & TUSBAUDIO_DEVPROP_FLAG_DFU_SUPPORTED)) ? "yes" : "no ",
              "Not obtained"
              );
        deviceCounter++;
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showNameOfAllDevice()
{
    QVector<QString> deviceNameVector=getNameOfAllDevice();
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showNameOfAllDevice //show list below";
    for(QVector<QString>::iterator it=deviceNameVector.begin();it!=deviceNameVector.end();++it)
    {
        qInfo() << " ->" << *it;
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showSampleRateOfActiveDeviceSupported()
{
    QVector<unsigned int> supportedSampleRateVector=getSampleRateOfActiveDeviceSupported();
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showSampleRateOfActiveDeviceSupported //show list below";
    for(QVector<unsigned int>::iterator it=supportedSampleRateVector.begin();it!=supportedSampleRateVector.end();++it)
    {
        qInfo() << " ->" << *it;
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showSampleRateOfActiveDevice()
{
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showSampleRateOfActiveDevice //show list below";
    qInfo() << " ->" << getSampleRateOfActiveDevice();
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showBufferSizeOfActiveDeviceSupported()
{
    QVector<unsigned int> supportedBufferSizeVector=getBufferSizeOfActiveDeviceSupported();
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showBufferSizeOfActiveDeviceSupported //show list below";
    for(QVector<unsigned int>::iterator it=supportedBufferSizeVector.begin();it!=supportedBufferSizeVector.end();++it)
    {
        qInfo() << " ->" << *it;
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showBufferSizeOfActiveDevice()
{
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showBufferSizeOfActiveDevice //show list below";
    qInfo() << " ->" << getBufferSizeOfActiveDevice();
    qInfo() << "";
    qInfo() << "";
    return *this;
}
#if (USB_AUDIO_MIXER_SUPPORT == 1)
USBAudioAPI& USBAudioAPI::showMaxChannelNumberOfMixer()
{
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showMaxChannelNumberOfMixer //show list below";
    qInfo() << " ->AppPlayback\t:"      << mMixer->ChannelCountAppPlayback();
    qInfo() << " ->DeviceInput\t:"      << mMixer->ChannelCountDeviceInput();
    qInfo() << " ->AppPlaybackVirt\t:"  << mMixer->ChannelCountAppPlaybackVirt();
    qInfo() << " ->DeviceOutput\t:"     << mMixer->ChannelCountDeviceOutput();
    qInfo() << " ->AppRecording\t:"     << mMixer->ChannelCountAppRecording();
    qInfo() << " ->AppRecordingVirt:"   << mMixer->ChannelCountAppRecordingVirt();
    qInfo() << "";
    qInfo() << "";
    return *this;
}
#endif
#endif


#ifdef Q_OS_MACOS
std::string GetFilterDescription(const std::string& argument);
USBAudioAPI::USBAudioAPI()
{
}
USBAudioAPI::~USBAudioAPI()
{
}


bool USBAudioAPI::init()
{
    QString bundlePath = QCoreApplication::applicationDirPath();
    QString dylibPath = bundlePath + "/../Frameworks/libtlusbdfuapi.dylib";

    if (!QFileInfo::exists(dylibPath)) {
        qWarning("DFU library not found at: %s", qPrintable(dylibPath));
        return false;
    }

    std::string dylibPathStd = dylibPath.toStdString();
    const T_UNICHAR* dfuApiFileName = reinterpret_cast<const T_UNICHAR*>(dylibPathStd.c_str());
    TLSTATUS st = TLDfuApi::Instance().LoadByName(dfuApiFileName);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("TLDfuApi::Instance().LoadByName(" UNISTR_FMT ") failed, st=0x%08X", dfuApiFileName, st);

    }
    qWarning("TL-USBDFU library loaded: " UNISTR_FMT "", dfuApiFileName);

    unsigned int api_ver = TLDFU_GetApiVersion();
    qWarning("TL-USBDFU library API Version %u.%02u", TLDFU_API_EXTRACT_MJ_VERSION(api_ver), TLDFU_API_EXTRACT_MN_VERSION(api_ver));
    return true;
}


// getter
unsigned int USBAudioAPI::getNumberOfAvailableDevice()
{
    unsigned int size = getNameOfAllDevice().size();
    return size;
}
QString CFStringToQString(CFStringRef str) {
    if (!str) return {};
    return QString::fromCFString(str);
}


static void qstringToWcharBuffer(const QString &qstr, wchar_t *dest, int maxLen) {
    if (!dest || maxLen <= 0) return;
    
    int actualLen = qstr.toWCharArray(dest);
    if (actualLen >= maxLen)
        dest[maxLen - 1] = L'\0';
    else
        dest[actualLen] = L'\0';
}


static QString getStringProperty(AudioDeviceID deviceID, AudioObjectPropertySelector selector) {
    CFStringRef cfStr = nullptr;
    UInt32 dataSize = sizeof(CFStringRef);
    AudioObjectPropertyAddress address = {
        selector,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };
    if (AudioObjectGetPropertyData(deviceID, &address, 0, nullptr, &dataSize, &cfStr) == noErr && cfStr)
        return CFStringToQString(cfStr);
    return {};
}

static double getCurrentSampleRate(AudioDeviceID deviceID) {
    Float64 rate = 0;
    UInt32 size = sizeof(rate);
    AudioObjectPropertyAddress addr = {
        kAudioDevicePropertyNominalSampleRate,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };
    if (AudioObjectGetPropertyData(deviceID, &addr, 0, nullptr, &size, &rate) == noErr)
        return rate;
    return 0;
}

static bool fillUsbInfo(USBAudioAPI::TUsbAudioDeviceProperties &info, const QString &deviceName) {
    bool isFined = false;
    CFStringRef cfName = CFStringCreateWithCString(kCFAllocatorDefault, deviceName.toUtf8().constData(), kCFStringEncodingUTF8);
    if (!cfName) return isFined;

    io_iterator_t iter;
    CFMutableDictionaryRef dict = IOServiceMatching(kIOUSBDeviceClassName);
    IOServiceGetMatchingServices(kIOMainPortDefault, dict, &iter);

    io_service_t device;
    while ((device = IOIteratorNext(iter))) {
        CFStringRef devName = (CFStringRef)IORegistryEntryCreateCFProperty(device, CFSTR("USB Product Name"), kCFAllocatorDefault, 0);
        if (devName && CFStringCompare(cfName, devName, 0) == kCFCompareEqualTo) {
            CFNumberRef vendor = (CFNumberRef)IORegistryEntryCreateCFProperty(device, CFSTR("idVendor"), kCFAllocatorDefault, 0);
            CFNumberRef product = (CFNumberRef)IORegistryEntryCreateCFProperty(device, CFSTR("idProduct"), kCFAllocatorDefault, 0);
            CFNumberRef revision = (CFNumberRef)IORegistryEntryCreateCFProperty(device, CFSTR("bcdDevice"), kCFAllocatorDefault, 0);
            CFStringRef serial = (CFStringRef)IORegistryEntryCreateCFProperty(device, CFSTR("USB Serial Number"), kCFAllocatorDefault, 0);
            CFStringRef manufacturer = (CFStringRef)IORegistryEntryCreateCFProperty(device, CFSTR("USB Vendor Name"), kCFAllocatorDefault, 0);

            if( CFStringToQString(manufacturer) == "Topping"){
                if (devName) qstringToWcharBuffer(CFStringToQString(devName), info.productString, TUSBAUDIO_MAX_STRDESC_STRLEN);
                if (vendor) CFNumberGetValue(vendor, kCFNumberIntType, &info.usbVendorId);
                if (product) CFNumberGetValue(product, kCFNumberIntType, &info.usbProductId);
                if (revision) CFNumberGetValue(revision, kCFNumberIntType, &info.usbRevisionId);
                if (serial) qstringToWcharBuffer(CFStringToQString(serial), info.serialNumberString, TUSBAUDIO_MAX_STRDESC_STRLEN);
                if (manufacturer) qstringToWcharBuffer(CFStringToQString(manufacturer), info.manufacturerString, TUSBAUDIO_MAX_STRDESC_STRLEN);

                if (devName) CFRelease(devName);
                if (vendor) CFRelease(vendor);
                if (product) CFRelease(product);
                if (revision) CFRelease(revision);
                if (serial) CFRelease(serial);
                if (manufacturer) CFRelease(manufacturer);
                isFined = true;
                break;
            }
        }

        if (devName) CFRelease(devName);
        IOObjectRelease(device);
    }

    CFRelease(cfName);
    IOObjectRelease(iter);
    return isFined;
}
TLSTATUS deviceInfo(const std::string& deviceFilter, QVector<USBAudioAPI::TUsbAudioDeviceProperties>& infos)
{
    TLSTATUS st;
    TLDfuEnumerator enumerator;

    // enumerate devices
    unsigned int devCnt = 0;
    st = enumerator.EnumerateUsbDfuDevices(deviceFilter.c_str(), devCnt);
    if ( TLSTATUS_SUCCESS != st ) {
        // failed
        printf("EnumerateUsbDfuDevices failed, st=0x%08x (%s)\n", st, TLDFU_GetErrorText(st));
        return st;
    }

    if ( 0 == devCnt ) {
        printf("No device found.\n");
        return TLSTATUS_NO_DEVICES;
    }

    printf("Found %u device(s).\n", devCnt);

    for(auto deviceIndex= 0; deviceIndex<devCnt;deviceIndex++){
        printf("Open device...\n");
        TLDfuDevice device;
        USBAudioAPI::TUsbAudioDeviceProperties info;
        st = device.OpenDevice(
                        enumerator,
                        deviceIndex
                        );
        if ( TLSTATUS_SUCCESS != st ) {
            printf("OpenDevice(%u) failed, st=0x%08x (%s)\n", deviceIndex, st, TLDFU_GetErrorText(st));
            return st;
        }

        // get some information about the device
        st = device.GetDevicePropertyUint(TLDfuDeviceProperty_VendorId, info.usbVendorId);
        if ( TLSTATUS_SUCCESS != st ) {
            printf("GetDevicePropertyUint(VendorId) failed, st=0x%08x (%s)\n", st, TLDFU_GetErrorText(st));
            return st;
        }

        st = device.GetDevicePropertyUint(TLDfuDeviceProperty_ProductId, info.usbProductId);
        if ( TLSTATUS_SUCCESS != st ) {
            printf("GetDevicePropertyUint(ProductId) failed, st=0x%08x (%s)\n", st, TLDFU_GetErrorText(st));
            return st;
        }

        st = device.GetDevicePropertyUint(TLDfuDeviceProperty_BcdDevice, info.usbRevisionId);
        if ( TLSTATUS_SUCCESS != st ) {
            printf("GetDevicePropertyUint(BcdDevice) failed, st=0x%08x (%s)\n", st, TLDFU_GetErrorText(st));
            return st;
        }

        unsigned int runMode;
        st = device.GetDevicePropertyUint(TLDfuDeviceProperty_CurrentRunMode, runMode);
        if ( TLSTATUS_SUCCESS != st ) {
            printf("GetDevicePropertyUint(CurrentRunMode) failed, st=0x%08x (%s)\n", st, TLDFU_GetErrorText(st));
            return st;
        }

        constexpr unsigned int cNumCharacters = 256;

        T_UNICHAR manufacturer[cNumCharacters] = {0};
        st = device.GetDevicePropertyString(
                                TLDfuDeviceProperty_UsbManufacturerString,
                                manufacturer,
                                cNumCharacters
                                );
        if ( TLSTATUS_SUCCESS != st ) {
            // ignore error, this string is optional
            manufacturer[cNumCharacters-1] = 0;
        }
        qstringToWcharBuffer(QString(manufacturer), info.manufacturerString, TUSBAUDIO_MAX_STRDESC_STRLEN);

        T_UNICHAR product[cNumCharacters] = {0};
        st = device.GetDevicePropertyString(
                                TLDfuDeviceProperty_UsbProductString,
                                product,
                                cNumCharacters
                                );
        if ( TLSTATUS_SUCCESS != st ) {
            // ignore error, this string is optional
            product[cNumCharacters-1] = 0;
        }
        qstringToWcharBuffer(QString(product), info.productString, TUSBAUDIO_MAX_STRDESC_STRLEN);

        T_UNICHAR serialNumber[256] = {0};
        st = device.GetDevicePropertyString(
                                TLDfuDeviceProperty_UsbSerialNumberString,
                                serialNumber,
                                cNumCharacters
                                );
        if ( TLSTATUS_SUCCESS != st ) {
            // ignore error, this string is optional
            serialNumber[cNumCharacters-1] = 0;
        }
        qstringToWcharBuffer(QString(serialNumber), info.serialNumberString, TUSBAUDIO_MAX_STRDESC_STRLEN);
        infos.push_back(std::move(info));
    }

    return TLSTATUS_SUCCESS;
}
QList<USBAudioAPI::TUsbAudioDeviceProperties> USBAudioAPI::getPropertiesOfAllDevice() {
    QMultiHash<QString, USBAudioAPI::TUsbAudioDeviceProperties> resultAudio, resultDfu;
    UInt32 size = 0;
    AudioObjectPropertyAddress address = {
        kAudioHardwarePropertyDevices,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };
    AudioObjectGetPropertyDataSize(kAudioObjectSystemObject, &address, 0, nullptr, &size);

    int count = size / sizeof(AudioDeviceID);
    AudioDeviceID *devices = new AudioDeviceID[count];
    AudioObjectGetPropertyData(kAudioObjectSystemObject, &address, 0, nullptr, &size, devices);

    for (int i = 0; i < count; ++i) {
        USBAudioAPI::TUsbAudioDeviceProperties info = {};
        QString deviceName = getStringProperty(devices[i], kAudioObjectPropertyName);
        info.flags = 0;
        info.audioControlInterfaceNumber = -1;
        if(fillUsbInfo(info, deviceName)){
            resultAudio.insert(deviceName, info);
        }
    }
    delete[] devices;
    
    for(const auto& it : mDevicePIDList){
        bool isOk = false;
        auto vplist = getVplistByDeviceID(it.first.toUInt(&isOk, 16), it.second.toUInt(&isOk, 16));
        auto deviceFilter = GetFilterDescription(vplist);
        if (deviceFilter.empty() ) {
            qWarning("Invalid argument '" UNISTR_FMT "'", vplist.c_str());
        }
        if(deviceFilter.size()>1)
            deviceFilter.erase(deviceFilter.size()-1,1);
        QVector<USBAudioAPI::TUsbAudioDeviceProperties> infos;
        if(deviceInfo(deviceFilter, infos) == TLSTATUS_SUCCESS){
            for(auto&info:infos){
                resultDfu.insert(QString::fromWCharArray(info.productString), info);
            }
        }
    }

    QList<USBAudioAPI::TUsbAudioDeviceProperties> result;
    for(auto it=resultAudio.begin();it!=resultAudio.end();++it){
        auto iter = resultDfu.equal_range(it.key());
        for(auto it2=iter.first;it2!=iter.second;++it2){
            if(it2.value().usbRevisionId == it.value().usbRevisionId){
                resultDfu.erase(it2);
                break;
            }
        }
        result.push_back(it.value());
    }
    for(auto it=resultDfu.begin();it!=resultDfu.end();++it){
        result.push_back(it.value());
    }

    return result;
}

QVector<QString> USBAudioAPI::getNameOfAllDevice()
{
    QVector<QString> deviceNames;
    auto allDeviceProperties = getPropertiesOfAllDevice();
    for(const auto& it : allDeviceProperties){
        auto productName = QString::fromWCharArray(it.productString);
        auto manufacturerName = QString::fromWCharArray(it.manufacturerString);
        deviceNames.push_back(productName);
    }
    return deviceNames;
}

AudioObjectID USBAudioAPI::getAudioObjectIDByName(const char *deviceName)
{
    AudioObjectPropertyAddress propertyAddress;
    propertyAddress.mSelector = kAudioHardwarePropertyDevices;
    propertyAddress.mScope    = kAudioObjectPropertyScopeGlobal;
    propertyAddress.mElement  = kAudioObjectPropertyElementMain;

    UInt32 dataSize = 0;
    OSStatus status = AudioObjectGetPropertyDataSize(
                kAudioObjectSystemObject, &propertyAddress, 0, NULL, &dataSize);

    if (status != noErr) {
        return kAudioObjectUnknown;
    }

    AudioObjectID *deviceIDs = (AudioObjectID *)malloc(dataSize);
    if (deviceIDs == NULL) {
        return kAudioObjectUnknown;
    }

    status = AudioObjectGetPropertyData(
                kAudioObjectSystemObject, &propertyAddress, 0, NULL, &dataSize, deviceIDs);

    if (status != noErr) {
        free(deviceIDs);
        return kAudioObjectUnknown;
    }

    UInt32 AudioDevsum = (dataSize / sizeof(AudioObjectID));
    for (UInt32 i = 0; i < AudioDevsum; i++) {
        char deviceNameBuffer[128];
        UInt32 Size = sizeof(deviceNameBuffer);
        propertyAddress.mSelector = kAudioDevicePropertyDeviceName;
        status = AudioObjectGetPropertyData(deviceIDs[i], &propertyAddress, 0, NULL, &Size, deviceNameBuffer);
        if (status == noErr) {
            if (strcmp(deviceName, deviceNameBuffer) == 0) {
                AudioObjectID NamedeviceID = deviceIDs[i];
                free(deviceIDs);
                return NamedeviceID;
            }
        }
    }
    free(deviceIDs);
    return kAudioObjectUnknown;
}

unsigned int USBAudioAPI::getDeviceVendorId(AudioObjectID deviceID)
{
    unsigned int vendorId = 0;
    
    CFStringRef deviceName = NULL;
    UInt32 size = sizeof(CFStringRef);
    AudioObjectPropertyAddress propertyAddress = {
        kAudioDevicePropertyDeviceName,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };
    
    OSStatus status = AudioObjectGetPropertyData(deviceID, &propertyAddress, 0, NULL, &size, &deviceName);
    if (status != noErr || !deviceName) {
        qWarning() << "Failed to get device name";
        return 0;
    }

    io_iterator_t iter;
    CFMutableDictionaryRef dict = IOServiceMatching(kIOUSBDeviceClassName);
    IOServiceGetMatchingServices(kIOMainPortDefault, dict, &iter);

    io_service_t device;
    while ((device = IOIteratorNext(iter))) {
        CFStringRef devName = (CFStringRef)IORegistryEntryCreateCFProperty(device, 
            CFSTR("USB Product Name"), kCFAllocatorDefault, 0);
            
        if (devName && CFStringCompare(deviceName, devName, 0) == kCFCompareEqualTo) {
            CFNumberRef vendor = (CFNumberRef)IORegistryEntryCreateCFProperty(device,
                CFSTR("idVendor"), kCFAllocatorDefault, 0);
                
            if (vendor) {
                CFNumberGetValue(vendor, kCFNumberIntType, &vendorId);
                CFRelease(vendor);
            }
            
            if (devName) CFRelease(devName);
            IOObjectRelease(device);
            break;
        }
        
        if (devName) CFRelease(devName);
        IOObjectRelease(device);
    }

    IOObjectRelease(iter);
    if (deviceName) CFRelease(deviceName);

    return vendorId;
}

unsigned int USBAudioAPI::getDeviceProductId(AudioObjectID deviceID) 
{
    unsigned int productId = 0;
    
    CFStringRef deviceName = NULL; 
    UInt32 size = sizeof(CFStringRef);
    AudioObjectPropertyAddress propertyAddress = {
        kAudioDevicePropertyDeviceName,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };

    OSStatus status = AudioObjectGetPropertyData(deviceID, &propertyAddress, 0, NULL, &size, &deviceName);
    if (status != noErr || !deviceName) {
        qWarning() << "Failed to get device name";
        return 0;
    }

    io_iterator_t iter;
    CFMutableDictionaryRef dict = IOServiceMatching(kIOUSBDeviceClassName);
    IOServiceGetMatchingServices(kIOMainPortDefault, dict, &iter);
    
    io_service_t device;
    while ((device = IOIteratorNext(iter))) {
        CFStringRef devName = (CFStringRef)IORegistryEntryCreateCFProperty(device,
            CFSTR("USB Product Name"), kCFAllocatorDefault, 0);
            
        if (devName && CFStringCompare(deviceName, devName, 0) == kCFCompareEqualTo) {
            CFNumberRef product = (CFNumberRef)IORegistryEntryCreateCFProperty(device,
                CFSTR("idProduct"), kCFAllocatorDefault, 0);
                
            if (product) {
                CFNumberGetValue(product, kCFNumberIntType, &productId);
                CFRelease(product);
            }
            
            if (devName) CFRelease(devName);
            IOObjectRelease(device);
            break;
        }
        
        if (devName) CFRelease(devName);
        IOObjectRelease(device);
    }

    IOObjectRelease(iter);
    if (deviceName) CFRelease(deviceName);

    return productId;
}

QVector<unsigned int> USBAudioAPI::getSampleRateOfActiveDeviceSupported()
{
    AudioObjectID deviceID = getAudioObjectIDByName(mDeviceName.toUtf8().data());
    if (deviceID == kAudioObjectUnknown) {
        qWarning() << "Failed to get device ID";
        return {};
    }
    QVector<unsigned int> list;
    AudioObjectPropertyAddress addr = {
        kAudioDevicePropertyAvailableNominalSampleRates,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };

    UInt32 dataSize = 0;
    if (AudioObjectGetPropertyDataSize(deviceID, &addr, 0, nullptr, &dataSize) != noErr)
        return list;

    auto *ranges = static_cast<AudioValueRange *>(malloc(dataSize));
    if (!ranges) return list;

    if (AudioObjectGetPropertyData(deviceID, &addr, 0, nullptr, &dataSize, ranges) == noErr) {
        int count = dataSize / sizeof(AudioValueRange);
        for (int i = 0; i < count; ++i) {
            if (ranges[i].mMinimum == ranges[i].mMaximum)
                list.append(ranges[i].mMinimum);
        }
    }

    free(ranges);
    return list;
}
unsigned int USBAudioAPI::getSampleRateOfActiveDevice()
{
    unsigned int MaccurrentSampleRate = 0;
    Float64 currentSampleRate;
    UInt32 size = sizeof(currentSampleRate);
    AudioObjectPropertyAddress propertyAddress;
    propertyAddress.mSelector = kAudioDevicePropertyNominalSampleRate;
    propertyAddress.mScope    = kAudioObjectPropertyScopeGlobal;
    propertyAddress.mElement  = kAudioObjectPropertyElementMain;
    OSStatus  status = AudioObjectGetPropertyData(getAudioObjectIDByName(mDeviceName.toUtf8().data()), &propertyAddress, 0, NULL, &size, &currentSampleRate);

    if (status != noErr) {
        return MaccurrentSampleRate;
    } else
    {
        MaccurrentSampleRate = currentSampleRate;
        return MaccurrentSampleRate;
    }
}
QVector<unsigned int> USBAudioAPI::getBufferSizeOfActiveDeviceSupported()
{
    QVector<unsigned int> supportedBufferSizeVector;

    AudioObjectPropertyAddress rangeAddress = {
        kAudioDevicePropertyBufferSizeRange,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };

    AudioObjectPropertyAddress settableAddress = {
        kAudioDevicePropertyBufferFrameSize,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };

    unsigned char isSettable = 0;
    OSStatus status = AudioObjectIsPropertySettable(
        getAudioObjectIDByName(mDeviceName.toUtf8().data()),
        &settableAddress,
        &isSettable
    );

    if (status != noErr) {
        qWarning("【USBAudioAPI::getBufferSizeOfActiveDeviceSupported】 查询BufferSize属性是否可设置失败，错误码: %d", status);
        return supportedBufferSizeVector;
    }

    if (!isSettable) {
        qWarning("【USBAudioAPI::getBufferSizeOfActiveDeviceSupported】 当前设备BufferSize属性不可设置！");
        return supportedBufferSizeVector;
    }

    AudioValueRange range;
    UInt32 dataSize = sizeof(range);

    status = AudioObjectGetPropertyData(
        getAudioObjectIDByName(mDeviceName.toUtf8().data()),
        &rangeAddress,
        0,
        nullptr,
        &dataSize,
        &range
    );

    if (status != noErr) {
        qWarning("【USBAudioAPI::getBufferSizeOfActiveDeviceSupported】 获取BufferSize范围失败，错误码: %d", status);
        return supportedBufferSizeVector;
    }

    const QVector<unsigned int> candidateSizes = {8, 16, 32, 64, 128, 256, 512, 1024, 2048};

    for (unsigned int size : candidateSizes) {
        if (size >= static_cast<unsigned int>(range.mMinimum) &&
            size <= static_cast<unsigned int>(range.mMaximum)) {
            supportedBufferSizeVector.append(size);
        }
    }

    return supportedBufferSizeVector;
}
unsigned int USBAudioAPI::getBufferSizeOfActiveDevice()
{
    AudioObjectPropertyAddress address = {
        kAudioDevicePropertyBufferFrameSize,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };

    UInt32 bufferSize = 0;
    UInt32 dataSize = sizeof(bufferSize);

    OSStatus status = AudioObjectGetPropertyData(
        getAudioObjectIDByName(mDeviceName.toUtf8().data()), 
        &address,
        0,
        nullptr,
        &dataSize,
        &bufferSize
    );

    if (status != noErr) {
        qWarning("【USBAudioAPI::getBufferSizeOfActiveDevice】 获取当前BufferSize失败，错误码: %d", status);
        return 0;
    }

    return bufferSize;
}
bool USBAudioAPI::getSafeModeOfActiveDevice()
{
    return false;
}
bool USBAudioAPI::getDFUStatus(TLDfuDevice::UpgradeStatus& status)
{ 
    TLSTATUS st= MacDFU.device.GetUpgradeStatus(status);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("GetUpgradeStatus failed, st=0x%08x (%s) ", st, TLDFU_GetErrorText(st));
        MacDFU.upgradeCompletionStatus = st;
        return false;
    }
    return true;
}
unsigned int USBAudioAPI::getDFUBytesTotal()
{
    TLDfuDevice::UpgradeStatus status;
    if ( !getDFUStatus(status) )
        return 0;
    return status.totalBytes;
}
unsigned int USBAudioAPI::getDFUBytesCurrent()
{
    TLDfuDevice::UpgradeStatus status;
    if ( !getDFUStatus(status) )
        return 0;
    return status.currentBytes;
}
unsigned int USBAudioAPI::getDFUPercentageRemain()
{
    auto totalBytes = getDFUBytesTotal();
    auto currentBytes = getDFUBytesCurrent();
    unsigned int percentage = 0;
    TLDfuDevice::UpgradeStatus status;
    if ( !getDFUStatus(status) )
        return percentage;
    if(status.upgradeState == TLDfuUpgradeState::TLDfuUpgradeState_Failed){
        return percentage;
    }
    if ( totalBytes > 0 )
        return ((totalBytes - currentBytes) * 100) / totalBytes;
    if(percentage <= 1)
    {
        percentage = 1;
    }
    else if(percentage >= 100)
    {
        percentage = 100;
    }
    return percentage;
}
unsigned int USBAudioAPI::getDFUPercentageComplete()
{
    auto totalBytes = getDFUBytesTotal();
    auto currentBytes = getDFUBytesCurrent();
    unsigned int percentage = 0;
    TLDfuDevice::UpgradeStatus status;
    if ( !getDFUStatus(status) )
        return percentage;
    if(status.upgradeState == TLDfuUpgradeState::TLDfuUpgradeState_Failed){
        return percentage;
    }
    if ( totalBytes > 0 )
        percentage = (currentBytes * 100) / totalBytes;
    if(percentage <= 1)
    {
        percentage = 1;
    }
    else if(percentage >= 100)
    {
        percentage = 100;
    }
    return percentage;
}
#if (USB_AUDIO_MIXER_SUPPORT == 1)
int USBAudioAPI::getNodeGain(unsigned int matrix_X, unsigned int matrix_Y)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(matrix_Y);
    return 0;
}
unsigned int USBAudioAPI::getNodeGainPercent(unsigned int matrix_X, unsigned int matrix_Y)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(matrix_Y);
    return 0;
}
double USBAudioAPI::getNodeGainLinear(unsigned int matrix_X, unsigned int matrix_Y)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(matrix_Y);
    return 0;
}
double USBAudioAPI::getNodeGainLog(unsigned int matrix_X, unsigned int matrix_Y)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(matrix_Y);
    return 0;
}
int USBAudioAPI::getOutputGain(unsigned int matrix_X)
{
    Q_UNUSED(matrix_X);
    return 0;
}
unsigned int USBAudioAPI::getOutputGainPercent(unsigned int matrix_X)
{
    Q_UNUSED(matrix_X);
    return 0;
}
double USBAudioAPI::getOutputGainLinear(unsigned int matrix_X)
{
    Q_UNUSED(matrix_X);
    return 0;
}
double USBAudioAPI::getOutputGainLog(unsigned int matrix_X)
{
    Q_UNUSED(matrix_X);
    return 0;
}
#if (USB_AUDIO_LEVEL_METER_SUPPORT == 1)
short USBAudioAPI::getLevelMeterOfChannelOutput(unsigned int matrix_X)
{
    Q_UNUSED(matrix_X);
    return 0;
}
QVector<short> USBAudioAPI::getLevelMeterOfChannelOutput()
{
    QVector<short> vectorLevel;
    return vectorLevel;
}
short USBAudioAPI::getLevelMeterOfChannelInput(unsigned int matrix_Y)
{
    Q_UNUSED(matrix_Y);
    return 0;
}
QVector<short> USBAudioAPI::getLevelMeterOfChannelInput()
{
    QVector<short> vectorLevel;
    return vectorLevel;
}
#endif
#endif


// setter
USBAudioAPI& USBAudioAPI::setDeviceToReset()
{
    return *this;
}
bool USBAudioAPI::setDeviceToActiveByName(QString deviceName)
{
    mDeviceName = deviceName;
    auto names = getNameOfAllDevice();
    bool isFind = false;
    for(auto& name: names){
        if(name == deviceName){
            isFind = true;
        }
    }
    return isFind;
}
bool USBAudioAPI::setDeviceToActiveByID(unsigned int deviceVendorId, unsigned int deviceProductId)
{
    QVector<TUsbAudioDeviceProperties> devicePropertiesVector=getPropertiesOfAllDevice();
    for(QVector<TUsbAudioDeviceProperties>::iterator it=devicePropertiesVector.begin();it!=devicePropertiesVector.end();++it)
    {
        if(it->usbVendorId == deviceVendorId && it->usbProductId == deviceProductId)
        {
            mDeviceName = QString::fromWCharArray(it->productString);
            return true;
        }
    }
    qWarning() << "USBAudioAPI::setDeviceToActiveByID //invalid device VID/PID";
    return false;
}
bool USBAudioAPI::setSampleRateOfActiveDevice(unsigned int sampleRate)
{
    QVector<unsigned int> supportedSampleRates = getSampleRateOfActiveDeviceSupported();

    if (!supportedSampleRates.contains(sampleRate)) {
        qWarning() << "Sample rate" << sampleRate << "is not supported by the active device";
        return false;
    }

    Float64 newSampleRate = static_cast<Float64>(sampleRate);

    AudioObjectPropertyAddress propertyAddress;
    propertyAddress.mSelector = kAudioDevicePropertyNominalSampleRate;
    propertyAddress.mScope    = kAudioObjectPropertyScopeGlobal;
    propertyAddress.mElement  = kAudioObjectPropertyElementMain;

    AudioObjectID deviceID = getAudioObjectIDByName(mDeviceName.toUtf8().constData());
    if (deviceID == kAudioObjectUnknown) {
        qWarning() << "Failed to get AudioObjectID for device" << mDeviceName;
        return false;
    }

    OSStatus status = AudioObjectSetPropertyData(
        deviceID, &propertyAddress, 0, NULL, sizeof(newSampleRate), &newSampleRate);

    if (status != noErr) {
        qWarning() << "Failed to set sample rate for device" << mDeviceName << "with error" << status;
        return false;
    }
    return true;
}
bool USBAudioAPI::setBufferSizeOfActiveDevice(unsigned int bufferSize)
{
    AudioObjectPropertyAddress address = {
        kAudioDevicePropertyBufferFrameSize,
        kAudioObjectPropertyScopeGlobal,
        kAudioObjectPropertyElementMain
    };

    UInt32 dataSize = sizeof(bufferSize);

    OSStatus status = AudioObjectSetPropertyData(
        getAudioObjectIDByName(mDeviceName.toUtf8().data()),
        &address,
        0,
        nullptr,
        dataSize,
        &bufferSize
    );

    if (status != noErr) {
        qWarning("【USBAudioAPI::setBufferSizeOfActiveDevice】 设置BufferSize失败，错误码: %d", status);
        return false;
    }

    return true;
}
bool USBAudioAPI::setSafeModeOfActiveDevice(bool safeMode)
{
    Q_UNUSED(safeMode);
    return true;
}

const char*     RunModeStr( unsigned int runMode )
{
    switch (runMode) {
    case TLDFU_RUNMODE_APPLICATION: return "APP (application)";
    case TLDFU_RUNMODE_BOOTLOADER: return "DFU (bootloader)";
    case TLDFU_RUNMODE_UNKNOWN:
    default: break;
    }
    return "unknown";
}

TLSTATUS EnumerateAndOpenDevice( TLDfuDevice& device,
                                unsigned int& runMode,
                                const std::string& deviceFilter,
                                unsigned int retryIntervalMillisecs
                                )
{
    TLSTATUS st;

    unsigned int retryDelay = 0;
    unsigned int retryCount = 0;
    if ( retryIntervalMillisecs > 0 ) {
        retryDelay = 100;
        retryCount = (retryIntervalMillisecs + (retryDelay - 1)) / retryDelay;
    }

    qWarning("Enumerate available devices... ");
    TLDfuEnumerator enumerator;
    unsigned int devCnt = 0;
    st = enumerator.EnumerateUsbDfuDevices(deviceFilter.c_str(), devCnt, retryCount, retryDelay);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("EnumerateUsbDfuDevices failed, st=0x%08x (%s)\n", st, TLDFU_GetErrorText(st));
        return st;
    }

    if ( 0 == devCnt ) {
        qWarning("No device found.\n");
        return TLSTATUS_NO_DEVICES;
    }

    qWarning("Found %u device(s). ", devCnt);

    if ( devCnt > 1 ) {
        qWarning("Firmware upgrade with multiple device instances in parallel is not supported. ");
        qWarning("Please make sure that only one device is connected.\n");
        return TLSTATUS_NOT_ALLOWED;
    }

    unsigned int deviceIndex = 0;

    qWarning("Open  the device...   ");
    st = device.OpenDevice(enumerator, deviceIndex);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("OpenDevice(%u) failed, st=0x%08x (%s) ", deviceIndex, st, TLDFU_GetErrorText(st));
        return st;
    }

    st = device.GetDevicePropertyUint(TLDfuDeviceProperty_CurrentRunMode, runMode);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("GetDevicePropertyUint(CurrentRunMode) failed, st=0x%08x (%s) ", st, TLDFU_GetErrorText(st));
        return st;
    }

    qWarning("Device is operating in run mode %s. ", RunModeStr(runMode));

    return TLSTATUS_SUCCESS;
}
TLSTATUS WaitUntilDeviceIsGone(TLDfuDevice& device)
{
    TLSTATUS st;
    unsigned int maxIntervalMs = 5 * 1000;

    std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
    for (;; ) {

        st = device.CheckDeviceConnection();
        if ( TLSTATUS_SUCCESS != st ) {
            break;
        }

        std::chrono::steady_clock::time_point now = std::chrono::steady_clock::now();
        unsigned int time = static_cast<unsigned int>(std::chrono::duration_cast<std::chrono::milliseconds>(now - begin).count());

        if ( time > maxIntervalMs ) {
            return TLSTATUS_TIMEOUT;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }

    return TLSTATUS_SUCCESS;
}
std::string GetFilterDescription(const std::string& argument)
{
    VidPidTupleList vpList;
    if ( !ParseVidPidTupleList(vpList, argument) ) {
        return std::string{};
    }
    return BuildDeviceFilterDescription(vpList);
}

std::string USBAudioAPI::getVplistByDeviceID(unsigned int deviceVendorId, unsigned int deviceProductId)
{
    unsigned int DeviceVersion;
    char vpBuffer[32];
    std::snprintf(vpBuffer, sizeof(vpBuffer), "0x%04X:0x%04X", deviceVendorId, deviceProductId);
    return vpBuffer;
}

std::string USBAudioAPI::getVplistByDeviceName(const QString &deviceName){
    QVector<TUsbAudioDeviceProperties> devicePropertiesVector=getPropertiesOfAllDevice();
    unsigned int PID, VID;
    bool isFinded = false;
    for(auto &deviceProperties : devicePropertiesVector)
    {
        if(QString::fromWCharArray(deviceProperties.productString) == mDeviceName)
        {
            isFinded = true;
            VID = deviceProperties.usbVendorId;
            PID = deviceProperties.usbProductId;
            break;
        }
    }
    if(!isFinded)
    {
        return "";
    }
    return getVplistByDeviceID(VID, PID);
}

unsigned int USBAudioAPI::setDFUStartByVplist(const std::string& vplist, const std::string& path)
{
    if(vplist.empty()){
        return 0;
    }
    MacDFU.vplist = vplist;
    MacDFU.firmwareFile = path;
    MacDFU.deviceFilter = GetFilterDescription(MacDFU.vplist);
    if ( MacDFU.deviceFilter.empty() ) {
        qWarning("Invalid argument '" UNISTR_FMT "'", MacDFU.vplist.c_str());
    }

    TLSTATUS st;
    st = MacDFU.FirmwareImage.LoadFirmwareImageFromFile(MacDFU.firmwareFile.c_str(), TLDfuImageType_RawBinary);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("LoadFirmwareImageFromFile(" UNISTR_FMT ") failed, st=0x%08x (%s) ", MacDFU.firmwareFile.c_str(), st, TLDFU_GetErrorText(st));
        return 0;
    }

    unsigned int runMode = TLDFU_RUNMODE_UNKNOWN;
    st = EnumerateAndOpenDevice(MacDFU.device, runMode, MacDFU.deviceFilter, 0);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("EnumerateAndOpenDevice() failed, st=0x%08x (%s) ", st, TLDFU_GetErrorText(st));
        return 0;
    }

    switch (runMode) {
    case TLDFU_RUNMODE_APPLICATION:
        qWarning("Rebooting device to switch to DFU mode... ");
        st = MacDFU.device.RebootDevice(TLDFU_RUNMODE_BOOTLOADER, MacDFU.deviceFlags);
        if ( TLSTATUS_SUCCESS != st ) {
             qWarning("RebootDevice(BOOTLOADER) failed, st=0x%08x (%s) ", st, TLDFU_GetErrorText(st));
             return 0;
        }
        qWarning("Waiting until device is gone... ");
        st = WaitUntilDeviceIsGone(MacDFU.device);
        if ( TLSTATUS_SUCCESS != st ) {
             qWarning("Device did not disconnect after reboot request. Upgrade failed. ");
             return 0;
        }
        break;

    case TLDFU_RUNMODE_BOOTLOADER:
        break;

    default:
        qWarning("Unexpected run mode %u. Upgrade failed. ", runMode);
        return 0;
    }

    MacDFU.device.CloseDevice();
    runMode = TLDFU_RUNMODE_UNKNOWN;

    st = EnumerateAndOpenDevice(MacDFU.device, runMode, MacDFU.deviceFilter, MacDFU.enumIntervalSecs * 1000);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("EnumerateAndOpenDevice(after reboot) failed, st=0x%08x (%s) ", st, TLDFU_GetErrorText(st));
        qWarning("If Windows is currently installing drivers, try to run the upgrade again after driver installation has finished. ");
        return 0;
    }

    if (runMode != TLDFU_RUNMODE_BOOTLOADER) {
        qWarning("Unexpected run mode %u after reboot. Upgrade failed. ", runMode);
        return 0;
    }

    unsigned int targetId = MacDFU.targetId;
    unsigned int startUpgradeFlags = MacDFU.deviceFlags;
    st = MacDFU.device.StartUpgrade(MacDFU.FirmwareImage, targetId, startUpgradeFlags);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("StartUpgrade(%u) failed, st=0x%08x (%s) ", targetId, st, TLDFU_GetErrorText(st));
        return 0;
    }

    qWarning("Downloading to target %u, press any key to abort... ", targetId);

    MacDFU.upgradeCompletionStatus = TLSTATUS_SUCCESS;
    qWarning("DFU_download  SUCCESS ");
    return 1;
}

unsigned int USBAudioAPI::setDFUStart(QString path)
{
    return setDFUStartByName(path, mDeviceName);
}
unsigned int USBAudioAPI::setDFUStartByName(QString path, QString deviceName)
{
    auto vplist = getVplistByDeviceName(deviceName);
    return setDFUStartByVplist(vplist, path.toStdString());
}
unsigned int USBAudioAPI::setDFUStartByID(QString path, unsigned int deviceVendorId, unsigned int deviceProductId)
{
    return setDFUStartByVplist(getVplistByDeviceID(deviceVendorId, deviceProductId), path.toStdString());
}
bool USBAudioAPI::setDFUTerminate()
{
    TLSTATUS st = MacDFU.device.FinishUpgrade();
    if ( TLSTATUS_SUCCESS != st ) {
        if (TLSTATUS_ABORTED == st) {
             qWarning("Upgrade was aborted. ");
        }
        else {
             qWarning("FinishUpgrade failed, st=0x%08x (%s) ", st, TLDFU_GetErrorText(st));
             return 0;
        }
    }

    if (MacDFU.upgradeCompletionStatus != TLSTATUS_SUCCESS) {
        return MacDFU.upgradeCompletionStatus;
    }

    qWarning("Rebooting device to revert back to APP mode... ");
    st = MacDFU.device.RebootDevice(TLDFU_RUNMODE_APPLICATION, MacDFU.deviceFlags);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("RebootDevice(APPLICATION) failed, st=0x%08x (%s) ", st, TLDFU_GetErrorText(st));
        return 0;
    }

    qWarning("Firmware upgrade finished. The device should start in application run mode now.");

    qWarning("Waiting until device is gone... ");
    st = WaitUntilDeviceIsGone(MacDFU.device);
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("Device did not disconnect after reboot request. ");
        return 0;
    }

    st = MacDFU.FirmwareImage.UnloadFirmwareImage();
    if ( TLSTATUS_SUCCESS != st ) {
        qWarning("UnloadFirmwareImage(" UNISTR_FMT ") failed, st=0x%08x (%s) ",
               MacDFU.firmwareFile.c_str(), st, TLDFU_GetErrorText(st));
        return 0;
    }

    return 1;
}
#if (USB_AUDIO_MIXER_SUPPORT == 1)
USBAudioAPI& USBAudioAPI::setNodeGain(unsigned int matrix_X, unsigned int matrix_Y, int gain)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(matrix_Y);
    Q_UNUSED(gain);
    return *this;
}
USBAudioAPI& USBAudioAPI::setNodeGainPercent(unsigned int matrix_X, unsigned int matrix_Y, unsigned int gain)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(matrix_Y);
    Q_UNUSED(gain);
    return *this;
}
USBAudioAPI& USBAudioAPI::setNodeGainLinear(unsigned int matrix_X, unsigned int matrix_Y, double gain)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(matrix_Y);
    Q_UNUSED(gain);
    return *this;
}
USBAudioAPI& USBAudioAPI::setNodeGainLog(unsigned int matrix_X, unsigned int matrix_Y, double gain)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(matrix_Y);
    Q_UNUSED(gain);
    return *this;
}
USBAudioAPI& USBAudioAPI::setOutputGain(unsigned int matrix_X, int gain)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(gain);
    return *this;
}
USBAudioAPI& USBAudioAPI::setOutputGainPercent(unsigned int matrix_X, unsigned int gain)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(gain);
    return *this;
}
USBAudioAPI& USBAudioAPI::setOutputGainLinear(unsigned int matrix_X, double gain)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(gain);
    return *this;
}
USBAudioAPI& USBAudioAPI::setOutputGainLog(unsigned int matrix_X, double gain)
{
    Q_UNUSED(matrix_X);
    Q_UNUSED(gain);
    return *this;
}
void USBAudioAPI::setMixerNodeAttachToActiveDevice()
{
}
#endif


// modify
USBAudioAPI& USBAudioAPI::modifyDevicePIDList(QVector<QPair<QString, QString>> list)
{
    mDevicePIDList = list;
    return *this;
}


// is
bool USBAudioAPI::isDeviceDuplicate(QStringList& deviceNameList)
{
    bool result=false;
    QVector<QString> deviceNameVector=getNameOfAllDevice();
    QMap<QString, int> deviceNameMap;
    deviceNameList.clear();
    for(auto it=deviceNameVector.constBegin();it!=deviceNameVector.constEnd();++it)
    {
        deviceNameMap[*it]++;
    }
    for(auto it=deviceNameMap.constBegin();it!=deviceNameMap.constEnd();++it)
    {
        if(it.value() > 1)
        {
            deviceNameList.append(it.key());
            result = true;
        }
    }
    return result;
}
bool USBAudioAPI::isDeviceOnline()
{
    return getSampleRateOfActiveDevice() ? true : false;
}


// check
bool USBAudioAPI::checkDriver()
{
    return true;
}
bool USBAudioAPI::checkApiVersion()
{
    unsigned int api_ver = TLDFU_GetApiVersion();
    if(TLDFU_CheckApiVersion( TLDFU_API_EXTRACT_MJ_VERSION(api_ver), TLDFU_API_EXTRACT_MN_VERSION(api_ver)))
    {
        return true;
    }
    else
    {
        return false;
    }
}


// show
USBAudioAPI& USBAudioAPI::showPropertiesOfAllDevice()
{
    unsigned int deviceCounter=0;
    QVector<TUsbAudioDeviceProperties> devicePropertiesVector=getPropertiesOfAllDevice();
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showPropertiesOfAllDevice //show list below";
    qInfo() << "Index\tDevice\tVID\tPID\tREV\tSerial\t\tDFU supported\tUSB mode";
    for(QVector<TUsbAudioDeviceProperties>::iterator it=devicePropertiesVector.begin();it!=devicePropertiesVector.end();++it)
    {
        qInfo("%02u\t%s\t0x%04X\t0x%04X\t0x%04X\t%s\t%s\t\t%s",
            deviceCounter,
            qPrintable(QString::fromWCharArray(it->productString)),
            it->usbVendorId,
            it->usbProductId,
            it->usbRevisionId,
            qPrintable(QString::fromWCharArray(it->serialNumberString)),
            (0 != it->flags) ? "yes" : "no ",
            "Not obtained"
            );

        deviceCounter++;
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showNameOfAllDevice()
{
    QVector<QString> deviceNameVector=getNameOfAllDevice();
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showNameOfAllDevice //show list below";
    for(QVector<QString>::iterator it=deviceNameVector.begin();it!=deviceNameVector.end();++it)
    {
        qInfo() << " ->" << *it;
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showSampleRateOfActiveDeviceSupported()
{
    QVector<unsigned int> supportedSampleRateVector=getSampleRateOfActiveDeviceSupported();
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showSampleRateOfActiveDeviceSupported //show list below";
    for(QVector<unsigned int>::iterator it=supportedSampleRateVector.begin();it!=supportedSampleRateVector.end();++it)
    {
        qInfo() << " ->" << *it;
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showSampleRateOfActiveDevice()
{
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showSampleRateOfActiveDevice //show list below";
    qInfo() << " ->" << getSampleRateOfActiveDevice();
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showBufferSizeOfActiveDeviceSupported()
{
    QVector<unsigned int> supportedBufferSizeVector=getBufferSizeOfActiveDeviceSupported();
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showBufferSizeOfActiveDeviceSupported //show list below";
    for(QVector<unsigned int>::iterator it=supportedBufferSizeVector.begin();it!=supportedBufferSizeVector.end();++it)
    {
        qInfo() << " ->" << *it;
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
USBAudioAPI& USBAudioAPI::showBufferSizeOfActiveDevice()
{
    qInfo() << "";
    qInfo() << "";
    qInfo() << "USBAudioAPI::showBufferSizeOfActiveDevice //show list below";
    qInfo() << " ->" << getBufferSizeOfActiveDevice();
    qInfo() << "";
    qInfo() << "";
    return *this;
}
#if (USB_AUDIO_MIXER_SUPPORT == 1)
USBAudioAPI& USBAudioAPI::showMaxChannelNumberOfMixer()
{
    return *this;
}
#endif
#endif


// tool
qint64 USBAudioAPI::GainToPercent(qint64 gain)
{
    return gain * 100 / mGainOne;
}
qint64 USBAudioAPI::PercentToGain(qint64 percent)
{
    return percent * mGainOne / 100;
}
double USBAudioAPI::GainToLinear(qint64 gain)
{
    return gain / mGainOneDotZero;
}
qint64 USBAudioAPI::LinearToGain(double attenuation)
{
    return attenuation * mGainOneDotZero;
}
double USBAudioAPI::GainToLog(qint64 gain)
{
    if(gain <= 0)
    {
        return -90;
    }
    return log10(gain / mGainOneDotZero) * 20.0;
}
qint64 USBAudioAPI::LogToGain(double dB, double minusInf)
{
    if(dB <= minusInf)
    {
        return mGainZero;
    }
    return pow(10.0, dB / 20.0) * mGainOneDotZero;
}


#!/bin/bash

set -e  # Exit on error

APP_NAME="M Control Center"
APP_VERSION="1.1.3"
APP_BUNDLE="../../../build/Source/$APP_NAME.app"
DMG_NAME=$APP_NAME
DEVELOPER_ID="Developer ID Application: Guangzhou TOPPING Technology Co., Ltd (M336Q22BHF)"
APPLE_ID="<EMAIL>"
APP_PASSWORD="ikya-nlde-bdxn-kihp"
TEAM_ID="M336Q22BHF"
MACDEPLOYQT="../../../../Qt/6.9.0/macos/bin/macdeployqt"

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

if [ ! -f "$MACDEPLOYQT" ]; then
    print_error "macdeployqt not found: $MACDEPLOYQT"
    exit 1
fi

if [ ! -d "$APP_BUNDLE" ]; then
    print_error "Application bundle not found: $APP_BUNDLE"
    print_error "Please ensure the application is built and located at the specified path"
    exit 1
fi

print_status "Found application bundle: $APP_BUNDLE"

print_status "Packaging Qt dependencies using macdeployqt..."
"$MACDEPLOYQT" "$APP_BUNDLE"

if [ $? -eq 0 ]; then
    print_status "Qt dependencies packaged successfully"
else
    print_error "Failed to package Qt dependencies"
    exit 1
fi

print_status "Signing the application..."
sudo codesign --deep --force --verify --verbose --sign "$DEVELOPER_ID" --options runtime "$APP_BUNDLE"

if [ $? -eq 0 ]; then
    print_status "Application signed successfully"
else
    print_error "Failed to sign application"
    exit 1
fi

print_status "Creating ZIP file for notarization..."
ZIP_FILE="${APP_BUNDLE}.zip"

if [ -f "$ZIP_FILE" ]; then
    rm "$ZIP_FILE"
fi

ditto -c -k --sequesterRsrc --keepParent "$APP_BUNDLE" "$ZIP_FILE"
if [ $? -eq 0 ]; then
    print_status "ZIP file created: $ZIP_FILE"
else
    print_error "Failed to create ZIP file"
    exit 1
fi

print_status "Submitting for notarization..."
xcrun notarytool submit --wait "$ZIP_FILE" --apple-id "$APPLE_ID" --password "$APP_PASSWORD" --team-id "$TEAM_ID"

if [ $? -eq 0 ]; then
    print_status "Notarization completed successfully"
    print_status "Performing final verification..."
    spctl -a -v "$APP_BUNDLE"
else
    print_error "Notarization failed"
    print_warning "DMG and ZIP files are still available for manual notarization"
fi

TEMP_DIR=$(mktemp -d)
DMG_DIR="$TEMP_DIR/dmg_contents"
mkdir -p "$DMG_DIR"

print_status "Copying app to temporary directory..."
cp -R "$APP_BUNDLE" "$DMG_DIR/"

ln -s /Applications "$DMG_DIR/Applications"

DMG_FILE="${DMG_NAME}.dmg"
print_status "Creating DMG file: $DMG_FILE"

if [ -f "$DMG_FILE" ]; then
    rm "$DMG_FILE"
    print_status "Removed existing DMG file"
fi

hdiutil create -volname "$DMG_NAME" -srcfolder "$DMG_DIR" -ov -format UDZO "$DMG_FILE"
if [ $? -eq 0 ]; then
    print_status "DMG created successfully: $DMG_FILE"
else
    print_error "Failed to create DMG"
    rm -rf "$TEMP_DIR"
    exit 1
fi

rm -rf "$ZIP_FILE"
rm -rf "$TEMP_DIR"

DIR=TPCC_Mac_V$APP_VERSION
mkdir $DIR
mv "$DMG_FILE" $DIR
ZIP_FILE="${DIR}.zip"
if [ -f "$ZIP_FILE" ]; then
    rm "$ZIP_FILE"
fi
ditto -c -k --sequesterRsrc --keepParent "$DIR" "$ZIP_FILE"

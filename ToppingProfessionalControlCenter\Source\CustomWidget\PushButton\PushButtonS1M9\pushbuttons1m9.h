#ifndef PUSHBUTTONS1M9_H
#define PUSHBUTTONS1M9_H


#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M9 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M9(QWidget* parent=nullptr);
    ~PushButtonS1M9();
    enum ButtonID
    {
        button48V=0,
        buttonMUTE,
    };
    PushButtonS1M9& setFont(QFont font);
    PushButtonS1M9& setPushButtonWeightWidth(int weight);
    PushButtonS1M9& setPushButtonState48V(bool state);
    PushButtonS1M9& setPushButtonStateMUTE(bool state);
    PushButtonS1M9& setPushButtonClicked48V(bool state);
    PushButtonS1M9& setPushButtonClickedMUTE(bool state);
    bool getPushButtonState48V();
    bool getPushButtonStateMUTE();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonState48V=false;
    bool mPushButtonStateMUTE=false;
    QPushButton mPushButton48V;
    QPushButton mPushButtonMUTE;
    int mWeightWidth=40;
private slots:
    void in_mPushButton48V_clicked();
    void in_mPushButtonMUTE_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M9_H


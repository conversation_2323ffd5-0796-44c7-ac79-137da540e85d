/************************************************************************
 *
 *  Module:       tstatus_codes_ex.h
 *
 *  Description:
 *    Project-specific status code definitions used by both
 *    kernel-mode and user-mode software layers.
 *
 *  Runtime Env.: Win32 / NT Kernel
 *
 *  Author(s):
 *    Udo <PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH
 *
 ************************************************************************/

//
// This file must be included by tstatus_codes.h only.
//
#ifndef __inside_tstatus_codes_h__
#error This file is an extension to tstatus_codes.h and must not be included otherwise.
#endif



///////////////////////////////////////////////////////////////////
// project-specific status codes
// A set of project-specific status codes may be defined per project.
// The project-specific status codes values have to use the range from
// 0x1000 ... 0x1FFF.
// Under Windows this is mapped to
// 0xE0001000 ... 0xE0001FFF
///////////////////////////////////////////////////////////////////

#define       TSTATUS_SOUND_DEVICE_IN_USE           TSTATUS_CODE(0x1001)
case_TSTATUS_(TSTATUS_SOUND_DEVICE_IN_USE, "The Windows sound device is currently in use.")

#define       TSTATUS_ASIO_IN_USE                   TSTATUS_CODE(0x1002)
case_TSTATUS_(TSTATUS_ASIO_IN_USE, "The ASIO interface is currently in use.")

#define       TSTATUS_INVALID_SAMPLE_SIZE           TSTATUS_CODE(0x1003)
case_TSTATUS_(TSTATUS_INVALID_SAMPLE_SIZE, "Invalid sample size.")

#define       TSTATUS_INVALID_SAMPLE_RATE           TSTATUS_CODE(0x1004)
case_TSTATUS_(TSTATUS_INVALID_SAMPLE_RATE, "The sample rate is invalid.")

#define       TSTATUS_SAMPLE_RATE_NOT_SUPPORTED     TSTATUS_CODE(0x1005)
case_TSTATUS_(TSTATUS_SAMPLE_RATE_NOT_SUPPORTED, "The sample rate is not supported.")

#define       TSTATUS_CANNOT_OPEN_INI_FILE          TSTATUS_CODE(0x1006)
case_TSTATUS_(TSTATUS_CANNOT_OPEN_INI_FILE, "Cannot open INI file.")

#define       TSTATUS_NO_IMAGE_LOADED               TSTATUS_CODE(0x1007)
case_TSTATUS_(TSTATUS_NO_IMAGE_LOADED, "No firmware image loaded.")

#define       TSTATUS_INVALID_AUDIO_FUNCTION        TSTATUS_CODE(0x1008)
case_TSTATUS_(TSTATUS_INVALID_AUDIO_FUNCTION, "The audio function was not initialized successfully.")

#define       TSTATUS_SS_EP_COMPANION_DESC_MISSING  TSTATUS_CODE(0x1009)
case_TSTATUS_(TSTATUS_SS_EP_COMPANION_DESC_MISSING, "The device is operating at Super Speed but there are no Endpoint Companion descriptors.")

#define       TSTATUS_NOT_SUPPORTED_BY_LITE_VERSION     TSTATUS_CODE(0x100A)
case_TSTATUS_(TSTATUS_NOT_SUPPORTED_BY_LITE_VERSION, "Not supported by LITE version.")

#define       TSTATUS_SWITCH_STREAM_FORMAT_NOT_ALLOWED  TSTATUS_CODE(0x100B)
case_TSTATUS_(TSTATUS_SWITCH_STREAM_FORMAT_NOT_ALLOWED, "Switch stream format not allowed.")

#define       TSTATUS_INVALID_LICENSE_DATA          TSTATUS_CODE(0x100C)
case_TSTATUS_(TSTATUS_INVALID_LICENSE_DATA, "Invalid license data.")

#define       TSTATUS_LICENSE_CHECK_FAILED          TSTATUS_CODE(0x100D)
case_TSTATUS_(TSTATUS_LICENSE_CHECK_FAILED, "License check failed.")

#define       TSTATUS_NOT_ALLOWED_IN_DSD_MODE       TSTATUS_CODE(0x100E)
case_TSTATUS_(TSTATUS_NOT_ALLOWED_IN_DSD_MODE, "Not allowed in DSD mode.")

#define       TSTATUS_INCOMPATIBLE_APP_CFG_FILE       TSTATUS_CODE(0x100F)
case_TSTATUS_(TSTATUS_INCOMPATIBLE_APP_CFG_FILE, "Incompatible application configuration file.")

#define       TSTATUS_APP_CFG_FILE_NOT_OPENED       TSTATUS_CODE(0x1010)
case_TSTATUS_(TSTATUS_APP_CFG_FILE_NOT_OPENED, "Failed to open application configuration file.")

#define       TSTATUS_NO_RELATED_DEVICE             TSTATUS_CODE(0x1011)
case_TSTATUS_(TSTATUS_NO_RELATED_DEVICE, "There is no related device.")


//!!! low byte contains T_UsbClsDfu_GetStatusResponse.bStatus
#define       TSTATUS_DFU_STATE_ERROR               TSTATUS_CODE(0x1100)
case_TSTATUS_(TSTATUS_DFU_STATE_ERROR, "DFU error.")
//#define       TSTATUS_DFU_STATE_ERROR_LAST        TSTATUS_CODE(0x11FF)



/*************************** EOF **************************************/

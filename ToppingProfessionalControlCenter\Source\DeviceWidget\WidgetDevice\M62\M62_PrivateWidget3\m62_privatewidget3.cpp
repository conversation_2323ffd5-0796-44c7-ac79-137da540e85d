#include "m62_privatewidget3.h"
#include "ui_m62_privatewidget3.h"
#include "dials1m1.h"
#include "globalfont.h"
#include "workspace.h"
#include <QStack>
#include <qboxlayout.h>
#include <qlabel.h>
#include <qlineedit.h>
#include <QButtonGroup>

M62_PrivateWidget3::M62_PrivateWidget3(QWidget* parent, QString name)
    : EffectBase(parent), WorkspaceObserver(nullptr), AppSettingsObserver(nullptr), ui(new Ui::M62_PrivateWidget3)
{
    ui->setupUi(this);
    QString style;
    style += "QWidget {"
            "   color: rgb(161, 161, 161);"
            "}";
    setStyleSheet(style);
    this->setObjectName(name);

    ui->ncWidget->setObjectName("ncWidget");
    ui->mutefxWidget->setObjectName("mutefxWidget");
    ui->ncBypassWidget->setObjectName("ncBypassWidget");
    ui->rbWidget->setObjectName("rbWidget");
    ui->widgetVolumeMeter->setColorBG(Qt::transparent);
    ui->widgetVolumeMeter->setWidthRatio(13, 24, 26);
    ui->widgetVolumeMeter->setHeightRatio(2, 2, 96, 0);
    ui->widgetVolumeMeter->setScaleLineHidden(true);
    ui->lineEditNoiseReduction->setDisabled(true);
    ui->lineEditReverb->setDisabled(true);
    ui->sliderInput1->setPageStep(1);
    ui->sliderInput2->setPageStep(1);
    ui->sliderAux->setPageStep(1);
    ui->sliderBluetooth->setPageStep(1);
    ui->sliderOtg->setPageStep(1);
    ui->sliderInput1->installEventFilter(this);
    ui->sliderInput2->installEventFilter(this);
    ui->sliderAux->installEventFilter(this);
    ui->sliderBluetooth->installEventFilter(this);
    ui->sliderOtg->installEventFilter(this);
    setInput1Range(-90, 12);
    setInput2Range(-90, 12);
    setAuxRange(-90, 12);
    setBluetoothRange(-90, 12);
    setOtgRange(-90, 12);

    ui->dialNoiseReduction->setRange(1, 70);
    ui->dialDryWet->setRange(0, 100);
    ui->dialRoom->setRange(1, 6).showText(false).showCircle(false);
    ui->dialDecay->setRange(0, 18).showText(false).showCircle(false);
    
    auto setNCType = [this](QString text){
        ui->pushButtonBypassNoise->setChecked(false);
        if(text != "Bypass"){
            mNoiseReductionTypePre = text;
        }
        save("NoiseReductionType", text);
        updateNoiseReductionType(text);
        updateAttribute();
        setNCIcon(mNoiseReductionType);
    };
    connect(ui->pushButtonNC1, &QPushButton::clicked, this, [this, setNCType](bool checked) {
        setNCType("NC1");
    });
    connect(ui->pushButtonNC2, &QPushButton::clicked, this, [this, setNCType](bool checked) {
        setNCType("NC2");
    });
    connect(ui->pushButtonBypassNoise, &QPushButton::clicked, this, [this, setNCType](bool checked) {
        setNCType(checked?"Bypass":mNoiseReductionTypePre);
        ui->pushButtonBypassNoise->setChecked(checked);
    });
    connect(ui->dialNoiseReduction, &DialS1M5::valueChanged, this, [this](float value) {
        WorkspaceObserver::setValue(getValueNoiseReductionKey(mNoiseReductionType), value);
        QString key = "NoiseReduction";
        if(isWidgetEmitAction())
        {
            emit attributeChanged(this->objectName(), "Save_" + key, QString::number(value));
        }
        updateAttribute();
    });


    auto setReverbType = [this](QString text){
        ui->pushButtonBypassReverb->setChecked(false);
        if(text != "Bypass"){
            mReverbTypePre = text;
        }
        save("ReverbType", text);
        updateReverbType(text);
        updateAttribute();
        setReverbIcon(mReverbType);
    };
    connect(ui->pushButtonReverbStudio, &QPushButton::clicked, this, [this, setReverbType](bool checked) {
        setReverbType("STUDIO");
    });
    connect(ui->pushButtonReverbLive, &QPushButton::clicked, this, [this, setReverbType](bool checked) {
        setReverbType("LIVE");
    });
    connect(ui->pushButtonReverbHall, &QPushButton::clicked, this, [this, setReverbType](bool checked) {
        setReverbType("HALL");
    });
    connect(ui->pushButtonBypassReverb, &QPushButton::clicked, this, [this, setReverbType](bool checked) {
        setReverbType(checked?"Bypass":mReverbTypePre);
        ui->pushButtonBypassReverb->setChecked(checked);
    });
    connect(ui->dialDryWet, &DialS1M5::valueChanged, this, &M62_PrivateWidget3::in_widgetDial_valueChanged);
    connect(ui->dialRoom, &DialS1M1::valueChanged, this, [this](float value){
        WorkspaceObserver::setValue(getValueReverbKey(mReverbType), value);
        QString key = "Room";
        if(isWidgetEmitAction())
        {
            emit attributeChanged(this->objectName(), "Save_" + key, QString::number(value));
        }
        updateAttribute();
    });
    connect(ui->dialDecay, &DialS1M1::valueChanged, this, &M62_PrivateWidget3::in_widgetDialDecay_valueChanged);
    
    // 音量滑块连接
    connect(ui->sliderInput1, &QSlider::valueChanged, this, &M62_PrivateWidget3::in_sliderInput1_valueChanged);
    connect(ui->sliderInput2, &QSlider::valueChanged, this, &M62_PrivateWidget3::in_sliderInput2_valueChanged);
    connect(ui->sliderAux, &QSlider::valueChanged, this, &M62_PrivateWidget3::in_sliderAux_valueChanged);
    connect(ui->sliderBluetooth, &QSlider::valueChanged, this, &M62_PrivateWidget3::in_sliderBluetooth_valueChanged);
    connect(ui->sliderOtg, &QSlider::valueChanged, this, &M62_PrivateWidget3::in_sliderOtg_valueChanged);

    connect(ui->pushButtonMuteFx, &QPushButton::clicked, this, [this](bool checked) {
        save("MuteFX", checked);
        updateAttribute();
        setMuteFxIcon(checked);
    });

    ui->pushButtonNC3->hide();
    ui->pushButtonMuteFx->installEventFilter(this);
}

M62_PrivateWidget3::~M62_PrivateWidget3()
{
    delete ui;
}

void M62_PrivateWidget3::setReverbIcon(const QString& text){
    QString png;
    if(text == "STUDIO"){
        png = ":/Image/PushButtonGroup/PBG8_1.png";
    }else if(text == "LIVE"){
        png = ":/Image/PushButtonGroup/PBG8_5.png";
    }else if(text == "HALL"){
        png = ":/Image/PushButtonGroup/PBG8_8.png";
    }else if(text == "Bypass"){
        png = ":/Image/PushButtonGroup/PBG8_10.png";
    }
    ui->rbWidget->setStyleSheet(QString("QWidget#rbWidget{image:url(%1);}").arg(png));
}

void M62_PrivateWidget3::setNCIcon(const QString& text){
    QString png;
    if(text == "NC1"){
        png = ":/Image/PushButtonGroup/PBG5_2.png";
    }else if(text == "NC2"){
        png = ":/Image/PushButtonGroup/PBG5_3.png";
    }else if(text == "Bypass"){
        png = ":/Image/PushButtonGroup/PBG5_0.png";
    }
    setNCBypassIcon(text == "Bypass");
    ui->ncWidget->setStyleSheet(QString("QWidget#ncWidget{image:url(%1);}").arg(png));
}

void M62_PrivateWidget3::setNCBypassIcon(bool enabled){
    QString png;
    if(!enabled){
        png = ":/Image/PushButtonGroup/PBG3_0.png";
    }else{
        png = ":/Image/PushButtonGroup/PBG3_1.png";
    }
    ui->ncBypassWidget->setStyleSheet(QString("QWidget#ncBypassWidget{image:url(%1);}").arg(png));
}

void M62_PrivateWidget3::setMuteFxIcon(bool enabled){
    QString png;
    if(!enabled){
        png = ":/Image/PushButtonGroup/PBG2_0.png";
    }else{
        png = ":/Image/PushButtonGroup/PBG2_1.png";
    }
    ui->mutefxWidget->setStyleSheet(QString("QWidget#mutefxWidget{image:url(%1);}").arg(png));
}

M62_PrivateWidget3& M62_PrivateWidget3::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setFont(QFont font)
{
    mFont = font;
    QStack<QWidget*> stack;
    stack.push(this);

    while (!stack.isEmpty())
    {
        QWidget* widget = stack.pop();
        widget->setFont(mFont);

        for (auto child : widget->children())
        {
            if (QWidget* childWidget = qobject_cast<QWidget*>(child))
            {
                stack.push(childWidget);
            }
        }
    }
    return *this;
}

QString M62_PrivateWidget3::getValueNoiseReductionKey(QString type)
{
    if(type == "NC1") {
        return "NoiseReductionNC1";
    } else if(type == "NC2") {
        return "NoiseReductionNC2";
    } else if(type == "NC3") {
        return "NoiseReductionNC3";
    } else if(type == "Bypass") {
        return "NoiseReductionBypass";
    }
    return "";
}
int M62_PrivateWidget3::getValueNoiseReduction(QString type)
{
    auto key = getValueNoiseReductionKey(type);
    return WorkspaceObserver::value(key).toFloat();
}
M62_PrivateWidget3& M62_PrivateWidget3::setValueNoiseReduction(int value)
{
    ui->dialNoiseReduction->setValue(value);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setValueNoiseReductionType(QString type)
{
    if(type!="Bypass"){
        mNoiseReductionTypePre=type;
    }
    mNoiseReductionType = type;
    int value = getValueNoiseReduction(mNoiseReductionType);
    ui->pushButtonBypassNoise->setChecked(false);
    if(type == "NC1") {
        mNoiseReductionNC1 = value;
    } else if(type == "NC2") {
        mNoiseReductionNC2 = value;
    } else if(type == "NC3") {
        mNoiseReductionNC3 = value;
    } else if(type == "Bypass") {
        ui->pushButtonBypassNoise->setChecked(true);
        mNoiseReductionBypass = value;
    }
    WorkspaceObserver::setValue("NoiseReductionType", mNoiseReductionType);
    updateNoiseReductionType(mNoiseReductionType);
    setNCIcon(mNoiseReductionType);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::updateNoiseReductionType(QString type)
{
    ui->dialNoiseReduction->setEnabled(true);
    if(type == "Bypass") {
        ui->dialNoiseReduction->setEnabled(false);
    }
    ui->dialNoiseReduction->setValue(getValueNoiseReduction(type));
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setValueDryWet(float value)
{
    if(mPreDryWet != value) {
        mPreDryWet = value;
        ui->dialDryWet->setValue(value);
        save("DryWet", value);
    }
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setValueRoom(float value)
{
    ui->dialRoom->setValue(value * 100);
    return *this;
}

QString M62_PrivateWidget3::getValueReverbKey(QString type)
{
    if(type == "STUDIO") {
        return "ReverbStudio";
    } else if(type == "LIVE") {
        return "ReverbLive";
    } else if(type == "HALL") {
        return "ReverbHall";
    } else if(type == "Bypass") {
        return "ReverbBypass";
    }
    return "";
}

int M62_PrivateWidget3::getValueReverb(QString type)
{
    auto key = getValueReverbKey(type);
    return WorkspaceObserver::value(key).toFloat();
}

M62_PrivateWidget3& M62_PrivateWidget3::updateReverbType(QString type)
{
    ui->dialRoom->setEnabled(true);
    if(type == "Bypass") {
        ui->dialRoom->setEnabled(false);
    }
    ui->dialRoom->setValue(getValueReverb(type));
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setValueDecay(float value)
{
    if(mPreDecay != value) {
        mPreDecay = value;
        ui->dialDecay->setValue(static_cast<int>(value * 100));
    }
    return *this;
}

// 音量滑块设置
M62_PrivateWidget3& M62_PrivateWidget3::setValueInput1(int value) {
    mValueInput1 = value;
    ui->sliderInput1->setValue(value);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setValueInput2(int value) {
    mValueInput2 = value;
    ui->sliderInput2->setValue(value);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setValueAux(int value) {
    mValueAux = value;
    ui->sliderAux->setValue(value);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setValueBluetooth(int value) {
    mValueBluetooth = value;
    ui->sliderBluetooth->setValue(value);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setValueOtg(int value) {
    mValueOtg = value;
    ui->sliderOtg->setValue(value);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setInput1Range(int min, int max) {
    ui->sliderInput1->setRange(min, max);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setInput2Range(int min, int max) {
    ui->sliderInput2->setRange(min, max);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setAuxRange(int min, int max) {
    ui->sliderAux->setRange(min, max);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setBluetoothRange(int min, int max) {
    ui->sliderBluetooth->setRange(min, max);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setOtgRange(int min, int max) {
    ui->sliderOtg->setRange(min, max);
    return *this;
}

// 标签文本设置
M62_PrivateWidget3& M62_PrivateWidget3::setLabelInput1Text(const QString& text) {
    ui->labelInput1->setText(text);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setLabelInput2Text(const QString& text) {
    ui->labelInput2->setText(text);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setLabelAuxText(const QString& text) {
    ui->labelAux->setText(text);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setLabelBluetoothText(const QString& text) {
    ui->labelBluetooth->setText(text);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setLabelOtgText(const QString& text) {
    ui->labelOtg->setText(text);
    return *this;
}

// 混响效果设置
M62_PrivateWidget3& M62_PrivateWidget3::setReverbType(QString type) {
    if(type!="Bypass"){
        mReverbTypePre=type;
    }
    ui->pushButtonBypassReverb->setChecked(false);
    mReverbType = type;
    if(type == "Bypass") {
        ui->pushButtonBypassReverb->setChecked(true);
    }
    WorkspaceObserver::setValue("ReverbType", mReverbType);
    updateReverbType(mReverbType);
    setReverbIcon(mReverbType);
    return *this;
}

// 标题设置
M62_PrivateWidget3& M62_PrivateWidget3::setReverbTitle(const QString& title) {
    ui->lineEditReverb->setText(title);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setNoiseReductionTitle(const QString& title) {
    ui->lineEditNoiseReduction->setText(title);
    return *this;
}

// 音量显示设置
M62_PrivateWidget3& M62_PrivateWidget3::setInputLevel1Text(const QString& text) {
    ui->labelInputLevel1->setText(text);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setInputLevel2Text(const QString& text) {
    ui->labelInput2Level->setText(text);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setAuxLevelText(const QString& text) {
    ui->labelAuxLevel->setText(text);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setBluetoothLevelText(const QString& text) {
    ui->labelBluetoothLevel->setText(text);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setOtgLevelText(const QString& text) {
    ui->labelOtgLevel->setText(text);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setLeftVolumeMeter(int value) {
    ui->widgetVolumeMeter->setValueLeft(value);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setRightVolumeMeter(int value) {
    ui->widgetVolumeMeter->setValueRight(value);
    return *this;
}

M62_PrivateWidget3& M62_PrivateWidget3::setMUTEFX(bool enabled){
    mMuteFX = enabled;
    ui->pushButtonMuteFx->setChecked(enabled);
    setMuteFxIcon(enabled);
    return *this;
}

void M62_PrivateWidget3::showEvent(QShowEvent* e) {
    Q_UNUSED(e);
    resizeEvent(nullptr);
}

void M62_PrivateWidget3::resizeEvent(QResizeEvent* e) {
    Q_UNUSED(e);
    ui->frameMain->layout()->setSpacing(ui->frameMain->height() * 0.02);

    {
        QRect widget1Rect = ui->widget1->geometry();
        int w1Width = widget1Rect.width();
        int w1Height = widget1Rect.height();
        float groupWidth = w1Width * 0.19;
        float spacing = (w1Width - groupWidth * 5) / 6;
        auto setSliderGeometry = [=](QLabel* label, QSlider* slider, QLabel* levelLabel, int column) {
            float xPos = (groupWidth+spacing)*column + spacing;
            label->setGeometry(xPos, w1Height * 0, groupWidth, w1Height * 0.08);
            slider->setGeometry(xPos+0.2*groupWidth,  w1Height * 0.08, 0.6*groupWidth, w1Height * 0.87);
            levelLabel->setGeometry(xPos,w1Height * 0.95, groupWidth, w1Height * 0.05);
        };
        setSliderGeometry(ui->labelInput1, ui->sliderInput1, ui->labelInputLevel1, 0);
        setSliderGeometry(ui->labelInput2, ui->sliderInput2, ui->labelInput2Level, 1);
        setSliderGeometry(ui->labelAux, ui->sliderAux, ui->labelAuxLevel, 2);
        setSliderGeometry(ui->labelBluetooth, ui->sliderBluetooth, ui->labelBluetoothLevel, 3);
        setSliderGeometry(ui->labelOtg, ui->sliderOtg, ui->labelOtgLevel, 4);
    }

    {
        QRect widget2Rect = ui->widget2->geometry();
        int w2Width = widget2Rect.width();
        int w2Height = widget2Rect.height();
        ui->lineEditNoiseReduction->setGeometry(w2Width * 0, w2Height * 0, w2Width, w2Height * 0.08);
        ui->dialNoiseReduction->setGeometry((w2Width - w2Height * 0.19) / 2 ,w2Height * 0.12, w2Height * 0.19, w2Height * 0.19);
        ui->dialNoiseReduction->setFont(mFont);
        ui->labelNoiseClass->setGeometry(w2Width * 0.35, w2Height * 0.30, w2Width * 0.3, w2Height * 0.06);
        int h = w2Height * 0.19;
        int w = h * 1.2;
        ui->ncWidget->setGeometry((w2Width - w) / 2, w2Height * 0.45, w, h);
        h = w*35/60;
        ui->ncBypassWidget->setGeometry((w2Width - w) / 2, w2Height * 0.88, w, h);
    }

    {
        QRect widget3Rect = ui->widget3->geometry();
        int w3Width = widget3Rect.width();
        int w3Height = widget3Rect.height();
        ui->lineEditReverb->setGeometry(w3Width * 0, w3Width * 0, w3Width, w3Height * 0.08);
        ui->dialDryWet->setGeometry((w3Width - w3Height * 0.19) / 2 ,w3Height * 0.12, w3Height * 0.19, w3Height * 0.19);
        ui->dialDryWet->setFont(mFont);
        ui->labelDry->setGeometry(w3Width * 0.09, w3Height * 0.23, w3Width * 0.19, w3Height * 0.05);
        ui->labelWet->setGeometry(w3Width * 0.75, w3Height * 0.23, w3Width * 0.19, w3Height * 0.05);

        ui->labelRoomSize->setGeometry(w3Width * 0, w3Height * 0.34, w3Width, w3Height * 0.04);
        ui->dialRoom->setGeometry((w3Width - w3Height * 0.10) / 2 ,w3Height * 0.38, w3Height * 0.10, w3Height * 0.10);
        ui->labelReverbRoomSmall->setGeometry(w3Width * 0, w3Height * 0.44, w3Width * 0.5, w3Height * 0.04);
        ui->labelReverbRoomLarge->setGeometry(w3Width * 0.5, w3Height * 0.44, w3Width * 0.5, w3Height * 0.04);

        ui->labelDecayRate->setGeometry(w3Width * 0, w3Height * 0.508, w3Width, w3Height * 0.04);
        ui->dialDecay->setGeometry((w3Width - w3Height * 0.10) / 2 ,w3Height * 0.55, w3Height * 0.10, w3Height * 0.10);
        ui->labelReverbDecayMin->setGeometry(w3Width * 0, w3Height * 0.61, w3Width * 0.5, w3Height * 0.04);
        ui->labelReverbDecayMax->setGeometry(w3Width * 0.5, w3Height * 0.61, w3Width * 0.5, w3Height * 0.04);

        int h = w3Height * 0.342;
        int w = h/1.5;
        ui->rbWidget->setGeometry((w3Width - w) / 2, w3Height * 0.662, w, h);
    }

    {
        QRect widget4Rect = ui->widget4->geometry();
        int w4Width = widget4Rect.width();
        int w4Height = widget4Rect.height();
        ui->labelFxIn->setGeometry(w4Width * 0, w4Width * 0, w4Width*0.5, w4Height * 0.08);
        ui->labelFxOut->setGeometry(w4Width * 0.5, w4Width * 0, w4Width*0.5, w4Height * 0.08);
        ui->labelFxMeterIn->setGeometry(w4Width * 0, w4Height * 0.07, w4Width * 0.5, w4Height * 0.08);
        ui->labelFxMeterOut->setGeometry(w4Width * 0.5, w4Height * 0.07, w4Width * 0.5, w4Height * 0.08);
        ui->widgetVolumeMeter->setGeometry(w4Width * 0.16, w4Height * 0.14, w4Width * 0.68, w4Height * 0.74);
        int h = ui->ncBypassWidget->height();
        int w = ui->ncBypassWidget->width();
        ui->mutefxWidget->setGeometry((w4Width - w) / 2, w4Height * 0.88, w, h);
    }
}

void M62_PrivateWidget3::updateStyle(){
    auto setLabelFontSize = [=](QLabel* label, double factor = 1.0) {
        QFont font = mFont;
        font.setPointSize(GLBFHandle.getSuitablePointSize(mFont, label->text(), label->rect())*factor);
        label->setFont(font);
    };

    auto setTitleFontSize = [=](QWidget* widget) {
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, widget->height()) - 3);
        if(mFont.pointSize() < 8)
        {
            mFont.setPointSize(mFont.pointSize());
        }
        else if(mFont.pointSize() < 12)
        {
            mFont.setPointSize(mFont.pointSize() - 1);
        }
        else if(mFont.pointSize() < 17)
        {
            mFont.setPointSize(mFont.pointSize() - 2);
        }
        else if(mFont.pointSize() < 22)
        {
            mFont.setPointSize(mFont.pointSize() - 3);
        }
        widget->setFont(mFont);
    };       

    setTitleFontSize(ui->labelInput1);
    setTitleFontSize(ui->labelInput2);
    setTitleFontSize(ui->labelAux);
    setTitleFontSize(ui->labelBluetooth);
    setTitleFontSize(ui->labelOtg);
    setLabelFontSize(ui->labelInputLevel1);
    setLabelFontSize(ui->labelInput2Level);
    setLabelFontSize(ui->labelAuxLevel);
    setLabelFontSize(ui->labelBluetoothLevel);
    setLabelFontSize(ui->labelOtgLevel);
    setTitleFontSize(ui->lineEditReverb);
    setLabelFontSize(ui->labelDry);
    setLabelFontSize(ui->labelWet);
    setLabelFontSize(ui->labelRoomSize);
    setLabelFontSize(ui->labelReverbRoomSmall, 0.9);
    setLabelFontSize(ui->labelReverbRoomLarge, 0.9);
    setLabelFontSize(ui->labelDecayRate);
    setLabelFontSize(ui->labelReverbDecayMin, 0.9);
    setLabelFontSize(ui->labelReverbDecayMax, 0.9);
    setTitleFontSize(ui->labelFxIn);
    setTitleFontSize(ui->labelFxOut);
    setTitleFontSize(ui->labelFxMeterIn);
    setTitleFontSize(ui->labelFxMeterOut);
    setTitleFontSize(ui->lineEditNoiseReduction);
    setLabelFontSize(ui->labelNoiseClass);

    QVector<QPushButton*> allButton={
        ui->pushButtonNC1,
        ui->pushButtonNC2,
        ui->pushButtonNC3,
        ui->pushButtonBypassNoise,
        ui->pushButtonReverbStudio,
        ui->pushButtonReverbLive,
        ui->pushButtonReverbHall,
        ui->pushButtonBypassReverb,
        ui->pushButtonMuteFx
    };
    QFont font = mFont;
    font.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->pushButtonMuteFx->height()*0.8));
    for (auto button : allButton) {
        button->setFont(font);
    }

    auto setSliderStyle = [this](QSlider* slider) {
        QString style = QString("QSlider {"
                    "   background-color: transparent;"
                    "}"
                    "QSlider::groove:vertical {"
                    "   background: #3c3c3c;"
                    "   border-radius: %1px;"
                    "   width: %2px;"
                    "}"
                    "QSlider::add-page:vertical {"
                    "   background: rgb(0,150,65);"
                    "   border-radius: %1px;"
                    "   width: %2px;"
                    "}"
                    "QSlider::handle:vertical {"
                    "   border-image: url(:/Icon/SliderHandle.png);"
                    "   height: %3px;"
                    "   margin: -0px -%4px;"
                    "}").arg(slider->width() * 0.1).arg(slider->width() * 0.25).arg(slider->width() * 0.7).arg(slider->width() * 0.225);
        slider->setStyleSheet(style);
    };
    setSliderStyle(ui->sliderInput1);
    setSliderStyle(ui->sliderInput2);
    setSliderStyle(ui->sliderAux);
    setSliderStyle(ui->sliderBluetooth);
    setSliderStyle(ui->sliderOtg);

    QString tempStyle, style;
    tempStyle = QString("QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-top-left-radius: %1px; border-top-right-radius: %1px;border-bottom-left-radius:0px; border-bottom-right-radius:0px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}").arg(ui->widget2->width() * 0.04);
    tempStyle += QString("QPushButton {"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:checked {"
            "   color: rgb(229, 229, 229);"
            "}");

    style = tempStyle;
    style.prepend("QWidget {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}");
    ui->widget2->setStyleSheet(style);
    ui->widget3->setStyleSheet(style);

    style = QString("QPushButton {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: transparent;"
                    "}"
                    "QPushButton:checked {"
                    "   color: rgb(229, 229, 229);"
                    "}");

    style.prepend("QWidget {"
        "   background-color: transparent;"
        "   border-radius: 8px;"
        "}");
    ui->widget4->setStyleSheet(style);
}

void M62_PrivateWidget3::updateAttribute()
{
    if(isWidgetReady())
    {
        // 检查噪声抑制类型变化
        if(mNoiseReductionType != WorkspaceObserver::value("NoiseReductionType").toString())
        {
            mNoiseReductionType = WorkspaceObserver::value("NoiseReductionType").toString();
            emit attributeChanged(this->objectName(), "NoiseReductionType", mNoiseReductionType);
            emit attributeChanged(this->objectName(), "NoiseReduction",  QString::number(getValueNoiseReduction(mNoiseReductionType)));
        }

        // 检查降噪设置变化
        auto valueNoiseReduction = getValueNoiseReduction(mNoiseReductionType);
        if(mNoiseReductionType == "NC1" && valueNoiseReduction != mNoiseReductionNC1){
            mNoiseReductionNC1 = valueNoiseReduction;
            emit attributeChanged(this->objectName(), "NoiseReduction", QString::number(mNoiseReductionNC1));
        }
        else if(mNoiseReductionType == "NC2" && valueNoiseReduction != mNoiseReductionNC2) {
            mNoiseReductionNC2 = valueNoiseReduction;
            emit attributeChanged(this->objectName(), "NoiseReduction", QString::number(mNoiseReductionNC2));
        }
        else if(mNoiseReductionType == "NC3" && valueNoiseReduction != mNoiseReductionNC3) {
            mNoiseReductionNC3 = valueNoiseReduction;
            emit attributeChanged(this->objectName(), "NoiseReduction", QString::number(mNoiseReductionNC3));
        }
        else if(mNoiseReductionType == "Bypass" && valueNoiseReduction != mNoiseReductionBypass) {
            mNoiseReductionBypass = valueNoiseReduction;
            emit attributeChanged(this->objectName(), "NoiseReduction", QString::number(mNoiseReductionBypass));
        }

        // 检查干湿比变化
        if(mPreDryWet != static_cast<int>(WorkspaceObserver::value("DryWet").toFloat()))
        {
            mPreDryWet = static_cast<int>(WorkspaceObserver::value("DryWet").toFloat());
            emit attributeChanged(this->objectName(), "DryWet", QString::number(mPreDryWet));
        }

        // 检查混响类型变化
        if(mReverbType != WorkspaceObserver::value("ReverbType").toString())
        {
            mReverbType = WorkspaceObserver::value("ReverbType").toString();
            emit attributeChanged(this->objectName(), "ReverbType", mReverbType);
        }

        // 检查房间大小变化
        auto valueRoom = getValueReverb(mReverbType);
        if(mReverbType == "STUDIO" && valueRoom != mReverbStudio) {
            mReverbStudio = valueRoom;
            emit attributeChanged(this->objectName(), "Room", QString::number(mReverbStudio));
        }
        else if(mReverbType == "LIVE" && valueRoom != mReverbLive) {
            mReverbLive = valueRoom;
            emit attributeChanged(this->objectName(), "Room", QString::number(mReverbLive));
        }
        else if(mReverbType == "HALL" && valueRoom != mReverbHall) {
            mReverbHall = valueRoom;
            emit attributeChanged(this->objectName(), "Room", QString::number(mReverbHall));
        }
        else if(mReverbType == "Bypass" && valueRoom != mReverbBypass) {
            mReverbBypass = valueRoom;
            emit attributeChanged(this->objectName(), "Room", QString::number(mReverbBypass));
        }

        // 检查衰减时间变化
        if(mPreDecay != static_cast<int>(WorkspaceObserver::value("Decay").toFloat()))
        {
            mPreDecay = static_cast<int>(WorkspaceObserver::value("Decay").toFloat());
            emit attributeChanged(this->objectName(), "Decay", QString::number(mPreDecay));
        }

        // 检查静音状态变化
        if(mMuteFX != static_cast<int>(WorkspaceObserver::value("MuteFX").toBool()))
        {
            mMuteFX = static_cast<int>(WorkspaceObserver::value("MuteFX").toBool());
            emit attributeChanged(this->objectName(), "MuteFX", QString::number(mMuteFX));
        }

        // 检查输入值变化
        int valueInput1 = WorkspaceObserver::value("Input1Level").toInt();
        if(mValueInput1 != valueInput1) {
            mValueInput1 = valueInput1;
            emit attributeChanged(this->objectName(), "Input1Level", QString::number(mValueInput1));
        }

        int valueInput2 = WorkspaceObserver::value("Input2Level").toInt();
        if(mValueInput2 != valueInput2) {
            mValueInput2 = valueInput2;
            emit attributeChanged(this->objectName(), "Input2Level", QString::number(mValueInput2));
        }

        int valueAux = WorkspaceObserver::value("AuxLevel").toInt();
        if(mValueAux != valueAux) {
            mValueAux = valueAux;
            emit attributeChanged(this->objectName(), "AuxLevel", QString::number(mValueAux));
        }

        int valueBluetooth = WorkspaceObserver::value("BluetoothLevel").toInt();
        if(mValueBluetooth != valueBluetooth) {
            mValueBluetooth = valueBluetooth;
            emit attributeChanged(this->objectName(), "BluetoothLevel", QString::number(mValueBluetooth));
        }

        int valueOtg = WorkspaceObserver::value("OtgLevel").toInt();
        if(mValueOtg != valueOtg) {
            mValueOtg = valueOtg;
            emit attributeChanged(this->objectName(), "OtgLevel", QString::number(mValueOtg));
        }
    }
}

void M62_PrivateWidget3::loadSettings()
{
    // 重置所有预状态变量
    mReverbType = "";
    mNoiseReductionType = "";
    mNoiseReductionNC1 = -2147483648;
    mNoiseReductionNC2 = -2147483648;
    mNoiseReductionNC3 = -2147483648;
    mNoiseReductionBypass = -2147483648;
    mPreDryWet = -2147483648;  
    mReverbStudio = -2147483648;
    mReverbLive = -2147483648;
    mReverbHall = -2147483648;
    mReverbBypass = -2147483648;
    mPreDecay = -2147483648;
    mMuteFX = -2147483648;
    mValueInput1 = -2147483648;
    mValueInput2 = -2147483648;
    mValueAux = -2147483648;
    mValueBluetooth = -2147483648;
    mValueOtg = -2147483648;
    setWidgetReady(false);

    // 检查设置是否存在
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag = WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();

    // 如果设置不存在,初始化默认值 
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("NoiseReductionNC1", 15);
        WorkspaceObserver::setValue("NoiseReductionNC2", 30);
        //WorkspaceObserver::setValue("NoiseReductionNC3", 45);
        WorkspaceObserver::setValue("NoiseReductionBypass", 1);
        WorkspaceObserver::setValue("NoiseReductionType", "Bypass");
        WorkspaceObserver::setValue("DryWet", 30);
        WorkspaceObserver::setValue("ReverbStudio", 1);
        WorkspaceObserver::setValue("ReverbLive", 1);
        WorkspaceObserver::setValue("ReverbHall", 1);
        WorkspaceObserver::setValue("ReverbBypass", 1);
        WorkspaceObserver::setValue("Decay", 0);
        WorkspaceObserver::setValue("ReverbType", "Bypass");
        WorkspaceObserver::setValue("MuteFX", false);
        WorkspaceObserver::setValue("Input1Level", mDefaulDbValueIN);
        WorkspaceObserver::setValue("Input2Level", mDefaulDbValueIN);
        WorkspaceObserver::setValue("AuxLevel", mDefaulDbValue);
        WorkspaceObserver::setValue("BluetoothLevel", mDefaulDbValue);
        WorkspaceObserver::setValue("OtgLevel", mDefaulDbValue);
    }

    ui->dialDryWet->setValue(WorkspaceObserver::value("DryWet").toFloat());
    ui->dialRoom->setValue(WorkspaceObserver::value("Room").toFloat());
    ui->dialDecay->setValue(WorkspaceObserver::value("Decay").toFloat());
    ui->pushButtonMuteFx->setChecked(WorkspaceObserver::value("MuteFX").toBool());
    setMuteFxIcon(WorkspaceObserver::value("MuteFX").toBool());

    auto noiseReductionType = WorkspaceObserver::value("NoiseReductionType").toString();
    auto reverbType = WorkspaceObserver::value("ReverbType").toString();

    auto value = WorkspaceObserver::value("Input1Level").toInt();
    ui->sliderInput1->setValue(value);
    ui->labelInputLevel1->setText(getDbText(value));
    value = WorkspaceObserver::value("Input2Level").toInt();
    ui->sliderInput2->setValue(value);
    ui->labelInput2Level->setText(getDbText(value));
    value = WorkspaceObserver::value("AuxLevel").toInt();
    ui->sliderAux->setValue(value);
    ui->labelAuxLevel->setText(getDbText(value));
    value = WorkspaceObserver::value("BluetoothLevel").toInt();
    ui->sliderBluetooth->setValue(value);
    ui->labelBluetoothLevel->setText(getDbText(value));
    value = WorkspaceObserver::value("OtgLevel").toInt();
    ui->sliderOtg->setValue(value);
    ui->labelOtgLevel->setText(getDbText(value));

    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Input1Level", QString::number(WorkspaceObserver::value("Input1Level").toInt()));
        emit attributeChanged(this->objectName(), "Save_Input2Level", QString::number(WorkspaceObserver::value("Input2Level").toInt()));
        emit attributeChanged(this->objectName(), "Save_AuxLevel", QString::number(WorkspaceObserver::value("AuxLevel").toInt()));
        emit attributeChanged(this->objectName(), "Save_BluetoothLevel", QString::number(WorkspaceObserver::value("BluetoothLevel").toInt()));
        emit attributeChanged(this->objectName(), "Save_OtgLevel", QString::number(WorkspaceObserver::value("OtgLevel").toInt()));
        emit attributeChanged(this->objectName(), "Save_NoiseReduction", QString::number(getValueNoiseReduction(noiseReductionType)));
        emit attributeChanged(this->objectName(), "Save_NoiseReductionType", WorkspaceObserver::value("NoiseReductionType").toString());
        emit attributeChanged(this->objectName(), "Save_DryWet", WorkspaceObserver::value("DryWet").toString());
        emit attributeChanged(this->objectName(), "Save_Room", QString::number(getValueReverb(reverbType)));
        emit attributeChanged(this->objectName(), "Save_Decay", WorkspaceObserver::value("Decay").toString());
        emit attributeChanged(this->objectName(), "Save_ReverbType", WorkspaceObserver::value("ReverbType").toString());
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
    }

    setWidgetReady(true);
    updateAttribute();
    setValueNoiseReductionType(noiseReductionType);
    setReverbType(reverbType);
    if(reverbType == "Bypass"){
        reverbType = "STUDIO";
    }
    mReverbTypePre = reverbType;
    if(noiseReductionType == "Bypass"){
        noiseReductionType = "NC1";
    }
    mNoiseReductionTypePre = noiseReductionType;
}

void M62_PrivateWidget3::AppSettingsChanged(QString objectName, QString attribute, QString value)
{

}

QString M62_PrivateWidget3::getDbText(int value){
    if(value == -90)
    {
        return "-∞";
    }
    else if(value > 0)
    {
        return "+" + QString::number(value);
    }
    else if(value < 0)
    {
        return QString::number(value);
    }
    else if(value == 0)
    {
        return "-0";
    }
    return QString::number(value);
}
void M62_PrivateWidget3::save(const QString& key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key, value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
void M62_PrivateWidget3::in_widgetDial_valueChanged(float value)
{
    save("DryWet", value);
    updateAttribute();
}

void M62_PrivateWidget3::in_widgetDialDecay_valueChanged(float value)
{
    save("Decay", value);
    updateAttribute();
}

// 音量滑块相关的槽函数实现
void M62_PrivateWidget3::in_sliderInput1_valueChanged(int value)
{
    ui->labelInputLevel1->setText(getDbText(value));
    save("Input1Level", value);
    updateAttribute();
}

void M62_PrivateWidget3::in_sliderInput2_valueChanged(int value)
{
    ui->labelInput2Level->setText(getDbText(value));
    save("Input2Level", value);
    updateAttribute();
}

void M62_PrivateWidget3::in_sliderAux_valueChanged(int value)
{
    ui->labelAuxLevel->setText(getDbText(value));
    save("AuxLevel", value);
    updateAttribute();
}

void M62_PrivateWidget3::in_sliderBluetooth_valueChanged(int value)
{
    ui->labelBluetoothLevel->setText(getDbText(value));
    save("BluetoothLevel", value);
    updateAttribute();
}

void M62_PrivateWidget3::in_sliderOtg_valueChanged(int value)
{
    ui->labelOtgLevel->setText(getDbText(value));
    save("OtgLevel", value);
    updateAttribute();
}

bool M62_PrivateWidget3::eventFilter(QObject *watched, QEvent *event)
{
    if(watched == ui->pushButtonMuteFx){
        if(event->type()==QEvent::Resize){
            updateStyle();
        }
    }else{
        if (event->type() == QEvent::MouseButtonDblClick) {
            if (QSlider* slider = qobject_cast<QSlider*>(watched)) {
                int defaultValue = mDefaulDbValue;
                if(slider==ui->sliderInput1||slider==ui->sliderInput2){
                    defaultValue = mDefaulDbValueIN;
                }
                slider->setValue(defaultValue);
                return true;
            }
        }
    }
    return QWidget::eventFilter(watched, event);
}

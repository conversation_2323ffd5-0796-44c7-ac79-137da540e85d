#include <QDir>
#include <QDebug>
#include <QVector>
#include <QSettings>
#include <QDateTime>
#include <QStandardPaths>

#include "workspace.h"


WorkspaceSubject WorkspaceSubject::mInstance;


// ******************************************************************************
// *********************************  WorkspaceObserver
// ******************************************************************************
void WorkspaceObserver::setValue(QAnyStringView key, const QVariant& value)
{
    (*mSettings)->beginGroup(mObserverName);
    (*mSettings)->setValue(key, value);
    (*mSettings)->endGroup();
}
QVariant WorkspaceObserver::value(QAnyStringView key)
{
    QVariant value;
    (*mSettings)->beginGroup(mObserverName);
    value = (*mSettings)->value(key);
    (*mSettings)->endGroup();
    return value;
}


// ******************************************************************************
// *********************************  WorkspaceSubject
// ******************************************************************************
WorkspaceSubject::~WorkspaceSubject()
{
    if(mFilePreprocessing == onCopy && QFile::exists(mPathTemp))
    {
        QFile::remove(mPathTemp);
    }
}


WorkspaceSubject& WorkspaceSubject::addObserver(WorkspaceObserver* observer)
{
    observer->assignSettings(&mSettings);
    mObservers.push_back(observer);
    return *this;
}
WorkspaceSubject& WorkspaceSubject::addObserverList(QVector<WorkspaceObserver*> observerList)
{
    for(auto element : observerList)
    {
        element->assignSettings(&mSettings);
        mObservers.push_back(element);
    }
    return *this;
}
WorkspaceSubject& WorkspaceSubject::removeObserverOne(WorkspaceObserver* observer)
{
    mObservers.removeOne(observer);
    return *this;
}
WorkspaceSubject& WorkspaceSubject::removeObserverAll()
{
    mObservers.clear();
    return *this;
}
WorkspaceSubject& WorkspaceSubject::listObserver()
{
    if(mObservers.empty())
    {
        qInfo() << "WorkspaceSubject::listObserver //observer list is empty";
        return *this;
    }
    int count=0;
    qInfo() << "";
    qInfo() << "";
    qInfo() << "WorkspaceSubject::listObserver //observer list [" << mObservers.size() << "]";
    for(auto observer : mObservers)
    {
        count++;
        qInfo() << "\t" << count << "\t" << observer->getObserverName();
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
bool WorkspaceSubject::isValid(QString file, QString device)
{
    QSettings settings(file, QSettings::IniFormat);
    if(settings.value("ConfigFileHeader/DeviceName").toString() == device)
    {
        return true;
    }
    return false;
}
bool WorkspaceSubject::isValid(QString workspaceName)
{
    QSettings settings(mWorkspacePath + QDir::separator() + mWorkspacePrefix + workspaceName + ".ini", QSettings::IniFormat);
    if(settings.value("ConfigFileHeader/DeviceName").toString() == mDevice)
    {
        return true;
    }
    return false;
}
WorkspaceSubject& WorkspaceSubject::modifyFile(QString file, FilePreprocessing preprocessing)
{
    if(!QFile::exists(file))
    {
        qWarning() << "WorkspaceSubject::modifyFile //file does not exist";
        return *this;
    }
    if(mSettings)
    {
        mSettings->sync();
        delete mSettings;
        if(mFilePreprocessing == onCopy && QFile::exists(mPathTemp))
        {
            QFile::remove(mPathTemp);
        }
    }
    mPath = file;
    mFilePreprocessing = preprocessing;
    mPathTemp = mPath;
    if(mFilePreprocessing == onCopy)
    {
        mPathTemp = QDir::tempPath() + QDir::separator() + QFileInfo(mPath).fileName();
        if(QFile::exists(mPathTemp))
        {
            QFile::remove(mPathTemp);
        }
        if(!QFile::copy(mPath, mPathTemp))
        {
            qWarning() << "WorkspaceSubject::modifyFile //failed to copy file to temp folder";
            return *this;
        }
    }
    mSettings = new QSettings(mPathTemp, QSettings::IniFormat);
    emit attributeChanged("Workspace", "ModifyFile", mPathTemp);
    emit attributeChanged("Workspace", "LoadSettings", "1");
    for(auto observer : mObservers)
    {
        observer->loadSettings();
    }
    emit attributeChanged("Workspace", "LoadSettings", "0");
    return *this;
}
WorkspaceSubject& WorkspaceSubject::modifyFilePreprocessing(FilePreprocessing preprocessing)
{
    if(mSettings && mFilePreprocessing != preprocessing)
    {
        mSettings->sync();
        if(mFilePreprocessing == onCopy)
        {
            if(QFile::exists(mPath))
            {
                QFile::remove(mPath);
            }
            if(!QFile::copy(mPathTemp, mPath))
            {
                qWarning() << "WorkspaceSubject::modifyFilePreprocessing //failed to copy file from temp folder";
                return *this;
            }
        }
        delete mSettings;
        if(mFilePreprocessing == onCopy && QFile::exists(mPathTemp))
        {
            QFile::remove(mPathTemp);
        }
        mFilePreprocessing = preprocessing;
        mPathTemp = mPath;
        if(mFilePreprocessing == onCopy)
        {
            mPathTemp = QDir::tempPath() + QDir::separator() + QFileInfo(mPath).fileName();
            if(QFile::exists(mPathTemp))
            {
                QFile::remove(mPathTemp);
            }
            if(!QFile::copy(mPath, mPathTemp))
            {
                qWarning() << "WorkspaceSubject::modifyFilePreprocessing //failed to copy file to temp folder";
                return *this;
            }
        }
        mSettings = new QSettings(mPathTemp, QSettings::IniFormat);
    }
    return *this;
}
WorkspaceSubject& WorkspaceSubject::saveFile()
{
    if(mSettings)
    {
        mSettings->sync();
        if(mFilePreprocessing == onCopy)
        {
            if(QFile::exists(mPath))
            {
                QFile::remove(mPath);
            }
            if(!QFile::copy(mPathTemp, mPath))
            {
                qWarning() << "WorkspaceSubject::saveFile //failed to copy file from temp folder";
                return *this;
            }
        }
    }
    return *this;
}
WorkspaceSubject& WorkspaceSubject::clearFile()
{
    if(mSettings)
    {
        QString deviceName=mSettings->value("ConfigFileHeader/DeviceName").toString();
        QString createUser=mSettings->value("ConfigFileHeader/CreateUser").toString();
        QString createDate=mSettings->value("ConfigFileHeader/CreateDate").toString();
        mSettings->clear();
        mSettings->setValue("ConfigFileHeader/DeviceName", deviceName);
        mSettings->setValue("ConfigFileHeader/CreateUser", createUser);
        mSettings->setValue("ConfigFileHeader/CreateDate", createDate);
        saveFile();
    }
    return *this;
}
WorkspaceSubject& WorkspaceSubject::createFile(QString file, QString device)
{
    if(file.isEmpty())
    {
        qWarning() << "WorkspaceSubject::createFile //file is empty";
        return *this;
    }
    if(QFile::exists(file))
    {
        QFile::remove(file);
    }
    QSettings settings(file, QSettings::IniFormat);
    settings.setValue("ConfigFileHeader/DeviceName", device);
    settings.setValue("ConfigFileHeader/CreateUser", QDir(QStandardPaths::writableLocation(QStandardPaths::HomeLocation)).dirName());
    settings.setValue("ConfigFileHeader/CreateDate", QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));
    return *this;
}
WorkspaceSubject& WorkspaceSubject::removeFile(QString file)
{
    if(file.isEmpty())
    {
        qWarning() << "WorkspaceSubject::removeFile //file is empty";
        return *this;
    }
    if(QFile::exists(file))
    {
        QFile::remove(file);
    }
    return *this;
}
WorkspaceSubject& WorkspaceSubject::fileSaveAs(QString file)
{
    if(mSettings)
    {
        mSettings->sync();
        if(file.isEmpty())
        {
            qWarning() << "WorkspaceSubject::fileSaveAs //file is empty";
            return *this;
        }
        if(QFile::exists(file))
        {
            QFile::remove(file);
        }
        if(!QFile::copy(mPathTemp, file))
        {
            qWarning() << "WorkspaceSubject::fileSaveAs //failed to copy file to target folder";
            return *this;
        }
        QSettings settings(file, QSettings::IniFormat);
        settings.setValue("ConfigFileHeader/CreateUser", QDir(QStandardPaths::writableLocation(QStandardPaths::HomeLocation)).dirName());
        settings.setValue("ConfigFileHeader/CreateDate", QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));
    }
    return *this;
}
WorkspaceSubject& WorkspaceSubject::assignWorkspace(QString device, QString path, QString prefix)
{
    mDevice = device;
    mWorkspacePath = path;
    mWorkspacePrefix = prefix;
    return *this;
}
WorkspaceSubject& WorkspaceSubject::modifyWorkspace(QString workspaceName, FilePreprocessing preprocessing)
{
    if(mDevice.isEmpty() || mWorkspacePath.isEmpty() || mWorkspacePrefix.isEmpty())
    {
        qWarning() << "WorkspaceSubject::modifyWorkspace //workspace info is empty";
        return *this;
    }
    QSettings().setValue(mDevice + "/" + mWorkspacePrefix + "Workspace", workspaceName);
    modifyFile(mWorkspacePath + QDir::separator() + mWorkspacePrefix + workspaceName + ".ini", preprocessing);
    return *this;
}
WorkspaceSubject& WorkspaceSubject::modifyWorkspacePreprocessing(FilePreprocessing preprocessing)
{
    if(mDevice.isEmpty() || mWorkspacePath.isEmpty() || mWorkspacePrefix.isEmpty())
    {
        qWarning() << "WorkspaceSubject::modifyWorkspacePreprocessing //workspace info is empty";
        return *this;
    }
    modifyFilePreprocessing(preprocessing);
    return *this;
}
WorkspaceSubject& WorkspaceSubject::saveWorkspace()
{
    if(mDevice.isEmpty() || mWorkspacePath.isEmpty() || mWorkspacePrefix.isEmpty())
    {
        qWarning() << "WorkspaceSubject::saveWorkspace //workspace info is empty";
        return *this;
    }
    saveFile();
    return *this;
}
WorkspaceSubject& WorkspaceSubject::clearWorkspace()
{
    if(mDevice.isEmpty() || mWorkspacePath.isEmpty() || mWorkspacePrefix.isEmpty())
    {
        qWarning() << "WorkspaceSubject::clearWorkspace //workspace info is empty";
        return *this;
    }
    clearFile();
    return *this;
}
WorkspaceSubject& WorkspaceSubject::createWorkspace(QString workspaceName)
{
    if(mDevice.isEmpty() || mWorkspacePath.isEmpty() || mWorkspacePrefix.isEmpty())
    {
        qWarning() << "WorkspaceSubject::createWorkspace //workspace info is empty";
        return *this;
    }
    createFile(mWorkspacePath + QDir::separator() + mWorkspacePrefix + workspaceName + ".ini", mDevice);
    return *this;
}
WorkspaceSubject& WorkspaceSubject::removeWorkspace(QString workspaceName)
{
    if(mDevice.isEmpty() || mWorkspacePath.isEmpty() || mWorkspacePrefix.isEmpty())
    {
        qWarning() << "WorkspaceSubject::removeWorkspace //workspace info is empty";
        return *this;
    }
    removeFile(mWorkspacePath + QDir::separator() + mWorkspacePrefix + workspaceName + ".ini");
    return *this;
}
WorkspaceSubject& WorkspaceSubject::workspaceSaveAs(QString workspaceName)
{
    if(mDevice.isEmpty() || mWorkspacePath.isEmpty() || mWorkspacePrefix.isEmpty())
    {
        qWarning() << "WorkspaceSubject::workspaceSaveAs //workspace info is empty";
        return *this;
    }
    fileSaveAs(mWorkspacePath + QDir::separator() + mWorkspacePrefix + workspaceName + ".ini");
    return *this;
}
QVector<QString> WorkspaceSubject::getWorkspaceList()
{
    QVector<QString> workspaceList;
    QDir dir(mWorkspacePath);
    dir.setNameFilters(QStringList(mWorkspacePrefix + "*.ini"));
    dir.setFilter(QDir::Files);
    QFileInfoList fileList=dir.entryInfoList();
    std::sort(fileList.begin(), fileList.end(), [](const QFileInfo& a, const QFileInfo& b) {
        QSettings settingsA(a.filePath(), QSettings::IniFormat);
        QSettings settingsB(b.filePath(), QSettings::IniFormat);
        return QDateTime::fromString(settingsA.value("ConfigFileHeader/CreateDate").toString(), "yyyy-MM-dd HH:mm:ss") < QDateTime::fromString(settingsB.value("ConfigFileHeader/CreateDate").toString(), "yyyy-MM-dd HH:mm:ss");
    });
    for(auto fileInfo : fileList)
    {
        QString name=fileInfo.completeBaseName();
        name.remove(0, mWorkspacePrefix.length());
        workspaceList.append(name);
    }
    return workspaceList;
}


#ifndef USBAU<PERSON>OA<PERSON>_H
#define USBAUDIOAPI_H


#include <QMap>
#include <QVector>
#include <QString>


#define SERIES_PROFESSIONAL 1
#define SERIES_DECODER 2
#define USB_AUDIO_SERIES SERIES_PROFESSIONAL


#ifdef Q_OS_WIN
#include "libwn.h"
#include "tusbaudioapi.h"
#include "TUsbAudioApiDll.h"

#if (USB_AUDIO_SERIES == SERIES_PROFESSIONAL)
    #define USB_AUDIO_MIXER_SUPPORT 1
    #if (USB_AUDIO_MIXER_SUPPORT == 1)
        #include "TUsbAudioMixer.h"
        #define USB_AUDIO_LEVEL_METER_SUPPORT 1
    #endif
#elif (USB_AUDIO_SERIES == SERIES_DECODER)
    #define USB_AUDIO_MIXER_SUPPORT 0
    #if (USB_AUDIO_MIXER_SUPPORT == 1)
        #include "TUsbAudioMixer.h"
        #define USB_AUDIO_LEVEL_METER_SUPPORT 0
    #endif
#else
    #define USB_AUDIO_MIXER_SUPPORT 0
    #if (USB_AUDIO_MIXER_SUPPORT == 1)
        #include "TUsbAudioMixer.h"
        #define USB_AUDIO_LEVEL_METER_SUPPORT 0
    #endif
#endif

class USBAudioAPI
{
public:
    struct DFUStatus
    {
        TUsbAudioDfuProcState statusDfuProc;
        TUsbAudioStatus statusCompletion;
        unsigned int currentBytes;
        unsigned int totalBytes;
    };
    static USBAudioAPI& instance() { return mInstance; }
    bool init();
    // getter
    unsigned int getNumberOfAvailableDevice();
    QVector<QString> getNameOfAllDevice();
    QString getNameOfActiveDevice() { return mDeviceName; }
    QVector<unsigned int> getSampleRateOfActiveDeviceSupported();
    unsigned int getSampleRateOfActiveDevice();
    QVector<unsigned int> getBufferSizeOfActiveDeviceSupported();
    unsigned int getBufferSizeOfActiveDevice();
    bool getSafeModeOfActiveDevice();
    unsigned int getDFUBytesTotal();
    unsigned int getDFUBytesCurrent();
    unsigned int getDFUPercentageRemain();
    unsigned int getDFUPercentageComplete();
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    int getNodeGain(unsigned int matrix_X, unsigned int matrix_Y);
    unsigned int getNodeGainPercent(unsigned int matrix_X, unsigned int matrix_Y);
    double getNodeGainLinear(unsigned int matrix_X, unsigned int matrix_Y);
    double getNodeGainLog(unsigned int matrix_X, unsigned int matrix_Y);
    int getOutputGain(unsigned int matrix_X);
    unsigned int getOutputGainPercent(unsigned int matrix_X);
    double getOutputGainLinear(unsigned int matrix_X);
    double getOutputGainLog(unsigned int matrix_X);
#if (USB_AUDIO_LEVEL_METER_SUPPORT == 1)
    short getLevelMeterOfChannelOutput(unsigned int matrix_X);
    QVector<short> getLevelMeterOfChannelOutput();
    short getLevelMeterOfChannelInput(unsigned int matrix_Y);
    QVector<short> getLevelMeterOfChannelInput();
#endif
#endif
    // setter
    USBAudioAPI& setDeviceToReset();
    bool setDeviceToActiveByName(QString deviceName);
    bool setDeviceToActiveByID(unsigned int deviceVendorId, unsigned int deviceProductId);
    bool setSampleRateOfActiveDevice(unsigned int sampleRate);
    bool setBufferSizeOfActiveDevice(unsigned int bufferSize);
    bool setSafeModeOfActiveDevice(bool safeMode);
    unsigned int setDFUStart(QString path);
    unsigned int setDFUStartByName(QString path, QString deviceName);
    unsigned int setDFUStartByID(QString path, unsigned int deviceVendorId, unsigned int deviceProductId);
    bool setDFUTerminate();
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    USBAudioAPI& setNodeGain(unsigned int matrix_X, unsigned int matrix_Y, int gain);
    USBAudioAPI& setNodeGainPercent(unsigned int matrix_X, unsigned int matrix_Y, unsigned int gain);
    USBAudioAPI& setNodeGainLinear(unsigned int matrix_X, unsigned int matrix_Y, double gain);
    USBAudioAPI& setNodeGainLog(unsigned int matrix_X, unsigned int matrix_Y, double gain);
    USBAudioAPI& setOutputGain(unsigned int matrix_X, int gain);
    USBAudioAPI& setOutputGainPercent(unsigned int matrix_X, unsigned int gain);
    USBAudioAPI& setOutputGainLinear(unsigned int matrix_X, double gain);
    USBAudioAPI& setOutputGainLog(unsigned int matrix_X, double gain);
#endif
    // modify
    USBAudioAPI& modifyDevicePIDList(QVector<QPair<QString, QString>> list);
    // is
    bool isDeviceDuplicate(QVector<QString>& deviceNameList);
    bool isDeviceOnline();
    // check
    bool checkDriver();
    bool checkApiVersion();
    // show
    USBAudioAPI& showPropertiesOfAllDevice();
    USBAudioAPI& showNameOfAllDevice();
    USBAudioAPI& showSampleRateOfActiveDeviceSupported();
    USBAudioAPI& showSampleRateOfActiveDevice();
    USBAudioAPI& showBufferSizeOfActiveDeviceSupported();
    USBAudioAPI& showBufferSizeOfActiveDevice();
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    USBAudioAPI& showMaxChannelNumberOfMixer();
#endif
    // tool
    qint64 GainToPercent(qint64 gain);
    qint64 PercentToGain(qint64 percent);
    double GainToLinear(qint64 gain);
    qint64 LinearToGain(double attenuation);
    double GainToLog(qint64 gain);
    qint64 LogToGain(double dB, double minusInf=-144.49);
private:
    static USBAudioAPI mInstance;
    TUsbAudioApiDll mAPI;
    TUsbAudioHandle mDeviceHandle;
    QString mDeviceName="None";
    QVector<QPair<QString, QString>> mDevicePIDList;
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    TUsbAudioMixer* mMixer;
    QVector<QMap<int, TUsbAudioMixer::Node>> mNodeMatrix;
#endif
    static constexpr int mGainZero=0;
    static constexpr int mGainOne=(1 << 25);
    static constexpr double mGainZeroDotZero=mGainZero;
    static constexpr double mGainOneDotZero=mGainOne;
    USBAudioAPI();
    USBAudioAPI(const USBAudioAPI&) = delete;
    ~USBAudioAPI();
    USBAudioAPI& operator=(const USBAudioAPI&) = delete;
    QVector<TUsbAudioDeviceProperties> getPropertiesOfAllDevice();
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    void setMixerNodeAttachToActiveDevice();
#endif
};
#endif


#ifdef Q_OS_MACOS
#include <IOKit/IOKitLib.h>
#include <IOKit/usb/IOUSBLib.h>
#include <IOKit/IOCFPlugIn.h>
#include <CoreAudio/CoreAudio.h>
#include <CoreFoundation/CoreFoundation.h>
#include <QFileInfo>
#include <QApplication>
#include <thread>
#include "tlusbdfusdk.h"
#include "helpers.h"
#if (USB_AUDIO_SERIES == SERIES_PROFESSIONAL)
    #define USB_AUDIO_MIXER_SUPPORT 1
    #if (USB_AUDIO_MIXER_SUPPORT == 1)
        #define USB_AUDIO_LEVEL_METER_SUPPORT 1
    #endif
#elif (USB_AUDIO_SERIES == SERIES_DECODER)
    #define USB_AUDIO_MIXER_SUPPORT 0
    #if (USB_AUDIO_MIXER_SUPPORT == 1)
        #define USB_AUDIO_LEVEL_METER_SUPPORT 0
    #endif
#else
    #define USB_AUDIO_MIXER_SUPPORT 0
    #if (USB_AUDIO_MIXER_SUPPORT == 1)
        #define USB_AUDIO_LEVEL_METER_SUPPORT 0
    #endif
#endif
#define TUSBAUDIO_MAX_STRDESC_STRLEN 256


class USBAudioAPI
{
public:
    typedef struct tagTUsbAudioDeviceProperties
    {
        unsigned int usbVendorId;
        unsigned int usbProductId;
        unsigned int usbRevisionId;

        wchar_t serialNumberString[TUSBAUDIO_MAX_STRDESC_STRLEN];
        wchar_t manufacturerString[TUSBAUDIO_MAX_STRDESC_STRLEN];
        wchar_t productString[TUSBAUDIO_MAX_STRDESC_STRLEN];

        unsigned int flags;
        int audioControlInterfaceNumber;

        unsigned int reserved[63];
    } TUsbAudioDeviceProperties;

    struct  MacDFUDef
    {
        std::basic_string<T_UNICHAR> vplist;
        std::basic_string<T_UNICHAR> firmwareFile;

        std::string  deviceFilter;
        TLDfuImage   FirmwareImage;
        unsigned int deviceFlags = TLDFU_DEVICE_FLAG_USE_XMOS_DFU_EXTENSIONS;
        TLDfuDevice  device;
        TLSTATUS     upgradeCompletionStatus;
        unsigned int enumIntervalSecs = 20;
        unsigned int targetId = 0;
    }MacDFU;
    static USBAudioAPI& instance() { return mInstance; }
    bool init();
    // getterc
    unsigned int getNumberOfAvailableDevice();
    QVector<QString> getNameOfAllDevice();
    AudioObjectID getAudioObjectIDByName(const char *deviceName);
    unsigned int getDeviceVendorId(AudioObjectID deviceID);
    unsigned int getDeviceProductId(AudioObjectID deviceID);
    QString getNameOfActiveDevice() { return mDeviceName; }
    QVector<unsigned int> getSampleRateOfActiveDeviceSupported();
    unsigned int getSampleRateOfActiveDevice();
    QVector<unsigned int> getBufferSizeOfActiveDeviceSupported();
    unsigned int getBufferSizeOfActiveDevice();
    bool getSafeModeOfActiveDevice();
    bool getDFUStatus(TLDfuDevice::UpgradeStatus& status);
    unsigned int getDFUBytesTotal();
    unsigned int getDFUBytesCurrent();
    unsigned int getDFUPercentageRemain();
    unsigned int getDFUPercentageComplete();
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    int getNodeGain(unsigned int matrix_X, unsigned int matrix_Y);
    unsigned int getNodeGainPercent(unsigned int matrix_X, unsigned int matrix_Y);
    double getNodeGainLinear(unsigned int matrix_X, unsigned int matrix_Y);
    double getNodeGainLog(unsigned int matrix_X, unsigned int matrix_Y);
    int getOutputGain(unsigned int matrix_X);
    unsigned int getOutputGainPercent(unsigned int matrix_X);
    double getOutputGainLinear(unsigned int matrix_X);
    double getOutputGainLog(unsigned int matrix_X);
#if (USB_AUDIO_LEVEL_METER_SUPPORT == 1)
    short getLevelMeterOfChannelOutput(unsigned int matrix_X);
    QVector<short> getLevelMeterOfChannelOutput();
    short getLevelMeterOfChannelInput(unsigned int matrix_Y);
    QVector<short> getLevelMeterOfChannelInput();
#endif
#endif
    // setter
    USBAudioAPI& setDeviceToReset();
    bool setDeviceToActiveByName(QString deviceName);
    bool setDeviceToActiveByID(unsigned int deviceVendorId, unsigned int deviceProductId);
    bool setSampleRateOfActiveDevice(unsigned int sampleRate);
    bool setBufferSizeOfActiveDevice(unsigned int bufferSize);
    bool setSafeModeOfActiveDevice(bool safeMode);
    unsigned int setDFUStart(QString path);
    unsigned int setDFUStartByName(QString path, QString deviceName);
    unsigned int setDFUStartByID(QString path, unsigned int deviceVendorId, unsigned int deviceProductId);
    bool setDFUTerminate();
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    USBAudioAPI& setNodeGain(unsigned int matrix_X, unsigned int matrix_Y, int gain);
    USBAudioAPI& setNodeGainPercent(unsigned int matrix_X, unsigned int matrix_Y, unsigned int gain);
    USBAudioAPI& setNodeGainLinear(unsigned int matrix_X, unsigned int matrix_Y, double gain);
    USBAudioAPI& setNodeGainLog(unsigned int matrix_X, unsigned int matrix_Y, double gain);
    USBAudioAPI& setOutputGain(unsigned int matrix_X, int gain);
    USBAudioAPI& setOutputGainPercent(unsigned int matrix_X, unsigned int gain);
    USBAudioAPI& setOutputGainLinear(unsigned int matrix_X, double gain);
    USBAudioAPI& setOutputGainLog(unsigned int matrix_X, double gain);
#endif
    // modify
    USBAudioAPI& modifyDevicePIDList(QVector<QPair<QString, QString>> list);
    // is
    bool isDeviceDuplicate(QStringList& deviceNameList);
    bool isDeviceOnline();
    // check
    bool checkDriver();
    bool checkApiVersion();
    // show
    USBAudioAPI& showPropertiesOfAllDevice();
    USBAudioAPI& showNameOfAllDevice();
    USBAudioAPI& showSampleRateOfActiveDeviceSupported();
    USBAudioAPI& showSampleRateOfActiveDevice();
    USBAudioAPI& showBufferSizeOfActiveDeviceSupported();
    USBAudioAPI& showBufferSizeOfActiveDevice();
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    USBAudioAPI& showMaxChannelNumberOfMixer();
#endif
    // tool
    qint64 GainToPercent(qint64 gain);
    qint64 PercentToGain(qint64 percent);
    double GainToLinear(qint64 gain);
    qint64 LinearToGain(double attenuation);
    double GainToLog(qint64 gain);
    qint64 LogToGain(double dB, double minusInf=-144.49);
private:
    static USBAudioAPI mInstance;
    QString mDeviceName="None";
    QVector<QPair<QString, QString>> mDevicePIDList;
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    // Mixer
#endif
    static constexpr int mGainZero=0;
    static constexpr int mGainOne=(1 << 25);
    static constexpr double mGainZeroDotZero=mGainZero;
    static constexpr double mGainOneDotZero=mGainOne;
    USBAudioAPI();
    USBAudioAPI(const USBAudioAPI&) = delete;
    ~USBAudioAPI();
    USBAudioAPI& operator=(const USBAudioAPI&) = delete;
    QVector<TUsbAudioDeviceProperties> getPropertiesOfAllDevice();
    std::string getVplistByDeviceName(const QString &deviceName);
    std::string getVplistByDeviceID(unsigned int deviceVendorId, unsigned int deviceProductId);
    unsigned int setDFUStartByVplist(const std::string& vplist, const std::string& path);
#if (USB_AUDIO_MIXER_SUPPORT == 1)
    void setMixerNodeAttachToActiveDevice();
#endif
};
#endif


#define USBAHandle USBAudioAPI::instance()


#endif // USBAUDIOAPI_H


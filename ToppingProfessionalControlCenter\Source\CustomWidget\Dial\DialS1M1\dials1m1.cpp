#include "dials1m1.h"
#include "globalfont.h"


DialS1M1::DialS1M1(QWidget* parent)
    : QWidget(parent)
{
    setFocusPolicy(Qt::StrongFocus);
}
DialS1M1::~DialS1M1()
{

}


// override
void DialS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int diameter = qMin(rect().width(), rect().height());
    mPenWidth=diameter / 20 + 1;
    mRectDial.setX(rect().x() + mPenWidth);
    mRectDial.setY(rect().y() + mPenWidth);
    if(diameter == rect().width())
    {
        mRectDial.setY(rect().y() + (rect().height() - diameter) / 2 + mPenWidth);
    }
    if(diameter == rect().height())
    {
        mRectDial.setX(rect().x() + (rect().width() - diameter) / 2 + mPenWidth);
    }
    mRectDial.setWidth(diameter - 2 * mPenWidth);
    mRectDial.setHeight(diameter - 2 * mPenWidth);
}
void DialS1M1::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void DialS1M1::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(mRectDial.contains(e->pos()))
    {
        if(mValue != mValueDefault)
        {
            mValue = mValueDefault;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M1::mousePressEvent(QMouseEvent* e)
{
    if(mMouseEnabled && mRectDial.contains(e->pos()))
    {
        mPressed = true;
        mPressedValue = mValue;
        mPressedPoint = e->pos();
    }
}
void DialS1M1::mouseMoveEvent(QMouseEvent* e)
{
    float value=0;
    if(mPressed)
    {
        value = mPressedValue;
        value += ((int) (mPressedPoint.y() - e->pos().y()) / mSensitivity) * mValueStep;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M1::mouseReleaseEvent(QMouseEvent* e)
{
    Q_UNUSED(e);
    mPressed = false;
}
void DialS1M1::wheelEvent(QWheelEvent* e)
{
    float value=mValue;
    value += e->angleDelta().y() / 120;
    value = qMax(value, mValueMin);
    value = qMin(value, mValueMax);
    if(mValue != value)
    {
        mValue = value;
        emit valueChanged(mValue);
    }
    update();
}
void DialS1M1::keyPressEvent(QKeyEvent* e)
{
    float value=mValue;
    if(e->key() == Qt::Key_Up || e->key() == Qt::Key_Right)
    {
        value += 1;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
    else if(e->key() == Qt::Key_Down || e->key() == Qt::Key_Left)
    {
        value -= 1;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
}
void DialS1M1::drawBG(QPainter* painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(mColorBG));
    painter->drawRect(rect());
    painter->restore();
}
void DialS1M1::drawElement(QPainter* painter)
{
    painter->save();
    // draw BG
    QPen pen=painter->pen();
    pen.setWidth(mPenWidth);
    pen.setColor(mColorCircleBG);
    painter->setPen(pen);
    painter->setBrush(QBrush(mColorDial));
    painter->drawEllipse(mRectDial);
    // draw Indicator
    // arrow
    QPolygon pts;
    pts.setPoints(3, -mRectDial.width() / 13, mRectDial.height() / 7 * 2, mRectDial.width() / 13, mRectDial.height() / 7 * 2, 0, mRectDial.height() / 5 * 2);
    painter->translate(mRectDial.x() + mRectDial.width() / 2, mRectDial.y() + mRectDial.height() / 2);
    painter->rotate(45);
    qreal degRotate = 270.0 / (mValueMax - mValueMin) * (mValue - mValueMin);
    painter->rotate(degRotate);
    if(mValueShowArrow)
    {
        pen.setWidth(1);
        pen.setColor(mColorHandle);
        painter->setPen(pen);
        painter->setBrush(mColorHandle);
        painter->drawConvexPolygon(pts);
    }
    // circle
    if(mValueShowCircle)
    {
        painter->resetTransform();
        pen.setWidth(mPenWidth);
        pen.setColor(mColorCircleValue);
        painter->setPen(pen);
        painter->setBrush(Qt::NoBrush);
        mDoublePercent ? (painter->drawArc(mRectDial, 90 * 16, (135 - degRotate) * 16)) : (painter->drawArc(mRectDial, 225 * 16, -degRotate * 16));
    }
    // draw Text
    if(mValueShowText)
    {
        painter->resetTransform();
        QRect rectText(mRectDial.x() + mRectDial.width() / 4, mRectDial.y() + mRectDial.height() / 4, mRectDial.width() / 2, mRectDial.height() / 2);
            // get suitable font
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "-88", rectText.width()) - 3);
        if(mFont.pointSize() < 4)
        {
            mFont.setPointSize(mFont.pointSize() + 2);
        }
        painter->setFont(mFont);
        painter->setPen(mColorText);
            // Text
        QString str;
        if(mValueShowSign)
        {
            if(mValue > 0)
            {
                str = QString("+%1").arg((double) mValue, 0, 'f', mPrecision);
            }
            else
            {
                str = QString("%1").arg((double) mValue, 0, 'f', mPrecision);
            }
        }
        else
        {
            str = QString("%1").arg((double) mValue, 0, 'f', mPrecision);
        }
        if(str.toFloat() == mValueMin)
        {
            str = mShowInfinitesimal ? ("-∞") : (str);
        }
        else if(str.toFloat() == mValueMax)
        {
            str = mShowInfinity ? ("+∞") : (str);
        }
        painter->drawText(rectText, Qt::AlignCenter, str);
    }
    painter->restore();
}


// setter & getter
DialS1M1& DialS1M1::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
DialS1M1& DialS1M1::setValue(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValue = value;
    update();
    return *this;
}
DialS1M1& DialS1M1::setDefault(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValueDefault = value;
    return *this;
}
DialS1M1& DialS1M1::setRange(float min, float max)
{
    if(min > max)
    {
        return *this;
    }
    mValueMin = min;
    mValueMax = max;
    if(mValueMax < mValueDefault || mValueDefault < mValueMin)
    {
        mValueDefault = mValueMin;
    }
    update();
    return *this;
}
DialS1M1& DialS1M1::setStep(float step)
{
    if(step > mValueMax - mValueMin || step <= 0)
    {
        return *this;
    }
    mValueStep = step;
    return *this;
}
DialS1M1& DialS1M1::setPrecision(int precision)
{
    if(precision > 3 || precision < 0)
    {
        return *this;
    }
    mPrecision = precision;
    update();
    return *this;
}
DialS1M1& DialS1M1::setSensitivity(int sensitivity)
{
    if(sensitivity > 100 || sensitivity <= 0)
    {
        return *this;
    }
    mSensitivity = sensitivity;
    return *this;
}
DialS1M1& DialS1M1::setMovable(bool status)
{
    mMouseEnabled = status;
    return *this;
}
DialS1M1& DialS1M1::setDoublePercent(bool status)
{
    mDoublePercent = status;
    update();
    return *this;
}
DialS1M1& DialS1M1::setColorBG(QColor color)
{
    mColorBG = color;
    update();
    return *this;
}
DialS1M1& DialS1M1::setColorDial(QColor color)
{
    mColorDial = color;
    update();
    return *this;
}
DialS1M1& DialS1M1::setColorCircleBG(QColor color)
{
    mColorCircleBG = color;
    update();
    return *this;
}
DialS1M1& DialS1M1::setColorCircleValue(QColor color)
{
    mColorCircleValue = color;
    update();
    return *this;
}
DialS1M1& DialS1M1::setColorHandle(QColor color)
{
    mColorHandle = color;
    update();
    return *this;
}
DialS1M1& DialS1M1::setColorText(QColor color)
{
    mColorText = color;
    update();
    return *this;
}
DialS1M1& DialS1M1::showArrow(bool status)
{
    mValueShowArrow = status;
    update();
    return *this;
}
DialS1M1& DialS1M1::showCircle(bool status)
{
    mValueShowCircle = status;
    update();
    return *this;
}
DialS1M1& DialS1M1::showText(bool status)
{
    mValueShowText = status;
    update();
    return *this;
}
DialS1M1& DialS1M1::showSign(bool status)
{
    mValueShowSign = status;
    update();
    return *this;
}
DialS1M1& DialS1M1::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
DialS1M1& DialS1M1::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}


#ifndef UPDATERFIRMWAREM1_H
#define UPDATERFIRMWAREM1_H

#include <QElapsedTimer>
#include "updaterbase.h"

class UpdaterFirmwareM1 : public UpdaterBase
{
    Q_OBJECT
    friend class UpdaterFactory;

protected:
    void doUpdate(const QString& filePath) override;
    
private:
    QElapsedTimer mTimer;
    unsigned int mFinished=0;
    explicit UpdaterFirmwareM1(QObject* parent = nullptr);
    ~UpdaterFirmwareM1();
    UpdaterFirmwareM1(const UpdaterFirmwareM1&) = delete;
    UpdaterFirmwareM1& operator=(const UpdaterFirmwareM1&) = delete;
    static UpdaterBase* instance();
};

#endif // UPDATERFIRMWAREM1_H


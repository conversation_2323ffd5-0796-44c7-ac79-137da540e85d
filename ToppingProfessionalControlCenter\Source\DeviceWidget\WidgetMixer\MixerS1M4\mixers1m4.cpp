#include "mixers1m4.h"
#include "globalfont.h"
#include "usbaudioapi.h"
#include "ui_mixers1m4.h"


MixerS1M4::MixerS1M4(QWidget* parent, QString name)
    : MixerBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::MixerS1M4)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 5px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(67, 207, 124);"
            "}";
    ui->pushButtonLink->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetLinkedMeterLeft->setWidthRatio(44, 6, 20, 16, 14);
    ui->widgetLinkedMeterRight->setWidthRatio(44, 6, 20, 16, 14);
    ui->widgetUnlinkMeter->setWidthRatio(9, 6, 12, 9, 28);
    ui->widgetLinkedMeterLeft->setScaleLineHidden(true);
    ui->widgetLinkedMeterRight->setScaleLineHidden(true);
    ui->widgetUnlinkMeter->setScaleLineHidden(true);
    ui->widgetDialLeft->setDoublePercent().showText(false).setRange(0, 100).setDefault(50);
    ui->widgetDialRight->setDoublePercent().showText(false).setRange(0, 100).setDefault(50);
    ui->widgetLinkedVSlider->setRange(-90, 12).setDefault(0).setHeightRatio(8, 4, 88).showInfinitesimal(true);
    ui->widgetUnlinkVSliderLeft->setRange(-90, 12).setDefault(0).setHeightRatio(8, 4, 88).showInfinitesimal(true);
    ui->widgetUnlinkVSliderRight->setRange(-90, 12).setDefault(0).setHeightRatio(8, 4, 88).showInfinitesimal(true);
    ui->widgetLinkedMeterLeft->setHidden(true);
    ui->widgetLinkedVSlider->setHidden(true);
    ui->widgetLinkedMeterRight->setHidden(true);
    ui->widgetPushButtonGroup1->setHidden(true);
    ui->widgetUnlinkVSliderLeft->setHidden(true);
    ui->widgetUnlinkMeter->setHidden(true);
    ui->widgetUnlinkVSliderRight->setHidden(true);
    ui->widgetPushButtonGroup2->setHidden(true);
    ui->widgetPushButtonGroup3->setHidden(true);
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetDialLeft, SIGNAL(valueChanged(float)), this, SLOT(in_widgetDialLeft_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetDialRight, SIGNAL(valueChanged(float)), this, SLOT(in_widgetDialRight_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetLinkedVSlider, SIGNAL(valueChanged(int)), this, SLOT(in_widgetLinkedVSlider_valueChanged(int)), Qt::UniqueConnection);
    connect(ui->widgetUnlinkVSliderLeft, SIGNAL(valueChanged(int)), this, SLOT(in_widgetUnlinkVSliderLeft_valueChanged(int)), Qt::UniqueConnection);
    connect(ui->widgetUnlinkVSliderRight, SIGNAL(valueChanged(int)), this, SLOT(in_widgetUnlinkVSliderRight_valueChanged(int)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup1, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup1_stateChanged(QString, QString)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup2, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup2_stateChanged(QString, QString)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup3, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup3_stateChanged(QString, QString)), Qt::UniqueConnection);
}
MixerS1M4::~MixerS1M4()
{
    delete ui;
}


// override
bool MixerS1M4::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void MixerS1M4::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * 10.5;
    int wUnlinkSliderLeft=wPixelPerRatio * 18;
    int wSpace2=wPixelPerRatio * 0;
    int wUnlinkMeter=wPixelPerRatio * 42;
    int wSpace3=wPixelPerRatio * 0;
    int wUnlinkSliderRight=wPixelPerRatio * 18;
    int xUnlinkSliderLeft=wSpace1 + (size().width() - wSpace1 - wUnlinkSliderLeft - wSpace2 - wUnlinkMeter - wSpace3 - wUnlinkSliderRight - wSpace1) / 2;
    int xUnlinkMeter=xUnlinkSliderLeft + wUnlinkSliderLeft + wSpace2;
    int xUnlinkSliderRight=xUnlinkMeter + wUnlinkMeter + wSpace3;
    int wSpace4=wPixelPerRatio * 14;
    int wLinkedMeterLeft=wPixelPerRatio * 25;
    int wSpace5=wPixelPerRatio * 2;
    int wLinkedSlider=wPixelPerRatio * 18;
    int wSpace6=wPixelPerRatio * 2;
    int wLinkedMeterRight=wPixelPerRatio * 25;
    int xLinkedMeterLeft=wSpace4 + (size().width() - wSpace4 - wLinkedMeterLeft - wSpace5 - wLinkedSlider - wSpace6 - wLinkedMeterRight - wSpace4) / 2;
    int xLinkedSlider=xLinkedMeterLeft + wLinkedMeterLeft + wSpace5;
    int xLinkedMeterRight=xLinkedSlider + wLinkedSlider + wSpace6;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 0;
    int hPushButtonLink=hPixelPerRatio * 3.5;
    int hSpace2=hPixelPerRatio * 0;
    int hDial=hPixelPerRatio * 12;
    int hSpace3=hPixelPerRatio * 2;
    int hMeter=hPixelPerRatio * 50;
    int hSpace4=hPixelPerRatio * 1;
    int hButtonGroup=hPixelPerRatio * 20;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->pushButtonLink->setGeometry(size().width() / 2 - hPushButtonLink * 1.2, hLineEdit + hSpace1, hPushButtonLink * 2.4, hPushButtonLink * 1.3);
    ui->widgetDialLeft->setGeometry(0 + wPixelPerRatio * 3, hLineEdit + hSpace1 + hPushButtonLink + hSpace2, size().width() / 2, hDial);
    ui->widgetDialRight->setGeometry(size().width() / 2 - wPixelPerRatio * 3, hLineEdit + hSpace1 + hPushButtonLink + hSpace2, size().width() / 2, hDial);
    ui->widgetUnlinkVSliderLeft->setGeometry(xUnlinkSliderLeft, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hDial + hSpace3 - hPixelPerRatio * 3.5, wUnlinkSliderLeft, hMeter + hPixelPerRatio * 3.4);
    ui->widgetUnlinkMeter->setGeometry(xUnlinkMeter, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hDial + hSpace3, wUnlinkMeter, hMeter);
    ui->widgetUnlinkVSliderRight->setGeometry(xUnlinkSliderRight, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hDial + hSpace3 - hPixelPerRatio * 3.5, wUnlinkSliderRight, hMeter + hPixelPerRatio * 3.4);
    ui->widgetLinkedMeterLeft->setGeometry(xLinkedMeterLeft, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hDial + hSpace3, wLinkedMeterLeft, hMeter);
    ui->widgetLinkedVSlider->setGeometry(xLinkedSlider, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hDial + hSpace3 - hPixelPerRatio * 3.4, wLinkedSlider, hMeter + hPixelPerRatio * 3.4);
    ui->widgetLinkedMeterRight->setGeometry(xLinkedMeterRight, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hDial + hSpace3, wLinkedMeterRight, hMeter);
    ui->widgetPushButtonGroup1->setHidden(false);
    ui->widgetPushButtonGroup2->setHidden(false);
    ui->widgetPushButtonGroup3->setHidden(false);
    ui->widgetPushButtonGroup1->setGeometry(wPixelPerRatio * 20.1, hPixelPerRatio * 75.5, size().width() - wPixelPerRatio * 40.2, (size().width() - wPixelPerRatio * 40.2) / ui->widgetPushButtonGroup1->minimumWidth() * ui->widgetPushButtonGroup1->minimumHeight());
    ui->widgetPushButtonGroup2->setGeometry(wPixelPerRatio * 10.5, hPixelPerRatio * 75.5, size().width() - wPixelPerRatio * 60.2, (size().width() - wPixelPerRatio * 60.2) / ui->widgetPushButtonGroup2->minimumWidth() * ui->widgetPushButtonGroup2->minimumHeight());
    ui->widgetPushButtonGroup3->setGeometry(wPixelPerRatio * 10.5 + ui->widgetPushButtonGroup2->width(), hPixelPerRatio * 75.5, size().width() - wPixelPerRatio * 60.2, (size().width() - wPixelPerRatio * 60.2) / ui->widgetPushButtonGroup3->minimumWidth() * ui->widgetPushButtonGroup3->minimumHeight());
    if(getLinkState())
    {
        ui->widgetPushButtonGroup2->setHidden(true);
        ui->widgetPushButtonGroup3->setHidden(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setHidden(true);
    }
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.04;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->pushButtonLink->height()));
    ui->pushButtonLink->setFont(mFont);
}
void MixerS1M4::handleFieldMixerChanged(QString mixer)
{
    QString style;
    setWidgetReady(false);
    if(WorkspaceObserver::value(mixer + "_Link").toBool())
    {
        ui->widgetLinkedVSlider->setHidden(false);
        ui->widgetLinkedMeterLeft->setHidden(false);
        ui->widgetLinkedMeterRight->setHidden(false);
        ui->widgetPushButtonGroup1->setHidden(false);
        ui->widgetUnlinkVSliderLeft->setHidden(true);
        ui->widgetUnlinkVSliderRight->setHidden(true);
        ui->widgetUnlinkMeter->setHidden(true);
        ui->widgetPushButtonGroup2->setHidden(true);
        ui->widgetPushButtonGroup3->setHidden(true);
        ui->widgetDialLeft->setDefault(0);
        ui->widgetDialLeft->setValue(WorkspaceObserver::value(mixer + "_BalanceLinkedLeft").toFloat());
        ui->widgetDialRight->setDefault(100);
        ui->widgetDialRight->setValue(WorkspaceObserver::value(mixer + "_BalanceLinkedRight").toFloat());
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
    }
    else
    {
        ui->widgetLinkedVSlider->setHidden(true);
        ui->widgetLinkedMeterLeft->setHidden(true);
        ui->widgetLinkedMeterRight->setHidden(true);
        ui->widgetPushButtonGroup1->setHidden(true);
        ui->widgetUnlinkVSliderLeft->setHidden(false);
        ui->widgetUnlinkVSliderRight->setHidden(false);
        ui->widgetUnlinkMeter->setHidden(false);
        ui->widgetPushButtonGroup2->setHidden(false);
        ui->widgetPushButtonGroup3->setHidden(false);
        ui->widgetDialLeft->setDefault(50);
        ui->widgetDialLeft->setValue(WorkspaceObserver::value(mixer + "_BalanceUnlinkLeft").toFloat());
        ui->widgetDialRight->setDefault(50);
        ui->widgetDialRight->setValue(WorkspaceObserver::value(mixer + "_BalanceUnlinkRight").toFloat());
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(60, 60, 60);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
    }
    ui->widgetPushButtonGroup1->setState("ANTI", QString::number(WorkspaceObserver::value(mixer + "ANTI").toBool()), false);
    ui->widgetPushButtonGroup2->setState("ANTI", QString::number(WorkspaceObserver::value(mixer + "_ANTILeft").toBool()), false);
    ui->widgetPushButtonGroup3->setState("ANTI", QString::number(WorkspaceObserver::value(mixer + "_ANTIRight").toBool()), false);
    ui->widgetLinkedVSlider->setValue(WorkspaceObserver::value(mixer + "_GAIN").toInt());
    ui->widgetUnlinkVSliderLeft->setValue(WorkspaceObserver::value(mixer + "_GAINLeft").toInt());
    ui->widgetUnlinkVSliderRight->setValue(WorkspaceObserver::value(mixer + "_GAINRight").toInt());
    // must load SOLO & MUTE at the end
    loadSoloMuteState(WorkspaceObserver::value(mixer + "_Link").toBool(),
                      WorkspaceObserver::value(mixer + "_SOLO").toBool(),
                      WorkspaceObserver::value(mixer + "_SOLOLeft").toBool(),
                      WorkspaceObserver::value(mixer + "_SOLORight").toBool(),
                      WorkspaceObserver::value(mixer + "_MUTE").toBool(),
                      WorkspaceObserver::value(mixer + "_MUTELeft").toBool(),
                      WorkspaceObserver::value(mixer + "_MUTERight").toBool()
                      );
    setWidgetReady(true);
    mPreGainMixerLeftChannelLeft = -2147483648;
    mPreGainMixerLeftChannelRight = -2147483648;
    mPreGainMixerRightChannelLeft = -2147483648;
    mPreGainMixerRightChannelRight = -2147483648;
}
void MixerS1M4::updateAttribute()
{
    if(isWidgetReady())
    {
        if(isWidgetEnable())
        {
            int gainMixerLeftChannelLeft=0, gainMixerLeftChannelRight=0, gainMixerRightChannelLeft=0, gainMixerRightChannelRight=0;
            if(getLinkState())
            {
                ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (gainMixerLeftChannelLeft = 0) : (gainMixerLeftChannelLeft = ((100 - ui->widgetDialLeft->getValue()) * USBAHandle.LogToGain(ui->widgetLinkedVSlider->getValue(), -90) / 100));
                ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (gainMixerLeftChannelRight = 0) : (gainMixerLeftChannelRight = ((100 - ui->widgetDialRight->getValue()) * USBAHandle.LogToGain(ui->widgetLinkedVSlider->getValue(), -90) / 100));
                ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (gainMixerRightChannelLeft = 0) : (gainMixerRightChannelLeft = (ui->widgetDialLeft->getValue() * USBAHandle.LogToGain(ui->widgetLinkedVSlider->getValue(), -90) / 100));
                ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (gainMixerRightChannelRight = 0) : (gainMixerRightChannelRight = (ui->widgetDialRight->getValue() * USBAHandle.LogToGain(ui->widgetLinkedVSlider->getValue(), -90) / 100));
                if(ui->widgetPushButtonGroup1->getState("ANTI").toInt())
                {
                    gainMixerLeftChannelLeft = gainMixerLeftChannelLeft * -1;
                    gainMixerLeftChannelRight = gainMixerLeftChannelRight * -1;
                    gainMixerRightChannelLeft = gainMixerRightChannelLeft * -1;
                    gainMixerRightChannelRight = gainMixerRightChannelRight * -1;
                }
            }
            else
            {
                ui->widgetPushButtonGroup2->getState("MUTE").toInt() ? (gainMixerLeftChannelLeft = 0) : (gainMixerLeftChannelLeft = ((100 - ui->widgetDialLeft->getValue()) * USBAHandle.LogToGain(ui->widgetUnlinkVSliderLeft->getValue(), -90) / 100));
                ui->widgetPushButtonGroup3->getState("MUTE").toInt() ? (gainMixerLeftChannelRight = 0) : (gainMixerLeftChannelRight = ((100 - ui->widgetDialRight->getValue()) * USBAHandle.LogToGain(ui->widgetUnlinkVSliderRight->getValue(), -90) / 100));
                ui->widgetPushButtonGroup2->getState("MUTE").toInt() ? (gainMixerRightChannelLeft = 0) : (gainMixerRightChannelLeft = (ui->widgetDialLeft->getValue() * USBAHandle.LogToGain(ui->widgetUnlinkVSliderLeft->getValue(), -90) / 100));
                ui->widgetPushButtonGroup3->getState("MUTE").toInt() ? (gainMixerRightChannelRight = 0) : (gainMixerRightChannelRight = (ui->widgetDialRight->getValue() * USBAHandle.LogToGain(ui->widgetUnlinkVSliderRight->getValue(), -90) / 100));
                if(ui->widgetPushButtonGroup2->getState("ANTI").toInt())
                {
                    gainMixerLeftChannelLeft = gainMixerLeftChannelLeft * -1;
                    gainMixerRightChannelLeft = gainMixerRightChannelLeft * -1;
                }
                if(ui->widgetPushButtonGroup3->getState("ANTI").toInt())
                {
                    gainMixerLeftChannelRight = gainMixerLeftChannelRight * -1;
                    gainMixerRightChannelRight = gainMixerRightChannelRight * -1;
                }
            }
            if(mPreGainMixerLeftChannelLeft != gainMixerLeftChannelLeft)
            {
                mPreGainMixerLeftChannelLeft = gainMixerLeftChannelLeft;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMLCL", QString::number(mPreGainMixerLeftChannelLeft));
            }
            if(mPreGainMixerLeftChannelRight != gainMixerLeftChannelRight)
            {
                mPreGainMixerLeftChannelRight = gainMixerLeftChannelRight;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMLCR", QString::number(mPreGainMixerLeftChannelRight));
            }
            if(mPreGainMixerRightChannelLeft != gainMixerRightChannelLeft)
            {
                mPreGainMixerRightChannelLeft = gainMixerRightChannelLeft;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMRCL", QString::number(mPreGainMixerRightChannelLeft));
            }
            if(mPreGainMixerRightChannelRight != gainMixerRightChannelRight)
            {
                mPreGainMixerRightChannelRight = gainMixerRightChannelRight;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMRCR", QString::number(mPreGainMixerRightChannelRight));
            }
        }
        else
        {
            if(mPreGainMixerLeftChannelLeft != 0)
            {
                mPreGainMixerLeftChannelLeft = 0;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMLCL", QString::number(mPreGainMixerLeftChannelLeft));
            }
            if(mPreGainMixerLeftChannelRight != 0)
            {
                mPreGainMixerLeftChannelRight = 0;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMLCR", QString::number(mPreGainMixerLeftChannelRight));
            }
            if(mPreGainMixerRightChannelLeft != 0)
            {
                mPreGainMixerRightChannelLeft = 0;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMRCL", QString::number(mPreGainMixerRightChannelLeft));
            }
            if(mPreGainMixerRightChannelRight != 0)
            {
                mPreGainMixerRightChannelRight = 0;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMRCR", QString::number(mPreGainMixerRightChannelRight));
            }
        }
    }
}
void MixerS1M4::setSoloState(bool state)
{
    ui->widgetPushButtonGroup1->setState("SOLO", QString::number(state), false);
}
void MixerS1M4::setSoloStateLeft(bool state)
{
    ui->widgetPushButtonGroup2->setState("SOLO", QString::number(state), false);
}
void MixerS1M4::setSoloStateRight(bool state)
{
    ui->widgetPushButtonGroup3->setState("SOLO", QString::number(state), false);
}
void MixerS1M4::setMuteState(bool state)
{
    ui->widgetPushButtonGroup1->setState("MUTE", QString::number(state), false);
}
void MixerS1M4::setMuteStateLeft(bool state)
{
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(state), false);
}
void MixerS1M4::setMuteStateRight(bool state)
{
    ui->widgetPushButtonGroup3->setState("MUTE", QString::number(state), false);
}
void MixerS1M4::setSoloClicked(bool state)
{
    ui->widgetPushButtonGroup1->setState("SOLO", QString::number(state), true);
}
void MixerS1M4::setSoloClickedLeft(bool state)
{
    ui->widgetPushButtonGroup2->setState("SOLO", QString::number(state), true);
}
void MixerS1M4::setSoloClickedRight(bool state)
{
    ui->widgetPushButtonGroup3->setState("SOLO", QString::number(state), true);
}
void MixerS1M4::setMuteClicked(bool state)
{
    ui->widgetPushButtonGroup1->setState("MUTE", QString::number(state), true);
}
void MixerS1M4::setMuteClickedLeft(bool state)
{
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(state), true);
}
void MixerS1M4::setMuteClickedRight(bool state)
{
    ui->widgetPushButtonGroup3->setState("MUTE", QString::number(state), true);
}
bool MixerS1M4::getSoloState()
{
    return (bool) ui->widgetPushButtonGroup1->getState("SOLO").toInt();
}
bool MixerS1M4::getSoloStateLeft()
{
    return (bool) ui->widgetPushButtonGroup2->getState("SOLO").toInt();
}
bool MixerS1M4::getSoloStateRight()
{
    return (bool) ui->widgetPushButtonGroup3->getState("SOLO").toInt();
}
bool MixerS1M4::getMuteState()
{
    return (bool) ui->widgetPushButtonGroup1->getState("MUTE").toInt();
}
bool MixerS1M4::getMuteStateLeft()
{
    return (bool) ui->widgetPushButtonGroup2->getState("MUTE").toInt();
}
bool MixerS1M4::getMuteStateRight()
{
    return (bool) ui->widgetPushButtonGroup3->getState("MUTE").toInt();
}
void MixerS1M4::loadSettings()
{
    mPreGainMixerLeftChannelLeft = -2147483648;
    mPreGainMixerLeftChannelRight = -2147483648;
    mPreGainMixerRightChannelLeft = -2147483648;
    mPreGainMixerRightChannelRight = -2147483648;
    setWidgetReady(false);
    QString mixer=getMixer();
    QVector<QString> mixerList=getMixerList();
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        for(auto element : mixerList)
        {
            WorkspaceObserver::setValue(element + "_Link", mLinkDefaultState);
            WorkspaceObserver::setValue(element + "_BalanceLinkedLeft", 0);
            WorkspaceObserver::setValue(element + "_BalanceLinkedRight", 100);
            WorkspaceObserver::setValue(element + "_BalanceUnlinkLeft", 50);
            WorkspaceObserver::setValue(element + "_BalanceUnlinkRight", 50);
            WorkspaceObserver::setValue(element + "_ANTI", false);
            WorkspaceObserver::setValue(element + "_ANTILeft", false);
            WorkspaceObserver::setValue(element + "_ANTIRight", false);
            WorkspaceObserver::setValue(element + "_GAIN", 0);
            WorkspaceObserver::setValue(element + "_GAINLeft", 0);
            WorkspaceObserver::setValue(element + "_GAINRight", 0);
            WorkspaceObserver::setValue(element + "_SOLO", false);
            WorkspaceObserver::setValue(element + "_SOLOLeft", false);
            WorkspaceObserver::setValue(element + "_SOLORight", false);
            WorkspaceObserver::setValue(element + "_MUTE", false);
            WorkspaceObserver::setValue(element + "_MUTELeft", false);
            WorkspaceObserver::setValue(element + "_MUTERight", false);
        }
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    for(auto element : mixerList)
    {
        if(element == mixer)
        {
            continue;
        }
        int gainMixerLeftChannelLeft=0, gainMixerLeftChannelRight=0, gainMixerRightChannelLeft=0, gainMixerRightChannelRight=0, visible=0;
        if(getOriginVisibleList(element).contains(getChannelName()))
        {
            visible = 1;
            if(WorkspaceObserver::value(element + "_Link").toBool())
            {
                bool muteState=false;
                if(getOriginSoloState(element))
                {
                    muteState = !WorkspaceObserver::value(element + "_SOLO").toBool();
                }
                else
                {
                    muteState = WorkspaceObserver::value(element + "_MUTE").toBool();
                }
                muteState ? (gainMixerLeftChannelLeft = 0) : (gainMixerLeftChannelLeft = ((100 - WorkspaceObserver::value(element + "_BalanceLinkedLeft").toFloat()) * USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) / 100));
                muteState ? (gainMixerLeftChannelRight = 0) : (gainMixerLeftChannelRight = ((100 - WorkspaceObserver::value(element + "_BalanceLinkedRight").toFloat()) * USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) / 100));
                muteState ? (gainMixerRightChannelLeft = 0) : (gainMixerRightChannelLeft = (WorkspaceObserver::value(element + "_BalanceLinkedLeft").toFloat() * USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) / 100));
                muteState ? (gainMixerRightChannelRight = 0) : (gainMixerRightChannelRight = (WorkspaceObserver::value(element + "_BalanceLinkedRight").toFloat() * USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) / 100));
                if(WorkspaceObserver::value(element + "_ANTI").toBool())
                {
                    gainMixerLeftChannelLeft = gainMixerLeftChannelLeft * -1;
                    gainMixerLeftChannelRight = gainMixerLeftChannelRight * -1;
                    gainMixerRightChannelLeft = gainMixerRightChannelLeft * -1;
                    gainMixerRightChannelRight = gainMixerRightChannelRight * -1;
                }
            }
            else
            {
                bool muteStateLeft=false;
                bool muteStateRight=false;
                if(getOriginSoloState(element))
                {
                    muteStateLeft = !WorkspaceObserver::value(element + "_SOLOLeft").toBool();
                    muteStateRight = !WorkspaceObserver::value(element + "_SOLORight").toBool();
                }
                else
                {
                    muteStateLeft = WorkspaceObserver::value(element + "_MUTELeft").toBool();
                    muteStateRight = WorkspaceObserver::value(element + "_MUTERight").toBool();
                }
                muteStateLeft ? (gainMixerLeftChannelLeft = 0) : (gainMixerLeftChannelLeft = ((100 - WorkspaceObserver::value(element + "_BalanceUnlinkLeft").toFloat()) * USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAINLeft").toInt(), -90) / 100));
                muteStateRight ? (gainMixerLeftChannelRight = 0) : (gainMixerLeftChannelRight = ((100 - WorkspaceObserver::value(element + "_BalanceUnlinkRight").toFloat()) * USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAINRight").toInt(), -90) / 100));
                muteStateLeft ? (gainMixerRightChannelLeft = 0) : (gainMixerRightChannelLeft = (WorkspaceObserver::value(element + "_BalanceUnlinkLeft").toFloat() * USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAINLeft").toInt(), -90) / 100));
                muteStateRight ? (gainMixerRightChannelRight = 0) : (gainMixerRightChannelRight = (WorkspaceObserver::value(element + "_BalanceUnlinkRight").toFloat() * USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAINRight").toInt(), -90) / 100));
                if(WorkspaceObserver::value(element + "_ANTILeft").toBool())
                {
                    gainMixerLeftChannelLeft = gainMixerLeftChannelLeft * -1;
                    gainMixerRightChannelLeft = gainMixerRightChannelLeft * -1;
                }
                if(WorkspaceObserver::value(element + "_ANTIRight").toBool())
                {
                    gainMixerLeftChannelRight = gainMixerLeftChannelRight * -1;
                    gainMixerRightChannelRight = gainMixerRightChannelRight * -1;
                }
            }
        }
        else
        {
            visible = 0;
            gainMixerLeftChannelLeft=0;
            gainMixerLeftChannelRight=0;
            gainMixerRightChannelLeft=0;
            gainMixerRightChannelRight=0;
        }
        if(isWidgetEmitAction())
        {
            emit attributeChanged(this->objectName(), "Save_" + element + "_Enable", QString::number(visible));
            emit attributeChanged(this->objectName(), "Save_" + element + "_Link", QString::number(WorkspaceObserver::value(element + "_Link").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_BalanceLinkedLeft", WorkspaceObserver::value(element + "_BalanceLinkedLeft").toString());
            emit attributeChanged(this->objectName(), "Save_" + element + "_BalanceLinkedRight", WorkspaceObserver::value(element + "_BalanceLinkedRight").toString());
            emit attributeChanged(this->objectName(), "Save_" + element + "_BalanceUnlinkLeft", WorkspaceObserver::value(element + "_BalanceUnlinkLeft").toString());
            emit attributeChanged(this->objectName(), "Save_" + element + "_BalanceUnlinkRight", WorkspaceObserver::value(element + "_BalanceUnlinkRight").toString());
            emit attributeChanged(this->objectName(), "Save_" + element + "_ANTI", QString::number(WorkspaceObserver::value(element + "_ANTI").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_ANTILeft", QString::number(WorkspaceObserver::value(element + "_ANTILeft").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_ANTIRight", QString::number(WorkspaceObserver::value(element + "_ANTIRight").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_GAIN", WorkspaceObserver::value(element + "_GAIN").toString());
            emit attributeChanged(this->objectName(), "Save_" + element + "_GAINLeft", WorkspaceObserver::value(element + "_GAINLeft").toString());
            emit attributeChanged(this->objectName(), "Save_" + element + "_GAINRight", WorkspaceObserver::value(element + "_GAINRight").toString());
            emit attributeChanged(this->objectName(), "Save_" + element + "_SOLO", QString::number(WorkspaceObserver::value(element + "_SOLO").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_SOLOLeft", QString::number(WorkspaceObserver::value(element + "_SOLOLeft").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_SOLORight", QString::number(WorkspaceObserver::value(element + "_SOLORight").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_MUTE", QString::number(WorkspaceObserver::value(element + "_MUTE").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_MUTELeft", QString::number(WorkspaceObserver::value(element + "_MUTELeft").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_MUTERight", QString::number(WorkspaceObserver::value(element + "_MUTERight").toBool()));
        }
        emit attributeChanged(this->objectName(), element + "_GainMLCL", QString::number(gainMixerLeftChannelLeft));
        emit attributeChanged(this->objectName(), element + "_GainMLCR", QString::number(gainMixerLeftChannelRight));
        emit attributeChanged(this->objectName(), element + "_GainMRCL", QString::number(gainMixerRightChannelLeft));
        emit attributeChanged(this->objectName(), element + "_GainMRCR", QString::number(gainMixerRightChannelRight));
    }
    QString style;
    if(WorkspaceObserver::value(mixer + "_Link").toBool())
    {
        ui->widgetLinkedVSlider->setHidden(false);
        ui->widgetLinkedMeterLeft->setHidden(false);
        ui->widgetLinkedMeterRight->setHidden(false);
        ui->widgetPushButtonGroup1->setHidden(false);
        ui->widgetUnlinkVSliderLeft->setHidden(true);
        ui->widgetUnlinkVSliderRight->setHidden(true);
        ui->widgetUnlinkMeter->setHidden(true);
        ui->widgetPushButtonGroup2->setHidden(true);
        ui->widgetPushButtonGroup3->setHidden(true);
        ui->widgetDialLeft->setDefault(0);
        ui->widgetDialLeft->setValue(WorkspaceObserver::value(mixer + "_BalanceLinkedLeft").toFloat());
        ui->widgetDialRight->setDefault(100);
        ui->widgetDialRight->setValue(WorkspaceObserver::value(mixer + "_BalanceLinkedRight").toFloat());
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
    }
    else
    {
        ui->widgetLinkedVSlider->setHidden(true);
        ui->widgetLinkedMeterLeft->setHidden(true);
        ui->widgetLinkedMeterRight->setHidden(true);
        ui->widgetPushButtonGroup1->setHidden(true);
        ui->widgetUnlinkVSliderLeft->setHidden(false);
        ui->widgetUnlinkVSliderRight->setHidden(false);
        ui->widgetUnlinkMeter->setHidden(false);
        ui->widgetPushButtonGroup2->setHidden(false);
        ui->widgetPushButtonGroup3->setHidden(false);
        ui->widgetDialLeft->setDefault(50);
        ui->widgetDialLeft->setValue(WorkspaceObserver::value(mixer + "_BalanceUnlinkLeft").toFloat());
        ui->widgetDialRight->setDefault(50);
        ui->widgetDialRight->setValue(WorkspaceObserver::value(mixer + "_BalanceUnlinkRight").toFloat());
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(60, 60, 60);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
    }
    ui->widgetPushButtonGroup1->setState("ANTI", QString::number(WorkspaceObserver::value(mixer + "ANTI").toBool()), false);
    ui->widgetPushButtonGroup2->setState("ANTI", QString::number(WorkspaceObserver::value(mixer + "_ANTILeft").toBool()), false);
    ui->widgetPushButtonGroup3->setState("ANTI", QString::number(WorkspaceObserver::value(mixer + "_ANTIRight").toBool()), false);
    ui->widgetLinkedVSlider->setValue(WorkspaceObserver::value(mixer + "_GAIN").toInt());
    ui->widgetUnlinkVSliderLeft->setValue(WorkspaceObserver::value(mixer + "_GAINLeft").toInt());
    ui->widgetUnlinkVSliderRight->setValue(WorkspaceObserver::value(mixer + "_GAINRight").toInt());
    // must load SOLO & MUTE at the end
    loadSoloMuteState(WorkspaceObserver::value(mixer + "_Link").toBool(),
                      WorkspaceObserver::value(mixer + "_SOLO").toBool(),
                      WorkspaceObserver::value(mixer + "_SOLOLeft").toBool(),
                      WorkspaceObserver::value(mixer + "_SOLORight").toBool(),
                      WorkspaceObserver::value(mixer + "_MUTE").toBool(),
                      WorkspaceObserver::value(mixer + "_MUTELeft").toBool(),
                      WorkspaceObserver::value(mixer + "_MUTERight").toBool()
                      );
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_Link", QString::number(WorkspaceObserver::value(mixer + "_Link").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_BalanceLinkedLeft", WorkspaceObserver::value(mixer + "_BalanceLinkedLeft").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_BalanceLinkedRight", WorkspaceObserver::value(mixer + "_BalanceLinkedRight").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_BalanceUnlinkLeft", WorkspaceObserver::value(mixer + "_BalanceUnlinkLeft").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_BalanceUnlinkRight", WorkspaceObserver::value(mixer + "_BalanceUnlinkRight").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_ANTI", QString::number(WorkspaceObserver::value(mixer + "_ANTI").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_ANTILeft", QString::number(WorkspaceObserver::value(mixer + "_ANTILeft").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_ANTIRight", QString::number(WorkspaceObserver::value(mixer + "_ANTIRight").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_GAIN", WorkspaceObserver::value(mixer + "_GAIN").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_GAINLeft", WorkspaceObserver::value(mixer + "_GAINLeft").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_GAINRight", WorkspaceObserver::value(mixer + "_GAINRight").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_SOLO", QString::number(WorkspaceObserver::value(mixer + "_SOLO").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_SOLOLeft", QString::number(WorkspaceObserver::value(mixer + "_SOLOLeft").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_SOLORight", QString::number(WorkspaceObserver::value(mixer + "_SOLORight").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_MUTE", QString::number(WorkspaceObserver::value(mixer + "_MUTE").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_MUTELeft", QString::number(WorkspaceObserver::value(mixer + "_MUTELeft").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_MUTERight", QString::number(WorkspaceObserver::value(mixer + "_MUTERight").toBool()));
    }
    setWidgetReady(true);
    updateAttribute();
}
void MixerS1M4::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void MixerS1M4::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void MixerS1M4::in_widgetDialLeft_valueChanged(float value)
{
    if(getLinkState())
    {
        save(getMixer() + "_BalanceLinkedLeft", value);
    }
    else
    {
        save(getMixer() + "_BalanceUnlinkLeft", value);
    }
    updateAttribute();
}
void MixerS1M4::in_widgetDialRight_valueChanged(float value)
{
    if(getLinkState())
    {
        save(getMixer() + "_BalanceLinkedRight", value);
    }
    else
    {
        save(getMixer() + "_BalanceUnlinkRight", value);
    }
    updateAttribute();
}
void MixerS1M4::in_widgetLinkedVSlider_valueChanged(int value)
{
    save(getMixer() + "_GAIN", value);
    updateAttribute();
}
void MixerS1M4::in_widgetUnlinkVSliderLeft_valueChanged(int value)
{
    save(getMixer() + "_GAINLeft", value);
    updateAttribute();
}
void MixerS1M4::in_widgetUnlinkVSliderRight_valueChanged(int value)
{
    save(getMixer() + "_GAINRight", value);
    updateAttribute();
}
void MixerS1M4::in_widgetPushButtonGroup1_stateChanged(QString button, QString state)
{
    if(button == "SOLO")
    {
        if(doSolo())
        {
            save(getMixer() + "_SOLO", (bool) state.toInt());
        }
    }
    else if(button == "MUTE")
    {
        if(doMute())
        {
            save(getMixer() + "_MUTE", (bool) state.toInt());
        }
        updateAttribute();
    }
    else if(button == "ANTI")
    {
        save(getMixer() + "_ANTI", (bool) state.toInt());
        updateAttribute();
    }
}
void MixerS1M4::in_widgetPushButtonGroup2_stateChanged(QString button, QString state)
{
    if(button == "SOLO")
    {
        if(doSoloLeft())
        {
            save(getMixer() + "_SOLOLeft", (bool) state.toInt());
        }
    }
    else if(button == "MUTE")
    {
        if(doMuteLeft())
        {
            save(getMixer() + "_MUTELeft", (bool) state.toInt());
        }
        updateAttribute();
    }
    else if(button == "ANTI")
    {
        save(getMixer() + "_ANTILeft", (bool) state.toInt());
        updateAttribute();
    }
}
void MixerS1M4::in_widgetPushButtonGroup3_stateChanged(QString button, QString state)
{
    if(button == "SOLO")
    {
        if(doSoloRight())
        {
            save(getMixer() + "_SOLORight", (bool) state.toInt());
        }
    }
    else if(button == "MUTE")
    {
        if(doMuteRight())
        {
            save(getMixer() + "_MUTERight", (bool) state.toInt());
        }
        updateAttribute();
    }
    else if(button == "ANTI")
    {
        save(getMixer() + "_ANTIRight", (bool) state.toInt());
        updateAttribute();
    }
}
void MixerS1M4::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void MixerS1M4::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void MixerS1M4::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}
void MixerS1M4::on_pushButtonLink_clicked()
{
    QString style;
    if(getLinkState())
    {
        ui->widgetLinkedVSlider->setHidden(true);
        ui->widgetLinkedMeterLeft->setHidden(true);
        ui->widgetLinkedMeterRight->setHidden(true);
        ui->widgetPushButtonGroup1->setHidden(true);
        ui->widgetUnlinkVSliderLeft->setHidden(false);
        ui->widgetUnlinkVSliderRight->setHidden(false);
        ui->widgetUnlinkMeter->setHidden(false);
        ui->widgetPushButtonGroup2->setHidden(false);
        ui->widgetPushButtonGroup3->setHidden(false);
        ui->widgetDialLeft->setDefault(50);
        ui->widgetDialLeft->setValue(WorkspaceObserver::value(getMixer() + "_BalanceUnlinkLeft").toFloat());
        ui->widgetDialRight->setDefault(50);
        ui->widgetDialRight->setValue(WorkspaceObserver::value(getMixer() + "_BalanceUnlinkRight").toFloat());
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(60, 60, 60);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
        setLinkState(false);
    }
    else
    {
        ui->widgetLinkedVSlider->setHidden(false);
        ui->widgetLinkedMeterLeft->setHidden(false);
        ui->widgetLinkedMeterRight->setHidden(false);
        ui->widgetPushButtonGroup1->setHidden(false);
        ui->widgetUnlinkVSliderLeft->setHidden(true);
        ui->widgetUnlinkVSliderRight->setHidden(true);
        ui->widgetUnlinkMeter->setHidden(true);
        ui->widgetPushButtonGroup2->setHidden(true);
        ui->widgetPushButtonGroup3->setHidden(true);
        ui->widgetDialLeft->setDefault(0);
        ui->widgetDialLeft->setValue(WorkspaceObserver::value(getMixer() + "_BalanceLinkedLeft").toFloat());
        ui->widgetDialRight->setDefault(100);
        ui->widgetDialRight->setValue(WorkspaceObserver::value(getMixer() + "_BalanceLinkedRight").toFloat());
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
        setLinkState(true);
    }
    save(getMixer() + "_Link", getLinkState());
    updateAttribute();
}


// setter & getter
void MixerS1M4::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
MixerS1M4& MixerS1M4::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
MixerS1M4& MixerS1M4::setFont(QFont font)
{
    mFont = font;
    ui->widgetDialLeft->setFont(font);
    ui->widgetDialRight->setFont(font);
    ui->widgetUnlinkVSliderLeft->setFont(font);
    ui->widgetUnlinkMeter->setFont(font);
    ui->widgetUnlinkVSliderRight->setFont(font);
    ui->widgetLinkedMeterLeft->setFont(font);
    ui->widgetLinkedVSlider->setFont(font);
    ui->widgetLinkedMeterRight->setFont(font);
    ui->widgetPushButtonGroup1->setFont(font);
    ui->widgetPushButtonGroup2->setFont(font);
    ui->widgetPushButtonGroup3->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
MixerS1M4& MixerS1M4::setVolumeMeterLeft(int value)
{
    mVolumeMeterLeft = qMax(-900, value);
    int gainL=0, gainR=0;
    if(getLinkState())
    {
        gainL = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
        gainR = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
    }
    else
    {
        gainL = ui->widgetPushButtonGroup2->getState("MUTE").toInt() ? (-90) : (ui->widgetUnlinkVSliderLeft->getValue());
        gainR = ui->widgetPushButtonGroup3->getState("MUTE").toInt() ? (-90) : (ui->widgetUnlinkVSliderRight->getValue());
    }
    int attenuationL=USBAHandle.GainToLog(USBAHandle.LogToGain(mVolumeMeterLeft / 10.0 + gainL, -90) * (100 - ui->widgetDialLeft->getValue()) / 100 + USBAHandle.LogToGain(mVolumeMeterRight / 10.0 + gainR, -90) * (100 - ui->widgetDialRight->getValue()) / 100) - mVolumeMeterLeft / 10.0;
    int attenuationR=USBAHandle.GainToLog(USBAHandle.LogToGain(mVolumeMeterLeft / 10.0 + gainL, -90) * ui->widgetDialLeft->getValue() / 100 + USBAHandle.LogToGain(mVolumeMeterRight / 10.0 + gainR, -90) * ui->widgetDialRight->getValue() / 100) - mVolumeMeterRight / 10.0;
    if(getLinkState())
    {
        ui->widgetLinkedMeterLeft->setValue(mVolumeMeterLeft, attenuationL);
        ui->widgetLinkedMeterRight->setValue(mVolumeMeterRight, attenuationR);
    }
    else
    {
        ui->widgetUnlinkMeter->setValueLeft(mVolumeMeterLeft, attenuationL);
        ui->widgetUnlinkMeter->setValueRight(mVolumeMeterRight, attenuationR);
    }
    return *this;
}
MixerS1M4& MixerS1M4::setVolumeMeterLeftClear()
{
    ui->widgetLinkedMeterLeft->setMeterClear();
    ui->widgetUnlinkMeter->setMeterLeftClear();
    return *this;
}
MixerS1M4& MixerS1M4::setVolumeMeterLeftSlip()
{
    ui->widgetLinkedMeterLeft->setMeterSlip();
    ui->widgetUnlinkMeter->setMeterLeftSlip();
    return *this;
}
MixerS1M4& MixerS1M4::setVolumeMeterRight(int value)
{
    mVolumeMeterRight = qMax(-900, value);
    int gainL=0, gainR=0;
    if(getLinkState())
    {
        gainL = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
        gainR = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
    }
    else
    {
        gainL = ui->widgetPushButtonGroup2->getState("MUTE").toInt() ? (-90) : (ui->widgetUnlinkVSliderLeft->getValue());
        gainR = ui->widgetPushButtonGroup3->getState("MUTE").toInt() ? (-90) : (ui->widgetUnlinkVSliderRight->getValue());
    }
    int attenuationL=USBAHandle.GainToLog(USBAHandle.LogToGain(mVolumeMeterLeft / 10.0 + gainL, -90) * (100 - ui->widgetDialLeft->getValue()) / 100 + USBAHandle.LogToGain(mVolumeMeterRight / 10.0 + gainR, -90) * (100 - ui->widgetDialRight->getValue()) / 100) - mVolumeMeterLeft / 10.0;
    int attenuationR=USBAHandle.GainToLog(USBAHandle.LogToGain(mVolumeMeterLeft / 10.0 + gainL, -90) * ui->widgetDialLeft->getValue() / 100 + USBAHandle.LogToGain(mVolumeMeterRight / 10.0 + gainR, -90) * ui->widgetDialRight->getValue() / 100) - mVolumeMeterRight / 10.0;
    if(getLinkState())
    {
        ui->widgetLinkedMeterLeft->setValue(mVolumeMeterLeft, attenuationL);
        ui->widgetLinkedMeterRight->setValue(mVolumeMeterRight, attenuationR);
    }
    else
    {
        ui->widgetUnlinkMeter->setValueLeft(mVolumeMeterLeft, attenuationL);
        ui->widgetUnlinkMeter->setValueRight(mVolumeMeterRight, attenuationR);
    }
    return *this;
}
MixerS1M4& MixerS1M4::setVolumeMeterRightClear()
{
    ui->widgetLinkedMeterRight->setMeterClear();
    ui->widgetUnlinkMeter->setMeterRightClear();
    return *this;
}
MixerS1M4& MixerS1M4::setVolumeMeterRightSlip()
{
    ui->widgetLinkedMeterRight->setMeterSlip();
    ui->widgetUnlinkMeter->setMeterRightSlip();
    return *this;
}
MixerS1M4& MixerS1M4::setLinkDefaultState(bool state)
{
    mLinkDefaultState = state;
    return *this;
}
MixerS1M4& MixerS1M4::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}


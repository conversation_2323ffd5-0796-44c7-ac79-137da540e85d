#include "dials1m6.h"
#include "globalfont.h"
#include <QPainterPath>
#include <QSvgRenderer>

DialS1M6::DialS1M6(QWidget* parent)
    : QWidget(parent)
{
    setFocusPolicy(Qt::StrongFocus);
}
DialS1M6::~DialS1M6()
{

}


// override
void DialS1M6::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int diameter = qMin(rect().width(), rect().height());
    mRectDial.setY(rect().y() + (rect().height() - diameter)/2);
    mRectDial.setX(rect().x() + (rect().width() - diameter)/2);
    mRectDial.setWidth(diameter);
    mRectDial.setHeight(diameter);
}
void DialS1M6::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void DialS1M6::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(mRectDial.contains(e->pos()))
    {
        if(mValue != mValueDefault)
        {
            mValue = mValueDefault;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M6::mousePressEvent(QMouseEvent* e)
{
    if(mMouseEnabled && mRectDial.contains(e->pos()))
    {
        mPressed = true;
        mPressedValue = mValue;
        mPressedPoint = e->pos();
    }
}
void DialS1M6::mouseMoveEvent(QMouseEvent* e)
{
    float value=0;
    if(mPressed)
    {
        value = mPressedValue;
        value += ((int) (mPressedPoint.y() - e->pos().y()) / mSensitivity) * 0.5;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            float newValueMod=std::abs(std::fmod(value, 1.0));
            if(value >= mValueEnd05)
            {
            }
            else if(value >= mValueEnd10)
            {
                if(newValueMod != 0)
                {
                    return;
                }
            }
            else if(value >= mValueEnd20)
            {
                if(newValueMod != 0 || ((int) value) % 2)
                {
                    return;
                }
            }
            mValue = value;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M6::mouseReleaseEvent(QMouseEvent* e)
{
    Q_UNUSED(e);
    mPressed = false;
}
void DialS1M6::wheelEvent(QWheelEvent* e)
{
    float value=mValue;
    int numSteps=e->angleDelta().y() / 120;
    int numStepsAbs=qAbs(numSteps);
    for(int i=0;i<numStepsAbs;i++)
    {
        if(numSteps > 0)
        {
            if(value >= mValueEnd05)
            {
                value += 0.5;
            }
            else if(value >= mValueEnd10)
            {
                value += 1;
            }
            else if(value >= mValueEnd20)
            {
                value += 2;
            }
        }
        else
        {
            if(value > mValueEnd05)
            {
                value -= 0.5;
            }
            else if(value > mValueEnd10)
            {
                value -= 1;
            }
            else if(value > mValueEnd20)
            {
                value -= 2;
            }
        }
    }
    value = qMax(value, mValueMin);
    value = qMin(value, mValueMax);
    if(mValue != value)
    {
        mValue = value;
        emit valueChanged(mValue);
    }
    update();
}
void DialS1M6::keyPressEvent(QKeyEvent* e)
{
    float value=mValue;
    if(e->key() == Qt::Key_Up || e->key() == Qt::Key_Right)
    {
        if(value >= mValueEnd05)
        {
            value += 0.5;
        }
        else if(value >= mValueEnd10)
        {
            value += 1;
        }
        else if(value >= mValueEnd20)
        {
            value += 2;
        }
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
    else if(e->key() == Qt::Key_Down || e->key() == Qt::Key_Left)
    {
        if(value > mValueEnd05)
        {
            value -= 0.5;
        }
        else if(value > mValueEnd10)
        {
            value -= 1;
        }
        else if(value > mValueEnd20)
        {
            value -= 2;
        }
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
}
QPixmap DialS1M6::extractArcPartSquare(const QPixmap &source, qreal mCurValueAngle, bool mDoublePercent)
{
    qreal devicePixelRatio = qApp->devicePixelRatio();
    int wh = qMin(source.width(), source.height());
    int logicalWh = wh / devicePixelRatio;

    QPixmap result(wh, wh);
    result.setDevicePixelRatio(devicePixelRatio);
    result.fill(Qt::transparent);

    QPainter painter(&result);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);

    QImage mask(wh, wh, QImage::Format_ARGB32_Premultiplied);
    mask.setDevicePixelRatio(devicePixelRatio);
    mask.fill(Qt::transparent);
    {
        QPainter maskPainter(&mask);
        maskPainter.setRenderHint(QPainter::Antialiasing, true);
        maskPainter.setRenderHint(QPainter::SmoothPixmapTransform, true);
        maskPainter.setPen(Qt::NoPen);
        maskPainter.setBrush(Qt::white);
        QRectF rect(0, 0, logicalWh, logicalWh);
        qreal startDeg = mDoublePercent ? 90 : (225 - mCurValueAngle);
        qreal spanDeg = mDoublePercent ? (135 - mCurValueAngle) : mCurValueAngle;
        QPainterPath path;
        path.moveTo(rect.center());
        path.arcTo(rect, startDeg, spanDeg);
        path.closeSubpath();
        maskPainter.drawPath(path);
    }

    painter.drawPixmap(QRect(0, 0, logicalWh, logicalWh), source);
    painter.setCompositionMode(QPainter::CompositionMode_DestinationIn);
    painter.drawImage(QRect(0, 0, logicalWh, logicalWh), mask);

    painter.end();
    return result;
}
void DialS1M6::drawBG(QPainter* painter)
{
    painter->fillRect(rect(), Qt::transparent);
}
void DialS1M6::drawElement(QPainter* painter)
{
    painter->save();
    QPen pen=painter->pen();
    qreal innerSpace = mRectDial.width() * 0.125;
    QRectF innerRect = mRectDial.adjusted(innerSpace, innerSpace, -innerSpace, -innerSpace);
    qreal mCurValueAngle = 270 * (mValue - mValueMin) / (mValueMax - mValueMin);
    qreal devicePixelRatio = qApp->devicePixelRatio();
    int pixelDiameter = mRectDial.width() * devicePixelRatio;

    QPixmap pixmapBackground(":/Icon/EllipseBackground.png");
    pixmapBackground = pixmapBackground.scaled(pixelDiameter, pixelDiameter, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    pixmapBackground.setDevicePixelRatio(devicePixelRatio);
    painter->drawPixmap(mRectDial, pixmapBackground);

    QPixmap pixmap(":/Icon/Ellipse.png");
    pixmap = pixmap.scaled(pixelDiameter, pixelDiameter, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    pixmap.setDevicePixelRatio(devicePixelRatio);
    pixmap = extractArcPartSquare(pixmap, mCurValueAngle);
    painter->drawPixmap(mRectDial, pixmap);

    if(mValueShowArrow)
    {
        painter->save();
        int penWidth = mRectDial.width() * 0.08;
        qreal innerCircleRadius = mRectDial.width()/2.0 - penWidth * 2.0;
        qreal indicatorWidth = innerCircleRadius * 0.05;
        qreal indicatorHeight = innerCircleRadius *0.23;
        painter->translate(innerRect.center());
        qreal degRotate = -135 + mCurValueAngle;
        painter->rotate(degRotate);
        QRectF indicatorRect(-indicatorWidth/2.0, -innerRect.width()*0.46, indicatorWidth, indicatorHeight);
        qreal cornerRadius = indicatorWidth/4.0;
        painter->setBrush(mColorHandle);
        pen.setColor(mColorHandle);
        pen.setWidth(penWidth * 0.4);
        painter->setPen(pen);
        painter->drawRoundedRect(indicatorRect, cornerRadius, cornerRadius);
        painter->restore();
    }

    // draw Text
    if(mValueShowText)
    {
        painter->resetTransform();
        int widthText=mRectDial.width() * 0.5;
        int heightText=mRectDial.height() * 0.3;
        QRect rectText(mRectDial.x() + (mRectDial.width() - widthText) / 2, mRectDial.y() + (mRectDial.height() - heightText) *0.48, widthText, heightText);
            // get suitable font
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "+88", rectText));
        painter->setFont(mFont);
        painter->setPen(mColorText);
            // Text
        QString str;
        if(mValue > mValueEnd05)
        {
            str = QString("%1").arg((double) mValue, 0, 'f', 1);
            if(mValue > 0)
            {
                str = QString("+%1").arg((double) mValue, 0, 'f', 1);
            }
        }
        else
        {
            str = QString("%1").arg((double) mValue, 0, 'f', 0);
        }
        if(str.toFloat() == mValueMin)
        {
            str = mShowInfinitesimal ? ("-∞") : (str);
        }
        else if(str.toFloat() == mValueMax)
        {
            str = mShowInfinity ? ("+∞") : (str);
        }
        painter->drawText(rectText, Qt::AlignCenter, str);
    }
    painter->restore();
}


// setter & getter
DialS1M6& DialS1M6::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
DialS1M6& DialS1M6::setValue(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValue = value;
    update();
    return *this;
}
DialS1M6& DialS1M6::setDefault(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValueDefault = value;
    return *this;
}
DialS1M6& DialS1M6::setRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20)
{
    mValueStart = valueStart;
    mValueEnd05 = valueEnd05;
    mValueEnd10 = valueEnd10;
    mValueEnd20 = valueEnd20;
    mValueMin = mValueEnd20;
    mValueMax = mValueStart;
    if(mValueMax < mValueDefault || mValueDefault < mValueMin)
    {
        mValueDefault = mValueMin;
    }
    update();
    return *this;
}
DialS1M6& DialS1M6::setSensitivity(int sensitivity)
{
    if(sensitivity > 100 || sensitivity <= 0)
    {
        return *this;
    }
    mSensitivity = sensitivity;
    return *this;
}
DialS1M6& DialS1M6::setMovable(bool status)
{
    mMouseEnabled = status;
    return *this;
}
DialS1M6& DialS1M6::setColorBG(QColor color)
{
    mColorBG = color;
    update();
    return *this;
}
DialS1M6& DialS1M6::setColorDial(QColor color)
{
    mColorDial = color;
    update();
    return *this;
}
DialS1M6& DialS1M6::setColorCircleBG(QColor color)
{
    mColorCircleBG = color;
    update();
    return *this;
}
DialS1M6& DialS1M6::setColorCircleValue(QColor color)
{
    mColorCircleValue = color;
    update();
    return *this;
}
DialS1M6& DialS1M6::setColorHandle(QColor color)
{
    mColorHandle = color;
    update();
    return *this;
}
DialS1M6& DialS1M6::setColorText(QColor color)
{
    mColorText = color;
    update();
    return *this;
}
DialS1M6& DialS1M6::showArrow(bool status)
{
    mValueShowArrow = status;
    update();
    return *this;
}
DialS1M6& DialS1M6::showCircle(bool status)
{
    mValueShowCircle = status;
    update();
    return *this;
}
DialS1M6& DialS1M6::showText(bool status)
{
    mValueShowText = status;
    update();
    return *this;
}
DialS1M6& DialS1M6::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
DialS1M6& DialS1M6::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}


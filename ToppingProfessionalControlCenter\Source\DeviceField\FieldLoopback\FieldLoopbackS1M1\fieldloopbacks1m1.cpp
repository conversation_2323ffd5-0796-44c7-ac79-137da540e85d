#include "fieldloopbacks1m1.h"


FieldLoopbackS1M1::FieldLoopbackS1M1(QWidget* parent, QString name)
    : FieldLoopbackBase1(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
{
    connect(this, &FieldLoopbackBase1::attributeChanged, this, &FieldLoopbackS1M1::in_widgetBase_attributeChanged, Qt::UniqueConnection);
}
FieldLoopbackS1M1::~FieldLoopbackS1M1()
{

}


// override
void FieldLoopbackS1M1::loadSettings()
{
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("Visible", mVisibleListDefault);
    }
    QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
    QVector<QString> list;
    for(auto element : visibleList)
    {
        list.append(element.toString());
    }
    setVisibleList(list);
}
void FieldLoopbackS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Language")
    {
        if(value == "English")
        {
            setFieldTitle("Loopback");
        }
        else if(value == "Chinese")
        {
            setFieldTitle("内录");
        }
    }
}


// slot
void FieldLoopbackS1M1::in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute == "Visible")
    {
        if(value.toInt())
        {
            QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
            visibleList.append(QVariant(objectName));
            WorkspaceObserver::setValue("Visible", visibleList);
        }
        else
        {
            QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
            visibleList.removeOne(QVariant(objectName));
            WorkspaceObserver::setValue("Visible", visibleList);
        }
    }
}
void FieldLoopbackS1M1::in_widgetList_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// setter & getter
FieldLoopbackS1M1& FieldLoopbackS1M1::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
FieldLoopbackS1M1& FieldLoopbackS1M1::modifyWidgetList(QVector<LoopbackBase*> list)
{
    mWidgetList = list;
    for(auto element : list)
    {
        element->setWidgetMovable(false);
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_widgetList_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    FieldLoopbackBase1::modifyWidgetList(list);
    return *this;
}
FieldLoopbackS1M1& FieldLoopbackS1M1::setVisibleListDefault(QVector<LoopbackBase*> list)
{
    for(auto widget : list)
    {
        mVisibleListDefault.append(QVariant(widget->getChannelName()));
    }
    return *this;
}


#ifndef BATTERYS1M1_H
#define BATTERYS1M1_H

#include <QWidget>

class QTimer;
class BatteryDrawStrategy;
struct BatteryColors;

class BatteryS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit BatteryS1M1(QWidget* parent = nullptr);
    ~BatteryS1M1();

    BatteryS1M1& setFont(QFont font);
    BatteryS1M1& setValue(int value);
    BatteryS1M1& setCharging(bool charging);
    BatteryS1M1& setIsShowChargingIcon(bool status = true);
    BatteryS1M1& setIsShowText(bool status = true);
    BatteryS1M1& setLowValue(int value);
    BatteryS1M1& setWarningValue(int value);

    BatteryS1M1& setDrawStrategy(std::unique_ptr<BatteryDrawStrategy> strategy);

    BatteryS1M1& setLowColorShellNormal(QColor color);
    BatteryS1M1& setWarningColorShellNormal(QColor color);
    BatteryS1M1& setColorShellNormal(QColor color);

    BatteryS1M1& setColorGrooveNormal(QColor color);
    BatteryS1M1& setColorGrooveCharging(QColor color);

    BatteryS1M1& setColorLightning(QColor color);

    BatteryS1M1& setColorElectricQuantityNormal(QColor color);
    BatteryS1M1& setColorElectricQuantityCharging(QColor color);

    BatteryS1M1& setColorTextNormal(QColor color);
    BatteryS1M1& setColorTextCharging(QColor color);

protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;

private:
    void drawBG(QPainter* painter);
    void drawElement(QPainter* painter);
    BatteryColors getBatteryColors() const;

private:
    bool mShowChargingIcon = false;
    bool mShowValueText = false;
    bool mShowChargingAnimation = true;
    bool mCharging = false;
    int mLowValue = 5;
    int mWarningValue = 10;
    int mValue = 100;
    int mPenWidth = 2;
    QRectF mRectBattery;
    QFont mFont;

    std::unique_ptr<BatteryDrawStrategy> mDrawStrategy;

    QColor mLowColorShellNormal = Qt::red;
    QColor mWarningColorShellNormal = Qt::yellow;
    QColor mColorShellNormal = QColor(229, 229, 229);

    QColor mColorGrooveNormal = QColor(31, 31, 31);
    QColor mColorGrooveCharging = QColor(31, 31, 31);

    QColor mColorLightning = QColor(132, 132, 132);

    QColor mColorElectricQuantityNormal = QColor(229, 229, 229);
    QColor mColorElectricQuantityCharging = QColor(0, 255, 0);

    QColor mColorTextNormal = QColor(255, 255, 255);
    QColor mColorTextCharging = QColor(255, 255, 255);
};

#endif // BATTERYS1M1_H

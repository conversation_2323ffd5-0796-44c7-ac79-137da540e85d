#include <QDebug>
#include <QVector>
#include <QSettings>
#include <QApplication>

#include "appsettings.h"


AppSettingsSubject AppSettingsSubject::mInstance;


// ******************************************************************************
// *********************************  AppSettingsObserver
// ******************************************************************************
QVariant AppSettingsObserver::value(QAnyStringView key)
{
    return QSettings().value(key);
}


// ******************************************************************************
// *********************************  AppSettingsSubject
// ******************************************************************************
AppSettingsSubject::AppSettingsSubject()
{
    QCoreApplication::setOrganizationName("TOPPING");
    QCoreApplication::setOrganizationDomain("topping.pro");
    QCoreApplication::setApplicationName("TOPPING Professional Control Center");
}
bool AppSettingsSubject::init()
{
    int resetFlag=6;
    QSettings settings;
    if(settings.value("ResetFlag").toInt() != resetFlag)
    {
        QStringList keys=settings.allKeys();
        for(auto key : keys)
        {
            settings.remove(key);
        }
        settings.setValue("ResetFlag", resetFlag);
    }
    if(!settings.contains("Language")) settings.setValue("Language", "English");
    if(!settings.contains("ScaleFactor")) settings.setValue("ScaleFactor", 1.50);
    if(!settings.contains("FollowSystemScale")) settings.setValue("FollowSystemScale", true);
    if(!settings.contains("AutoStartOnBoot")) settings.setValue("AutoStartOnBoot", true);
    if(!settings.contains("AutoCheckForUpdates")) settings.setValue("AutoCheckForUpdates", true);
    if(!settings.contains("AutoSaveWorkspace")) settings.setValue("AutoSaveWorkspace", true);
    return true;
}
AppSettingsSubject& AppSettingsSubject::addObserver(AppSettingsObserver* observer)
{
    mObservers.push_back(observer);
    return *this;
}
AppSettingsSubject& AppSettingsSubject::addObserverList(QVector<AppSettingsObserver*> observerList)
{
    for(auto element : observerList)
    {
        mObservers.push_back(element);
    }
    return *this;
}
AppSettingsSubject& AppSettingsSubject::removeObserverOne(AppSettingsObserver* observer)
{
    mObservers.removeOne(observer);
    return *this;
}
AppSettingsSubject& AppSettingsSubject::removeObserverAll()
{
    mObservers.clear();
    return *this;
}
AppSettingsSubject& AppSettingsSubject::listObserver()
{
    if(mObservers.empty())
    {
        qInfo() << "AppSettingsSubject::listObserver //observer list is empty";
        return *this;
    }
    int count=0;
    qInfo() << "";
    qInfo() << "";
    qInfo() << "AppSettingsSubject::listObserver //observer list [" << mObservers.size() << "]";
    for(auto observer : mObservers)
    {
        count++;
        qInfo() << "\t" << count << "\t" << observer->getObserverName();
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}
AppSettingsSubject& AppSettingsSubject::changeLanguage(QString language)
{
    QSettings().setValue("Language", language);
    emit attributeChanged("AppSettings", "ModifyLanguage", language);
    emit attributeChanged("AppSettings", "ChangeLanguage", "1");
    for(auto observer : mObservers)
    {
        observer->AppSettingsChanged("", "Language", language);
    }
    emit attributeChanged("AppSettings", "ChangeLanguage", "0");
    return *this;
}
AppSettingsSubject& AppSettingsSubject::changeScaleFactor(QString factor)
{
    QSettings().setValue("ScaleFactor", factor);
    emit attributeChanged("AppSettings", "ModifyScaleFactor", factor);
    emit attributeChanged("AppSettings", "ChangeScaleFactor", "1");
    for(auto observer : mObservers)
    {
        observer->AppSettingsChanged("", "ScaleFactor", factor);
    }
    emit attributeChanged("AppSettings", "ChangeScaleFactor", "0");
    return *this;
}


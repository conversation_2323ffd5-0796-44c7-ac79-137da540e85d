#include "workspace.h"
#include "usbaudioapi.h"
#include "fieldheads1m2.h"


FieldHeadS1M2::FieldHeadS1M2(QWidget* parent, QString name)
    : FieldHeadBase2(parent)
    , AppSettingsObserver(name)
{
    connect(this, &FieldHeadBase2::attributeChanged, this, &FieldHeadS1M2::in_widgetBase_attributeChanged, Qt::UniqueConnection);
}
FieldHeadS1M2::~FieldHeadS1M2()
{

}


// override
void FieldHeadS1M2::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Language")
    {
        setLanguage(value);
    }
}


// slot
void FieldHeadS1M2::in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(objectName == "SampleRate")
    {
        if(attribute == "ItemChanged")
        {
            USBAHandle.setSampleRateOfActiveDevice(value.toUInt());
        }
    }
    else if(objectName == "BufferSize")
    {
        if(attribute == "ItemChanged")
        {
            USBAHandle.setBufferSizeOfActiveDevice(value.toUInt());
        }
    }
    else if(objectName == "SystemSettings")
    {
        if(attribute == "Clicked")
        {
            emit attributeChanged(objectName, attribute, value);
        }
    }
}


// setter & getter
FieldHeadS1M2& FieldHeadS1M2::setName(QString name)
{
    setObjectName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}


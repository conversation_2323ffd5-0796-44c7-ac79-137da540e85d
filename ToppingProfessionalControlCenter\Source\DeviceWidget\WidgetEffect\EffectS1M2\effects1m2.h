#ifndef EffectS1M2_H
#define EffectS1M2_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "effectbase.h"
#include "workspace.h"
#include "appsettings.h"
#include "pushbuttons1m5.h"
#include "pushbuttons1m7.h"


namespace Ui {
class EffectS1M2;
}


class EffectS1M2 : public EffectBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
    enum NavigationDisplayMode{
        invalid=-1,
        collapse,
        expand
    };

public:
    explicit EffectS1M2(QWidget* parent=nullptr, QString name="");
    ~EffectS1M2();
    EffectS1M2& setName(QString name);
    EffectS1M2& setFont(QFont font);
    EffectS1M2& setVolumeMeter(int value);
    EffectS1M2& setVolumeMeterClear();
    EffectS1M2& setVolumeMeterSlip();
    EffectS1M2& setDialTexts(const QStringList& texts);
    EffectS1M2& setChannelNameEditable(bool state=true);
    EffectS1M2& setValueReverbChannel(QString channel);
    EffectS1M2& setValueReverbType(QString type);
    EffectS1M2& setValueDryWet(float value);
    EffectS1M2& setValueRoom(float value);
    EffectS1M2& setValueDecay(float value);
    EffectS1M2& setValueMUTE(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e)override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    void setNavigationDisplayMode(NavigationDisplayMode mode=invalid);
private:
    Ui::EffectS1M2* ui;
    QTimer mTimer;
    QFont mFont;
    QString mPreReverbChannel="";
    QString mPreReverbType="";
    float mPreDryWet=-2147483648;
    float mPreRoom=-2147483648;
    float mPreDecay=-2147483648;
    int mPreMute=-2147483648;
    NavigationDisplayMode mDisplayMode = invalid;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M5::ButtonID button, bool state);
    void in_widgetDial_valueChanged(float value);
    void in_widgetDialRoom_valueChanged(float value);
    void in_widgetDialDecay_valueChanged(float value);
    void in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M7::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
    void on_pushButtonNavigation_clicked();
};


#endif // EffectS1M2_H


#ifndef ORIGINS1M2_H
#define ORIGINS1M2_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"
#include "pushbuttons1m9.h"


namespace Ui {
class OriginS1M2;
}


class OriginS1M2 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M2(QWidget* parent=nullptr, QString name="");
    ~OriginS1M2();
    OriginS1M2& setName(QString name);
    OriginS1M2& setFont(QFont font);
    OriginS1M2& setVolumeMeter(int value);
    OriginS1M2& setVolumeMeterClear();
    OriginS1M2& setVolumeMeterSlip();
    OriginS1M2& setGain(float value);
    OriginS1M2& setGainLock(bool state=true);
    OriginS1M2& setMuteAffectGain(bool state=true);
    OriginS1M2& setGainAffectMute(bool state=true);
    OriginS1M2& setGainRange(float min, float max);
    OriginS1M2& setGainDefault(float value);
    OriginS1M2& setGainWidgetDisable(float value);
    OriginS1M2& setChannelNameEditable(bool state=true);
    OriginS1M2& setValue48V(bool state=true);
    OriginS1M2& setValueGAIN(float value);
    OriginS1M2& setValueMUTE(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OriginS1M2* ui;
    QTimer mTimer;
    QFont mFont;
    int mPre48V=-2147483648;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetVSlider_valueChanged(int value);
    void in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M9::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // ORIGINS1M2_H


/********************************************************************************
** Form generated from reading UI file 'deviceconnectorviews1m1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_DEVICECONNECTORVIEWS1M1_H
#define UI_DEVICECONNECTORVIEWS1M1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_DeviceConnectorViewS1M1
{
public:
    QGridLayout *gridLayout;
    QStackedWidget *stackedWidget;
    QWidget *Page0;
    QGridLayout *gridLayout_2;
    QSpacerItem *verticalSpacerTop;
    QLabel *Page0Label1;
    QSpacerItem *verticalSpacerBottom;
    QWidget *Page1;
    QGridLayout *gridLayout_3;
    QSpacerItem *verticalSpacerTop_2;
    QLabel *Page1Label1;
    QSpacerItem *verticalSpacerBottom_2;
    QWidget *Page2;
    QGridLayout *gridLayout_4;
    QSpacerItem *verticalSpacer;
    QLabel *Page2Label1;
    QSpacerItem *verticalSpacer_2;
    QWidget *Page3;
    QGridLayout *gridLayout_5;
    QSpacerItem *verticalSpacer_3;
    QLabel *Page3Label1;
    QSpacerItem *verticalSpacer_4;
    QWidget *Page4;
    QGridLayout *gridLayout_6;
    QSpacerItem *verticalSpacer_5;
    QLabel *Page4Label1;
    QSpacerItem *verticalSpacer_6;
    QWidget *Page5;
    QGridLayout *gridLayout_10;
    QSpacerItem *verticalSpacer_12;
    QLabel *Page5Label1;
    QSpacerItem *verticalSpacer_13;
    QWidget *Page6;
    QGridLayout *gridLayout_9;
    QSpacerItem *verticalSpacer_7;
    QLabel *Page6Label1;
    QSpacerItem *verticalSpacer_8;
    QLabel *Page6Label2;
    QSpacerItem *verticalSpacer_9;
    QGridLayout *gridLayout_7;
    QSpacerItem *horizontalSpacer;
    QPushButton *Page6PushButton2;
    QPushButton *Page6PushButton1;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *horizontalSpacer_3;
    QSpacerItem *verticalSpacer_10;
    QGridLayout *gridLayout_8;
    QSpacerItem *horizontalSpacer_4;
    QCheckBox *Page6CheckBox;
    QSpacerItem *horizontalSpacer_5;
    QSpacerItem *verticalSpacer_11;
    QWidget *Page7;
    QGridLayout *gridLayout_13;
    QSpacerItem *verticalSpacer_14;
    QLabel *Page7Label1;
    QSpacerItem *verticalSpacer_15;
    QGridLayout *gridLayout_11;
    QSpacerItem *horizontalSpacer_6;
    QPushButton *Page7PushButton1;
    QSpacerItem *horizontalSpacer_7;
    QPushButton *Page7PushButton2;
    QSpacerItem *horizontalSpacer_8;
    QSpacerItem *verticalSpacer_16;
    QGridLayout *gridLayout_12;
    QSpacerItem *horizontalSpacer_9;
    QCheckBox *Page7CheckBox;
    QSpacerItem *horizontalSpacer_10;
    QSpacerItem *verticalSpacer_17;
    QWidget *Page8;
    QGridLayout *gridLayout_18;
    QSpacerItem *verticalSpacer_18;
    QLabel *Page8Label1;
    QSpacerItem *verticalSpacer_19;
    QGridLayout *gridLayout_14;
    QSpacerItem *horizontalSpacer_17;
    QProgressBar *Page8ProgressBar;
    QSpacerItem *horizontalSpacer_16;
    QSpacerItem *verticalSpacer_20;
    QLabel *Page8Label2;
    QSpacerItem *verticalSpacer_21;
    QWidget *Page9;
    QGridLayout *gridLayout_17;
    QSpacerItem *verticalSpacer_22;
    QLabel *Page9Label1;
    QSpacerItem *verticalSpacer_23;
    QGridLayout *gridLayout_15;
    QSpacerItem *horizontalSpacer_11;
    QPushButton *Page9PushButton1;
    QSpacerItem *horizontalSpacer_12;
    QPushButton *Page9PushButton2;
    QSpacerItem *horizontalSpacer_13;
    QSpacerItem *verticalSpacer_24;
    QGridLayout *gridLayout_16;
    QSpacerItem *horizontalSpacer_14;
    QCheckBox *Page9CheckBox;
    QSpacerItem *horizontalSpacer_15;
    QSpacerItem *verticalSpacer_25;
    QWidget *PageA;
    QGridLayout *gridLayout_39;
    QSpacerItem *verticalSpacer_51;
    QLabel *PageALabel1;
    QSpacerItem *verticalSpacer_52;
    QGridLayout *gridLayout_38;
    QSpacerItem *horizontalSpacer_40;
    QPushButton *PageAPushButton1;
    QSpacerItem *horizontalSpacer_41;
    QPushButton *PageAPushButton2;
    QSpacerItem *horizontalSpacer_42;
    QSpacerItem *verticalSpacer_53;
    QWidget *PageB;
    QGridLayout *gridLayout_19;
    QSpacerItem *verticalSpacer_26;
    QLabel *PageBLabel;
    QSpacerItem *verticalSpacer_27;
    QGridLayout *gridLayout_40;
    QSpacerItem *horizontalSpacer_45;
    QPushButton *PageBPushButton;
    QSpacerItem *horizontalSpacer_43;
    QSpacerItem *verticalSpacer_28;
    QWidget *PageC;
    QGridLayout *gridLayout_20;
    QSpacerItem *verticalSpacer_29;
    QLabel *PageCLabel;
    QSpacerItem *verticalSpacer_30;
    QGridLayout *gridLayout_41;
    QSpacerItem *horizontalSpacer_46;
    QPushButton *PageCPushButton;
    QSpacerItem *horizontalSpacer_44;
    QSpacerItem *verticalSpacer_31;

    void setupUi(QWidget *DeviceConnectorViewS1M1)
    {
        if (DeviceConnectorViewS1M1->objectName().isEmpty())
            DeviceConnectorViewS1M1->setObjectName("DeviceConnectorViewS1M1");
        DeviceConnectorViewS1M1->resize(300, 200);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(DeviceConnectorViewS1M1->sizePolicy().hasHeightForWidth());
        DeviceConnectorViewS1M1->setSizePolicy(sizePolicy);
        DeviceConnectorViewS1M1->setMinimumSize(QSize(30, 20));
        gridLayout = new QGridLayout(DeviceConnectorViewS1M1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        stackedWidget = new QStackedWidget(DeviceConnectorViewS1M1);
        stackedWidget->setObjectName("stackedWidget");
        sizePolicy.setHeightForWidth(stackedWidget->sizePolicy().hasHeightForWidth());
        stackedWidget->setSizePolicy(sizePolicy);
        stackedWidget->setLineWidth(0);
        Page0 = new QWidget();
        Page0->setObjectName("Page0");
        sizePolicy.setHeightForWidth(Page0->sizePolicy().hasHeightForWidth());
        Page0->setSizePolicy(sizePolicy);
        gridLayout_2 = new QGridLayout(Page0);
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName("gridLayout_2");
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        verticalSpacerTop = new QSpacerItem(20, 127, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_2->addItem(verticalSpacerTop, 0, 0, 1, 1);

        Page0Label1 = new QLabel(Page0);
        Page0Label1->setObjectName("Page0Label1");
        sizePolicy.setHeightForWidth(Page0Label1->sizePolicy().hasHeightForWidth());
        Page0Label1->setSizePolicy(sizePolicy);
        Page0Label1->setLineWidth(0);
        Page0Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_2->addWidget(Page0Label1, 1, 0, 1, 1);

        verticalSpacerBottom = new QSpacerItem(20, 127, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_2->addItem(verticalSpacerBottom, 2, 0, 1, 1);

        gridLayout_2->setRowStretch(0, 9);
        gridLayout_2->setRowStretch(1, 3);
        gridLayout_2->setRowStretch(2, 12);
        stackedWidget->addWidget(Page0);
        Page1 = new QWidget();
        Page1->setObjectName("Page1");
        sizePolicy.setHeightForWidth(Page1->sizePolicy().hasHeightForWidth());
        Page1->setSizePolicy(sizePolicy);
        gridLayout_3 = new QGridLayout(Page1);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName("gridLayout_3");
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        verticalSpacerTop_2 = new QSpacerItem(20, 114, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacerTop_2, 0, 0, 1, 1);

        Page1Label1 = new QLabel(Page1);
        Page1Label1->setObjectName("Page1Label1");
        sizePolicy.setHeightForWidth(Page1Label1->sizePolicy().hasHeightForWidth());
        Page1Label1->setSizePolicy(sizePolicy);
        Page1Label1->setLineWidth(0);
        Page1Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_3->addWidget(Page1Label1, 1, 0, 1, 1);

        verticalSpacerBottom_2 = new QSpacerItem(20, 114, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacerBottom_2, 2, 0, 1, 1);

        gridLayout_3->setRowStretch(0, 9);
        gridLayout_3->setRowStretch(1, 3);
        gridLayout_3->setRowStretch(2, 12);
        stackedWidget->addWidget(Page1);
        Page2 = new QWidget();
        Page2->setObjectName("Page2");
        sizePolicy.setHeightForWidth(Page2->sizePolicy().hasHeightForWidth());
        Page2->setSizePolicy(sizePolicy);
        gridLayout_4 = new QGridLayout(Page2);
        gridLayout_4->setSpacing(0);
        gridLayout_4->setObjectName("gridLayout_4");
        gridLayout_4->setContentsMargins(0, 0, 0, 0);
        verticalSpacer = new QSpacerItem(20, 72, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_4->addItem(verticalSpacer, 0, 0, 1, 1);

        Page2Label1 = new QLabel(Page2);
        Page2Label1->setObjectName("Page2Label1");
        sizePolicy.setHeightForWidth(Page2Label1->sizePolicy().hasHeightForWidth());
        Page2Label1->setSizePolicy(sizePolicy);
        Page2Label1->setLineWidth(0);
        Page2Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_4->addWidget(Page2Label1, 1, 0, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 97, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_4->addItem(verticalSpacer_2, 2, 0, 1, 1);

        gridLayout_4->setRowStretch(0, 9);
        gridLayout_4->setRowStretch(1, 3);
        gridLayout_4->setRowStretch(2, 12);
        stackedWidget->addWidget(Page2);
        Page3 = new QWidget();
        Page3->setObjectName("Page3");
        sizePolicy.setHeightForWidth(Page3->sizePolicy().hasHeightForWidth());
        Page3->setSizePolicy(sizePolicy);
        gridLayout_5 = new QGridLayout(Page3);
        gridLayout_5->setSpacing(0);
        gridLayout_5->setObjectName("gridLayout_5");
        gridLayout_5->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_3 = new QSpacerItem(20, 72, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_5->addItem(verticalSpacer_3, 0, 0, 1, 1);

        Page3Label1 = new QLabel(Page3);
        Page3Label1->setObjectName("Page3Label1");
        sizePolicy.setHeightForWidth(Page3Label1->sizePolicy().hasHeightForWidth());
        Page3Label1->setSizePolicy(sizePolicy);
        Page3Label1->setLineWidth(0);
        Page3Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_5->addWidget(Page3Label1, 1, 0, 1, 1);

        verticalSpacer_4 = new QSpacerItem(20, 97, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_5->addItem(verticalSpacer_4, 2, 0, 1, 1);

        gridLayout_5->setRowStretch(0, 9);
        gridLayout_5->setRowStretch(1, 3);
        gridLayout_5->setRowStretch(2, 12);
        stackedWidget->addWidget(Page3);
        Page4 = new QWidget();
        Page4->setObjectName("Page4");
        sizePolicy.setHeightForWidth(Page4->sizePolicy().hasHeightForWidth());
        Page4->setSizePolicy(sizePolicy);
        gridLayout_6 = new QGridLayout(Page4);
        gridLayout_6->setSpacing(0);
        gridLayout_6->setObjectName("gridLayout_6");
        gridLayout_6->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_5 = new QSpacerItem(20, 72, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_6->addItem(verticalSpacer_5, 0, 0, 1, 1);

        Page4Label1 = new QLabel(Page4);
        Page4Label1->setObjectName("Page4Label1");
        sizePolicy.setHeightForWidth(Page4Label1->sizePolicy().hasHeightForWidth());
        Page4Label1->setSizePolicy(sizePolicy);
        Page4Label1->setLineWidth(0);
        Page4Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_6->addWidget(Page4Label1, 1, 0, 1, 1);

        verticalSpacer_6 = new QSpacerItem(20, 97, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_6->addItem(verticalSpacer_6, 2, 0, 1, 1);

        gridLayout_6->setRowStretch(0, 9);
        gridLayout_6->setRowStretch(1, 3);
        gridLayout_6->setRowStretch(2, 12);
        stackedWidget->addWidget(Page4);
        Page5 = new QWidget();
        Page5->setObjectName("Page5");
        sizePolicy.setHeightForWidth(Page5->sizePolicy().hasHeightForWidth());
        Page5->setSizePolicy(sizePolicy);
        gridLayout_10 = new QGridLayout(Page5);
        gridLayout_10->setSpacing(0);
        gridLayout_10->setObjectName("gridLayout_10");
        gridLayout_10->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_12 = new QSpacerItem(20, 72, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_10->addItem(verticalSpacer_12, 0, 0, 1, 1);

        Page5Label1 = new QLabel(Page5);
        Page5Label1->setObjectName("Page5Label1");
        sizePolicy.setHeightForWidth(Page5Label1->sizePolicy().hasHeightForWidth());
        Page5Label1->setSizePolicy(sizePolicy);
        Page5Label1->setLineWidth(0);
        Page5Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_10->addWidget(Page5Label1, 1, 0, 1, 1);

        verticalSpacer_13 = new QSpacerItem(20, 97, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_10->addItem(verticalSpacer_13, 2, 0, 1, 1);

        gridLayout_10->setRowStretch(0, 9);
        gridLayout_10->setRowStretch(1, 3);
        gridLayout_10->setRowStretch(2, 12);
        stackedWidget->addWidget(Page5);
        Page6 = new QWidget();
        Page6->setObjectName("Page6");
        sizePolicy.setHeightForWidth(Page6->sizePolicy().hasHeightForWidth());
        Page6->setSizePolicy(sizePolicy);
        gridLayout_9 = new QGridLayout(Page6);
        gridLayout_9->setSpacing(0);
        gridLayout_9->setObjectName("gridLayout_9");
        gridLayout_9->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_7 = new QSpacerItem(20, 22, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_9->addItem(verticalSpacer_7, 0, 0, 1, 1);

        Page6Label1 = new QLabel(Page6);
        Page6Label1->setObjectName("Page6Label1");
        sizePolicy.setHeightForWidth(Page6Label1->sizePolicy().hasHeightForWidth());
        Page6Label1->setSizePolicy(sizePolicy);
        Page6Label1->setLineWidth(0);
        Page6Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_9->addWidget(Page6Label1, 1, 0, 1, 1);

        verticalSpacer_8 = new QSpacerItem(20, 22, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_9->addItem(verticalSpacer_8, 2, 0, 1, 1);

        Page6Label2 = new QLabel(Page6);
        Page6Label2->setObjectName("Page6Label2");
        sizePolicy.setHeightForWidth(Page6Label2->sizePolicy().hasHeightForWidth());
        Page6Label2->setSizePolicy(sizePolicy);
        Page6Label2->setLineWidth(0);
        Page6Label2->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_9->addWidget(Page6Label2, 3, 0, 1, 1);

        verticalSpacer_9 = new QSpacerItem(20, 21, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_9->addItem(verticalSpacer_9, 4, 0, 1, 1);

        gridLayout_7 = new QGridLayout();
        gridLayout_7->setSpacing(0);
        gridLayout_7->setObjectName("gridLayout_7");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_7->addItem(horizontalSpacer, 0, 0, 1, 1);

        Page6PushButton2 = new QPushButton(Page6);
        Page6PushButton2->setObjectName("Page6PushButton2");
        sizePolicy.setHeightForWidth(Page6PushButton2->sizePolicy().hasHeightForWidth());
        Page6PushButton2->setSizePolicy(sizePolicy);

        gridLayout_7->addWidget(Page6PushButton2, 0, 3, 1, 1);

        Page6PushButton1 = new QPushButton(Page6);
        Page6PushButton1->setObjectName("Page6PushButton1");
        sizePolicy.setHeightForWidth(Page6PushButton1->sizePolicy().hasHeightForWidth());
        Page6PushButton1->setSizePolicy(sizePolicy);

        gridLayout_7->addWidget(Page6PushButton1, 0, 1, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_7->addItem(horizontalSpacer_2, 0, 2, 1, 1);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_7->addItem(horizontalSpacer_3, 0, 4, 1, 1);

        gridLayout_7->setColumnStretch(0, 13);
        gridLayout_7->setColumnStretch(1, 8);
        gridLayout_7->setColumnStretch(2, 1);
        gridLayout_7->setColumnStretch(3, 8);
        gridLayout_7->setColumnStretch(4, 13);

        gridLayout_9->addLayout(gridLayout_7, 5, 0, 1, 1);

        verticalSpacer_10 = new QSpacerItem(20, 22, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_9->addItem(verticalSpacer_10, 6, 0, 1, 1);

        gridLayout_8 = new QGridLayout();
        gridLayout_8->setSpacing(0);
        gridLayout_8->setObjectName("gridLayout_8");
        horizontalSpacer_4 = new QSpacerItem(13, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_8->addItem(horizontalSpacer_4, 0, 0, 1, 1);

        Page6CheckBox = new QCheckBox(Page6);
        Page6CheckBox->setObjectName("Page6CheckBox");
        sizePolicy.setHeightForWidth(Page6CheckBox->sizePolicy().hasHeightForWidth());
        Page6CheckBox->setSizePolicy(sizePolicy);

        gridLayout_8->addWidget(Page6CheckBox, 0, 1, 1, 1);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_8->addItem(horizontalSpacer_5, 0, 2, 1, 1);

        gridLayout_8->setColumnStretch(0, 2);
        gridLayout_8->setColumnStretch(1, 5);
        gridLayout_8->setColumnStretch(2, 10);

        gridLayout_9->addLayout(gridLayout_8, 7, 0, 1, 1);

        verticalSpacer_11 = new QSpacerItem(20, 22, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_9->addItem(verticalSpacer_11, 8, 0, 1, 1);

        gridLayout_9->setRowStretch(0, 13);
        gridLayout_9->setRowStretch(1, 5);
        gridLayout_9->setRowStretch(2, 1);
        gridLayout_9->setRowStretch(3, 5);
        gridLayout_9->setRowStretch(4, 5);
        gridLayout_9->setRowStretch(5, 7);
        gridLayout_9->setRowStretch(6, 12);
        gridLayout_9->setRowStretch(7, 5);
        gridLayout_9->setRowStretch(8, 10);
        stackedWidget->addWidget(Page6);
        Page7 = new QWidget();
        Page7->setObjectName("Page7");
        sizePolicy.setHeightForWidth(Page7->sizePolicy().hasHeightForWidth());
        Page7->setSizePolicy(sizePolicy);
        gridLayout_13 = new QGridLayout(Page7);
        gridLayout_13->setSpacing(0);
        gridLayout_13->setObjectName("gridLayout_13");
        gridLayout_13->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_14 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_13->addItem(verticalSpacer_14, 0, 0, 1, 1);

        Page7Label1 = new QLabel(Page7);
        Page7Label1->setObjectName("Page7Label1");
        sizePolicy.setHeightForWidth(Page7Label1->sizePolicy().hasHeightForWidth());
        Page7Label1->setSizePolicy(sizePolicy);
        Page7Label1->setLineWidth(0);
        Page7Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_13->addWidget(Page7Label1, 1, 0, 1, 1);

        verticalSpacer_15 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_13->addItem(verticalSpacer_15, 2, 0, 1, 1);

        gridLayout_11 = new QGridLayout();
        gridLayout_11->setSpacing(0);
        gridLayout_11->setObjectName("gridLayout_11");
        horizontalSpacer_6 = new QSpacerItem(13, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_11->addItem(horizontalSpacer_6, 0, 0, 1, 1);

        Page7PushButton1 = new QPushButton(Page7);
        Page7PushButton1->setObjectName("Page7PushButton1");
        sizePolicy.setHeightForWidth(Page7PushButton1->sizePolicy().hasHeightForWidth());
        Page7PushButton1->setSizePolicy(sizePolicy);

        gridLayout_11->addWidget(Page7PushButton1, 0, 1, 1, 1);

        horizontalSpacer_7 = new QSpacerItem(13, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_11->addItem(horizontalSpacer_7, 0, 2, 1, 1);

        Page7PushButton2 = new QPushButton(Page7);
        Page7PushButton2->setObjectName("Page7PushButton2");
        sizePolicy.setHeightForWidth(Page7PushButton2->sizePolicy().hasHeightForWidth());
        Page7PushButton2->setSizePolicy(sizePolicy);

        gridLayout_11->addWidget(Page7PushButton2, 0, 3, 1, 1);

        horizontalSpacer_8 = new QSpacerItem(13, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_11->addItem(horizontalSpacer_8, 0, 4, 1, 1);

        gridLayout_11->setColumnStretch(0, 13);
        gridLayout_11->setColumnStretch(1, 8);
        gridLayout_11->setColumnStretch(2, 1);
        gridLayout_11->setColumnStretch(3, 8);
        gridLayout_11->setColumnStretch(4, 13);

        gridLayout_13->addLayout(gridLayout_11, 3, 0, 1, 1);

        verticalSpacer_16 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_13->addItem(verticalSpacer_16, 4, 0, 1, 1);

        gridLayout_12 = new QGridLayout();
        gridLayout_12->setSpacing(0);
        gridLayout_12->setObjectName("gridLayout_12");
        horizontalSpacer_9 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_12->addItem(horizontalSpacer_9, 0, 0, 1, 1);

        Page7CheckBox = new QCheckBox(Page7);
        Page7CheckBox->setObjectName("Page7CheckBox");
        sizePolicy.setHeightForWidth(Page7CheckBox->sizePolicy().hasHeightForWidth());
        Page7CheckBox->setSizePolicy(sizePolicy);

        gridLayout_12->addWidget(Page7CheckBox, 0, 1, 1, 1);

        horizontalSpacer_10 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_12->addItem(horizontalSpacer_10, 0, 2, 1, 1);

        gridLayout_12->setColumnStretch(0, 2);
        gridLayout_12->setColumnStretch(1, 5);
        gridLayout_12->setColumnStretch(2, 10);

        gridLayout_13->addLayout(gridLayout_12, 5, 0, 1, 1);

        verticalSpacer_17 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_13->addItem(verticalSpacer_17, 6, 0, 1, 1);

        gridLayout_13->setRowStretch(0, 19);
        gridLayout_13->setRowStretch(1, 5);
        gridLayout_13->setRowStretch(2, 5);
        gridLayout_13->setRowStretch(3, 7);
        gridLayout_13->setRowStretch(4, 12);
        gridLayout_13->setRowStretch(5, 5);
        gridLayout_13->setRowStretch(6, 10);
        stackedWidget->addWidget(Page7);
        Page8 = new QWidget();
        Page8->setObjectName("Page8");
        sizePolicy.setHeightForWidth(Page8->sizePolicy().hasHeightForWidth());
        Page8->setSizePolicy(sizePolicy);
        gridLayout_18 = new QGridLayout(Page8);
        gridLayout_18->setSpacing(0);
        gridLayout_18->setObjectName("gridLayout_18");
        gridLayout_18->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_18 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_18->addItem(verticalSpacer_18, 0, 1, 1, 1);

        Page8Label1 = new QLabel(Page8);
        Page8Label1->setObjectName("Page8Label1");
        sizePolicy.setHeightForWidth(Page8Label1->sizePolicy().hasHeightForWidth());
        Page8Label1->setSizePolicy(sizePolicy);
        Page8Label1->setLineWidth(0);
        Page8Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_18->addWidget(Page8Label1, 1, 0, 1, 2);

        verticalSpacer_19 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_18->addItem(verticalSpacer_19, 2, 0, 1, 1);

        gridLayout_14 = new QGridLayout();
        gridLayout_14->setSpacing(0);
        gridLayout_14->setObjectName("gridLayout_14");
        horizontalSpacer_17 = new QSpacerItem(17, 5, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_14->addItem(horizontalSpacer_17, 0, 0, 1, 1);

        Page8ProgressBar = new QProgressBar(Page8);
        Page8ProgressBar->setObjectName("Page8ProgressBar");
        sizePolicy.setHeightForWidth(Page8ProgressBar->sizePolicy().hasHeightForWidth());
        Page8ProgressBar->setSizePolicy(sizePolicy);
        Page8ProgressBar->setMinimumSize(QSize(1, 1));
        Page8ProgressBar->setValue(0);
        Page8ProgressBar->setAlignment(Qt::AlignmentFlag::AlignCenter);
        Page8ProgressBar->setTextVisible(false);

        gridLayout_14->addWidget(Page8ProgressBar, 0, 1, 1, 1);

        horizontalSpacer_16 = new QSpacerItem(17, 5, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_14->addItem(horizontalSpacer_16, 0, 2, 1, 1);

        gridLayout_14->setColumnStretch(0, 10);
        gridLayout_14->setColumnStretch(1, 25);
        gridLayout_14->setColumnStretch(2, 10);

        gridLayout_18->addLayout(gridLayout_14, 3, 0, 1, 2);

        verticalSpacer_20 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_18->addItem(verticalSpacer_20, 4, 0, 1, 1);

        Page8Label2 = new QLabel(Page8);
        Page8Label2->setObjectName("Page8Label2");
        sizePolicy.setHeightForWidth(Page8Label2->sizePolicy().hasHeightForWidth());
        Page8Label2->setSizePolicy(sizePolicy);
        Page8Label2->setLineWidth(0);
        Page8Label2->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_18->addWidget(Page8Label2, 5, 0, 1, 2);

        verticalSpacer_21 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_18->addItem(verticalSpacer_21, 6, 0, 1, 1);

        gridLayout_18->setRowStretch(0, 25);
        gridLayout_18->setRowStretch(1, 10);
        gridLayout_18->setRowStretch(2, 6);
        gridLayout_18->setRowStretch(3, 1);
        gridLayout_18->setRowStretch(4, 4);
        gridLayout_18->setRowStretch(5, 10);
        gridLayout_18->setRowStretch(6, 40);
        stackedWidget->addWidget(Page8);
        Page9 = new QWidget();
        Page9->setObjectName("Page9");
        sizePolicy.setHeightForWidth(Page9->sizePolicy().hasHeightForWidth());
        Page9->setSizePolicy(sizePolicy);
        gridLayout_17 = new QGridLayout(Page9);
        gridLayout_17->setSpacing(0);
        gridLayout_17->setObjectName("gridLayout_17");
        gridLayout_17->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_22 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_17->addItem(verticalSpacer_22, 0, 0, 1, 1);

        Page9Label1 = new QLabel(Page9);
        Page9Label1->setObjectName("Page9Label1");
        sizePolicy.setHeightForWidth(Page9Label1->sizePolicy().hasHeightForWidth());
        Page9Label1->setSizePolicy(sizePolicy);
        Page9Label1->setLineWidth(0);
        Page9Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_17->addWidget(Page9Label1, 1, 0, 1, 1);

        verticalSpacer_23 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_17->addItem(verticalSpacer_23, 2, 0, 1, 1);

        gridLayout_15 = new QGridLayout();
        gridLayout_15->setSpacing(0);
        gridLayout_15->setObjectName("gridLayout_15");
        horizontalSpacer_11 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_15->addItem(horizontalSpacer_11, 0, 0, 1, 1);

        Page9PushButton1 = new QPushButton(Page9);
        Page9PushButton1->setObjectName("Page9PushButton1");
        sizePolicy.setHeightForWidth(Page9PushButton1->sizePolicy().hasHeightForWidth());
        Page9PushButton1->setSizePolicy(sizePolicy);

        gridLayout_15->addWidget(Page9PushButton1, 0, 1, 1, 1);

        horizontalSpacer_12 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_15->addItem(horizontalSpacer_12, 0, 2, 1, 1);

        Page9PushButton2 = new QPushButton(Page9);
        Page9PushButton2->setObjectName("Page9PushButton2");
        sizePolicy.setHeightForWidth(Page9PushButton2->sizePolicy().hasHeightForWidth());
        Page9PushButton2->setSizePolicy(sizePolicy);

        gridLayout_15->addWidget(Page9PushButton2, 0, 3, 1, 1);

        horizontalSpacer_13 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_15->addItem(horizontalSpacer_13, 0, 4, 1, 1);

        gridLayout_15->setColumnStretch(0, 13);
        gridLayout_15->setColumnStretch(1, 8);
        gridLayout_15->setColumnStretch(2, 1);
        gridLayout_15->setColumnStretch(3, 8);
        gridLayout_15->setColumnStretch(4, 13);

        gridLayout_17->addLayout(gridLayout_15, 3, 0, 1, 1);

        verticalSpacer_24 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_17->addItem(verticalSpacer_24, 4, 0, 1, 1);

        gridLayout_16 = new QGridLayout();
        gridLayout_16->setSpacing(0);
        gridLayout_16->setObjectName("gridLayout_16");
        horizontalSpacer_14 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_16->addItem(horizontalSpacer_14, 0, 0, 1, 1);

        Page9CheckBox = new QCheckBox(Page9);
        Page9CheckBox->setObjectName("Page9CheckBox");
        sizePolicy.setHeightForWidth(Page9CheckBox->sizePolicy().hasHeightForWidth());
        Page9CheckBox->setSizePolicy(sizePolicy);

        gridLayout_16->addWidget(Page9CheckBox, 0, 1, 1, 1);

        horizontalSpacer_15 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_16->addItem(horizontalSpacer_15, 0, 2, 1, 1);

        gridLayout_16->setColumnStretch(0, 2);
        gridLayout_16->setColumnStretch(1, 5);
        gridLayout_16->setColumnStretch(2, 10);

        gridLayout_17->addLayout(gridLayout_16, 5, 0, 1, 1);

        verticalSpacer_25 = new QSpacerItem(20, 26, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_17->addItem(verticalSpacer_25, 6, 0, 1, 1);

        gridLayout_17->setRowStretch(0, 19);
        gridLayout_17->setRowStretch(1, 5);
        gridLayout_17->setRowStretch(2, 5);
        gridLayout_17->setRowStretch(3, 7);
        gridLayout_17->setRowStretch(4, 12);
        gridLayout_17->setRowStretch(5, 5);
        gridLayout_17->setRowStretch(6, 10);
        stackedWidget->addWidget(Page9);
        PageA = new QWidget();
        PageA->setObjectName("PageA");
        sizePolicy.setHeightForWidth(PageA->sizePolicy().hasHeightForWidth());
        PageA->setSizePolicy(sizePolicy);
        gridLayout_39 = new QGridLayout(PageA);
        gridLayout_39->setSpacing(0);
        gridLayout_39->setObjectName("gridLayout_39");
        gridLayout_39->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_51 = new QSpacerItem(20, 37, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_39->addItem(verticalSpacer_51, 0, 0, 1, 1);

        PageALabel1 = new QLabel(PageA);
        PageALabel1->setObjectName("PageALabel1");
        sizePolicy.setHeightForWidth(PageALabel1->sizePolicy().hasHeightForWidth());
        PageALabel1->setSizePolicy(sizePolicy);
        PageALabel1->setLineWidth(0);
        PageALabel1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_39->addWidget(PageALabel1, 1, 0, 1, 1);

        verticalSpacer_52 = new QSpacerItem(20, 37, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_39->addItem(verticalSpacer_52, 2, 0, 1, 1);

        gridLayout_38 = new QGridLayout();
        gridLayout_38->setSpacing(0);
        gridLayout_38->setObjectName("gridLayout_38");
        horizontalSpacer_40 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_38->addItem(horizontalSpacer_40, 0, 0, 1, 1);

        PageAPushButton1 = new QPushButton(PageA);
        PageAPushButton1->setObjectName("PageAPushButton1");
        sizePolicy.setHeightForWidth(PageAPushButton1->sizePolicy().hasHeightForWidth());
        PageAPushButton1->setSizePolicy(sizePolicy);

        gridLayout_38->addWidget(PageAPushButton1, 0, 1, 1, 1);

        horizontalSpacer_41 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_38->addItem(horizontalSpacer_41, 0, 2, 1, 1);

        PageAPushButton2 = new QPushButton(PageA);
        PageAPushButton2->setObjectName("PageAPushButton2");
        sizePolicy.setHeightForWidth(PageAPushButton2->sizePolicy().hasHeightForWidth());
        PageAPushButton2->setSizePolicy(sizePolicy);

        gridLayout_38->addWidget(PageAPushButton2, 0, 3, 1, 1);

        horizontalSpacer_42 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_38->addItem(horizontalSpacer_42, 0, 4, 1, 1);

        gridLayout_38->setColumnStretch(0, 13);
        gridLayout_38->setColumnStretch(1, 8);
        gridLayout_38->setColumnStretch(2, 1);
        gridLayout_38->setColumnStretch(3, 8);
        gridLayout_38->setColumnStretch(4, 13);

        gridLayout_39->addLayout(gridLayout_38, 3, 0, 1, 1);

        verticalSpacer_53 = new QSpacerItem(20, 37, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_39->addItem(verticalSpacer_53, 4, 0, 1, 1);

        gridLayout_39->setRowStretch(0, 19);
        gridLayout_39->setRowStretch(1, 5);
        gridLayout_39->setRowStretch(2, 5);
        gridLayout_39->setRowStretch(3, 7);
        gridLayout_39->setRowStretch(4, 27);
        stackedWidget->addWidget(PageA);
        PageB = new QWidget();
        PageB->setObjectName("PageB");
        sizePolicy.setHeightForWidth(PageB->sizePolicy().hasHeightForWidth());
        PageB->setSizePolicy(sizePolicy);
        gridLayout_19 = new QGridLayout(PageB);
        gridLayout_19->setSpacing(0);
        gridLayout_19->setObjectName("gridLayout_19");
        gridLayout_19->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_26 = new QSpacerItem(20, 56, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_19->addItem(verticalSpacer_26, 0, 0, 1, 1);

        PageBLabel = new QLabel(PageB);
        PageBLabel->setObjectName("PageBLabel");
        sizePolicy.setHeightForWidth(PageBLabel->sizePolicy().hasHeightForWidth());
        PageBLabel->setSizePolicy(sizePolicy);
        PageBLabel->setLineWidth(0);
        PageBLabel->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_19->addWidget(PageBLabel, 1, 0, 1, 1);

        verticalSpacer_27 = new QSpacerItem(20, 13, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_19->addItem(verticalSpacer_27, 2, 0, 1, 1);

        gridLayout_40 = new QGridLayout();
        gridLayout_40->setSpacing(0);
        gridLayout_40->setObjectName("gridLayout_40");
        horizontalSpacer_45 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_40->addItem(horizontalSpacer_45, 0, 2, 1, 1);

        PageBPushButton = new QPushButton(PageB);
        PageBPushButton->setObjectName("PageBPushButton");
        sizePolicy.setHeightForWidth(PageBPushButton->sizePolicy().hasHeightForWidth());
        PageBPushButton->setSizePolicy(sizePolicy);

        gridLayout_40->addWidget(PageBPushButton, 0, 1, 1, 1);

        horizontalSpacer_43 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_40->addItem(horizontalSpacer_43, 0, 0, 1, 1);

        gridLayout_40->setColumnStretch(0, 100);
        gridLayout_40->setColumnStretch(1, 40);
        gridLayout_40->setColumnStretch(2, 100);

        gridLayout_19->addLayout(gridLayout_40, 3, 0, 1, 1);

        verticalSpacer_28 = new QSpacerItem(20, 81, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_19->addItem(verticalSpacer_28, 4, 0, 1, 1);

        gridLayout_19->setRowStretch(0, 19);
        gridLayout_19->setRowStretch(1, 5);
        gridLayout_19->setRowStretch(2, 5);
        gridLayout_19->setRowStretch(3, 7);
        gridLayout_19->setRowStretch(4, 27);
        stackedWidget->addWidget(PageB);
        PageC = new QWidget();
        PageC->setObjectName("PageC");
        sizePolicy.setHeightForWidth(PageC->sizePolicy().hasHeightForWidth());
        PageC->setSizePolicy(sizePolicy);
        gridLayout_20 = new QGridLayout(PageC);
        gridLayout_20->setSpacing(0);
        gridLayout_20->setObjectName("gridLayout_20");
        gridLayout_20->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_29 = new QSpacerItem(20, 56, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_20->addItem(verticalSpacer_29, 0, 0, 1, 1);

        PageCLabel = new QLabel(PageC);
        PageCLabel->setObjectName("PageCLabel");
        sizePolicy.setHeightForWidth(PageCLabel->sizePolicy().hasHeightForWidth());
        PageCLabel->setSizePolicy(sizePolicy);
        PageCLabel->setLineWidth(0);
        PageCLabel->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_20->addWidget(PageCLabel, 1, 0, 1, 1);

        verticalSpacer_30 = new QSpacerItem(20, 13, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_20->addItem(verticalSpacer_30, 2, 0, 1, 1);

        gridLayout_41 = new QGridLayout();
        gridLayout_41->setSpacing(0);
        gridLayout_41->setObjectName("gridLayout_41");
        horizontalSpacer_46 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_41->addItem(horizontalSpacer_46, 0, 2, 1, 1);

        PageCPushButton = new QPushButton(PageC);
        PageCPushButton->setObjectName("PageCPushButton");
        sizePolicy.setHeightForWidth(PageCPushButton->sizePolicy().hasHeightForWidth());
        PageCPushButton->setSizePolicy(sizePolicy);

        gridLayout_41->addWidget(PageCPushButton, 0, 1, 1, 1);

        horizontalSpacer_44 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_41->addItem(horizontalSpacer_44, 0, 0, 1, 1);

        gridLayout_41->setColumnStretch(0, 100);
        gridLayout_41->setColumnStretch(1, 40);
        gridLayout_41->setColumnStretch(2, 100);

        gridLayout_20->addLayout(gridLayout_41, 3, 0, 1, 1);

        verticalSpacer_31 = new QSpacerItem(20, 81, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_20->addItem(verticalSpacer_31, 4, 0, 1, 1);

        gridLayout_20->setRowStretch(0, 19);
        gridLayout_20->setRowStretch(1, 5);
        gridLayout_20->setRowStretch(2, 5);
        gridLayout_20->setRowStretch(3, 7);
        gridLayout_20->setRowStretch(4, 27);
        stackedWidget->addWidget(PageC);

        gridLayout->addWidget(stackedWidget, 0, 0, 1, 1);


        retranslateUi(DeviceConnectorViewS1M1);

        QMetaObject::connectSlotsByName(DeviceConnectorViewS1M1);
    } // setupUi

    void retranslateUi(QWidget *DeviceConnectorViewS1M1)
    {
        DeviceConnectorViewS1M1->setWindowTitle(QCoreApplication::translate("DeviceConnectorViewS1M1", "Form", nullptr));
        Page0Label1->setText(QString());
        Page1Label1->setText(QString());
        Page2Label1->setText(QString());
        Page3Label1->setText(QString());
        Page4Label1->setText(QString());
        Page5Label1->setText(QString());
        Page6Label1->setText(QString());
        Page6Label2->setText(QString());
        Page6PushButton2->setText(QString());
        Page6PushButton1->setText(QString());
        Page6CheckBox->setText(QString());
        Page7Label1->setText(QString());
        Page7PushButton1->setText(QString());
        Page7PushButton2->setText(QString());
        Page7CheckBox->setText(QString());
        Page8Label1->setText(QString());
        Page8Label2->setText(QString());
        Page9Label1->setText(QString());
        Page9PushButton1->setText(QString());
        Page9PushButton2->setText(QString());
        Page9CheckBox->setText(QString());
        PageALabel1->setText(QString());
        PageAPushButton1->setText(QString());
        PageAPushButton2->setText(QString());
        PageBLabel->setText(QString());
        PageBPushButton->setText(QString());
        PageCLabel->setText(QString());
        PageCPushButton->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class DeviceConnectorViewS1M1: public Ui_DeviceConnectorViewS1M1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_DEVICECONNECTORVIEWS1M1_H

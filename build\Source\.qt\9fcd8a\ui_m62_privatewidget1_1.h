/********************************************************************************
** Form generated from reading UI file 'm62_privatewidget1_1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_M62_PRIVATEWIDGET1_1_H
#define UI_M62_PRIVATEWIDGET1_1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <volumemeters1m8.h>

QT_BEGIN_NAMESPACE

class Ui_M62_PrivateWidget1_1
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    VolumeMeterS1M8 *widgetVolumeMeter;
    QLineEdit *lineEdit;

    void setupUi(QWidget *M62_PrivateWidget1_1)
    {
        if (M62_PrivateWidget1_1->objectName().isEmpty())
            M62_PrivateWidget1_1->setObjectName("M62_PrivateWidget1_1");
        M62_PrivateWidget1_1->resize(140, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(M62_PrivateWidget1_1->sizePolicy().hasHeightForWidth());
        M62_PrivateWidget1_1->setSizePolicy(sizePolicy);
        M62_PrivateWidget1_1->setMinimumSize(QSize(80, 220));
        gridLayout = new QGridLayout(M62_PrivateWidget1_1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(M62_PrivateWidget1_1);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::NoFrame);
        frame->setFrameShadow(QFrame::Shadow::Plain);
        frame->setLineWidth(0);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 21));
        widgetVolumeMeter = new VolumeMeterS1M8(frame);
        widgetVolumeMeter->setObjectName("widgetVolumeMeter");
        widgetVolumeMeter->setGeometry(QRect(10, 40, 91, 231));
        sizePolicy.setHeightForWidth(widgetVolumeMeter->sizePolicy().hasHeightForWidth());
        widgetVolumeMeter->setSizePolicy(sizePolicy);
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(10, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(M62_PrivateWidget1_1);

        QMetaObject::connectSlotsByName(M62_PrivateWidget1_1);
    } // setupUi

    void retranslateUi(QWidget *M62_PrivateWidget1_1)
    {
        M62_PrivateWidget1_1->setWindowTitle(QCoreApplication::translate("M62_PrivateWidget1_1", "Form", nullptr));
        pushButtonClose->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class M62_PrivateWidget1_1: public Ui_M62_PrivateWidget1_1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_M62_PRIVATEWIDGET1_1_H

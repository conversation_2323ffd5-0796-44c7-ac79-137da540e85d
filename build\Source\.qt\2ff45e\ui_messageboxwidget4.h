/********************************************************************************
** Form generated from reading UI file 'messageboxwidget4.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MESSAGEBOXWIDGET4_H
#define UI_MESSAGEBOXWIDGET4_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MessageBoxWidget4
{
public:
    QGridLayout *gridLayout_3;
    QSpacerItem *verticalSpacer_2;
    QGridLayout *gridLayout_2;
    QSpacerItem *horizontalSpacer;
    QGridLayout *gridLayout;
    QCheckBox *CheckBox1;
    QSpacerItem *verticalSpacer;
    QCheckBox *CheckBox2;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer_3;
    QGridLayout *gridLayout_11;
    QSpacerItem *horizontalSpacer_23;
    QPushButton *PushButton1;
    QSpacerItem *horizontalSpacer_24;
    QPushButton *PushButton2;
    QSpacerItem *horizontalSpacer_25;
    QSpacerItem *verticalSpacer_4;

    void setupUi(QWidget *MessageBoxWidget4)
    {
        if (MessageBoxWidget4->objectName().isEmpty())
            MessageBoxWidget4->setObjectName("MessageBoxWidget4");
        MessageBoxWidget4->resize(300, 200);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(MessageBoxWidget4->sizePolicy().hasHeightForWidth());
        MessageBoxWidget4->setSizePolicy(sizePolicy);
        gridLayout_3 = new QGridLayout(MessageBoxWidget4);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName("gridLayout_3");
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_2 = new QSpacerItem(20, 34, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer_2, 0, 0, 1, 1);

        gridLayout_2 = new QGridLayout();
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName("gridLayout_2");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_2->addItem(horizontalSpacer, 0, 0, 1, 1);

        gridLayout = new QGridLayout();
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        CheckBox1 = new QCheckBox(MessageBoxWidget4);
        CheckBox1->setObjectName("CheckBox1");
        sizePolicy.setHeightForWidth(CheckBox1->sizePolicy().hasHeightForWidth());
        CheckBox1->setSizePolicy(sizePolicy);
        CheckBox1->setMinimumSize(QSize(1, 1));

        gridLayout->addWidget(CheckBox1, 0, 0, 1, 1);

        verticalSpacer = new QSpacerItem(20, 17, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer, 1, 0, 1, 1);

        CheckBox2 = new QCheckBox(MessageBoxWidget4);
        CheckBox2->setObjectName("CheckBox2");
        sizePolicy.setHeightForWidth(CheckBox2->sizePolicy().hasHeightForWidth());
        CheckBox2->setSizePolicy(sizePolicy);
        CheckBox2->setMinimumSize(QSize(1, 1));

        gridLayout->addWidget(CheckBox2, 2, 0, 1, 1);

        gridLayout->setRowStretch(0, 100);
        gridLayout->setRowStretch(1, 60);
        gridLayout->setRowStretch(2, 100);

        gridLayout_2->addLayout(gridLayout, 0, 1, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_2->addItem(horizontalSpacer_2, 0, 2, 1, 1);

        gridLayout_2->setColumnStretch(0, 8);
        gridLayout_2->setColumnStretch(1, 100);
        gridLayout_2->setColumnStretch(2, 8);

        gridLayout_3->addLayout(gridLayout_2, 1, 0, 1, 1);

        verticalSpacer_3 = new QSpacerItem(20, 35, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer_3, 2, 0, 1, 1);

        gridLayout_11 = new QGridLayout();
        gridLayout_11->setSpacing(0);
        gridLayout_11->setObjectName("gridLayout_11");
        horizontalSpacer_23 = new QSpacerItem(40, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_11->addItem(horizontalSpacer_23, 0, 0, 1, 1);

        PushButton1 = new QPushButton(MessageBoxWidget4);
        PushButton1->setObjectName("PushButton1");
        sizePolicy.setHeightForWidth(PushButton1->sizePolicy().hasHeightForWidth());
        PushButton1->setSizePolicy(sizePolicy);
        PushButton1->setMinimumSize(QSize(1, 1));

        gridLayout_11->addWidget(PushButton1, 0, 1, 1, 1);

        horizontalSpacer_24 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_11->addItem(horizontalSpacer_24, 0, 2, 1, 1);

        PushButton2 = new QPushButton(MessageBoxWidget4);
        PushButton2->setObjectName("PushButton2");
        sizePolicy.setHeightForWidth(PushButton2->sizePolicy().hasHeightForWidth());
        PushButton2->setSizePolicy(sizePolicy);
        PushButton2->setMinimumSize(QSize(1, 1));

        gridLayout_11->addWidget(PushButton2, 0, 3, 1, 1);

        horizontalSpacer_25 = new QSpacerItem(40, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_11->addItem(horizontalSpacer_25, 0, 4, 1, 1);

        gridLayout_11->setColumnStretch(0, 100);
        gridLayout_11->setColumnStretch(1, 80);
        gridLayout_11->setColumnStretch(2, 10);
        gridLayout_11->setColumnStretch(3, 80);
        gridLayout_11->setColumnStretch(4, 100);

        gridLayout_3->addLayout(gridLayout_11, 3, 0, 1, 1);

        verticalSpacer_4 = new QSpacerItem(20, 34, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer_4, 4, 0, 1, 1);

        gridLayout_3->setRowStretch(0, 70);
        gridLayout_3->setRowStretch(1, 90);
        gridLayout_3->setRowStretch(2, 130);
        gridLayout_3->setRowStretch(3, 60);
        gridLayout_3->setRowStretch(4, 90);

        retranslateUi(MessageBoxWidget4);

        QMetaObject::connectSlotsByName(MessageBoxWidget4);
    } // setupUi

    void retranslateUi(QWidget *MessageBoxWidget4)
    {
        MessageBoxWidget4->setWindowTitle(QCoreApplication::translate("MessageBoxWidget4", "Form", nullptr));
        CheckBox1->setText(QString());
        CheckBox2->setText(QString());
        PushButton1->setText(QString());
        PushButton2->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class MessageBoxWidget4: public Ui_MessageBoxWidget4 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MESSAGEBOXWIDGET4_H

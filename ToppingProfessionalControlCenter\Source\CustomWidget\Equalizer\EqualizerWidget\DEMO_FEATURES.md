# 均衡器控件演示功能

## 控件外观

根据您提供的设计图，我们的均衡器控件实现了以下视觉特性：

### 整体布局
- **深色主题**: 黑色背景 (#1a1a1a) 配合绿色强调色 (#43cf7c)
- **水平排列**: 多个均衡器段水平排列，支持滚动
- **动态段数**: 可以动态添加和删除均衡器段（1-32段）

### 单段控件设计
每个均衡器段包含以下元素（从上到下）：

1. **段号标识**
   - 显示段号（1, 2, 3, 4...）
   - 白色文字，居中显示

2. **类型选择器**
   - 标签: "Type"
   - 下拉菜单，包含滤波器类型选项：
     - 高通滤波
     - 高通滤波  
     - High Shelf
     - High Shelf

3. **增益控制**
   - 标签: "Gain"
   - 大型圆形旋钮 (80x80px)
   - 范围: -20dB 到 +20dB
   - 精度: 0.1dB
   - 绿色指示器显示当前值
   - 支持正负值显示

4. **频率控制**
   - 标签: "Freq"
   - 中型圆形旋钮 (60x60px)
   - 范围: 20Hz 到 20kHz
   - 显示当前频率值

5. **Q值控制**
   - 标签: "Q"
   - 中型圆形旋钮 (60x60px)
   - 范围: 0.1 到 10.0
   - 精度: 0.1
   - 显示当前Q值

6. **启用开关**
   - 绿色复选框
   - 选中时显示绿色勾选标记
   - 控制该段是否启用

### 控制面板
- **添加段按钮**: 绿色按钮，用于添加新的均衡器段
- **删除段按钮**: 绿色按钮，用于删除最后一个均衡器段
- **段数显示**: 实时显示当前段数
- **段数控制**: 数字输入框，可直接设置段数

### 交互特性

#### 旋钮控制
- **鼠标拖拽**: 上下拖拽改变数值
- **鼠标滚轮**: 滚轮调节数值
- **双击重置**: 双击旋钮重置为默认值
- **键盘控制**: 方向键微调数值

#### 实时反馈
- **数值显示**: 旋钮下方实时显示当前数值
- **视觉反馈**: 旋钮指示器实时跟随数值变化
- **颜色变化**: 启用/禁用状态有不同的视觉效果

#### 数据同步
- **实时更新**: 任何参数变化都会立即触发信号
- **批量操作**: 支持批量设置多个段的参数
- **预设管理**: 可保存和加载均衡器预设

## 技术特性

### 响应式设计
- **自适应宽度**: 控件宽度根据段数自动调整
- **滚动支持**: 段数过多时自动显示水平滚动条
- **最小尺寸**: 每段最小宽度120px，确保控件可用性

### 性能优化
- **延迟加载**: 只在需要时创建控件实例
- **内存管理**: 自动管理子控件的生命周期
- **信号优化**: 避免不必要的信号发射

### 扩展性
- **模块化设计**: 单段控件和主控件分离
- **可定制样式**: 支持自定义CSS样式
- **插件架构**: 易于添加新的滤波器类型

## 使用场景

### 音频处理
- **专业音频设备**: 如您的Topping产品
- **音频软件**: DAW、音频编辑器
- **音响系统**: 家庭影院、专业音响

### 信号处理
- **频率响应调节**: 精确控制各频段响应
- **音色塑造**: 通过EQ调节音色特性
- **房间校正**: 补偿房间声学特性

### 教育培训
- **音频工程教学**: 直观展示EQ原理
- **产品演示**: 展示设备功能特性
- **用户培训**: 帮助用户理解EQ操作

## 兼容性

### 平台支持
- **Windows**: Windows 10/11
- **macOS**: macOS 10.15+
- **Linux**: Ubuntu 18.04+

### Qt版本
- **Qt6**: 推荐版本
- **Qt5**: 兼容支持（需要少量修改）

### 编译器
- **MSVC**: Visual Studio 2019+
- **GCC**: GCC 8+
- **Clang**: Clang 10+

/********************************************************************************
** Form generated from reading UI file 'widgetsytem1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_WIDGETSYTEM1_H
#define UI_WIDGETSYTEM1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include <comboboxs1m3.h>

QT_BEGIN_NAMESPACE

class Ui_WidgetSytem1
{
public:
    QGridLayout *gridLayout;
    QSpacerItem *verticalSpacer_7;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer;
    QVBoxLayout *verticalLayout;
    QWidget *widget1;
    QLabel *icon1;
    QLabel *label1;
    ComboBoxS1M3 *comboBox1;
    QSpacerItem *verticalSpacer_3;
    QWidget *widget2;
    QLabel *icon2;
    QLabel *label2;
    ComboBoxS1M3 *comboBox2;
    QSpacerItem *verticalSpacer_2;
    QWidget *widget3;
    QLabel *icon3;
    QLabel *label3;
    QPushButton *button3;
    QSpacerItem *verticalSpacer_4;
    QWidget *widget4;
    QLabel *icon4;
    QLabel *label4;
    QPushButton *button4;
    QSpacerItem *verticalSpacer_5;
    QWidget *widget5;
    QLabel *icon5;
    QLabel *label5;
    QPushButton *button5;
    QSpacerItem *verticalSpacer_6;
    QWidget *widget6;
    QLabel *icon6;
    QLabel *label6;
    QPushButton *button6;
    QSpacerItem *horizontalSpacer;

    void setupUi(QWidget *WidgetSytem1)
    {
        if (WidgetSytem1->objectName().isEmpty())
            WidgetSytem1->setObjectName("WidgetSytem1");
        WidgetSytem1->resize(694, 397);
        gridLayout = new QGridLayout(WidgetSytem1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_7 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_7, 0, 0, 1, 3);

        horizontalSpacer_2 = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer_2, 1, 2, 1, 1);

        verticalSpacer = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer, 2, 0, 1, 3);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName("verticalLayout");
        widget1 = new QWidget(WidgetSytem1);
        widget1->setObjectName("widget1");
        icon1 = new QLabel(widget1);
        icon1->setObjectName("icon1");
        icon1->setGeometry(QRect(1, 1, 16, 16));
        icon1->setStyleSheet(QString::fromUtf8("image: url(:/Icon/language.svg);"));
        icon1->setScaledContents(false);
        icon1->setAlignment(Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter);
        label1 = new QLabel(widget1);
        label1->setObjectName("label1");
        label1->setGeometry(QRect(30, 1, 57, 16));
        comboBox1 = new ComboBoxS1M3(widget1);
        comboBox1->setObjectName("comboBox1");
        comboBox1->setGeometry(QRect(470, 1, 72, 23));

        verticalLayout->addWidget(widget1);

        verticalSpacer_3 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_3);

        widget2 = new QWidget(WidgetSytem1);
        widget2->setObjectName("widget2");
        icon2 = new QLabel(widget2);
        icon2->setObjectName("icon2");
        icon2->setGeometry(QRect(1, 1, 16, 16));
        icon2->setStyleSheet(QString::fromUtf8("image: url(:/Icon/interfaceRatio.svg);"));
        label2 = new QLabel(widget2);
        label2->setObjectName("label2");
        label2->setGeometry(QRect(30, 1, 47, 16));
        comboBox2 = new ComboBoxS1M3(widget2);
        comboBox2->setObjectName("comboBox2");
        comboBox2->setGeometry(QRect(470, 1, 72, 23));

        verticalLayout->addWidget(widget2);

        verticalSpacer_2 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_2);

        widget3 = new QWidget(WidgetSytem1);
        widget3->setObjectName("widget3");
        icon3 = new QLabel(widget3);
        icon3->setObjectName("icon3");
        icon3->setGeometry(QRect(1, 1, 16, 16));
        icon3->setStyleSheet(QString::fromUtf8("image: url(:/Icon/followSystem.svg);"));
        label3 = new QLabel(widget3);
        label3->setObjectName("label3");
        label3->setGeometry(QRect(30, 1, 201, 16));
        button3 = new QPushButton(widget3);
        button3->setObjectName("button3");
        button3->setGeometry(QRect(495, 1, 32, 16));
        QSizePolicy sizePolicy(QSizePolicy::Policy::Preferred, QSizePolicy::Policy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(button3->sizePolicy().hasHeightForWidth());
        button3->setSizePolicy(sizePolicy);
        button3->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/Icon/switchOff.svg);\n"
"}\n"
"QPushButton:checked{\n"
"	image: url(:/Icon/switchOn.svg);\n"
"}"));
        button3->setCheckable(true);

        verticalLayout->addWidget(widget3);

        verticalSpacer_4 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_4);

        widget4 = new QWidget(WidgetSytem1);
        widget4->setObjectName("widget4");
        icon4 = new QLabel(widget4);
        icon4->setObjectName("icon4");
        icon4->setGeometry(QRect(1, 1, 16, 16));
        icon4->setStyleSheet(QString::fromUtf8("image: url(:/Icon/work.svg);"));
        label4 = new QLabel(widget4);
        label4->setObjectName("label4");
        label4->setGeometry(QRect(30, 1, 124, 16));
        button4 = new QPushButton(widget4);
        button4->setObjectName("button4");
        button4->setGeometry(QRect(495, 1, 32, 16));
        sizePolicy.setHeightForWidth(button4->sizePolicy().hasHeightForWidth());
        button4->setSizePolicy(sizePolicy);
        button4->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/Icon/switchOff.svg);\n"
"}\n"
"QPushButton:checked{\n"
"	image: url(:/Icon/switchOn.svg);\n"
"}"));
        button4->setCheckable(true);

        verticalLayout->addWidget(widget4);

        verticalSpacer_5 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_5);

        widget5 = new QWidget(WidgetSytem1);
        widget5->setObjectName("widget5");
        icon5 = new QLabel(widget5);
        icon5->setObjectName("icon5");
        icon5->setGeometry(QRect(1, 1, 16, 16));
        icon5->setStyleSheet(QString::fromUtf8("image: url(:/Icon/autoStart.svg);"));
        label5 = new QLabel(widget5);
        label5->setObjectName("label5");
        label5->setGeometry(QRect(30, 1, 46, 16));
        button5 = new QPushButton(widget5);
        button5->setObjectName("button5");
        button5->setGeometry(QRect(495, 1, 32, 16));
        sizePolicy.setHeightForWidth(button5->sizePolicy().hasHeightForWidth());
        button5->setSizePolicy(sizePolicy);
        button5->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/Icon/switchOff.svg);\n"
"}\n"
"QPushButton:checked{\n"
"	image: url(:/Icon/switchOn.svg);\n"
"}"));
        button5->setCheckable(true);

        verticalLayout->addWidget(widget5);

        verticalSpacer_6 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_6);

        widget6 = new QWidget(WidgetSytem1);
        widget6->setObjectName("widget6");
        icon6 = new QLabel(widget6);
        icon6->setObjectName("icon6");
        icon6->setGeometry(QRect(1, 1, 16, 16));
        icon6->setStyleSheet(QString::fromUtf8("image: url(:/Icon/autoUpdate.svg);"));
        label6 = new QLabel(widget6);
        label6->setObjectName("label6");
        label6->setGeometry(QRect(30, 1, 136, 16));
        button6 = new QPushButton(widget6);
        button6->setObjectName("button6");
        button6->setGeometry(QRect(495, 1, 32, 16));
        sizePolicy.setHeightForWidth(button6->sizePolicy().hasHeightForWidth());
        button6->setSizePolicy(sizePolicy);
        button6->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/Icon/switchOff.svg);\n"
"}\n"
"QPushButton:checked{\n"
"	image: url(:/Icon/switchOn.svg);\n"
"}"));
        button6->setCheckable(true);

        verticalLayout->addWidget(widget6);

        verticalLayout->setStretch(0, 26);
        verticalLayout->setStretch(1, 22);
        verticalLayout->setStretch(2, 26);
        verticalLayout->setStretch(3, 22);
        verticalLayout->setStretch(4, 26);
        verticalLayout->setStretch(5, 22);
        verticalLayout->setStretch(6, 26);
        verticalLayout->setStretch(7, 22);
        verticalLayout->setStretch(8, 26);
        verticalLayout->setStretch(9, 22);
        verticalLayout->setStretch(10, 26);

        gridLayout->addLayout(verticalLayout, 1, 1, 1, 1);

        horizontalSpacer = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer, 1, 0, 1, 1);

        gridLayout->setRowStretch(0, 24);
        gridLayout->setRowStretch(1, 266);
        gridLayout->setRowStretch(2, 83);
        gridLayout->setColumnStretch(0, 60);
        gridLayout->setColumnStretch(1, 480);
        gridLayout->setColumnStretch(2, 60);

        retranslateUi(WidgetSytem1);

        QMetaObject::connectSlotsByName(WidgetSytem1);
    } // setupUi

    void retranslateUi(QWidget *WidgetSytem1)
    {
        WidgetSytem1->setWindowTitle(QCoreApplication::translate("WidgetSytem1", "WidgetSytem1", nullptr));
        icon1->setText(QString());
        label1->setText(QCoreApplication::translate("WidgetSytem1", "Language", nullptr));
        icon2->setText(QString());
        label2->setText(QCoreApplication::translate("WidgetSytem1", "UI Scale", nullptr));
        icon3->setText(QString());
        label3->setText(QCoreApplication::translate("WidgetSytem1", "UI Scales follow OS display setting", nullptr));
        button3->setText(QString());
        icon4->setText(QString());
        label4->setText(QCoreApplication::translate("WidgetSytem1", "Auto save workspace", nullptr));
        button4->setText(QString());
        icon5->setText(QString());
        label5->setText(QCoreApplication::translate("WidgetSytem1", "Autorun", nullptr));
        button5->setText(QString());
        icon6->setText(QString());
        label6->setText(QCoreApplication::translate("WidgetSytem1", "Auto check for updates", nullptr));
        button6->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class WidgetSytem1: public Ui_WidgetSytem1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_WIDGETSYTEM1_H

/************************************************************************
 *
 *  Module:       WnHandle.h
 *
 *  Description:
 *    Wrapper for a Win32 HANDLE object
 *
 *  Runtime Env.:
 *    Win32
 *
 *  Author(s):
 *    <PERSON>, <PERSON>@thesycon.de
 *    Udo <PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnHandle_h__
#define __WnHandle_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// WnHandle
//
// Wrapper for a Win32 HANDLE object
//
class WnHandle
{
public:
    // constructor
    WnHandle()
            {
                mHandle = NULL;
            }

    // construct from an existing handle
    WnHandle(
        HANDLE handle
        )
            {
                mHandle = handle;
            }

    // destructor
    virtual
    ~WnHandle()
            {
                WnHandle::Close();
            }


/////////////////////////////////////////
// Methods
/////////////////////////////////////////
public:

    //
    // Close handle, invalidate handle.
    // To be overridden in a subclass, if required.
    //
    virtual
    void
    Close()
        {
            if ( NULL != mHandle ) {
                BOOL succ = ::CloseHandle(mHandle);
                mHandle = NULL;
                if ( !succ ) {
                    WNTRACE(TRCERR,tprint(__FUNCTION__": CloseHandle failed, err=0x%08X\n", ::GetLastError()));
                }
            }
        }


    // returns true if a valid handle is attached
    bool
    IsValid() const
            { return (NULL != mHandle); }

    //
    // get the attached handle,
    // returns NULL if no valid handle is attached
    //
    HANDLE
    GetHandle() const
            { return mHandle; }


    //
    // attach a handle
    // closes existing handle, if any
    //
    void
    AttachHandle(
        HANDLE handle
        )
            {
                // make sure the current handle is closed
                Close();
                // save new handle
                mHandle = handle;
            }

    //
    // detach handle
    // returns current handle
    //
    HANDLE
    DetachHandle()
        {
            HANDLE h = mHandle;
            mHandle = NULL;
            return h;
        }


    // wait on this handle
    // wrapper for WaitForSingleObject()
    // returns WAIT_OBJECT_0 (0) if the object is signaled
    // returns WAIT_TIMEOUT in case of timeout
    // returns WAIT_FAILED in case of failure
    DWORD
    Wait(
        DWORD dwMilliseconds = INFINITE
        ) const
            {
                DWORD ret;
                WNASSERT(IsValid());
                // To make debugging easier, in case of INFINITE we print a message every second
                if ( INFINITE == dwMilliseconds ) {
                    for (;;) {
                        ret = ::WaitForSingleObject(mHandle, 1000);
                        if ( WAIT_TIMEOUT != ret ) break;
                        WNTRACE(TRCWRN, tprint(__FUNCTION__": waiting INFINITE...\n"));
                    }
                } else {
                    ret = ::WaitForSingleObject(mHandle, dwMilliseconds);
                }
                return ret;
            }

    //
    // Wait on this handle and up to 3 additional objects.
    // This handle will be index zero in the handle array.
    //
    // NOTE: Depending on the implementation, the order of the events in the array
    // might be important (see also below). In this case don't use this function.
    // Use the native WaitForMultipleObjects() function instead.

    // If waitOnAll == false:
    // The function returns WAIT_OBJECT_0+index if one of the objects gets signaled.
    // If more than one object gets signaled, the smallest index will be returned.
    // See also the documentation of WaitForMultipleObjects().

    // In case of a timeout the function returns WAIT_TIMEOUT.
    // in case of a failure the function returns WAIT_FAILED. The caller can
    // call ::GetLastError() to get extended error information.
    //
    DWORD
    WaitForMultiple(
        DWORD waitTimeout = INFINITE,
        WnHandle* additionalObject1 = NULL,
        WnHandle* additionalObject2 = NULL,
        WnHandle* additionalObject3 = NULL,
        bool waitOnAll = false
        )
            {
                WNASSERT(IsValid());

            // index zero = this handle
            DWORD eventCount = 1;
            HANDLE events[4];
            events[0] = GetHandle();

            // additional handles, stop at first NULL pointer
            if (NULL != additionalObject1){
                events[eventCount++] = additionalObject1->GetHandle();
                if (NULL != additionalObject2){
                    events[eventCount++] = additionalObject2->GetHandle();
                    if (NULL != additionalObject3){
                        events[eventCount++] = additionalObject3->GetHandle();
                    }
                }
            }

            WNASSERT(eventCount<=TB_ARRAY_ELEMENTS(events));

            return ::WaitForMultipleObjects(
                              eventCount,        // __in  DWORD nCount
                              events,            // __in  const HANDLE *lpHandles
                              waitOnAll ? TRUE : FALSE,  // __in  BOOL bWaitAll
                              waitTimeout                // __in  DWORD dwMilliseconds
                              );
            }



/////////////////////////////////////////
// Implementation
/////////////////////////////////////////
protected:


/////////////////////////////////////////
// Data Members
/////////////////////////////////////////
protected:

    // handle value, NULL if invalid
    HANDLE mHandle;


// make copy constructor and assignment operator unavailable
PRIVATE_COPY_CONSTRUCTOR(WnHandle)
PRIVATE_ASSIGNMENT_OPERATOR(WnHandle)

}; // class WnHandle

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnHandle_h__

/***************************** EOF **************************************/

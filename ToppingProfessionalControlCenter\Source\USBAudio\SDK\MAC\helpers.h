/************************************************************************

    Description:
        helper functions

    Author(s):
        <PERSON><PERSON>
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __helpers_h__
#define __helpers_h__

#include <string>
#include <vector>


// convert from WCHAR to char
std::string
ConvertToCharString(
    const std::wstring& wstr
    );

// overload for the trivial case
inline
std::string
ConvertToCharString(
    const std::string& str
    )
{
    return str;
}



void
LeftTrimString(
    std::string& str
    );

void
RightTrimString(
    std::string& str
    );

void
TrimString(
    std::string& str
    );



struct VidPidTuple
{
    unsigned int vid {0};
    unsigned int pid {0};
};

using VidPidTupleList = std::vector<VidPidTuple>;


// Parse string in the format "VID:PID".
// Returns true if successful, false if the conversion failed.
bool
ParseVidPidTuple(
    VidPidTuple& vp,
    const std::string& str
    );


// Parse string in the format "VID:PID,VID:PID,...".
// Returns true if successful, false if the conversion failed.
bool
ParseVidPidTupleList(
    VidPidTupleList& vpList,
    const std::string& str
    );


// Build filter description string as required by the TL-USBDFU API.
std::string
BuildDeviceFilterDescription(
    const VidPidTuple& vp
    );

std::string
BuildDeviceFilterDescription(
    const VidPidTupleList& vpList
    );





#endif

/******************************** EOF ***********************************/

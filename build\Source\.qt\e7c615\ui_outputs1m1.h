/********************************************************************************
** Form generated from reading UI file 'outputs1m1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_OUTPUTS1M1_H
#define UI_OUTPUTS1M1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <pushbuttongroups1m4.h>
#include <toolbuttons1m1.h>
#include <volumemeters1m4.h>
#include <volumemeters1m5.h>
#include <vsliders1m2.h>

QT_BEGIN_NAMESPACE

class Ui_OutputS1M1
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    QLineEdit *lineEdit;
    PushButtonGroupS1M4 *widgetPushButtonGroup1;
    VolumeMeterS1M4 *widgetLinkedMeterLeft;
    VolumeMeterS1M5 *widgetLinkedMeterRight;
    VSliderS1M2 *widgetLinkedVSlider;
    ToolButtonS1M1 *widgetToolButton;
    QWidget *widgetOverlay;

    void setupUi(QWidget *OutputS1M1)
    {
        if (OutputS1M1->objectName().isEmpty())
            OutputS1M1->setObjectName("OutputS1M1");
        OutputS1M1->resize(140, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(OutputS1M1->sizePolicy().hasHeightForWidth());
        OutputS1M1->setSizePolicy(sizePolicy);
        OutputS1M1->setMinimumSize(QSize(60, 220));
        gridLayout = new QGridLayout(OutputS1M1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(OutputS1M1);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 21));
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(10, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetPushButtonGroup1 = new PushButtonGroupS1M4(frame);
        widgetPushButtonGroup1->setObjectName("widgetPushButtonGroup1");
        widgetPushButtonGroup1->setGeometry(QRect(10, 230, 91, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup1->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup1->setSizePolicy(sizePolicy);
        widgetPushButtonGroup1->setMinimumSize(QSize(12, 7));
        widgetLinkedMeterLeft = new VolumeMeterS1M4(frame);
        widgetLinkedMeterLeft->setObjectName("widgetLinkedMeterLeft");
        widgetLinkedMeterLeft->setGeometry(QRect(10, 110, 21, 111));
        sizePolicy.setHeightForWidth(widgetLinkedMeterLeft->sizePolicy().hasHeightForWidth());
        widgetLinkedMeterLeft->setSizePolicy(sizePolicy);
        widgetLinkedMeterRight = new VolumeMeterS1M5(frame);
        widgetLinkedMeterRight->setObjectName("widgetLinkedMeterRight");
        widgetLinkedMeterRight->setGeometry(QRect(80, 110, 21, 111));
        sizePolicy.setHeightForWidth(widgetLinkedMeterRight->sizePolicy().hasHeightForWidth());
        widgetLinkedMeterRight->setSizePolicy(sizePolicy);
        widgetLinkedVSlider = new VSliderS1M2(frame);
        widgetLinkedVSlider->setObjectName("widgetLinkedVSlider");
        widgetLinkedVSlider->setGeometry(QRect(40, 110, 31, 111));
        widgetToolButton = new ToolButtonS1M1(frame);
        widgetToolButton->setObjectName("widgetToolButton");
        widgetToolButton->setGeometry(QRect(10, 40, 91, 21));
        widgetOverlay = new QWidget(frame);
        widgetOverlay->setObjectName("widgetOverlay");
        widgetOverlay->setGeometry(QRect(110, 40, 21, 21));

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(OutputS1M1);

        QMetaObject::connectSlotsByName(OutputS1M1);
    } // setupUi

    void retranslateUi(QWidget *OutputS1M1)
    {
        OutputS1M1->setWindowTitle(QCoreApplication::translate("OutputS1M1", "Form", nullptr));
        pushButtonClose->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class OutputS1M1: public Ui_OutputS1M1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_OUTPUTS1M1_H

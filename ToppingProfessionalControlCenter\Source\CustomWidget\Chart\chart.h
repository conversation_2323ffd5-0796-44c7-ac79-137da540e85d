#ifndef CHART_H
#define CHART_H


#include <QTimer>
#include <QtCharts>
#include <QGraphicsLineItem>
#include <QGraphicsRectItem>


class Chart : public QChartView
{
    Q_OBJECT
public:
    explicit Chart(QWidget *parent = nullptr);
    ~Chart();
    // Enum
    enum CustomAxis
    {
        axisXT=0,
        axisXB,
        axisYL,
        axisYR,
        axisEnd
    };
    // Chart
    Chart& setChartDefault();
    Chart& setChartFont(QFont font);
    Chart& setChartColor(QColor color);
    Chart& setChartPlotAreaColor(QColor color);
    Chart& setChartGridVisible(bool visible = true);
    Chart& setChartGridVisible(CustomAxis axis, bool visible = true);
    Chart& setChartAxisVisible(bool visible = true);
    Chart& setChartAxisVisible(CustomAxis axis, bool visible = true);
    Chart& setChartRectFrameVisible(bool visible = true);
    Chart& setChartRectFrameColor(QColor color);
    Chart& setChartRectFrameWidth(int width);
    // Axis
    Chart& setAxis(CustomAxis axis, qreal min, qreal max, int count, const QString &format);
    Chart& setAxisRange(CustomAxis axis, qreal min, qreal max);
    Chart& setAxisTickCount(CustomAxis axis, int count);
    Chart& setAxisLabelFormat(CustomAxis axis, const QString &format);
    Chart& setAxisLabelFont(CustomAxis axis, QFont font);
    Chart& setAxisLabelColor(CustomAxis axis, QColor color);
    Chart& setAxisLineColor(CustomAxis axis, QColor color);
    Chart& setAxisLineWidth(CustomAxis axis, int width);
    Chart& setAxisGridColor(CustomAxis axis, QColor color);
    Chart& setAxisGridWidth(CustomAxis axis, int width);
    // Handle
    Chart& addHandle(QString name, QPointF point, const QString &imageFile);
    Chart& removeHandle(QString name);
    Chart& setHandlePosition(QString name, QPointF point);
    Chart& setHandleVisible(QString name, bool visible = true);
    Chart& setHandleAxisVisible(QString name, bool visible = true);
    Chart& setHandleAxisVisibleX(QString name, bool visible = true);
    Chart& setHandleAxisVisibleY(QString name, bool visible = true);
    Chart& setHandleAxisStyle(QString name, Qt::PenStyle style);
    Chart& setHandleAxisStyleX(QString name, Qt::PenStyle style);
    Chart& setHandleAxisStyleY(QString name, Qt::PenStyle style);
    Chart& setHandleAxisWidth(QString name, int width);
    Chart& setHandleAxisWidthX(QString name, int width);
    Chart& setHandleAxisWidthY(QString name, int width);
    Chart& setHandleAxisColor(QString name, QColor color);
    Chart& setHandleAxisColorX(QString name, QColor color);
    Chart& setHandleAxisColorY(QString name, QColor color);
    Chart& setHandleAxisLock(QString name, bool lock = true);
    Chart& setHandleAxisLockX(QString name, bool lock = true);
    Chart& setHandleAxisLockY(QString name, bool lock = true);
    // ScrollLineSeries
    Chart& addScrollLineSeries(QString name, qreal defaultData);
    Chart& removeScrollLineSeries(QString name);
    Chart& addScrollLineSeriesData(QString name, qreal data);
    Chart& clearScrollLineSeries(QString name);
    Chart& setScrollLineSeriesStart(QString name);
    Chart& setScrollLineSeriesStop(QString name);
    Chart& setScrollLineSeriesWidth(QString name, int width);
    Chart& setScrollLineSeriesColor(QString name, QColor color);
    Chart& setScrollLineSeriesRefreshParam(QString name, qreal stepValue, int nHz); //stepValue(0, 1]  nHz[1, 1000/(1/stepValue)]  stepValue需要对1整除
    qreal getScrollLineSeriesData(QString name);
    // ScrollSplineSeries
    Chart& addScrollSplineSeries(QString name, qreal defaultData);
    Chart& removeScrollSplineSeries(QString name);
    Chart& addScrollSplineSeriesData(QString name, qreal data);
    Chart& clearScrollSplineSeries(QString name);
    Chart& setScrollSplineSeriesStart(QString name);
    Chart& setScrollSplineSeriesStop(QString name);
    Chart& setScrollSplineSeriesWidth(QString name, int width);
    Chart& setScrollSplineSeriesColor(QString name, QColor color);
    Chart& setScrollSplineSeriesRefreshParam(QString name, qreal stepValue, int nHz); //stepValue(0, 1]  nHz[1, 1000/(1/stepValue)]  stepValue需要对1整除
    qreal getScrollSplineSeriesData(QString name);
    // ScrollAreaSeries
    Chart& addScrollAreaSeries(QString name, qreal defaultData);
    Chart& removeScrollAreaSeries(QString name);
    Chart& addScrollAreaSeriesData(QString name, qreal data);
    Chart& clearScrollAreaSeries(QString name);
    Chart& setScrollAreaSeriesStart(QString name);
    Chart& setScrollAreaSeriesStop(QString name);
    Chart& setScrollAreaSeriesWidth(QString name, int width);
    Chart& setScrollAreaSeriesColor(QString name, QColor color);
    Chart& setScrollAreaSeriesRefreshParam(QString name, qreal stepValue, int nHz); //stepValue(0, 1]  nHz[1, 1000/(1/stepValue)]  stepValue需要对1整除
    qreal getScrollAreaSeriesData(QString name);
protected:
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
private:
    // Struct
    struct CustomHandle
    {
        QScatterSeries point;
        QLineSeries axisX;
        QLineSeries axisY;
    };
    struct ScrollLineSeries
    {
        QLineSeries series;
        QTimer timer;
    };
    struct ScrollSplineSeries
    {
        QSplineSeries series;
        QTimer timer;
    };
    struct ScrollAreaSeries
    {
        QAreaSeries series;
        QTimer timer;
    };
    // Property
    QChart mChart;
    QValueAxis mAxis[axisEnd];
    QGraphicsRectItem mRectFrame;
    QMap<QString, CustomHandle*> mHandle;
    bool mHandleIsMoving;
    float mHandleMovingStep;
    QString mMovingHandle;
    QMap<QString, ScrollLineSeries*> mScrollLineSeries;
    QMap<QString, ScrollSplineSeries*> mScrollSplineSeries;
    QMap<QString, ScrollAreaSeries*> mScrollAreaSeries;
signals:
    void handleMovement(QString name, QPointF point);
    //public slots:
    //protected slots:
private slots:
    void on_ScrollLineSeriesTimers_timeout();
    void on_ScrollSplineSeriesTimers_timeout();
    void on_ScrollAreaSeriesTimers_timeout();
};


#endif // CHART_H


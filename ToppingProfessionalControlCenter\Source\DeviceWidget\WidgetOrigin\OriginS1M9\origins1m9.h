#ifndef ORIGINS1M9_H
#define ORIGINS1M9_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"
#include "pushbuttons1m7.h"


namespace Ui {
class OriginS1M9;
}


class OriginS1M9 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M9(QWidget* parent=nullptr, QString name="");
    ~OriginS1M9();
    OriginS1M9& setName(QString name);
    OriginS1M9& setFont(QFont font);
    OriginS1M9& setVolumeMeterLeft(int value);
    OriginS1M9& setVolumeMeterLeftClear();
    OriginS1M9& setVolumeMeterLeftSlip();
    OriginS1M9& setVolumeMeterRight(int value);
    OriginS1M9& setVolumeMeterRightClear();
    OriginS1M9& setVolumeMeterRightSlip();
    OriginS1M9& setGain(float value);
    OriginS1M9& setGainLock(bool state=true);
    OriginS1M9& setMuteAffectGain(bool state=true);
    OriginS1M9& setGainAffectMute(bool state=true);
    OriginS1M9& setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    OriginS1M9& setGainDefault(float value);
    OriginS1M9& setGainWidgetDisable(float value);
    OriginS1M9& setChannelNameEditable(bool state=true);
    OriginS1M9& setValueGAIN(float value);
    OriginS1M9& setValueMUTE(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OriginS1M9* ui;
    QTimer mTimer;
    QFont mFont;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(float value);
    void in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M7::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // ORIGINS1M9_H


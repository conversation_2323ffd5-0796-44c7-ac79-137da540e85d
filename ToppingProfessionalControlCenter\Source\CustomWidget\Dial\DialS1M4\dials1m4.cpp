#include "dials1m4.h"
#include "globalfont.h"


DialS1M4::DialS1M4(QWidget* parent)
    : QWidget(parent)
{
    setFocusPolicy(Qt::StrongFocus);
}
DialS1M4::~DialS1M4()
{

}


// override
void DialS1M4::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int diameter = qMin(rect().width(), rect().height());
    mPenWidth=diameter / 20 + 1;
    mRectDial.setX(rect().x() + mPenWidth);
    mRectDial.setY(rect().y() + mPenWidth);
    if(diameter == rect().width())
    {
        mRectDial.setY(rect().y() + (rect().height() - diameter) / 2 + mPenWidth);
    }
    if(diameter == rect().height())
    {
        mRectDial.setX(rect().x() + (rect().width() - diameter) / 2 + mPenWidth);
    }
    mRectDial.setWidth(diameter - 2 * mPenWidth);
    mRectDial.setHeight(diameter - 2 * mPenWidth);
}
void DialS1M4::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void DialS1M4::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(mRectDial.contains(e->pos()))
    {
        if(mValue != mValueDefault)
        {
            mValue = mValueDefault;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M4::mousePressEvent(QMouseEvent* e)
{
    if(mMouseEnabled && mRectDial.contains(e->pos()))
    {
        mPressed = true;
        mPressedValue = mValue;
        mPressedPoint = e->pos();
    }
}
void DialS1M4::mouseMoveEvent(QMouseEvent* e)
{
    float value=0;
    if(mPressed)
    {
        value = mPressedValue;
        value += ((int) (mPressedPoint.y() - e->pos().y()) / mSensitivity) * 0.5;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            float newValueMod=std::abs(std::fmod(value, 1.0));
            if(value >= mValueEnd05)
            {
            }
            else if(value >= mValueEnd10)
            {
                if(newValueMod != 0)
                {
                    return;
                }
            }
            else if(value >= mValueEnd20)
            {
                if(newValueMod != 0 || ((int) value) % 2)
                {
                    return;
                }
            }
            mValue = value;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M4::mouseReleaseEvent(QMouseEvent* e)
{
    Q_UNUSED(e);
    mPressed = false;
}
void DialS1M4::wheelEvent(QWheelEvent* e)
{
    float value=mValue;
    int numSteps=e->angleDelta().y() / 120;
    int numStepsAbs=qAbs(numSteps);
    for(int i=0;i<numStepsAbs;i++)
    {
        if(numSteps > 0)
        {
            if(value >= mValueEnd05)
            {
                value += 0.5;
            }
            else if(value >= mValueEnd10)
            {
                value += 1;
            }
            else if(value >= mValueEnd20)
            {
                value += 2;
            }
        }
        else
        {
            if(value > mValueEnd05)
            {
                value -= 0.5;
            }
            else if(value > mValueEnd10)
            {
                value -= 1;
            }
            else if(value > mValueEnd20)
            {
                value -= 2;
            }
        }
    }
    value = qMax(value, mValueMin);
    value = qMin(value, mValueMax);
    if(mValue != value)
    {
        mValue = value;
        emit valueChanged(mValue);
    }
    update();
}
void DialS1M4::keyPressEvent(QKeyEvent* e)
{
    float value=mValue;
    if(e->key() == Qt::Key_Up || e->key() == Qt::Key_Right)
    {
        if(value >= mValueEnd05)
        {
            value += 0.5;
        }
        else if(value >= mValueEnd10)
        {
            value += 1;
        }
        else if(value >= mValueEnd20)
        {
            value += 2;
        }
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
    else if(e->key() == Qt::Key_Down || e->key() == Qt::Key_Left)
    {
        if(value > mValueEnd05)
        {
            value -= 0.5;
        }
        else if(value > mValueEnd10)
        {
            value -= 1;
        }
        else if(value > mValueEnd20)
        {
            value -= 2;
        }
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
}
void DialS1M4::drawBG(QPainter* painter)
{
    painter->fillRect(rect(), Qt::transparent);
}
void DialS1M4::drawElement(QPainter* painter)
{
    painter->save();
    QPen pen=painter->pen();
    qreal arcPenWidth = mPenWidth * 0.9;
    qreal innerPenWidth = mPenWidth * 0.6;
    qreal innerSpace = mPenWidth * 1.5;
    qreal mCurValueAngle = 270 * (mValue - mValueMin) / (mValueMax - mValueMin);

    pen.setWidth(arcPenWidth);
    pen.setColor(mColorCircleBG);
    painter->setPen(pen);
    painter->drawArc(mRectDial, -45 * 16, 270 * 16);

    pen.setColor(mColorDial);
    pen.setWidth(innerPenWidth);
    painter->setPen(pen);
    painter->setBrush(mColorCircleBG);
    QRectF innerRect = mRectDial.adjusted(innerSpace, innerSpace, -innerSpace, -innerSpace);
    painter->drawEllipse(innerRect);

    pen.setWidth(arcPenWidth);
    pen.setColor(mColorCircleValue);
    painter->setPen(pen);
    qreal curAngle = (225 - mCurValueAngle) * 16;
    painter->drawArc(mRectDial,curAngle,mCurValueAngle * 16);
    
    if(mValueShowArrow)
    {
        painter->save();
        qreal dialRadius = mRectDial.width() / 2.0;
        qreal innerCircleRadius = dialRadius - mPenWidth * 2.0;
        qreal indicatorWidth = innerCircleRadius * 0.05;
        qreal indicatorHeight = innerCircleRadius *0.23;
        painter->translate(mRectDial.center());
        qreal degRotate = -135 + mCurValueAngle;
        painter->rotate(degRotate);
        QRectF indicatorRect(-indicatorWidth/2.0, -dialRadius + mPenWidth * 1.5, indicatorWidth, indicatorHeight);
        qreal cornerRadius = indicatorWidth/4.0;
        painter->setBrush(mColorHandle);
        pen.setColor(mColorHandle);
        pen.setWidth(mPenWidth * 0.4);
        painter->setPen(pen);
        painter->drawRoundedRect(indicatorRect, cornerRadius, cornerRadius);
        painter->restore();
    }

    // draw Text
    if(mValueShowText)
    {
        painter->resetTransform();
        int widthText=mRectDial.width() * 0.6;
        int heightText=mRectDial.height() * 0.3;
        QRect rectText(mRectDial.x() + (mRectDial.width() - widthText) / 2, mRectDial.y() + (mRectDial.height() - heightText) / 2, widthText, heightText);
            // get suitable font
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "+88", rectText));
        painter->setFont(mFont);
        painter->setPen(mColorText);
            // Text
        QString str;
        if(mValue > mValueEnd05)
        {
            str = QString("%1").arg((double) mValue, 0, 'f', 1);
            if(mValue > 0)
            {
                str = QString("+%1").arg((double) mValue, 0, 'f', 1);
            }
        }
        else
        {
            str = QString("%1").arg((double) mValue, 0, 'f', 0);
        }
        if(str.toFloat() == mValueMin)
        {
            str = mShowInfinitesimal ? ("-∞") : (str);
        }
        else if(str.toFloat() == mValueMax)
        {
            str = mShowInfinity ? ("+∞") : (str);
        }
        painter->drawText(rectText, Qt::AlignCenter, str);
    }
    painter->restore();
}


// setter & getter
DialS1M4& DialS1M4::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
DialS1M4& DialS1M4::setValue(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValue = value;
    update();
    return *this;
}
DialS1M4& DialS1M4::setDefault(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValueDefault = value;
    return *this;
}
DialS1M4& DialS1M4::setRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20)
{
    mValueStart = valueStart;
    mValueEnd05 = valueEnd05;
    mValueEnd10 = valueEnd10;
    mValueEnd20 = valueEnd20;
    mValueMin = mValueEnd20;
    mValueMax = mValueStart;
    if(mValueMax < mValueDefault || mValueDefault < mValueMin)
    {
        mValueDefault = mValueMin;
    }
    update();
    return *this;
}
DialS1M4& DialS1M4::setSensitivity(int sensitivity)
{
    if(sensitivity > 100 || sensitivity <= 0)
    {
        return *this;
    }
    mSensitivity = sensitivity;
    return *this;
}
DialS1M4& DialS1M4::setMovable(bool status)
{
    mMouseEnabled = status;
    return *this;
}
DialS1M4& DialS1M4::setColorBG(QColor color)
{
    mColorBG = color;
    update();
    return *this;
}
DialS1M4& DialS1M4::setColorDial(QColor color)
{
    mColorDial = color;
    update();
    return *this;
}
DialS1M4& DialS1M4::setColorCircleBG(QColor color)
{
    mColorCircleBG = color;
    update();
    return *this;
}
DialS1M4& DialS1M4::setColorCircleValue(QColor color)
{
    mColorCircleValue = color;
    update();
    return *this;
}
DialS1M4& DialS1M4::setColorHandle(QColor color)
{
    mColorHandle = color;
    update();
    return *this;
}
DialS1M4& DialS1M4::setColorText(QColor color)
{
    mColorText = color;
    update();
    return *this;
}
DialS1M4& DialS1M4::showArrow(bool status)
{
    mValueShowArrow = status;
    update();
    return *this;
}
DialS1M4& DialS1M4::showCircle(bool status)
{
    mValueShowCircle = status;
    update();
    return *this;
}
DialS1M4& DialS1M4::showText(bool status)
{
    mValueShowText = status;
    update();
    return *this;
}
DialS1M4& DialS1M4::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
DialS1M4& DialS1M4::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}


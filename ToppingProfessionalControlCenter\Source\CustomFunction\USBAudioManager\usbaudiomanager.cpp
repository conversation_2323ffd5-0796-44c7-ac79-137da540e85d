#include "usbaudioapi.h"
#include "usbaudiomanager.h"


USBAudioManager::USBAudioManager()
{
    mTimer.setInterval(500);
    connect(&mTimer, &QTimer::timeout, this, &USBAudioManager::in_mTimer_timeout, Qt::UniqueConnection);
}
USBAudioManager::~USBAudioManager()
{

}


// init
bool USBAudioManager::init()
{
    return true;
}
void USBAudioManager::start()
{
    in_mTimer_timeout();
    mTimer.start();
}
void USBAudioManager::stop()
{
    mTimer.stop();
    mSampleRate = -1;
    mBufferSize = -1;
    mSafeMode = -1;
}


// slot
void USBAudioManager::in_mTimer_timeout()
{
    int sampleRate=USBAHandle.getSampleRateOfActiveDevice();
    int bufferSize=USBAHandle.getBufferSizeOfActiveDevice();
    int safeMode=USBAHandle.getSafeModeOfActiveDevice();
    if(mSampleRate != sampleRate)
    {
        mSampleRate = sampleRate;
        emit attributeChanged("SampleRate", QString::number(mSampleRate));
    }
    if(mBufferSize != bufferSize)
    {
        mBufferSize = bufferSize;
        emit attributeChanged("BufferSize", QString::number(mBufferSize));
    }
    if(mSafeMode != safeMode)
    {
        mSafeMode = safeMode;
        emit attributeChanged("SafeMode", QString::number(mSafeMode));
    }
}


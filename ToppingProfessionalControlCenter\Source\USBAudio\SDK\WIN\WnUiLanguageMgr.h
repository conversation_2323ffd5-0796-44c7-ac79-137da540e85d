/************************************************************************
 *  The module manages UI languages.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnUiLanguageMgr_h__
#define __WnUiLanguageMgr_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

#define UILM_LANGUAGE_TEXTS_FILE_EXTENSION L".txt"

//
// Manager of UI languages.
//
class WnUiLanguageMgr
{
//construction/destruction/assignment
public:    
    WnUiLanguageMgr();    
    ~WnUiLanguageMgr();

    // make copy constructor and assignment operator unavailable
    PRIVATE_COPY_CONSTRUCTOR(WnUiLanguageMgr)
    PRIVATE_ASSIGNMENT_OPERATOR(WnUiLanguageMgr)

//interface
public:

    //
    // Initialize the manager.
    //
    // The method determines the languages supported by the application and selects the one to use, if there is one.
    // However, it also returns with success, if no supported language could be found and/or no supported language is
    // suitable to be selected.
    //
    // parameters:
    //      srcPath     full path of the directory where the information about the UI languages is stored
    //
    WNERR
    Initialize(
        const WString& srcPath
    );

    //
    // get the list of UI languages supported by the application
    //
    const WnUiLanguageVector& 
    GetUiLanguagesSupportedByApp() const
    {
        return mUiLanguagesSupportedByApp;
    }

    //
    // Get the UI language supported by the application that should be used. If no language is selected 
    // the state of the returned instance is invalid.
    //
    const WnUiLanguage&
    GetSelectedUiLanguage() const
    {
        return mSelectedUILanguage;
    }

    //
    // select the UI language supported by the application that should be used
    //
    // parameters:
    //      idStr       identifier of the UI language
    //
    WNERR
    SelectedUiLanguage(
        const WString& idStr
    );

    //
    // add a new group for UI language texts
    //
    // Note: Since the group is copied internally it should contain all texts before it is added.
    //    
    void AddUiLanguageTextGroup(const WnUiLanguageTextGroup& group)
    {
        //add the new text to the list
        mUiLanguageTextGroups.push_back(group);
    }

    //
    // discard all registered languages texts
    //
    void
    ClearAllUiLanguageTexts()
    {
        for (auto& group : mUiLanguageTextGroups) {
            group.Clear();
        }
        mUiLanguageTextGroups.clear();
    }

    //
    // find the UI language text with the given identifier, or return nullptr if the text doesn't exist
    //
    UiLanguageTextHandle
    FindUILanguageText(
        const wchar_t* idStr
    ) const;

    //
    // find the given group for UI language texts, or return nullptr if the group doesn't exist
    //
    const WnUiLanguageTextGroup*
    FindUILanguageTextGroup(
        const wchar_t* idStr
    ) const;

    //
    // get the UI language text for the currently selected UI language
    //
    // parameters:
    //      handle           identifier handle
    //
    // returns:
    //      Language text, if available, otherwise the default text. An empty string is returned for an unknown identifier.
    WString
    GetUiLanguageText(
        const UiLanguageTextHandle& handle
    ) const;

    //
    // load the texts for the supported UI language to use
    //
    WNERR
    LoadTextsForSelectedUiLanguage();

    //
    // Get all UI language texts of the selected UI language, for which no language-specific text was loaded.
    // The texts are returned with their corresponding group.
    //
    void
    GetMissingTextsOfSelectedUiLanguage(
        WnUiLanguageTextGroupVector& missingTexts
    ) const;

    //
    // get the full path and name of the file containing the UI language texts of the selected UI language
    //
    WString
    GetFileForSelectedUiLanguageTexts() const;

    //
    // write a language file for the default UI language (English) with the default texts, including comments
    //
    // parameters:
    //      filePathAndName     Full path and name of the file.
    //                          Note: Writing to some directories may not be permitted or may require administrator privileges.
    //                                Therefore a subfolder in the current user's folder is recommended, e.g. the temp-folder. 
    //                          Note furthermore that the directory of the file must already exist. Otherwise the function fails.
    // 
    WNERR
    WriteDefaultUiLanguageFile(
        WString filePathAndName
        );

    static
    const wchar_t*
    GetIdentifierStrForHandle(UiLanguageTextHandle handle)
    {
        return (handle ? handle->GetIdentifierStr() : nullptr);
    }  

private:
    //
    // Load the information about all UI languages supported by the application from the given directory.
    // In this directory, the function expects a subdirectory for each supported language, where the name
    // of the subdirectory corresponds to its identifier string (Examples: en-GB, zh-Hans_HK, zh-Hans, de).
    // Other subdirectories should not be present in the given directory.
    //
    // The method also returns with success, if no supported language could be found. 
    //
    // parameters:  
    //      srcPath    full path of the directory where the information about the UI languages is stored
    //
    WNERR
    LoadUiLanguagesSupportedByApp(
        const WString& srcPath
    );

    //
    // Calculate the UI language supported by the application that should be used due to the UI settings 
    // of the current user.
    //
    // The method also returns with success, if no supported language is suitable and therefore no no language could be selected.
    //
    WNERR
    CalcSelectedUiLanguage();    
       
//data
private:
    //UI languages supported by the application
    WnUiLanguageVector mUiLanguagesSupportedByApp;

    //UI language supported by the application that should be used. If no language is selected its state is invalid.
    WnUiLanguage mSelectedUILanguage;

    //Groups of UI language texts used by the application
    WnUiLanguageTextGroupVector mUiLanguageTextGroups;

    //full path of the directory where the information about the UI languages is stored
    WString mUiLanguageSrcPath;

    //extension of files which contain the UI language texts (e.g.: ".txt")     
    WString mUiLanguageTextFileExtension{UILM_LANGUAGE_TEXTS_FILE_EXTENSION};
};

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguageMgr_h__

/*************************** EOF **************************************/

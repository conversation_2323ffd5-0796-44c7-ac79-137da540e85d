#include <QScrollBar>
#include <qnamespace.h>

#include "fieldoriginbase2.h"
#include "inputs2m1.h"
#include "inputs2m2.h"

FieldOriginBase2::FieldOriginBase2(QWidget* parent)
    : QWidget(parent)
{
    setStyleSheet(QString("background: rgb(%1, %2, %3);").arg(mColorBG.red()).arg(mColorBG.green()).arg(mColorBG.blue()));
    mScrollArea.setParent(this);
    mScrollArea.setWidget(&mWidget);
    mScrollArea.setWidgetResizable(true);
    mScrollArea.setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea.setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOn);
    mScrollArea.horizontalScrollBar()->setContextMenuPolicy(Qt::NoContextMenu);
    mScrollArea.verticalScrollBar()->setContextMenuPolicy(Qt::NoContextMenu);
    mScrollArea.setFrameShape(QFrame::Shape::NoFrame);
    mScrollArea.setFrameShadow(QFrame::Shadow::Plain);
    mScrollArea.setLineWidth(0);
    connect(mScrollArea.horizontalScrollBar(), SIGNAL(valueChanged(int)), this, SLOT(in_mScrollArea_valueChanged(int)), Qt::UniqueConnection);
    QString style;
    style = "QScrollArea {"
            "   background-color: transparent;"
            "}"
            "QScrollBar::sub-line:vertical {"
            "   height: 0px;"
            "}"
            "QScrollBar::add-line:vertical {"
            "   height: 0px;"
            "}"
            "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {"
            "   background: none;"
            "}";
    mScrollArea.setStyleSheet(style);
    style = "QWidget {"
            "   background-color: transparent;"
            "}";
    mWidget.setStyleSheet(style);
    mWidgetAddition.setParent(&mWidget);
    mWidgetAddition.hide();
    connect(&mWidgetAddition, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mWidgetAddition_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);

    auto  sss = new InputS2M1(&mWidget);
    mWidgetList.append(new MixerWidget{true, sss});
        WKSPHandle.addObserver(sss);
    sss= new InputS2M1(&mWidget);
        WKSPHandle.addObserver(sss);
    sss->setHideMic();
    mWidgetList.append(  new MixerWidget{true, sss});
    auto sss1= new InputS2M2(&mWidget);
    WKSPHandle.addObserver(sss1);
    mWidgetList.append(  new MixerWidget{true, sss1});
}
FieldOriginBase2::~FieldOriginBase2()
{
    for(auto element : mWidgetList)
    {
        delete element;
    }
}


// override
void FieldOriginBase2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mRectBody = rect();
    mScrollArea.setGeometry(mRectBody);
    float wPixelPerRatio=mRectBody.width() / 100.0;
    int marginTop = wPixelPerRatio * 2;
    int marginBottom = wPixelPerRatio * 2;
    int marginLeft = wPixelPerRatio * 2;
    int marginRight = wPixelPerRatio * 4;
    int spacing = wPixelPerRatio * 1.2;
    int y=marginTop, w=mRectBody.width() - marginLeft - marginRight, h=0;
    for(auto element : mWidgetList)
    {
        if(element->visible)
        {
            h = qMax(w, element->widget->minimumWidth()) / ((float) element->widget->minimumWidth()) * element->widget->minimumHeight();
            element->widget->setGeometry(marginLeft, y, w, h);
            y += h;
            y += spacing;
        }
    }
    y -= spacing;
    y += marginBottom;
    mWidget.setFixedHeight(y);
    mRectWidgetArea = mRectBody;
    QString style = QString("QScrollBar:vertical {"
                    "   background: rgb(22, 22, 22);"
                    "   width: %1px;"
                    "   border-radius: %2px;"
                    "   margin-top: %3px;"
                    "   margin-bottom: %3px;"
                    "   margin-left: %4px;"
                    "   margin-right: %4px;"
                    "}").arg(marginRight * 1).arg(marginRight * 0.1).arg(marginRight * 0.3).arg(marginRight * 0.3);
    if(mScrollArea.verticalScrollBar()->maximum() > 0)
    {
        style += QString("QScrollBar::handle:vertical {"
                        "   background: rgb(161, 161, 161);"
                        "   min-height: 20px;"
                        "   border-radius: %1px;"
                        "}"
                        "QScrollBar::handle:vertical:hover {"
                        "   background: rgb(224, 224, 224);"
                        "}"
                        "QScrollBar::handle:vertical:pressed {"
                        "   background: rgb(224, 224, 224);"
                        "}").arg(marginRight * 0.1);
    }
    else
    {
        style += "QScrollBar::handle:vertical { background: transparent; }";
    }
    mScrollArea.verticalScrollBar()->setStyleSheet(style);
}
void FieldOriginBase2::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
}
void FieldOriginBase2::drawBG(QPainter *painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(mColorBG));
    painter->drawRect(rect());
    painter->restore();
}

// slot
void FieldOriginBase2::in_mScrollArea_valueChanged(int value)
{
    mScrollBarValue = value;
    update();
}
void FieldOriginBase2::in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(value);
    if(attribute == "Hide")
    {
        for(auto element : mWidgetList)
        {
            if(element->widget->getChannelName() == objectName)
            {
                element->visible = false;
                element->widget->setHidden(true);
                element->widget->setWidgetEnableWithUpdate(false);
                mWidgetAddition.setButtonVisible(objectName);
                QResizeEvent e(size(), size());
                resizeEvent(&e);
                update();
                emit attributeChanged(objectName, "Visible", QString::number(0));
                break;
            }
        }
    }
    else if(attribute == "Resize")
    {
        QResizeEvent e(size(), size());
        resizeEvent(&e);
        update();
    }
}
void FieldOriginBase2::in_mWidgetAddition_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(attribute);
    Q_UNUSED(value);
    for(auto element : mWidgetList)
    {
        if(element->widget->getChannelName() == objectName)
        {
            element->visible = true;
            element->widget->setHidden(false);
            element->widget->setWidgetEnableWithUpdate(true);
            QResizeEvent e(size(), size());
            resizeEvent(&e);
            update();
            emit attributeChanged(objectName, "Visible", QString::number(1));
            break;
        }
    }
}


// setter & getter
FieldOriginBase2& FieldOriginBase2::modifyWidgetList(QVector<OriginBase*> list)
{
    QVector<QString> nameList;
    for(auto element : mWidgetList)
    {
        delete element;
    }
    mWidgetList.clear();
    for(auto element : list)
    {
        nameList.append(element->getChannelName());
        element->setParent(&mWidget);
        element->setHidden(true);
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mWidgetListAll_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
        MixerWidget* newWidget=new MixerWidget();
        newWidget->visible = false;
        newWidget->widget = element;
        mWidgetList.append(newWidget);
    }
    mWidgetAddition.modifyButtonList(nameList);
    return *this;
}
FieldOriginBase2& FieldOriginBase2::setFont(QFont font)
{
    mFont = font;
    mWidgetAddition.setFont(mFont);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase2& FieldOriginBase2::setVisibleList(QVector<QString> list)
{
    QVector<QString> nameList;
    for(auto element : mWidgetList)
    {
        if(list.contains(element->widget->getChannelName()))
        {
            element->visible = true;
            element->widget->setHidden(false);
            element->widget->setWidgetEnable(true);
        }
        else
        {
            nameList.append(element->widget->getChannelName());
            element->visible = false;
            element->widget->setHidden(true);
            element->widget->setWidgetEnable(false);
        }
    }
    mWidgetAddition.setVisibleList(nameList);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase2& FieldOriginBase2::setFieldColor(QColor color)
{
    mColorBG = color;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase2& FieldOriginBase2::setWidgetAreaColor(QColor color)
{
    mColorWidgetArea = color;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase2& FieldOriginBase2::setWidgetAreaVisible(bool state)
{
    mWidgetAreaVisible = state;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase2& FieldOriginBase2::setAdditionVisible(bool state)
{
    mAdditionVisible = state;
    mWidgetAddition.setHidden(!mAdditionVisible);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase2& FieldOriginBase2::setAdditionButtonWeight(int weightWidth, int weightHeight)
{
    mWidgetAddition.setButtonWeight(weightWidth, weightHeight);
    return *this;
}


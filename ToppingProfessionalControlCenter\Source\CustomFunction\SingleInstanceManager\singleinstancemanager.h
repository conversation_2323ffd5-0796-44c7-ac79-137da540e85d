#ifndef SINGLEINSTANCEMANAGER_H
#define SINGLEINSTANCEMANAGER_H

#include <QObject>
#include "singleton.h"

class QSharedMemory;
class QLocalServer;
class QSystemTrayIcon;
class QMenu;
class SingleInstanceManager : public QObject
{
    Q_OBJECT
    BE_SINGLETON(SingleInstanceManager)
public:
    void check();
    void setupTrayIcon();
    void setTrayIcon(const QString& fileName);

private:
    explicit SingleInstanceManager(QObject *parent = nullptr);
    ~SingleInstanceManager();
    bool isAnotherInstanceRunning();
    void activateExistingInstance();
    void handleNewConnection();
    void initializeLocalServer();
    void createTrayMenu();
    void showWindows();

    QSharedMemory *m_sharedMemory;
    QString m_uniqueKey;
    QLocalServer *m_localServer;
    bool m_isFirstInstance;
    QSystemTrayIcon *m_trayIcon;
    QMenu *m_trayMenu;
};

#define SGIMHandle SingleInstanceManager::instance()

#endif // SINGLEINSTANCEMANAGER_H

<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EffectS1M2</class>
 <widget class="QWidget" name="EffectS1M2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>280</width>
    <height>280</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>160</width>
    <height>220</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QFrame" name="frameLeft">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>131</width>
        <height>281</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <widget class="QPushButton" name="pushButtonClose">
       <property name="geometry">
        <rect>
         <x>110</x>
         <y>10</y>
         <width>21</width>
         <height>20</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
      <widget class="QLineEdit" name="lineEdit">
       <property name="geometry">
        <rect>
         <x>9</x>
         <y>10</y>
         <width>91</width>
         <height>21</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignCenter</set>
       </property>
      </widget>
      <widget class="PushButtonS1M5" name="widgetPushButtonGroup1" native="true">
       <property name="geometry">
        <rect>
         <x>80</x>
         <y>40</y>
         <width>21</width>
         <height>131</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
      </widget>
      <widget class="VolumeMeterS1M1" name="widgetVolumeMeter" native="true">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>40</y>
         <width>21</width>
         <height>131</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
      </widget>
      <widget class="DialS1M3" name="widgetDial" native="true">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>170</y>
         <width>31</width>
         <height>31</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
      </widget>
      <widget class="PushButtonS1M7" name="widgetPushButtonGroup2" native="true">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>230</y>
         <width>90</width>
         <height>40</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
      </widget>
      <widget class="QLabel" name="labelWet">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>200</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Wet</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelDry">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>200</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Dry</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
       </property>
      </widget>
     </widget>
     <widget class="QFrame" name="frameRight">
      <property name="geometry">
       <rect>
        <x>140</x>
        <y>10</y>
        <width>131</width>
        <height>261</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <widget class="DialS1M3" name="widgetDialRoom" native="true">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>70</y>
         <width>31</width>
         <height>21</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
      </widget>
      <widget class="DialS1M3" name="widgetDialDecay" native="true">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>200</y>
         <width>31</width>
         <height>41</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButtonNavigation">
       <property name="geometry">
        <rect>
         <x>110</x>
         <y>40</y>
         <width>10</width>
         <height>45</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>8</width>
         <height>35</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
      <widget class="QLabel" name="labelMin">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>240</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Min</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelMax">
       <property name="geometry">
        <rect>
         <x>70</x>
         <y>240</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Max</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelRate">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>180</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Rate</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelDecay">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>160</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Decay</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignHCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelSize">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>40</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Size</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelRoom">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>20</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Room</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignHCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelSmall">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>100</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>Small</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelLarge">
       <property name="geometry">
        <rect>
         <x>70</x>
         <y>100</y>
         <width>53</width>
         <height>21</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Large</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
       </property>
      </widget>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>VolumeMeterS1M1</class>
   <extends>QWidget</extends>
   <header location="global">volumemeters1m1.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>DialS1M3</class>
   <extends>QWidget</extends>
   <header location="global">dials1m3.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>PushButtonS1M7</class>
   <extends>QWidget</extends>
   <header location="global">pushbuttons1m7.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>PushButtonS1M5</class>
   <extends>QWidget</extends>
   <header location="global">pushbuttons1m5.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>

#ifndef MAINWINDOW_M62_H
#define MAINWINDOW_M62_H


#include <QTimer>
#include <QWidget>

#include "devicem62.h"
#include "inputs1m1.h"
#include "inputs1m2.h"
#include "inputs1m3.h"
#include "inputs1m4.h"
#include "inputs1m5.h"
#include "inputs1m6.h"
#include "mixers1m2.h"
#include "mixers1m3.h"
#include "mixers1m4.h"
#include "outputs1m1.h"
#include "outputs1m3.h"
#include "origins1m1.h"
#include "origins1m2.h"
#include "origins1m3.h"
#include "origins1m4.h"
#include "origins1m6.h"
#include "origins1m7.h"
#include "origins1m8.h"
#include "origins1m9.h"
#include "loopbacks1m1.h"
#include "loopbacks1m2.h"
#include "widgetsytem1.h"
#include "widgetaudio1.h"
#include "widgetabout1.h"
#include "tabwidgets1m1.h"
#include "mainwindow_base.h"
#include "m62_privatewidget1.h"
#include "m62_privatewidget3.h"
#include "m62_privatewidget7.h"


namespace Ui {
class MainWindow_M62;
}


class MainWindow_M62 : public MainWindow_Base
{
    Q_OBJECT
public:
    explicit MainWindow_M62(QWidget* parent=nullptr);
    ~MainWindow_M62();
    enum PageID
    {
        PageUpgd=0,
        PageFcty,
        PageMble,
        PageLive,
        PageTyro,
        PageProf
    };
    void setPageToActive(PageID id);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void closeEvent(QCloseEvent* e) override;
    void changeEvent(QEvent* e) override;
    void sendAuthInfoToDevice() override;
    void onAuthResult(int result) override;
    void onUSBAudioAttributeChanged(QString attribute, QString value) override;
private:
    Ui::MainWindow_M62* ui;
    DeviceM62 mDevice;
    TabWidgetS1M1 mSettings;
    WidgetSytem1 mSettingsSystem;
    WidgetAudio1 mSettingsAudio;
    WidgetAbout1 mSettingsAbout;
    M62_PrivateWidget7 mSettingsDevice;
    PageID mCurrentPage=PageLive;
    QVector<WorkspaceObserver*> mPageLiveWorkspaceObserverList;
    QVector<WorkspaceObserver*> mPageTyroWorkspaceObserverList;
    QVector<WorkspaceObserver*> mPageProfWorkspaceObserverList;
    QVector<int> mVersionList={0, 0, 0, 0, 0, 0};
    void initMainWindowWidget();
    // PageFcty
    void initPageFctyFieldHead();
    void initPageFctyWidget();
    // PageMble
    void initPageMbleFieldHead();
    void initPageMbleWidget();
    // PageLive
    InputS1M1 mPageLiveFieldInput_IN1;
    InputS1M2 mPageLiveFieldInput_IN2;
    InputS1M3 mPageLiveFieldInput_AUX;
    InputS1M3 mPageLiveFieldInput_BT;
    InputS1M3 mPageLiveFieldInput_OTGIN;
    MixerS1M3 mPageLiveFieldMixer_IN12;
    MixerS1M2 mPageLiveFieldMixer_AUX;
    MixerS1M2 mPageLiveFieldMixer_BT;
    MixerS1M2 mPageLiveFieldMixer_OTGIN;
    MixerS1M2 mPageLiveFieldMixer_FX;
    MixerS1M2 mPageLiveFieldMixer_PB12;
    MixerS1M2 mPageLiveFieldMixer_PB34;
    MixerS1M2 mPageLiveFieldMixer_PB56;
    MixerS1M2 mPageLiveFieldMixer_PB78;
    MixerS1M2 mPageLiveFieldMixer_PB910;
    M62_PrivateWidget3 mPageLiveFieldEffect_Integration;
    LoopbackS1M1 mPageLiveFieldLoopback_LB12;
    LoopbackS1M1 mPageLiveFieldLoopback_LB34;
    LoopbackS1M1 mPageLiveFieldLoopback_LB56;
    LoopbackS1M1 mPageLiveFieldLoopback_LB78;
    OutputS1M1 mPageLiveFieldOutput_HP;
    OutputS1M1 mPageLiveFieldOutput_OTGOUT;
    M62_PrivateWidget1 mPageLiveWidgetDucking;
    void initPageLiveFieldHead();
    void initPageLiveFieldInput();
    void initPageLiveFieldMixer();
    void initPageLiveFieldEffect();
    void initPageLiveFieldLoopback();
    void initPageLiveFieldOutput();
    void initPageLiveWidget();
    // PageTyro
    OriginS1M1 mPageTyroFieldTop_IN1;
    OriginS1M2 mPageTyroFieldTop_IN2;
    OriginS1M9 mPageTyroFieldTop_AUX;
    OriginS1M9 mPageTyroFieldTop_BT;
    OriginS1M9 mPageTyroFieldTop_OTGIN;
    OriginS1M8 mPageTyroFieldTop_PB12;
    OriginS1M8 mPageTyroFieldTop_PB34;
    OriginS1M3 mPageTyroFieldBottom_ConfigMenu;
    OriginS1M4 mPageTyroFieldBottom_Effect;
    OriginS1M7 mPageTyroFieldBottom_LB12;
    OriginS1M6 mPageTyroFieldBottom_HP;
    OriginS1M6 mPageTyroFieldBottom_OTGOUT;
    void initPageTyroFieldTop();
    void initPageTyroFieldBottom();
    // PageProf
    InputS1M4 mPageProfFieldInput_IN1;
    InputS1M5 mPageProfFieldInput_IN2;
    InputS1M6 mPageProfFieldInput_AUX;
    InputS1M6 mPageProfFieldInput_BT;
    InputS1M6 mPageProfFieldInput_OTGIN;
    MixerS1M4 mPageProfFieldMixer_IN12;
    MixerS1M4 mPageProfFieldMixer_AUX;
    MixerS1M4 mPageProfFieldMixer_BT;
    MixerS1M4 mPageProfFieldMixer_OTGIN;
    MixerS1M4 mPageProfFieldMixer_PB12;
    MixerS1M4 mPageProfFieldMixer_PB34;
    MixerS1M4 mPageProfFieldMixer_PB56;
    MixerS1M4 mPageProfFieldMixer_PB78;
    MixerS1M4 mPageProfFieldMixer_PB910;
    LoopbackS1M2 mPageProfFieldLoopback_LB12;
    LoopbackS1M2 mPageProfFieldLoopback_LB34;
    LoopbackS1M2 mPageProfFieldLoopback_LB56;
    LoopbackS1M2 mPageProfFieldLoopback_LB78;
    OutputS1M3 mPageProfFieldOutput_HP;
    OutputS1M3 mPageProfFieldOutput_OTGOUT;
    void initPageProfFieldHead();
    void initPageProfFieldInput();
    void initPageProfFieldMixer();
    void initPageProfFieldLoopback();
    void initPageProfFieldOutput();
    void initPageProfWidget();
    // setup
    void setupMainWindowWidget();
    void setupPageMble();
    void setupPageLive();
    void setupPageTyro();
    void setupPageProf();
    // adjustGeometry
    void adjustGeometryPageFcty();
    void adjustGeometryPageMble();
    void adjustGeometryPageLive();
    void adjustGeometryPageTyro();
    void adjustGeometryPageProf();
    // do
    void doNewFrameReceived_cmdS(DeviceType1::FrameInfo& frame);
    void doNewFrameReceived_cmdI(DeviceType1::FrameInfo& frame);
    void doNewFrameReceived_cmdM(DeviceType1::FrameInfo& frame);
    void doNewFrameReceived_cmdE(DeviceType1::FrameInfo& frame);
    void doNewFrameReceived_cmdL(DeviceType1::FrameInfo& frame);
    void doNewFrameReceived_cmdO(DeviceType1::FrameInfo& frame);
    void doNewFrameReceived_cmdT(DeviceType1::FrameInfo& frame);
private slots:
    void in_mWidgetAll_attributeChanged(QString objectName, QString attribute, QString value);
    void in_mDevice_newFrameReceived(DeviceType1::FrameInfo frame);
    void on_PageFctyPushButton1_clicked();
    void on_PageFctyPushButton2_clicked();
    void on_PageMblePushButton_clicked();
};


#endif // MAINWINDOW_M62_H


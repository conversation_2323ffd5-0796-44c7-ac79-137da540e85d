#ifndef PUSHBUTTONS1M11_H
#define PUSHBUTTONS1M11_H


#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M11 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M11(QWidget* parent=nullptr);
    ~PushButtonS1M11();
    enum ButtonID
    {
        buttonSOLO=0,
        buttonMUTE,
        buttonANTI
    };
    PushButtonS1M11& setFont(QFont font);
    PushButtonS1M11& setPushButtonWeightWidth(int weight);
    PushButtonS1M11& setPushButtonStateSOLO(bool state);
    PushButtonS1M11& setPushButtonStateMUTE(bool state);
    PushButtonS1M11& setPushButtonStateANTI(bool state);
    PushButtonS1M11& setPushButtonClickedSOLO(bool state);
    PushButtonS1M11& setPushButtonClickedMUTE(bool state);
    PushButtonS1M11& setPushButtonClickedANTI(bool state);
    bool getPushButtonStateSOLO();
    bool getPushButtonStateMUTE();
    bool getPushButtonStateANTI();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateSOLO=false;
    bool mPushButtonStateMUTE=false;
    bool mPushButtonStateANTI=false;
    QPushButton mPushButtonSOLO;
    QPushButton mPushButtonMUTE;
    QPushButton mPushButtonANTI;
    int mWeightWidth=40;
    int mRadius=0;
private slots:
    void in_mPushButtonSOLO_clicked();
    void in_mPushButtonMUTE_clicked();
    void in_mPushButtonANTI_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M11_H


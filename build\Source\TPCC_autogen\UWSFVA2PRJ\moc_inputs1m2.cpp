/****************************************************************************
** Meta object code from reading C++ file 'inputs1m2.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'inputs1m2.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN9InputS1M2E_t {};
} // unnamed namespace

template <> constexpr inline auto InputS1M2::qt_create_metaobjectdata<qt_meta_tag_ZN9InputS1M2E_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "InputS1M2",
        "in_mAutoGain_attributeChanged",
        "",
        "attribute",
        "value",
        "in_mTimer_timeout",
        "in_widgetPushButtonGroup1_stateChanged",
        "button",
        "state",
        "in_widgetPushButtonGroup2_stateChanged",
        "in_widgetDial_valueChanged",
        "in_widgetHSlider_valueChanged",
        "on_lineEdit_textChanged",
        "arg1",
        "on_lineEdit_editingFinished",
        "on_pushButtonClose_clicked"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'in_mAutoGain_attributeChanged'
        QtMocHelpers::SlotData<void(QString, QString)>(1, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::QString, 4 },
        }}),
        // Slot 'in_mTimer_timeout'
        QtMocHelpers::SlotData<void()>(5, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'in_widgetPushButtonGroup1_stateChanged'
        QtMocHelpers::SlotData<void(QString, QString)>(6, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 7 }, { QMetaType::QString, 8 },
        }}),
        // Slot 'in_widgetPushButtonGroup2_stateChanged'
        QtMocHelpers::SlotData<void(QString, QString)>(9, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 7 }, { QMetaType::QString, 8 },
        }}),
        // Slot 'in_widgetDial_valueChanged'
        QtMocHelpers::SlotData<void(float)>(10, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Float, 4 },
        }}),
        // Slot 'in_widgetHSlider_valueChanged'
        QtMocHelpers::SlotData<void(float)>(11, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Float, 4 },
        }}),
        // Slot 'on_lineEdit_textChanged'
        QtMocHelpers::SlotData<void(const QString &)>(12, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 13 },
        }}),
        // Slot 'on_lineEdit_editingFinished'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButtonClose_clicked'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<InputS1M2, qt_meta_tag_ZN9InputS1M2E_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject InputS1M2::staticMetaObject = { {
    QMetaObject::SuperData::link<InputBase::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9InputS1M2E_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9InputS1M2E_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN9InputS1M2E_t>.metaTypes,
    nullptr
} };

void InputS1M2::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<InputS1M2 *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->in_mAutoGain_attributeChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 1: _t->in_mTimer_timeout(); break;
        case 2: _t->in_widgetPushButtonGroup1_stateChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 3: _t->in_widgetPushButtonGroup2_stateChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 4: _t->in_widgetDial_valueChanged((*reinterpret_cast< std::add_pointer_t<float>>(_a[1]))); break;
        case 5: _t->in_widgetHSlider_valueChanged((*reinterpret_cast< std::add_pointer_t<float>>(_a[1]))); break;
        case 6: _t->on_lineEdit_textChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: _t->on_lineEdit_editingFinished(); break;
        case 8: _t->on_pushButtonClose_clicked(); break;
        default: ;
        }
    }
}

const QMetaObject *InputS1M2::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *InputS1M2::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9InputS1M2E_t>.strings))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "WorkspaceObserver"))
        return static_cast< WorkspaceObserver*>(this);
    if (!strcmp(_clname, "AppSettingsObserver"))
        return static_cast< AppSettingsObserver*>(this);
    return InputBase::qt_metacast(_clname);
}

int InputS1M2::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = InputBase::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 9;
    }
    return _id;
}
QT_WARNING_POP

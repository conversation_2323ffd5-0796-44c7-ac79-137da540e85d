/********************************************************************************
** Form generated from reading UI file 'm62_privatewidget1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_M62_PRIVATEWIDGET1_H
#define UI_M62_PRIVATEWIDGET1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include <dials1m5.h>
#include <m62_privatewidget1_1.h>

QT_BEGIN_NAMESPACE

class Ui_M62_PrivateWidget1
{
public:
    QVBoxLayout *verticalLayout;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *horizontalLayout_2;
    QSpacerItem *horizontalSpacer_3;
    QWidget *widget_2;
    M62_PrivateWidget1_1 *input;
    QSpacerItem *horizontalSpacer_4;
    QWidget *widget_3;
    QWidget *widgetDials;
    QWidget *widgetD;
    QWidget *widgetThreshold;
    QLabel *DialThresholdT;
    DialS1M5 *DialThreshold;
    QLabel *DialThresholdL;
    QLabel *DialThresholdR;
    QWidget *widgetlAttack;
    QLabel *DialAttackT;
    DialS1M5 *DialAttack;
    QLabel *DialAttackL;
    QLabel *DialAttackR;
    QWidget *widgeReduction;
    QLabel *DialReductionT;
    DialS1M5 *DialReduction;
    QLabel *DialReductionL;
    QLabel *DialReductionR;
    QWidget *widgetRelease;
    QLabel *DialReleaseT;
    DialS1M5 *DialRelease;
    QLabel *DialReleaseL;
    QLabel *DialReleaseR;
    QPushButton *buttonOFF;
    QSpacerItem *horizontalSpacer_5;
    QWidget *widget_4;
    QWidget *widgetDockingMap;
    QLabel *labelTitle_2;
    QWidget *widget;
    QPushButton *IN1BT;
    QLabel *labelAUX;
    QPushButton *IN2AUX;
    QLabel *labelpb56;
    QLabel *labelBT;
    QPushButton *playback7_8IN2;
    QPushButton *playback3_4IN1;
    QPushButton *IN1AUX;
    QPushButton *IN1OTG;
    QLabel *labelpb34;
    QPushButton *playback1_2IN2;
    QPushButton *IN2BT;
    QPushButton *playback5_6IN2;
    QLabel *labelOTG;
    QPushButton *playback5_6IN1;
    QPushButton *playback3_4IN2;
    QLabel *labelIN1;
    QLabel *labelpb12;
    QPushButton *playback7_8IN1;
    QPushButton *IN2OTG;
    QLabel *labelIN2;
    QPushButton *playback1_2IN1;
    QLabel *labelpb78;
    QLabel *labelpb910;
    QPushButton *playback9_10IN2;
    QPushButton *playback9_10IN1;
    QSpacerItem *horizontalSpacer_6;
    QSpacerItem *verticalSpacer_2;

    void setupUi(QWidget *M62_PrivateWidget1)
    {
        if (M62_PrivateWidget1->objectName().isEmpty())
            M62_PrivateWidget1->setObjectName("M62_PrivateWidget1");
        M62_PrivateWidget1->resize(1435, 686);
        M62_PrivateWidget1->setMinimumSize(QSize(0, 0));
        verticalLayout = new QVBoxLayout(M62_PrivateWidget1);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer = new QSpacerItem(20, 65, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName("horizontalLayout_2");
        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_3);

        widget_2 = new QWidget(M62_PrivateWidget1);
        widget_2->setObjectName("widget_2");
        widget_2->setStyleSheet(QString::fromUtf8(""));
        input = new M62_PrivateWidget1_1(widget_2);
        input->setObjectName("input");
        input->setGeometry(QRect(20, 70, 81, 181));
        input->setMinimumSize(QSize(32, 65));

        horizontalLayout_2->addWidget(widget_2);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_4);

        widget_3 = new QWidget(M62_PrivateWidget1);
        widget_3->setObjectName("widget_3");
        widget_3->setMinimumSize(QSize(0, 0));
        widget_3->setStyleSheet(QString::fromUtf8(""));
        widgetDials = new QWidget(widget_3);
        widgetDials->setObjectName("widgetDials");
        widgetDials->setGeometry(QRect(0, 0, 301, 331));
        widgetDials->setMinimumSize(QSize(53, 65));
        widgetD = new QWidget(widgetDials);
        widgetD->setObjectName("widgetD");
        widgetD->setGeometry(QRect(30, 20, 261, 231));
        widgetD->setMinimumSize(QSize(0, 0));
        widgetThreshold = new QWidget(widgetD);
        widgetThreshold->setObjectName("widgetThreshold");
        widgetThreshold->setGeometry(QRect(0, 0, 167, 157));
        widgetThreshold->setMinimumSize(QSize(50, 50));
        widgetThreshold->setStyleSheet(QString::fromUtf8(""));
        DialThresholdT = new QLabel(widgetThreshold);
        DialThresholdT->setObjectName("DialThresholdT");
        DialThresholdT->setGeometry(QRect(40, 0, 58, 16));
        DialThresholdT->setMinimumSize(QSize(0, 0));
        DialThresholdT->setAlignment(Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop);
        DialThreshold = new DialS1M5(widgetThreshold);
        DialThreshold->setObjectName("DialThreshold");
        DialThreshold->setGeometry(QRect(20, 30, 91, 81));
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(DialThreshold->sizePolicy().hasHeightForWidth());
        DialThreshold->setSizePolicy(sizePolicy);
        DialThreshold->setMinimumSize(QSize(0, 0));
        DialThresholdL = new QLabel(widgetThreshold);
        DialThresholdL->setObjectName("DialThresholdL");
        DialThresholdL->setGeometry(QRect(30, 130, 23, 16));
        DialThresholdL->setMinimumSize(QSize(0, 0));
        DialThresholdL->setStyleSheet(QString::fromUtf8(""));
        DialThresholdL->setAlignment(Qt::AlignmentFlag::AlignCenter);
        DialThresholdR = new QLabel(widgetThreshold);
        DialThresholdR->setObjectName("DialThresholdR");
        DialThresholdR->setGeometry(QRect(110, 130, 16, 16));
        DialThresholdR->setMinimumSize(QSize(0, 0));
        DialThresholdR->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetlAttack = new QWidget(widgetD);
        widgetlAttack->setObjectName("widgetlAttack");
        widgetlAttack->setGeometry(QRect(167, 0, 167, 157));
        widgetlAttack->setMinimumSize(QSize(50, 50));
        widgetlAttack->setStyleSheet(QString::fromUtf8(""));
        DialAttackT = new QLabel(widgetlAttack);
        DialAttackT->setObjectName("DialAttackT");
        DialAttackT->setGeometry(QRect(50, 10, 53, 21));
        DialAttackT->setMinimumSize(QSize(0, 0));
        DialAttackT->setAlignment(Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop);
        DialAttack = new DialS1M5(widgetlAttack);
        DialAttack->setObjectName("DialAttack");
        DialAttack->setGeometry(QRect(40, 40, 61, 51));
        sizePolicy.setHeightForWidth(DialAttack->sizePolicy().hasHeightForWidth());
        DialAttack->setSizePolicy(sizePolicy);
        DialAttack->setMinimumSize(QSize(0, 0));
        DialAttackL = new QLabel(widgetlAttack);
        DialAttackL->setObjectName("DialAttackL");
        DialAttackL->setGeometry(QRect(10, 110, 53, 21));
        DialAttackL->setMinimumSize(QSize(0, 0));
        DialAttackL->setStyleSheet(QString::fromUtf8(""));
        DialAttackL->setAlignment(Qt::AlignmentFlag::AlignCenter);
        DialAttackR = new QLabel(widgetlAttack);
        DialAttackR->setObjectName("DialAttackR");
        DialAttackR->setGeometry(QRect(120, 130, 53, 21));
        DialAttackR->setMinimumSize(QSize(0, 0));
        DialAttackR->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgeReduction = new QWidget(widgetD);
        widgeReduction->setObjectName("widgeReduction");
        widgeReduction->setGeometry(QRect(0, 157, 167, 156));
        widgeReduction->setMinimumSize(QSize(50, 50));
        widgeReduction->setStyleSheet(QString::fromUtf8(""));
        DialReductionT = new QLabel(widgeReduction);
        DialReductionT->setObjectName("DialReductionT");
        DialReductionT->setGeometry(QRect(50, 20, 61, 21));
        DialReductionT->setMinimumSize(QSize(0, 0));
        DialReductionT->setAlignment(Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop);
        DialReduction = new DialS1M5(widgeReduction);
        DialReduction->setObjectName("DialReduction");
        DialReduction->setGeometry(QRect(50, 50, 61, 61));
        sizePolicy.setHeightForWidth(DialReduction->sizePolicy().hasHeightForWidth());
        DialReduction->setSizePolicy(sizePolicy);
        DialReduction->setMinimumSize(QSize(0, 0));
        DialReductionL = new QLabel(widgeReduction);
        DialReductionL->setObjectName("DialReductionL");
        DialReductionL->setGeometry(QRect(10, 120, 53, 21));
        DialReductionL->setMinimumSize(QSize(0, 0));
        DialReductionL->setStyleSheet(QString::fromUtf8(""));
        DialReductionL->setAlignment(Qt::AlignmentFlag::AlignCenter);
        DialReductionR = new QLabel(widgeReduction);
        DialReductionR->setObjectName("DialReductionR");
        DialReductionR->setGeometry(QRect(100, 120, 53, 21));
        DialReductionR->setMinimumSize(QSize(0, 0));
        DialReductionR->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetRelease = new QWidget(widgetD);
        widgetRelease->setObjectName("widgetRelease");
        widgetRelease->setGeometry(QRect(167, 157, 167, 156));
        widgetRelease->setMinimumSize(QSize(50, 50));
        widgetRelease->setStyleSheet(QString::fromUtf8(""));
        DialReleaseT = new QLabel(widgetRelease);
        DialReleaseT->setObjectName("DialReleaseT");
        DialReleaseT->setGeometry(QRect(40, 20, 53, 21));
        DialReleaseT->setMinimumSize(QSize(0, 0));
        DialReleaseT->setAlignment(Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop);
        DialRelease = new DialS1M5(widgetRelease);
        DialRelease->setObjectName("DialRelease");
        DialRelease->setGeometry(QRect(50, 50, 71, 61));
        sizePolicy.setHeightForWidth(DialRelease->sizePolicy().hasHeightForWidth());
        DialRelease->setSizePolicy(sizePolicy);
        DialRelease->setMinimumSize(QSize(0, 0));
        DialReleaseL = new QLabel(widgetRelease);
        DialReleaseL->setObjectName("DialReleaseL");
        DialReleaseL->setGeometry(QRect(20, 120, 53, 21));
        DialReleaseL->setMinimumSize(QSize(0, 0));
        DialReleaseL->setStyleSheet(QString::fromUtf8(""));
        DialReleaseL->setAlignment(Qt::AlignmentFlag::AlignCenter);
        DialReleaseR = new QLabel(widgetRelease);
        DialReleaseR->setObjectName("DialReleaseR");
        DialReleaseR->setGeometry(QRect(110, 120, 53, 21));
        DialReleaseR->setMinimumSize(QSize(0, 0));
        DialReleaseR->setAlignment(Qt::AlignmentFlag::AlignCenter);
        buttonOFF = new QPushButton(widgetDials);
        buttonOFF->setObjectName("buttonOFF");
        buttonOFF->setGeometry(QRect(160, 270, 75, 51));
        QSizePolicy sizePolicy1(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Preferred);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(buttonOFF->sizePolicy().hasHeightForWidth());
        buttonOFF->setSizePolicy(sizePolicy1);
        buttonOFF->setMinimumSize(QSize(0, 15));
        buttonOFF->setCheckable(true);

        horizontalLayout_2->addWidget(widget_3);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_5);

        widget_4 = new QWidget(M62_PrivateWidget1);
        widget_4->setObjectName("widget_4");
        widgetDockingMap = new QWidget(widget_4);
        widgetDockingMap->setObjectName("widgetDockingMap");
        widgetDockingMap->setGeometry(QRect(30, 10, 221, 351));
        widgetDockingMap->setMinimumSize(QSize(32, 65));
        labelTitle_2 = new QLabel(widgetDockingMap);
        labelTitle_2->setObjectName("labelTitle_2");
        labelTitle_2->setGeometry(QRect(40, 10, 79, 16));
        labelTitle_2->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widget = new QWidget(widgetDockingMap);
        widget->setObjectName("widget");
        widget->setGeometry(QRect(10, 40, 151, 261));
        IN1BT = new QPushButton(widget);
        IN1BT->setObjectName("IN1BT");
        IN1BT->setGeometry(QRect(0, 65, 30, 16));
        QSizePolicy sizePolicy2(QSizePolicy::Policy::Preferred, QSizePolicy::Policy::Preferred);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(IN1BT->sizePolicy().hasHeightForWidth());
        IN1BT->setSizePolicy(sizePolicy2);
        IN1BT->setMinimumSize(QSize(10, 10));
        IN1BT->setMaximumSize(QSize(16777215, 16777215));
        IN1BT->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        IN1BT->setCheckable(true);
        labelAUX = new QLabel(widget);
        labelAUX->setObjectName("labelAUX");
        labelAUX->setGeometry(QRect(96, 35, 25, 16));
        labelAUX->setAlignment(Qt::AlignmentFlag::AlignCenter);
        IN2AUX = new QPushButton(widget);
        IN2AUX->setObjectName("IN2AUX");
        IN2AUX->setGeometry(QRect(48, 35, 30, 16));
        sizePolicy2.setHeightForWidth(IN2AUX->sizePolicy().hasHeightForWidth());
        IN2AUX->setSizePolicy(sizePolicy2);
        IN2AUX->setMinimumSize(QSize(10, 10));
        IN2AUX->setMaximumSize(QSize(16777215, 16777215));
        IN2AUX->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        IN2AUX->setCheckable(true);
        labelpb56 = new QLabel(widget);
        labelpb56->setObjectName("labelpb56");
        labelpb56->setGeometry(QRect(96, 185, 74, 16));
        labelpb56->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelBT = new QLabel(widget);
        labelBT->setObjectName("labelBT");
        labelBT->setGeometry(QRect(96, 65, 16, 16));
        labelBT->setAlignment(Qt::AlignmentFlag::AlignCenter);
        playback7_8IN2 = new QPushButton(widget);
        playback7_8IN2->setObjectName("playback7_8IN2");
        playback7_8IN2->setGeometry(QRect(48, 215, 30, 16));
        sizePolicy2.setHeightForWidth(playback7_8IN2->sizePolicy().hasHeightForWidth());
        playback7_8IN2->setSizePolicy(sizePolicy2);
        playback7_8IN2->setMinimumSize(QSize(10, 10));
        playback7_8IN2->setMaximumSize(QSize(16777215, 16777215));
        playback7_8IN2->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback7_8IN2->setCheckable(true);
        playback3_4IN1 = new QPushButton(widget);
        playback3_4IN1->setObjectName("playback3_4IN1");
        playback3_4IN1->setGeometry(QRect(0, 155, 30, 16));
        sizePolicy2.setHeightForWidth(playback3_4IN1->sizePolicy().hasHeightForWidth());
        playback3_4IN1->setSizePolicy(sizePolicy2);
        playback3_4IN1->setMinimumSize(QSize(10, 10));
        playback3_4IN1->setMaximumSize(QSize(16777215, 16777215));
        playback3_4IN1->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback3_4IN1->setCheckable(true);
        IN1AUX = new QPushButton(widget);
        IN1AUX->setObjectName("IN1AUX");
        IN1AUX->setGeometry(QRect(0, 35, 30, 16));
        sizePolicy2.setHeightForWidth(IN1AUX->sizePolicy().hasHeightForWidth());
        IN1AUX->setSizePolicy(sizePolicy2);
        IN1AUX->setMinimumSize(QSize(10, 10));
        IN1AUX->setMaximumSize(QSize(16777215, 16777215));
        IN1AUX->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        IN1AUX->setCheckable(true);
        IN1OTG = new QPushButton(widget);
        IN1OTG->setObjectName("IN1OTG");
        IN1OTG->setGeometry(QRect(0, 95, 30, 16));
        sizePolicy2.setHeightForWidth(IN1OTG->sizePolicy().hasHeightForWidth());
        IN1OTG->setSizePolicy(sizePolicy2);
        IN1OTG->setMinimumSize(QSize(10, 10));
        IN1OTG->setMaximumSize(QSize(16777215, 16777215));
        IN1OTG->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        IN1OTG->setCheckable(true);
        labelpb34 = new QLabel(widget);
        labelpb34->setObjectName("labelpb34");
        labelpb34->setGeometry(QRect(96, 155, 74, 16));
        labelpb34->setAlignment(Qt::AlignmentFlag::AlignCenter);
        playback1_2IN2 = new QPushButton(widget);
        playback1_2IN2->setObjectName("playback1_2IN2");
        playback1_2IN2->setGeometry(QRect(48, 125, 30, 16));
        sizePolicy2.setHeightForWidth(playback1_2IN2->sizePolicy().hasHeightForWidth());
        playback1_2IN2->setSizePolicy(sizePolicy2);
        playback1_2IN2->setMinimumSize(QSize(10, 10));
        playback1_2IN2->setMaximumSize(QSize(16777215, 16777215));
        playback1_2IN2->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback1_2IN2->setCheckable(true);
        IN2BT = new QPushButton(widget);
        IN2BT->setObjectName("IN2BT");
        IN2BT->setGeometry(QRect(48, 65, 30, 16));
        sizePolicy2.setHeightForWidth(IN2BT->sizePolicy().hasHeightForWidth());
        IN2BT->setSizePolicy(sizePolicy2);
        IN2BT->setMinimumSize(QSize(10, 10));
        IN2BT->setMaximumSize(QSize(16777215, 16777215));
        IN2BT->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        IN2BT->setCheckable(true);
        playback5_6IN2 = new QPushButton(widget);
        playback5_6IN2->setObjectName("playback5_6IN2");
        playback5_6IN2->setGeometry(QRect(48, 185, 30, 16));
        sizePolicy2.setHeightForWidth(playback5_6IN2->sizePolicy().hasHeightForWidth());
        playback5_6IN2->setSizePolicy(sizePolicy2);
        playback5_6IN2->setMinimumSize(QSize(10, 10));
        playback5_6IN2->setMaximumSize(QSize(16777215, 16777215));
        playback5_6IN2->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback5_6IN2->setCheckable(true);
        labelOTG = new QLabel(widget);
        labelOTG->setObjectName("labelOTG");
        labelOTG->setGeometry(QRect(96, 95, 43, 16));
        labelOTG->setAlignment(Qt::AlignmentFlag::AlignCenter);
        playback5_6IN1 = new QPushButton(widget);
        playback5_6IN1->setObjectName("playback5_6IN1");
        playback5_6IN1->setGeometry(QRect(0, 185, 30, 16));
        sizePolicy2.setHeightForWidth(playback5_6IN1->sizePolicy().hasHeightForWidth());
        playback5_6IN1->setSizePolicy(sizePolicy2);
        playback5_6IN1->setMinimumSize(QSize(10, 10));
        playback5_6IN1->setMaximumSize(QSize(16777215, 16777215));
        playback5_6IN1->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback5_6IN1->setCheckable(true);
        playback3_4IN2 = new QPushButton(widget);
        playback3_4IN2->setObjectName("playback3_4IN2");
        playback3_4IN2->setGeometry(QRect(48, 155, 30, 16));
        sizePolicy2.setHeightForWidth(playback3_4IN2->sizePolicy().hasHeightForWidth());
        playback3_4IN2->setSizePolicy(sizePolicy2);
        playback3_4IN2->setMinimumSize(QSize(10, 10));
        playback3_4IN2->setMaximumSize(QSize(16777215, 16777215));
        playback3_4IN2->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback3_4IN2->setCheckable(true);
        labelIN1 = new QLabel(widget);
        labelIN1->setObjectName("labelIN1");
        labelIN1->setGeometry(QRect(0, 0, 21, 16));
        labelIN1->setAlignment(Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignHCenter);
        labelpb12 = new QLabel(widget);
        labelpb12->setObjectName("labelpb12");
        labelpb12->setGeometry(QRect(96, 125, 74, 16));
        labelpb12->setAlignment(Qt::AlignmentFlag::AlignCenter);
        playback7_8IN1 = new QPushButton(widget);
        playback7_8IN1->setObjectName("playback7_8IN1");
        playback7_8IN1->setGeometry(QRect(0, 215, 30, 16));
        sizePolicy2.setHeightForWidth(playback7_8IN1->sizePolicy().hasHeightForWidth());
        playback7_8IN1->setSizePolicy(sizePolicy2);
        playback7_8IN1->setMinimumSize(QSize(10, 10));
        playback7_8IN1->setMaximumSize(QSize(16777215, 16777215));
        playback7_8IN1->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback7_8IN1->setCheckable(true);
        IN2OTG = new QPushButton(widget);
        IN2OTG->setObjectName("IN2OTG");
        IN2OTG->setGeometry(QRect(48, 95, 30, 16));
        sizePolicy2.setHeightForWidth(IN2OTG->sizePolicy().hasHeightForWidth());
        IN2OTG->setSizePolicy(sizePolicy2);
        IN2OTG->setMinimumSize(QSize(10, 10));
        IN2OTG->setMaximumSize(QSize(16777215, 16777215));
        IN2OTG->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        IN2OTG->setCheckable(true);
        labelIN2 = new QLabel(widget);
        labelIN2->setObjectName("labelIN2");
        labelIN2->setGeometry(QRect(48, 0, 21, 16));
        labelIN2->setAlignment(Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignHCenter);
        playback1_2IN1 = new QPushButton(widget);
        playback1_2IN1->setObjectName("playback1_2IN1");
        playback1_2IN1->setGeometry(QRect(0, 125, 30, 16));
        sizePolicy2.setHeightForWidth(playback1_2IN1->sizePolicy().hasHeightForWidth());
        playback1_2IN1->setSizePolicy(sizePolicy2);
        playback1_2IN1->setMinimumSize(QSize(10, 10));
        playback1_2IN1->setMaximumSize(QSize(16777215, 16777215));
        playback1_2IN1->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback1_2IN1->setCheckable(true);
        labelpb78 = new QLabel(widget);
        labelpb78->setObjectName("labelpb78");
        labelpb78->setGeometry(QRect(96, 215, 74, 16));
        labelpb78->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelpb910 = new QLabel(widget);
        labelpb910->setObjectName("labelpb910");
        labelpb910->setGeometry(QRect(100, 240, 74, 16));
        labelpb910->setAlignment(Qt::AlignmentFlag::AlignCenter);
        playback9_10IN2 = new QPushButton(widget);
        playback9_10IN2->setObjectName("playback9_10IN2");
        playback9_10IN2->setGeometry(QRect(52, 240, 30, 16));
        sizePolicy2.setHeightForWidth(playback9_10IN2->sizePolicy().hasHeightForWidth());
        playback9_10IN2->setSizePolicy(sizePolicy2);
        playback9_10IN2->setMinimumSize(QSize(10, 10));
        playback9_10IN2->setMaximumSize(QSize(16777215, 16777215));
        playback9_10IN2->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback9_10IN2->setCheckable(true);
        playback9_10IN1 = new QPushButton(widget);
        playback9_10IN1->setObjectName("playback9_10IN1");
        playback9_10IN1->setGeometry(QRect(10, 240, 30, 16));
        sizePolicy2.setHeightForWidth(playback9_10IN1->sizePolicy().hasHeightForWidth());
        playback9_10IN1->setSizePolicy(sizePolicy2);
        playback9_10IN1->setMinimumSize(QSize(10, 10));
        playback9_10IN1->setMaximumSize(QSize(16777215, 16777215));
        playback9_10IN1->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"	image: url(:/Icon/radioUncheck.svg);\n"
"}\n"
"QPushButton:checked {\n"
"	image: url(:/Icon/radioCheck.svg);\n"
"}"));
        playback9_10IN1->setCheckable(true);

        horizontalLayout_2->addWidget(widget_4);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_6);

        horizontalLayout_2->setStretch(0, 28);
        horizontalLayout_2->setStretch(1, 236);
        horizontalLayout_2->setStretch(2, 28);
        horizontalLayout_2->setStretch(3, 394);
        horizontalLayout_2->setStretch(4, 28);
        horizontalLayout_2->setStretch(5, 236);
        horizontalLayout_2->setStretch(6, 28);

        verticalLayout->addLayout(horizontalLayout_2);

        verticalSpacer_2 = new QSpacerItem(20, 65, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_2);

        verticalLayout->setStretch(0, 18);
        verticalLayout->setStretch(1, 200);
        verticalLayout->setStretch(2, 18);

        retranslateUi(M62_PrivateWidget1);

        QMetaObject::connectSlotsByName(M62_PrivateWidget1);
    } // setupUi

    void retranslateUi(QWidget *M62_PrivateWidget1)
    {
        M62_PrivateWidget1->setWindowTitle(QCoreApplication::translate("M62_PrivateWidget1", "Form", nullptr));
        DialThresholdT->setText(QCoreApplication::translate("M62_PrivateWidget1", "Threshold", nullptr));
        DialThresholdL->setText(QCoreApplication::translate("M62_PrivateWidget1", "0dB", nullptr));
        DialThresholdR->setText(QCoreApplication::translate("M62_PrivateWidget1", "-\342\210\236", nullptr));
        DialAttackT->setText(QCoreApplication::translate("M62_PrivateWidget1", "Attack", nullptr));
        DialAttackL->setText(QCoreApplication::translate("M62_PrivateWidget1", "Fast", nullptr));
        DialAttackR->setText(QCoreApplication::translate("M62_PrivateWidget1", "Slow", nullptr));
        DialReductionT->setText(QCoreApplication::translate("M62_PrivateWidget1", "Reduction", nullptr));
        DialReductionL->setText(QCoreApplication::translate("M62_PrivateWidget1", "0dB", nullptr));
        DialReductionR->setText(QCoreApplication::translate("M62_PrivateWidget1", "-\342\210\236", nullptr));
        DialReleaseT->setText(QCoreApplication::translate("M62_PrivateWidget1", "Release", nullptr));
        DialReleaseL->setText(QCoreApplication::translate("M62_PrivateWidget1", "Fast", nullptr));
        DialReleaseR->setText(QCoreApplication::translate("M62_PrivateWidget1", "Slow", nullptr));
        buttonOFF->setText(QCoreApplication::translate("M62_PrivateWidget1", "OFF", nullptr));
        labelTitle_2->setText(QCoreApplication::translate("M62_PrivateWidget1", "Ducking Map", nullptr));
        IN1BT->setText(QString());
        labelAUX->setText(QCoreApplication::translate("M62_PrivateWidget1", "AUX", nullptr));
        IN2AUX->setText(QString());
        labelpb56->setText(QCoreApplication::translate("M62_PrivateWidget1", "Playback 5/6", nullptr));
        labelBT->setText(QCoreApplication::translate("M62_PrivateWidget1", "BT", nullptr));
        playback7_8IN2->setText(QString());
        playback3_4IN1->setText(QString());
        IN1AUX->setText(QString());
        IN1OTG->setText(QString());
        labelpb34->setText(QCoreApplication::translate("M62_PrivateWidget1", "Playback 3/4", nullptr));
        playback1_2IN2->setText(QString());
        IN2BT->setText(QString());
        playback5_6IN2->setText(QString());
        labelOTG->setText(QCoreApplication::translate("M62_PrivateWidget1", "OTG IN", nullptr));
        playback5_6IN1->setText(QString());
        playback3_4IN2->setText(QString());
        labelIN1->setText(QCoreApplication::translate("M62_PrivateWidget1", "IN1", nullptr));
        labelpb12->setText(QCoreApplication::translate("M62_PrivateWidget1", "Playback 1/2", nullptr));
        playback7_8IN1->setText(QString());
        IN2OTG->setText(QString());
        labelIN2->setText(QCoreApplication::translate("M62_PrivateWidget1", "IN2", nullptr));
        playback1_2IN1->setText(QString());
        labelpb78->setText(QCoreApplication::translate("M62_PrivateWidget1", "Playback 7/8", nullptr));
        labelpb910->setText(QCoreApplication::translate("M62_PrivateWidget1", "Playback 9/10", nullptr));
        playback9_10IN2->setText(QString());
        playback9_10IN1->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class M62_PrivateWidget1: public Ui_M62_PrivateWidget1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_M62_PRIVATEWIDGET1_H

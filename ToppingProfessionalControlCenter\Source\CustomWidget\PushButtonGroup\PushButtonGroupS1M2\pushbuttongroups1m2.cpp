#include "globalfont.h"
#include "pushbuttongroups1m2.h"
#include "ui_pushbuttongroups1m2.h"


PushButtonGroupS1M2::PushButtonGroupS1M2(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PushButtonGroupS1M2)
{
    ui->setupUi(this);
    ui->PushButtonSOLO->setCheckable(true);
    ui->PushButtonMUTE->setCheckable(true);
    ui->PushButtonSOLO->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonMUTE->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    mStyle[0] = "QFrame { image: url(:/Image/PushButtonGroup/PBG4_0.png); }";
    mStyle[1] = "QFrame { image: url(:/Image/PushButtonGroup/PBG4_2.png); }";
    mStyle[2] = "QFrame { image: url(:/Image/PushButtonGroup/PBG4_3.png); }";
    mStyle[3] = "QFrame { image: url(:/Image/PushButtonGroup/PBG4_1.png); }";
    setState("SOLO", "0", false);
    setState("MUTE", "0", false);
    setLanguage("English");
}
PushButtonGroupS1M2::~PushButtonGroupS1M2()
{
    delete ui;
}


// override
void PushButtonGroupS1M2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->PushButtonMUTE->height()) - 1);
    ui->PushButtonSOLO->setFont(mFont);
    ui->PushButtonMUTE->setFont(mFont);
}


// slot
void PushButtonGroupS1M2::on_PushButtonSOLO_clicked(bool checked)
{
    setState("SOLO", QString::number(checked));
}
void PushButtonGroupS1M2::on_PushButtonMUTE_clicked(bool checked)
{
    setState("MUTE", QString::number(checked));
}


// setter & getter
PushButtonGroupS1M2& PushButtonGroupS1M2::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonGroupS1M2& PushButtonGroupS1M2::setLanguage(QString language)
{
    if(language == "English")
    {
        // ui->PushButtonSOLO->setText("SOLO");
        // ui->PushButtonMUTE->setText("MUTE");
    }
    else if(language == "Chinese")
    {
        // ui->PushButtonSOLO->setText("SOLO");
        // ui->PushButtonMUTE->setText("MUTE");
    }
    return *this;
}
PushButtonGroupS1M2& PushButtonGroupS1M2::setState(QString button, QString state, bool needEmit)
{
    unsigned int bitMask=0;
    QPushButton* currentButton=nullptr;
    if(button == "SOLO")
    {
        bitMask = 0x0001;
        currentButton = ui->PushButtonSOLO;
    }
    else if(button == "MUTE")
    {
        bitMask = 0x0002;
        currentButton = ui->PushButtonMUTE;
    }
    if(currentButton != nullptr)
    {
        mBitmap &= ~bitMask;
        if(state.toInt()) mBitmap |= bitMask;
        ui->frame->setStyleSheet(mStyle.value(mBitmap));
        currentButton->setChecked(state.toInt());
        currentButton->setStyleSheet(state.toInt() ? "QPushButton { color: rgb(222, 222, 222); }" : "QPushButton { color: rgb(161, 161, 161); }");
        if(needEmit) emit stateChanged(button, state);
    }
    return *this;
}
QString PushButtonGroupS1M2::getState(QString button)
{
    QPushButton* currentButton=nullptr;
    if(button == "SOLO")
    {
        currentButton = ui->PushButtonSOLO;
    }
    else if(button == "MUTE")
    {
        currentButton = ui->PushButtonMUTE;
    }
    if(currentButton != nullptr)
    {
        return QString::number(currentButton->isChecked());
    }
    return QString::number(false);
}


#include "loopbackbase.h"


// setter & getter
<PERSON>backBase& LoopbackBase::setChannelName(QString name)
{
    mChannelName = name;
    return *this;
}
LoopbackBase& LoopbackBase::setWidgetEnable(bool state)
{
    mEnable = state;
    return *this;
}
LoopbackBase& LoopbackBase::setWidgetEnableWithUpdate(bool state)
{
    mEnable = state;
    if(mEmitAction)
    {
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(mEnable));
    }
    updateAttribute();
    return *this;
}
LoopbackBase& LoopbackBase::setWidgetMovable(bool state)
{
    mMovable = state;
    return *this;
}
LoopbackBase& LoopbackBase::setWidgetReady(bool state)
{
    mReady = state;
    return *this;
}
LoopbackBase& LoopbackBase::setWidgetEmitAction(bool state)
{
    mEmitAction = state;
    return *this;
}


/************************************************************************
 *
 *  Module:       WnStringUtils_impl.h
 *
 *  Description:  String Utility Functions for internal use
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    Udo <PERSON>hardt,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnStringUtils_impl_h__
#define __WnStringUtils_impl_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

// The below utility functions are used by the library internally only.
// See WnStringUtils.h and WnStringUtils.cpp for more info.

//
// sprintf style functions that are used by the trace module.
// To avoid endless recursion, these functions don't call trace functions.
// See WnStringUtils.cpp for implementation.
//

WNERR
WnStringPrintf_impl(
    char* buffer,
    unsigned int maxChars,
    const char* format,
    ...
    );

WNERR
WnStringPrintf_impl(
    wchar_t* buffer,
    unsigned int maxChars,
    const wchar_t* format,
    ...
    );

WNERR
WnStringVPrintf_impl(
    char* buffer,
    unsigned int maxChars,
    const char* format,
    va_list argList
    );

WNERR
WnStringVPrintf_impl(
    wchar_t* buffer,
    unsigned int maxChars,
    const wchar_t* format,
    va_list argList
    );

#ifdef LIBWN_NAMESPACE
}
#endif

#endif //__WnStringUtils_impl_h__

/********************************* EOF *********************************/

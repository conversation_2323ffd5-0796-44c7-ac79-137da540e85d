#ifndef MESSAGEBOXWIDGET1_H
#define MESSAGEBOXWIDGET1_H


#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class MessageBoxWidget1;
}


class MessageBoxWidget1 : public QWidget
{
    Q_OBJECT
public:
    explicit MessageBoxWidget1(QWidget* parent=nullptr);
    ~MessageBoxWidget1();
    MessageBoxWidget1& setFont(QFont font);
    MessageBoxWidget1& setLanguage(QString language);
    MessageBoxWidget1& showItemText(QString text);
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    Ui::MessageBoxWidget1* ui;
    QFont mFont;
private slots:
    void on_PushButton1_clicked();
    void on_PushButton2_clicked();
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // MESSAGEBOXWIDGET1_H


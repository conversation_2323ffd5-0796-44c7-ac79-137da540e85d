#ifndef VOLUMEMETERS1M3_H
#define VOLUMEMETERS1M3_H


#include <QFont>
#include <QRect>
#include <QTimer>
#include <QWidget>
#include <QPainter>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QResizeEvent>


class VolumeMeterS1M3 : public QWidget
{
    Q_OBJECT
public:
    explicit VolumeMeterS1M3(QWidget* parent=nullptr);
    ~VolumeMeterS1M3();
    void setFont(QFont font);
    void setColorBG(QColor color);
    void setValueLeft(int value, int gain);
    void setValueRight(int value, int gain);
    void setMeterLeftClear();
    void setMeterLeftSlip();
    void setMeterRightClear();
    void setMeterRightSlip();
    void setWidthRatio(int space1, int space2, int meterOrigin, int meterGained, int scale);
    void setHeightRatio(int clip, int space1, int volume, int space2);
    void setScaleLineHidden(bool hidden=true);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
    void mouseDoubleClickEvent(QMouseEvent* e) override;
private:
    struct RectMeter
    {
        int volumeValue=-90;
        bool clipStatus=false;
        QRect clip;
        QRect volume;
        QTimer timerClip;
    };
    QTimer mTimerMeterLeft;
    QTimer mTimerMeterRight;
    RectMeter mRectMeterLeftOrigin;
    RectMeter mRectMeterLeftGained;
    RectMeter mRectMeterRightOrigin;
    RectMeter mRectMeterRightGained;
    QRect mRectScale;
    QFont mFont;
    QColor mColorBG=QColor(22, 22, 22);
    int mSpace1=7;
    int mSpace2=8;
    int mMeterOrigin=12;
    int mMeterGained=8;
    int mScale=30;
    int mHClip=3;
    int mHSpace1=2;
    int mHVolume=93;
    int mHSpace2=2;
    bool mScaleLineHidden=false;
    void drawBG(QPainter* painter);
    void drawElement(QPainter* painter);
private slots:
    void in_timerClipLeftOrigin_timeout();
    void in_timerClipLeftGained_timeout();
    void in_timerClipRightOrigin_timeout();
    void in_timerClipRightGained_timeout();
    void in_mTimerMeterLeft_timeout();
    void in_mTimerMeterRight_timeout();
};


#endif // VOLUMEMETERS1M3_H


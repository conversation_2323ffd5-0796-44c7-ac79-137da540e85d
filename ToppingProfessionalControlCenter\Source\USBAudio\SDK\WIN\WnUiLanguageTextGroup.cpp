/************************************************************************
 *  Group of UI language texts.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

 // Module is empty if .h file was not included (category turned off).
#ifdef __WnUiLanguageTextGroup_h__

 // optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

WnUiLanguageTextGroup::WnUiLanguageTextGroup()
{
}

WnUiLanguageTextGroup::WnUiLanguageTextGroup(const wchar_t* idStr, bool isOptional /*= false*/)
    : mIdentifierStr(idStr), mIsOptional(isOptional)
{
}


WnUiLanguageTextGroup::~WnUiLanguageTextGroup()
{
}


WnUiLanguageTextGroup::WnUiLanguageTextGroup( const WnUiLanguageTextGroup& src)
{
    *this = src;
}


WnUiLanguageTextGroup& WnUiLanguageTextGroup::operator = (const WnUiLanguageTextGroup& src)
{
    // Self assignment? 
    if (&src == this) return *this;

    mIdentifierStr = src.mIdentifierStr;
    mIsOptional = src.mIsOptional;
    mUiLanguageTexts = src.mUiLanguageTexts;   

    return *this;
}


void WnUiLanguageTextGroup::Clear()
{
    mIdentifierStr = nullptr;
    mIsOptional = false;
    for (auto uiText : mUiLanguageTexts) {
        uiText->Clear();
    }
    mUiLanguageTexts.clear();    
}


UiLanguageTextHandle WnUiLanguageTextGroup::FindUILanguageText(const wchar_t* idStr) const
{
    for (auto uiText : mUiLanguageTexts) {
        if (TbStringEqual(uiText->GetIdentifierStr(), idStr)) {
            return uiText;
        }
    }
    return nullptr;
}


#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguageText_h__

/*************************** EOF **************************************/

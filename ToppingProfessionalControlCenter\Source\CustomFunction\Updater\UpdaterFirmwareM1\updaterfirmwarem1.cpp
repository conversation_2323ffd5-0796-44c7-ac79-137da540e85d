#include <QDir>
#include <QProcess>
#include <QFileInfo>

#include "usbaudioapi.h"
#include "updaterfirmwarem1.h"


UpdaterBase* UpdaterFirmwareM1::instance()
{
    static UpdaterFirmwareM1 instance;
    return &instance;
}

UpdaterFirmwareM1::UpdaterFirmwareM1(QObject* parent)
    : UpdaterBase(parent)
{

}

UpdaterFirmwareM1::~UpdaterFirmwareM1()
{

}

void UpdaterFirmwareM1::doUpdate(const QString& file)
{
    QMetaObject::invokeMethod(this, [this, file](){
        bool isSuccess=false;
        unsigned int preFinished=0;
        if(QFileInfo::exists(file))
        {
            int exitCode=0;
            QString firmware="";
            if(file.endsWith(".zip"))
            {
                QFileInfo fileInfo(file);
                firmware = fileInfo.absolutePath() + QDir::separator() + fileInfo.completeBaseName() + ".bin";
#ifdef Q_OS_WIN
                QString command=QString("powershell -Command \"Expand-Archive -Path \\\"%1\\\" -DestinationPath \\\"%2\\\" -Force\"")
                                        .arg(file)
                                        .arg(fileInfo.absolutePath());
                exitCode = QProcess::execute(command);
#elif defined(Q_OS_MAC)
                exitCode = QProcess::execute("unzip", QStringList() << "-o" << file << "-d" << fileInfo.absolutePath());
#endif
            }
            else if(file.endsWith(".bin"))
            {
                firmware = file;
            }
            else
            {
                exitCode = -1;
            }
            if(exitCode == 0 && QFileInfo::exists(firmware))
            {
                if(USBAHandle.setDFUStart(firmware))
                {
                    auto getSize = [](qint64 bytes, bool perSecond=true) {
                        QString unit;
                        double value = bytes;
                        if (value >= 1024 * 1024) {
                            unit = "MB";
                            value /= 1024 * 1024;
                        } else if (value >= 1024) {
                            unit = "KB";
                            value /= 1024;
                        } else {
                            unit = "B";
                        }
                        return QString("%1 %2%3").arg(QString::number(value, 'f', 1)).arg(unit).arg(perSecond ? "/s" : "");
                    };
                    emit sigProgress("UpdateState", "Start");
                    emit sigProgress("UpdateFileSize", getSize(USBAHandle.getDFUBytesTotal(), false));
                    mTimer.start();
                    while(1)
                    {
                        unsigned int finished=USBAHandle.getDFUPercentageComplete();
                        if(preFinished != finished)
                        {
                            emit sigProgress("UpdateProgress", QString::number(finished));
                            emit sigProgress("UpdateCompleted", getSize(USBAHandle.getDFUBytesCurrent(), false));
                        }
                        preFinished = finished;
                        if(finished == 0)
                        {
                            USBAHandle.setDFUTerminate();
                            break;
                        }
                        else if(finished == 100)
                        {
                            USBAHandle.setDFUTerminate();
                            isSuccess = true;
                            break;
                        }
                        qint64 elapsedMs=speedTimer.elapsed();
                        if(elapsedMs >= 1000)
                        {
                            unsigned int finished=USBAHandle.getDFUBytesCurrent();
                            qint64 speed=(finished - mFinished) * 1000 / elapsedMs;
                            mFinished = finished;
                            speedTimer.restart();
                            emit sigProgress("UpdateSpeed", getSize(speed));
                        }
                        QThread::msleep(10);
                    }
                }
            }
            mFinished = 0;
        }
        emit sigProgress("UpdateState", isSuccess ? "Success" : "Failed");
    });
}

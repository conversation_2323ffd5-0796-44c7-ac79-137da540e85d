#include <QDebug>

#include "chart.h"


Chart::Chart(QWidget *parent) :
    QChartView(parent)
{
    setRenderHint(QPainter::Antialiasing);
    mChart.setPlotAreaBackgroundVisible(true);
    mChart.addAxis(&mAxis[axisXB], Qt::AlignBottom);
    mChart.addAxis(&mAxis[axisXT], Qt::AlignTop);
    mChart.addAxis(&mAxis[axisYL], Qt::AlignLeft);
    mChart.addAxis(&mAxis[axisYR], Qt::AlignRight);
    mChart.legend()->setVisible(false);
    setChart(&mChart);
    mRectFrame.setRect(mChart.plotArea());
    mRectFrame.setVisible(false);
    mChart.scene()->addItem(&mRectFrame);
    mHandleIsMoving = false;
    mHandleMovingStep = 1;
    mMovingHandle = "";
    setChartDefault();
}
Chart::~Chart()
{
    for(auto key : mHandle.keys())
    {
        delete mHandle.value(key);
    }
    for(auto key : mScrollLineSeries.keys())
    {
        delete mScrollLineSeries.value(key);
    }
    for(auto key : mScrollSplineSeries.keys())
    {
        delete mScrollSplineSeries.value(key);
    }
    for(auto key : mScrollAreaSeries.keys())
    {
        delete mScrollAreaSeries.value(key)->series.upperSeries();
        delete mScrollAreaSeries.value(key)->series.lowerSeries();
        delete mScrollAreaSeries.value(key);
    }
    mChart.removeAllSeries();
}


// override
void Chart::mousePressEvent(QMouseEvent *event)
{
    for(auto key : mHandle.keys())
    {
        if(mHandle.value(key)->point.isVisible())
        {
            if(QLineF(event->pos(), mChart.mapToPosition(mHandle.value(key)->point.at(0))).length() < mHandle.value(key)->point.markerSize())
            {
                mHandleIsMoving = true;
                mMovingHandle = key;
                break;
            }
        }
    }
    QChartView::mousePressEvent(event);
}
void Chart::mouseMoveEvent(QMouseEvent *event)
{
    if(mHandleIsMoving && !mMovingHandle.isEmpty())
    {
        QPointF point = mHandle.value(mMovingHandle)->point.at(0);
        QPointF pointNew = mChart.mapToValue(event->pos());
        if(!mHandle.value(mMovingHandle)->point.property("LockX").toBool())
        {
            pointNew.setY(qRound(pointNew.y() / mHandleMovingStep) * mHandleMovingStep);
            if(pointNew.y() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min())
            {
                pointNew.setY(qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
            }
            if(pointNew.y() > qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max())
            {
                pointNew.setY(qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max());
            }
            point.setY(pointNew.y());
            mHandle.value(mMovingHandle)->axisX.replace(0, qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min(), point.y());
            mHandle.value(mMovingHandle)->axisX.replace(1, qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max(), point.y());
        }
        if(!mHandle.value(mMovingHandle)->point.property("LockY").toBool())
        {
            pointNew.setX(qRound(pointNew.x() / mHandleMovingStep) * mHandleMovingStep);
            if(pointNew.x() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min())
            {
                pointNew.setX(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min());
            }
            if(pointNew.x() > qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max())
            {
                pointNew.setX(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max());
            }
            point.setX(pointNew.x());
            mHandle.value(mMovingHandle)->axisY.replace(0, point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
            mHandle.value(mMovingHandle)->axisY.replace(1, point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max());
        }
        if(point != mHandle.value(mMovingHandle)->point.at(0))
        {
            mHandle.value(mMovingHandle)->point.replace(0, point);
            emit handleMovement(mMovingHandle, point);
        }
    }
    QChartView::mouseMoveEvent(event);
}
void Chart::mouseReleaseEvent(QMouseEvent *event)
{
    if(mHandleIsMoving && !mMovingHandle.isEmpty())
    {
        mHandleIsMoving = false;
        mMovingHandle = "";
    }
    QChartView::mouseReleaseEvent(event);
}
void Chart::paintEvent(QPaintEvent *event)
{
    if(mRectFrame.rect() != mChart.plotArea())
    {
        mRectFrame.setRect(mChart.plotArea());
    }
    QChartView::paintEvent(event);
}


// slot
void Chart::on_ScrollLineSeriesTimers_timeout()
{
    QString name=qobject_cast<QTimer*>(QObject::sender())->property("Binding").toString();
    qreal defaultData=mScrollLineSeries.value(name)->series.property("DefaultData").toReal();
    qreal newData=mScrollLineSeries.value(name)->series.property("NewData").toReal();
    qreal stepValue=mScrollLineSeries.value(name)->series.property("StepValue").toReal();
    int stepCount=mScrollLineSeries.value(name)->series.property("StepCount").toInt();
    int stepCountMax=mScrollLineSeries.value(name)->series.property("StepCountMax").toInt();
    QVector<QPointF> vecPoint;
    for(int i=0;i<mScrollLineSeries.value(name)->series.count();i++)
    {
        vecPoint.append(QPointF(mScrollLineSeries.value(name)->series.at(i).x() - stepValue, mScrollLineSeries.value(name)->series.at(i).y()));
    }
    mScrollLineSeries.value(name)->series.replace(vecPoint);
    stepCount++;
    if(stepCount == stepCountMax)
    {
        stepCount = 0;
        if(mScrollLineSeries.value(name)->series.at(0).x() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min() - 1)
        {
            mScrollLineSeries.value(name)->series.remove(0);
        }
        mScrollLineSeries.value(name)->series.append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 1, newData);
        mScrollLineSeries.value(name)->series.setProperty("NewData", defaultData);
        bool paramChange=mScrollLineSeries.value(name)->series.property("ParamChange").toBool();
        if(paramChange)
        {
            qreal stepValueNew=mScrollLineSeries.value(name)->series.property("StepValueNew").toReal();
            int refreshFreNew=mScrollLineSeries.value(name)->series.property("RefreshFreNew").toInt();
            mScrollLineSeries.value(name)->series.setProperty("ParamChange", false);
            mScrollLineSeries.value(name)->series.setProperty("StepValue", stepValueNew);
            mScrollLineSeries.value(name)->series.setProperty("StepCountMax", (int) (1.0 / stepValueNew));
            mScrollLineSeries.value(name)->series.setProperty("RefreshFre", refreshFreNew);
            mScrollLineSeries.value(name)->timer.setInterval((int) ((1000.0 * stepValueNew) / refreshFreNew));
        }
    }
    mScrollLineSeries.value(name)->series.setProperty("StepCount", stepCount);
}
void Chart::on_ScrollSplineSeriesTimers_timeout()
{
    QString name=qobject_cast<QTimer*>(QObject::sender())->property("Binding").toString();
    qreal defaultData=mScrollSplineSeries.value(name)->series.property("DefaultData").toReal();
    qreal newData=mScrollSplineSeries.value(name)->series.property("NewData").toReal();
    qreal stepValue=mScrollSplineSeries.value(name)->series.property("StepValue").toReal();
    int stepCount=mScrollSplineSeries.value(name)->series.property("StepCount").toInt();
    int stepCountMax=mScrollSplineSeries.value(name)->series.property("StepCountMax").toInt();
    QVector<QPointF> vecPoint;
    for(int i=0;i<mScrollSplineSeries.value(name)->series.count();i++)
    {
        vecPoint.append(QPointF(mScrollSplineSeries.value(name)->series.at(i).x() - stepValue, mScrollSplineSeries.value(name)->series.at(i).y()));
    }
    mScrollSplineSeries.value(name)->series.replace(vecPoint);
    stepCount++;
    if(stepCount == stepCountMax)
    {
        stepCount = 0;
        if(mScrollSplineSeries.value(name)->series.at(0).x() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min() - 1)
        {
            mScrollSplineSeries.value(name)->series.remove(0);
        }
        mScrollSplineSeries.value(name)->series.append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 1, newData);
        mScrollSplineSeries.value(name)->series.setProperty("NewData", defaultData);
        bool paramChange=mScrollSplineSeries.value(name)->series.property("ParamChange").toBool();
        if(paramChange)
        {
            qreal stepValueNew=mScrollSplineSeries.value(name)->series.property("StepValueNew").toReal();
            int refreshFreNew=mScrollSplineSeries.value(name)->series.property("RefreshFreNew").toInt();
            mScrollSplineSeries.value(name)->series.setProperty("ParamChange", false);
            mScrollSplineSeries.value(name)->series.setProperty("StepValue", stepValueNew);
            mScrollSplineSeries.value(name)->series.setProperty("StepCountMax", (int) (1.0 / stepValueNew));
            mScrollSplineSeries.value(name)->series.setProperty("RefreshFre", refreshFreNew);
            mScrollSplineSeries.value(name)->timer.setInterval((int) ((1000.0 * stepValueNew) / refreshFreNew));
        }
    }
    mScrollSplineSeries.value(name)->series.setProperty("StepCount", stepCount);
}
void Chart::on_ScrollAreaSeriesTimers_timeout()
{
    QString name=qobject_cast<QTimer*>(QObject::sender())->property("Binding").toString();
    qreal defaultData=mScrollAreaSeries.value(name)->series.property("DefaultData").toReal();
    qreal newData=mScrollAreaSeries.value(name)->series.property("NewData").toReal();
    qreal stepValue=mScrollAreaSeries.value(name)->series.property("StepValue").toReal();
    int stepCount=mScrollAreaSeries.value(name)->series.property("StepCount").toInt();
    int stepCountMax=mScrollAreaSeries.value(name)->series.property("StepCountMax").toInt();
    QVector<QPointF> vecPoint;
    for(int i=0;i<mScrollAreaSeries.value(name)->series.upperSeries()->count();i++)
    {
        vecPoint.append(QPointF(mScrollAreaSeries.value(name)->series.upperSeries()->at(i).x() - stepValue, mScrollAreaSeries.value(name)->series.upperSeries()->at(i).y()));
    }
    if(mScrollAreaSeries.value(name)->series.lowerSeries()->at(0).x() > qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min())
    {
        mScrollAreaSeries.value(name)->series.lowerSeries()->replace(0, mScrollAreaSeries.value(name)->series.lowerSeries()->at(0).x() - stepValue, qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
    }
    mScrollAreaSeries.value(name)->series.upperSeries()->replace(vecPoint);
    stepCount++;
    if(stepCount == stepCountMax)
    {
        stepCount = 0;
        if(mScrollAreaSeries.value(name)->series.upperSeries()->at(0).x() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min() - 1)
        {
            mScrollAreaSeries.value(name)->series.upperSeries()->remove(0);
        }
        mScrollAreaSeries.value(name)->series.upperSeries()->append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 1, newData);
        mScrollAreaSeries.value(name)->series.setProperty("NewData", defaultData);
        bool paramChange=mScrollAreaSeries.value(name)->series.property("ParamChange").toBool();
        if(paramChange)
        {
            qreal stepValueNew=mScrollAreaSeries.value(name)->series.property("StepValueNew").toReal();
            int refreshFreNew=mScrollAreaSeries.value(name)->series.property("RefreshFreNew").toInt();
            mScrollAreaSeries.value(name)->series.setProperty("ParamChange", false);
            mScrollAreaSeries.value(name)->series.setProperty("StepValue", stepValueNew);
            mScrollAreaSeries.value(name)->series.setProperty("StepCountMax", (int) (1.0 / stepValueNew));
            mScrollAreaSeries.value(name)->series.setProperty("RefreshFre", refreshFreNew);
            mScrollAreaSeries.value(name)->timer.setInterval((int) ((1000.0 * stepValueNew) / refreshFreNew));
        }
    }
    mScrollAreaSeries.value(name)->series.setProperty("StepCount", stepCount);
}


// setter & getter Chart
Chart& Chart::setChartDefault()
{
    setChartColor(QColor(22, 22, 22)).setChartPlotAreaColor(QColor(31, 31, 31));
    setChartFont(QFont("Source Han Serif SC", 12));
    for(int i=axisXT;i<axisEnd;i++)
    {
        setAxis((CustomAxis) i, -50, 50, 10, "%ddB");
        setAxisLabelColor((CustomAxis) i, Qt::white);
        setAxisLineColor((CustomAxis) i, Qt::lightGray);
        setAxisLineWidth((CustomAxis) i, 2);
    }
    setChartAxisVisible(axisXT, false);
    //    setChartAxisVisible(axisXB, false);
    setChartAxisVisible(axisYR, false);
    setChartGridVisible(false);
    setChartRectFrameColor(Qt::lightGray).setChartRectFrameWidth(2);
    setChartRectFrameVisible();
    for(int i=0;i<10;i++)
    {
        addHandle(QString("Arrow").append(QString::number(i)), QPointF((i * 5) - 5, 0), "C:/Users/<USER>/Desktop/Chart/Chart/Chart/Handle.png");
        setHandlePosition(QString("Arrow").append(QString::number(i)), QPointF((i * 5) - 25, 0));
    }
    removeHandle("Arrow2");
    removeHandle("Arrow3");
    removeHandle("Arrow4");
    removeHandle("Arrow5");
    //    setHandleVisible("Arrow6", false);
    //    setHandleAxisVisible("Arrow7", false);
    setHandleAxisVisibleX("Arrow7", true);
    setHandleAxisStyle("Arrow7", Qt::SolidLine);
    setHandleAxisWidth("Arrow7", 3);
    setHandleAxisColor("Arrow7", Qt::blue);
    //    setHandleAxisLockX("Arrow7", true);
    setHandleAxisLockY("Arrow7", true);
    //    setHandlePosition("Arrow7", QPointF(21, 0));
    setAxis((CustomAxis) axisXB, -50, 50, 10, "%ddB");
    return *this;
}
Chart& Chart::setChartFont(QFont font)
{
    for(int i=axisXT;i<axisEnd;i++)
    {
        mAxis[i].setLabelsFont(font);
    }
    return *this;
}
Chart& Chart::setChartColor(QColor color)
{
    mChart.setBackgroundBrush(QBrush(color));
    return *this;
}
Chart& Chart::setChartPlotAreaColor(QColor color)
{
    mChart.setPlotAreaBackgroundBrush(QBrush(color));
    return *this;
}
Chart& Chart::setChartGridVisible(bool visible)
{
    for(int i=axisXT;i<axisEnd;i++)
    {
        mAxis[i].setGridLineVisible(visible);
    }
    return *this;
}
Chart& Chart::setChartGridVisible(CustomAxis axis, bool visible)
{
    mAxis[axis].setGridLineVisible(visible);
    return *this;
}
Chart& Chart::setChartAxisVisible(bool visible)
{
    for(int i=axisXT;i<axisEnd;i++)
    {
        mAxis[i].setVisible(visible);
    }
    return *this;
}
Chart& Chart::setChartAxisVisible(CustomAxis axis, bool visible)
{
    mAxis[axis].setVisible(visible);
    return *this;
}
Chart& Chart::setChartRectFrameVisible(bool visible)
{
    mRectFrame.setVisible(visible);
    return *this;
}
Chart& Chart::setChartRectFrameColor(QColor color)
{
    QPen pen=mRectFrame.pen();
    pen.setColor(color);
    mRectFrame.setPen(pen);
    return *this;
}
Chart& Chart::setChartRectFrameWidth(int width)
{
    QPen pen=mRectFrame.pen();
    pen.setWidth(width);
    mRectFrame.setPen(pen);
    return *this;
}


// setter & getter Axis
Chart& Chart::setAxis(CustomAxis axis, qreal min, qreal max, int count, const QString &format)
{
    mAxis[axis].setRange(min, max);
    mAxis[axis].setTickCount(count + 1);
    mAxis[axis].setLabelFormat(format);
    for(auto key : mHandle.keys())
    {
        setHandlePosition(key, mHandle.value(key)->point.at(0));
    }
    return *this;
}
Chart& Chart::setAxisRange(CustomAxis axis, qreal min, qreal max)
{
    mAxis[axis].setRange(min, max);
    for(auto key : mHandle.keys())
    {
        setHandlePosition(key, mHandle.value(key)->point.at(0));
    }
    return *this;
}
Chart& Chart::setAxisTickCount(CustomAxis axis, int count)
{
    mAxis[axis].setTickCount(count + 1);
    return *this;
}
Chart& Chart::setAxisLabelFormat(CustomAxis axis, const QString &format)
{
    mAxis[axis].setLabelFormat(format);
    return *this;
}
Chart& Chart::setAxisLabelFont(CustomAxis axis, QFont font)
{
    mAxis[axis].setLabelsFont(font);
    return *this;
}
Chart& Chart::setAxisLabelColor(CustomAxis axis, QColor color)
{
    mAxis[axis].setLabelsColor(color);
    return *this;
}
Chart& Chart::setAxisLineColor(CustomAxis axis, QColor color)
{
    QPen pen=mAxis[axis].linePen();
    pen.setColor(color);
    mAxis[axis].setLinePen(pen);
    return *this;
}
Chart& Chart::setAxisLineWidth(CustomAxis axis, int width)
{
    QPen pen=mAxis[axis].linePen();
    pen.setWidth(width);
    mAxis[axis].setLinePen(pen);
    return *this;
}
Chart& Chart::setAxisGridColor(CustomAxis axis, QColor color)
{
    mAxis[axis].setGridLineColor(color);
    return *this;
}
Chart& Chart::setAxisGridWidth(CustomAxis axis, int width)
{
    QPen pen=mAxis[axis].gridLinePen();
    pen.setWidth(width);
    mAxis[axis].setGridLinePen(pen);
    return *this;
}


// setter & getter Handle
Chart& Chart::addHandle(QString name, QPointF point, const QString &imageFile)
{
    if(name.isEmpty() || mHandle.contains(name) || QImage(imageFile).isNull())
    {
        return *this;
    }
    CustomHandle* handle = new CustomHandle();
    mChart.addSeries(&handle->axisX);
    mChart.addSeries(&handle->axisY);
    mChart.addSeries(&handle->point);
    int w, h;
    w = QImage(imageFile).width();
    h = QImage(imageFile).height();
    QImage image(w, h, QImage::Format_ARGB32);
    image.fill(Qt::transparent);
    QPainter painter(&image);
    QPixmap pixmap(imageFile);
    painter.drawPixmap(QPointF(0, 0), pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    handle->point.setMarkerShape(QScatterSeries::MarkerShapeRectangle);
    handle->point.setMarkerSize(qMax(w, h));
    handle->point.setBrush(image);
    handle->point.setPen(QColor(Qt::transparent));
    handle->point.attachAxis(mChart.axes(Qt::Horizontal).first());
    handle->point.attachAxis(mChart.axes(Qt::Vertical).first());
    handle->point.setProperty("LockX", false);
    handle->point.setProperty("LockY", false);
    handle->point.append(point);
    QPen pen(Qt::DashLine);
    pen.setColor(Qt::gray);
    handle->axisX.setPen(pen);
    handle->axisX.attachAxis(mChart.axes(Qt::Horizontal).first());
    handle->axisX.attachAxis(mChart.axes(Qt::Vertical).first());
    handle->axisX.append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min(), point.y());
    handle->axisX.append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max(), point.y());
    handle->axisY.setPen(pen);
    handle->axisY.attachAxis(mChart.axes(Qt::Horizontal).first());
    handle->axisY.attachAxis(mChart.axes(Qt::Vertical).first());
    handle->axisY.append(point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
    handle->axisY.append(point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max());
    mHandle.insert(name, handle);
    return *this;
}
Chart& Chart::removeHandle(QString name)
{
    if(mHandle.contains(name))
    {
        mChart.removeSeries(&mHandle.value(name)->point);
        mChart.removeSeries(&mHandle.value(name)->axisX);
        mChart.removeSeries(&mHandle.value(name)->axisY);
        delete mHandle.value(name);
        mHandle.remove(name);
    }
    return *this;
}
Chart& Chart::setHandlePosition(QString name, QPointF point)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.replace(0, point);
        mHandle.value(name)->axisX.replace(0, qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min(), point.y());
        mHandle.value(name)->axisX.replace(1, qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max(), point.y());
        mHandle.value(name)->axisY.replace(0, point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
        mHandle.value(name)->axisY.replace(1, point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max());
    }
    return *this;
}
Chart& Chart::setHandleVisible(QString name, bool visible)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.setVisible(visible);
        mHandle.value(name)->axisX.setVisible(visible);
        mHandle.value(name)->axisY.setVisible(visible);
    }
    return *this;
}
Chart& Chart::setHandleAxisVisible(QString name, bool visible)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->axisX.setVisible(visible);
        mHandle.value(name)->axisY.setVisible(visible);
    }
    return *this;
}
Chart& Chart::setHandleAxisVisibleX(QString name, bool visible)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->axisX.setVisible(visible);
    }
    return *this;
}
Chart& Chart::setHandleAxisVisibleY(QString name, bool visible)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->axisY.setVisible(visible);
    }
    return *this;
}
Chart& Chart::setHandleAxisStyle(QString name, Qt::PenStyle style)
{
    if(mHandle.contains(name))
    {
        QPen pen;
        pen=mHandle.value(name)->axisX.pen();
        pen.setStyle(style);
        mHandle.value(name)->axisX.setPen(pen);
        pen=mHandle.value(name)->axisY.pen();
        pen.setStyle(style);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisStyleX(QString name, Qt::PenStyle style)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisX.pen();
        pen.setStyle(style);
        mHandle.value(name)->axisX.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisStyleY(QString name, Qt::PenStyle style)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisY.pen();
        pen.setStyle(style);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisWidth(QString name, int width)
{
    if(mHandle.contains(name))
    {
        QPen pen;
        pen=mHandle.value(name)->axisX.pen();
        pen.setWidth(width);
        mHandle.value(name)->axisX.setPen(pen);
        pen=mHandle.value(name)->axisY.pen();
        pen.setWidth(width);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisWidthX(QString name, int width)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisX.pen();
        pen.setWidth(width);
        mHandle.value(name)->axisX.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisWidthY(QString name, int width)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisY.pen();
        pen.setWidth(width);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisColor(QString name, QColor color)
{
    if(mHandle.contains(name))
    {
        QPen pen;
        pen=mHandle.value(name)->axisX.pen();
        pen.setColor(color);
        mHandle.value(name)->axisX.setPen(pen);
        pen=mHandle.value(name)->axisY.pen();
        pen.setColor(color);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisColorX(QString name, QColor color)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisX.pen();
        pen.setColor(color);
        mHandle.value(name)->axisX.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisColorY(QString name, QColor color)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisY.pen();
        pen.setColor(color);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisLock(QString name, bool lock)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.setProperty("LockX", lock);
        mHandle.value(name)->point.setProperty("LockY", lock);
    }
    return *this;
}
Chart& Chart::setHandleAxisLockX(QString name, bool lock)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.setProperty("LockX", lock);
    }
    return *this;
}
Chart& Chart::setHandleAxisLockY(QString name, bool lock)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.setProperty("LockY", lock);
    }
    return *this;
}


// setter & getter ScrollLineSeries
Chart& Chart::addScrollLineSeries(QString name, qreal defaultData)
{
    if(name.isEmpty() || mScrollLineSeries.contains(name))
    {
        return *this;
    }
    ScrollLineSeries* series = new ScrollLineSeries();
    mChart.addSeries(&series->series);
    QPen pen(Qt::SolidLine);
    pen.setWidth(1);
    pen.setColor(QColor(33, 119, 218, 255));
    series->series.setPen(pen);
    series->series.attachAxis(mChart.axes(Qt::Horizontal).first());
    series->series.attachAxis(mChart.axes(Qt::Vertical).first());
    series->series.setProperty("DefaultData", defaultData);
    series->series.setProperty("NewData", defaultData);
    series->series.setProperty("StepValue", 0.2);
    series->series.setProperty("StepValueNew", 0.2);
    series->series.setProperty("StepCount", 0);
    series->series.setProperty("StepCountMax", 5);
    series->series.setProperty("RefreshFre", 20);
    series->series.setProperty("RefreshFreNew", 20);
    series->series.setProperty("ParamChange", false);
    series->timer.setProperty("Binding", name);
    series->timer.setInterval(10);
    connect(&series->timer, SIGNAL(timeout()), this, SLOT(on_ScrollLineSeriesTimers_timeout()));
    mScrollLineSeries.insert(name, series);
    return *this;
}
Chart& Chart::removeScrollLineSeries(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        mChart.removeSeries(&mScrollLineSeries.value(name)->series);
        delete mScrollLineSeries.value(name);
        mScrollLineSeries.remove(name);
    }
    return *this;
}
Chart& Chart::addScrollLineSeriesData(QString name, qreal data)
{
    if(mScrollLineSeries.contains(name))
    {
        mScrollLineSeries.value(name)->series.setProperty("NewData", data);
    }
    return *this;
}
Chart& Chart::clearScrollLineSeries(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        mScrollLineSeries.value(name)->series.clear();
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesStart(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        mScrollLineSeries.value(name)->timer.start();
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesStop(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        mScrollLineSeries.value(name)->timer.stop();
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesWidth(QString name, int width)
{
    if(mScrollLineSeries.contains(name))
    {
        QPen pen=mScrollLineSeries.value(name)->series.pen();
        pen.setWidth(width);
        mScrollLineSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesColor(QString name, QColor color)
{
    if(mScrollLineSeries.contains(name))
    {
        QPen pen=mScrollLineSeries.value(name)->series.pen();
        pen.setColor(color);
        mScrollLineSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesRefreshParam(QString name, qreal stepValue, int nHz)
{
    if(mScrollLineSeries.contains(name) && 0 < stepValue && stepValue <= 1 && !(10 % (int) (stepValue * 10)) && nHz <= (1000 / (int) (1.0 / stepValue)))
    {
        mScrollLineSeries.value(name)->series.setProperty("StepValueNew", stepValue);
        mScrollLineSeries.value(name)->series.setProperty("RefreshFreNew", nHz);
        mScrollLineSeries.value(name)->series.setProperty("ParamChange", true);
    }
    return *this;
}
qreal Chart::getScrollLineSeriesData(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        return mScrollLineSeries.value(name)->series.property("NewData").toReal();
    }
    return -1;
}


// setter & getter ScrollSplineSeries
Chart& Chart::addScrollSplineSeries(QString name, qreal defaultData)
{
    if(name.isEmpty() || mScrollSplineSeries.contains(name))
    {
        return *this;
    }
    ScrollSplineSeries* series = new ScrollSplineSeries();
    mChart.addSeries(&series->series);
    QPen pen(Qt::SolidLine);
    pen.setWidth(1);
    pen.setColor(QColor(33, 119, 218, 255));
    series->series.setPen(pen);
    series->series.attachAxis(mChart.axes(Qt::Horizontal).first());
    series->series.attachAxis(mChart.axes(Qt::Vertical).first());
    series->series.setProperty("DefaultData", defaultData);
    series->series.setProperty("NewData", defaultData);
    series->series.setProperty("StepValue", 0.2);
    series->series.setProperty("StepValueNew", 0.2);
    series->series.setProperty("StepCount", 0);
    series->series.setProperty("StepCountMax", 5);
    series->series.setProperty("RefreshFre", 20);
    series->series.setProperty("RefreshFreNew", 20);
    series->series.setProperty("ParamChange", false);
    series->timer.setProperty("Binding", name);
    series->timer.setInterval(10);
    connect(&series->timer, SIGNAL(timeout()), this, SLOT(on_ScrollSplineSeriesTimers_timeout()));
    mScrollSplineSeries.insert(name, series);
    return *this;
}
Chart& Chart::removeScrollSplineSeries(QString name)
{
    if(mScrollSplineSeries.contains(name))
    {
        mChart.removeSeries(&mScrollSplineSeries.value(name)->series);
        delete mScrollSplineSeries.value(name);
        mScrollSplineSeries.remove(name);
    }
    return *this;
}
Chart& Chart::addScrollSplineSeriesData(QString name, qreal data)
{
    if(mScrollSplineSeries.contains(name))
    {
        mScrollSplineSeries.value(name)->series.setProperty("NewData", data);
    }
    return *this;
}
Chart& Chart::clearScrollSplineSeries(QString name)
{
    if(mScrollSplineSeries.contains(name))
    {
        mScrollSplineSeries.value(name)->series.clear();
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesStart(QString name)
{
    if(mScrollSplineSeries.contains(name))
    {
        mScrollSplineSeries.value(name)->timer.start();
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesStop(QString name)
{
    if(mScrollSplineSeries.contains(name))
    {
        mScrollSplineSeries.value(name)->timer.stop();
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesWidth(QString name, int width)
{
    if(mScrollSplineSeries.contains(name))
    {
        QPen pen=mScrollSplineSeries.value(name)->series.pen();
        pen.setWidth(width);
        mScrollSplineSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesColor(QString name, QColor color)
{
    if(mScrollSplineSeries.contains(name))
    {
        QPen pen=mScrollSplineSeries.value(name)->series.pen();
        pen.setColor(color);
        mScrollSplineSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesRefreshParam(QString name, qreal stepValue, int nHz)
{
    if(mScrollSplineSeries.contains(name) && 0 < stepValue && stepValue <= 1 && !(10 % (int) (stepValue * 10)) && nHz <= (1000 / (int) (1.0 / stepValue)))
    {
        mScrollSplineSeries.value(name)->series.setProperty("StepValueNew", stepValue);
        mScrollSplineSeries.value(name)->series.setProperty("RefreshFreNew", nHz);
        mScrollSplineSeries.value(name)->series.setProperty("ParamChange", true);
    }
    return *this;
}
qreal Chart::getScrollSplineSeriesData(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        return mScrollSplineSeries.value(name)->series.property("NewData").toReal();
    }
    return -1;
}


// setter & getter ScrollAreaSeries
Chart& Chart::addScrollAreaSeries(QString name, qreal defaultData)
{
    if(name.isEmpty() || mScrollAreaSeries.contains(name))
    {
        return *this;
    }
    QLineSeries *seriesUpper = new QLineSeries();
    QLineSeries *seriesLower = new QLineSeries();
    ScrollAreaSeries* series = new ScrollAreaSeries();
    series->series.setUpperSeries(seriesUpper);
    seriesLower->append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 2, qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
    seriesLower->append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 1, qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
    series->series.setLowerSeries(seriesLower);
    mChart.addSeries(&series->series);
    QPen pen(Qt::SolidLine);
    pen.setWidth(1);
    pen.setColor(QColor(33, 119, 218, 255));
    series->series.setPen(pen);
    series->series.attachAxis(mChart.axes(Qt::Horizontal).first());
    series->series.attachAxis(mChart.axes(Qt::Vertical).first());
    series->series.setProperty("DefaultData", defaultData);
    series->series.setProperty("NewData", defaultData);
    series->series.setProperty("StepValue", 0.2);
    series->series.setProperty("StepValueNew", 0.2);
    series->series.setProperty("StepCount", 0);
    series->series.setProperty("StepCountMax", 5);
    series->series.setProperty("RefreshFre", 20);
    series->series.setProperty("RefreshFreNew", 20);
    series->series.setProperty("ParamChange", false);
    series->timer.setProperty("Binding", name);
    series->timer.setInterval(10);
    connect(&series->timer, SIGNAL(timeout()), this, SLOT(on_ScrollAreaSeriesTimers_timeout()));
    mScrollAreaSeries.insert(name, series);
    return *this;
}
Chart& Chart::removeScrollAreaSeries(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        mChart.removeSeries(&mScrollAreaSeries.value(name)->series);
        delete mScrollAreaSeries.value(name)->series.upperSeries();
        delete mScrollAreaSeries.value(name)->series.lowerSeries();
        delete mScrollAreaSeries.value(name);
        mScrollAreaSeries.remove(name);
    }
    return *this;
}
Chart& Chart::addScrollAreaSeriesData(QString name, qreal data)
{
    if(mScrollAreaSeries.contains(name))
    {
        mScrollAreaSeries.value(name)->series.setProperty("NewData", data);
    }
    return *this;
}
Chart& Chart::clearScrollAreaSeries(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        mScrollAreaSeries.value(name)->series.upperSeries()->clear();
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesStart(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        mScrollAreaSeries.value(name)->timer.start();
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesStop(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        mScrollAreaSeries.value(name)->timer.stop();
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesWidth(QString name, int width)
{
    if(mScrollAreaSeries.contains(name))
    {
        QPen pen=mScrollAreaSeries.value(name)->series.pen();
        pen.setWidth(width);
        mScrollAreaSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesColor(QString name, QColor color)
{
    if(mScrollAreaSeries.contains(name))
    {
        QPen pen=mScrollAreaSeries.value(name)->series.pen();
        pen.setColor(color);
        mScrollAreaSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesRefreshParam(QString name, qreal stepValue, int nHz)
{
    if(mScrollAreaSeries.contains(name) && 0 < stepValue && stepValue <= 1 && !(10 % (int) (stepValue * 10)) && nHz <= (1000 / (int) (1.0 / stepValue)))
    {
        mScrollAreaSeries.value(name)->series.setProperty("StepValueNew", stepValue);
        mScrollAreaSeries.value(name)->series.setProperty("RefreshFreNew", nHz);
        mScrollAreaSeries.value(name)->series.setProperty("ParamChange", true);
    }
    return *this;
}
qreal Chart::getScrollAreaSeriesData(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        return mScrollAreaSeries.value(name)->series.property("NewData").toReal();
    }
    return -1;
}


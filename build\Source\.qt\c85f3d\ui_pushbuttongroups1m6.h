/********************************************************************************
** Form generated from reading UI file 'pushbuttongroups1m6.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PUSHBUTTONGROUPS1M6_H
#define UI_PUSHBUTTONGROUPS1M6_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_PushButtonGroupS1M6
{
public:
    QGridLayout *gridLayout_2;
    QFrame *frame;
    QGridLayout *gridLayout_6;
    QGridLayout *gridLayout_5;
    QGridLayout *gridLayout;
    QLabel *Item1Label;
    QSpacerItem *horizontalSpacer;
    QPushButton *Item1PushButton;
    QSpacerItem *verticalSpacer;
    QGridLayout *gridLayout_3;
    QLabel *Item2Label;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *Item2PushButton;
    QSpacerItem *verticalSpacer_2;
    QGridLayout *gridLayout_4;
    QLabel *Item3Label;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *Item3PushButton;

    void setupUi(QWidget *PushButtonGroupS1M6)
    {
        if (PushButtonGroupS1M6->objectName().isEmpty())
            PushButtonGroupS1M6->setObjectName("PushButtonGroupS1M6");
        PushButtonGroupS1M6->resize(100, 60);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(PushButtonGroupS1M6->sizePolicy().hasHeightForWidth());
        PushButtonGroupS1M6->setSizePolicy(sizePolicy);
        PushButtonGroupS1M6->setMinimumSize(QSize(1, 1));
        gridLayout_2 = new QGridLayout(PushButtonGroupS1M6);
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName("gridLayout_2");
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(PushButtonGroupS1M6);
        frame->setObjectName("frame");
        sizePolicy.setHeightForWidth(frame->sizePolicy().hasHeightForWidth());
        frame->setSizePolicy(sizePolicy);
        frame->setFrameShape(QFrame::Shape::NoFrame);
        frame->setFrameShadow(QFrame::Shadow::Plain);
        frame->setLineWidth(0);
        gridLayout_6 = new QGridLayout(frame);
        gridLayout_6->setSpacing(0);
        gridLayout_6->setObjectName("gridLayout_6");
        gridLayout_6->setContentsMargins(0, 0, 0, 0);
        gridLayout_5 = new QGridLayout();
        gridLayout_5->setSpacing(0);
        gridLayout_5->setObjectName("gridLayout_5");
        gridLayout = new QGridLayout();
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        Item1Label = new QLabel(frame);
        Item1Label->setObjectName("Item1Label");
        sizePolicy.setHeightForWidth(Item1Label->sizePolicy().hasHeightForWidth());
        Item1Label->setSizePolicy(sizePolicy);
        Item1Label->setLineWidth(0);
        Item1Label->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout->addWidget(Item1Label, 0, 0, 1, 1);

        horizontalSpacer = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer, 0, 1, 1, 1);

        Item1PushButton = new QPushButton(frame);
        Item1PushButton->setObjectName("Item1PushButton");
        sizePolicy.setHeightForWidth(Item1PushButton->sizePolicy().hasHeightForWidth());
        Item1PushButton->setSizePolicy(sizePolicy);
        Item1PushButton->setMinimumSize(QSize(1, 1));

        gridLayout->addWidget(Item1PushButton, 0, 2, 1, 1);

        gridLayout->setColumnStretch(0, 5);
        gridLayout->setColumnStretch(1, 10);
        gridLayout->setColumnStretch(2, 100);

        gridLayout_5->addLayout(gridLayout, 0, 0, 1, 1);

        verticalSpacer = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_5->addItem(verticalSpacer, 1, 0, 1, 1);

        gridLayout_3 = new QGridLayout();
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName("gridLayout_3");
        Item2Label = new QLabel(frame);
        Item2Label->setObjectName("Item2Label");
        sizePolicy.setHeightForWidth(Item2Label->sizePolicy().hasHeightForWidth());
        Item2Label->setSizePolicy(sizePolicy);
        Item2Label->setLineWidth(0);
        Item2Label->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_3->addWidget(Item2Label, 0, 0, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_2, 0, 1, 1, 1);

        Item2PushButton = new QPushButton(frame);
        Item2PushButton->setObjectName("Item2PushButton");
        sizePolicy.setHeightForWidth(Item2PushButton->sizePolicy().hasHeightForWidth());
        Item2PushButton->setSizePolicy(sizePolicy);
        Item2PushButton->setMinimumSize(QSize(1, 1));

        gridLayout_3->addWidget(Item2PushButton, 0, 2, 1, 1);

        gridLayout_3->setColumnStretch(0, 5);
        gridLayout_3->setColumnStretch(1, 10);
        gridLayout_3->setColumnStretch(2, 100);

        gridLayout_5->addLayout(gridLayout_3, 2, 0, 1, 1);

        verticalSpacer_2 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_5->addItem(verticalSpacer_2, 3, 0, 1, 1);

        gridLayout_4 = new QGridLayout();
        gridLayout_4->setSpacing(0);
        gridLayout_4->setObjectName("gridLayout_4");
        Item3Label = new QLabel(frame);
        Item3Label->setObjectName("Item3Label");
        sizePolicy.setHeightForWidth(Item3Label->sizePolicy().hasHeightForWidth());
        Item3Label->setSizePolicy(sizePolicy);
        Item3Label->setLineWidth(0);
        Item3Label->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_4->addWidget(Item3Label, 0, 0, 1, 1);

        horizontalSpacer_3 = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_4->addItem(horizontalSpacer_3, 0, 1, 1, 1);

        Item3PushButton = new QPushButton(frame);
        Item3PushButton->setObjectName("Item3PushButton");
        sizePolicy.setHeightForWidth(Item3PushButton->sizePolicy().hasHeightForWidth());
        Item3PushButton->setSizePolicy(sizePolicy);
        Item3PushButton->setMinimumSize(QSize(1, 1));

        gridLayout_4->addWidget(Item3PushButton, 0, 2, 1, 1);

        gridLayout_4->setColumnStretch(0, 5);
        gridLayout_4->setColumnStretch(1, 10);
        gridLayout_4->setColumnStretch(2, 100);

        gridLayout_5->addLayout(gridLayout_4, 4, 0, 1, 1);

        gridLayout_5->setRowStretch(0, 100);
        gridLayout_5->setRowStretch(1, 30);
        gridLayout_5->setRowStretch(2, 100);
        gridLayout_5->setRowStretch(3, 30);
        gridLayout_5->setRowStretch(4, 100);

        gridLayout_6->addLayout(gridLayout_5, 0, 0, 1, 1);


        gridLayout_2->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(PushButtonGroupS1M6);

        QMetaObject::connectSlotsByName(PushButtonGroupS1M6);
    } // setupUi

    void retranslateUi(QWidget *PushButtonGroupS1M6)
    {
        PushButtonGroupS1M6->setWindowTitle(QCoreApplication::translate("PushButtonGroupS1M6", "Form", nullptr));
        Item1Label->setText(QString());
        Item1PushButton->setText(QString());
        Item2Label->setText(QString());
        Item2PushButton->setText(QString());
        Item3Label->setText(QString());
        Item3PushButton->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class PushButtonGroupS1M6: public Ui_PushButtonGroupS1M6 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PUSHBUTTONGROUPS1M6_H

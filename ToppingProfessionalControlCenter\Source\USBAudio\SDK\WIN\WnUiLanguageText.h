/************************************************************************
 *  The module is a wrapper for an UI language text.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnUiLanguageText_h__
#define __WnUiLanguageText_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

#define UILT_UI_NEWLINE L"${UI_NL}"
#define UILT_FILE_NEWLINE L"${F_NL}"

//
// Wrapper class for an UI language.
//
class WnUiLanguageText
{
//construction/destruction/assignment
public:
    WnUiLanguageText();
    WnUiLanguageText(const wchar_t* idStr, const wchar_t* defaultText, const WString& comment = L"");
    ~WnUiLanguageText();
    WnUiLanguageText( const WnUiLanguageText& src );
    WnUiLanguageText& operator =( const WnUiLanguageText& src );

//interface
public:
    //
    // get/set the identifier string of the text
    //
    const wchar_t* GetIdentifierStr() const
    {
        return mIdentifierStr;
    }

    void SetIdentifierStr(const wchar_t* idStr)
    {
        mIdentifierStr = idStr;
    }

    //
    // get/set the UI text, in a specific language
    //
    // Note: The text may contain placeholders for file newlines (see WnUiLanguageText::F_NL) 
    //       and UI newlines (WnUiLanguageText::UI_NL). 
    //       A file newline is used to format the text if it is written to a file. It is replaced 
    //       by a line continuation token. 
    //       An UI newline is used to format the displayed text, e.g. the error text in a dialog 
    //       box. Both placeholders should never be displayed in the UI. Please use the method 
    //       GetLanguageTextToDisplay() to get the appropriate text for this purpose.
    //       
    const WString& GetLanguageText() const
    {
        return mLanguageText;
    }

    void SetLanguageText(const WString& text)
    {
        mLanguageText = text;
        mIsLanguageTextValid = true;
    }

    //
    // clear and invalidate the current language text 
    //
    void ClearLanguageText()
    {
        mLanguageText = L"";
        mIsLanguageTextValid = false;
    }

    //
    // Since an empty string may also be a valid language text this flag indicates whether a valid language text was already set.
    // The text returned by GetLanguageText() can be ignored, if this flag is false.
    //
    bool IsLanguageTextValid() const {
        return mIsLanguageTextValid;
    }

    //
    // get/set the default UI text, in English
    //
    // Note: The text may contain placeholders for file newlines (see WnUiLanguageText::F_NL) 
    //       and UI newlines (WnUiLanguageText::UI_NL). 
    //       A file newline is used to format the text if it is written to a file. It is replaced 
    //       by a line continuation token. 
    //       An UI newline is used to format the displayed text, e.g. the error text in a dialog 
    //       box. Both placeholders should never be displayed in the UI. Please use the method 
    //       GetLanguageTextToDisplay() to get the appropriate text for this purpose.
    //
    const wchar_t* GetDefaultText() const
    {
        return mDefaultText;
    }

    void SetDefaultText(const wchar_t* text)
    {
        mDefaultText = text;
    }    

    //
    // Get/set the optional comment which describes the usage of the text in the UI or contains some hints for translation.
    // Usually this text is only written to a language file in case such a file is created by the application. It is
    // never displayed by the application UI.
    //
    // Note: The comment may contain placeholders for file newlines (see WnUiLanguageText::F_NL).
    //       A file newline is used to format the comment if it is written to a file.     
    //
    const WString& GetComment() const
    {
        return mComment;
    }

    void SetComment(const WString& comment)
    {
        mComment = comment;
    }

    //
    // clear any information 
    //
    void Clear();

    //
    // Get the language text to display in the UI. If the specific language text is not available the default text is returned.
    // Any formatting placeholders like file newlines (see WnUiLanguageText::F_NL) and UI newlines (WnUiLanguageText::UI_NL)
    // are preprocessed and removed.
    //
    WString 
    GetLanguageTextToDisplay() const;    

private:
    
//data
public:
    //placeholder for a newline to be displayed in the GUI  
    static WString UI_NL;

    //placeholder for a new line that only formats the file content in case the text is written to a file
    static WString F_NL;

private:
    //identifier string of the text
    const wchar_t* mIdentifierStr{nullptr};

    //UI text, in a specific language
    WString mLanguageText;

    //Since an empty string may also be a valid language text this flag indicates whether a valid language text was already set.
    //The text returned by GetLanguageText() can be ignored, if this flag is false.
    bool mIsLanguageTextValid{false};

    //default UI text, in English
    const wchar_t* mDefaultText{nullptr};

    //optional comment which describes the usage of the text in the UI or contains some hints for translation
    WString mComment;    
};

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguageText_h__

/*************************** EOF **************************************/

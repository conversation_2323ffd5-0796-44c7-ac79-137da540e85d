#ifndef DIALS1M4_H
#define DIALS1M4_H


#include <QFont>
#include <QRect>
#include <QColor>
#include <QWidget>
#include <QPointF>
#include <QPainter>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QResizeEvent>


class DialS1M4 : public QWidget
{
    Q_OBJECT
public:
    explicit DialS1M4(QWidget* parent=nullptr);
    ~DialS1M4();
    DialS1M4& setFont(QFont font);
    DialS1M4& setValue(float value);
    DialS1M4& setDefault(float value);
    DialS1M4& setRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    DialS1M4& setSensitivity(int sensitivity);
    DialS1M4& setMovable(bool status=true);
    DialS1M4& setColorBG(QColor color);
    DialS1M4& setColorDial(QColor color);
    DialS1M4& setColorCircleBG(QColor color);
    DialS1M4& setColorCircleValue(QColor color);
    DialS1M4& setColorHandle(QColor color);
    DialS1M4& setColorText(QColor color);
    float getValue() { return mValue; }
    float getDefault() { return mValueDefault; }
    int getMin() { return mValueEnd20; }
    int getMax() { return mValueStart; }
    DialS1M4& showArrow(bool status=true);
    DialS1M4& showCircle(bool status=true);
    DialS1M4& showText(bool status=true);
    DialS1M4& showInfinity(bool state=true);
    DialS1M4& showInfinitesimal(bool state=true);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
    void mouseDoubleClickEvent(QMouseEvent* e) override;
    void mousePressEvent(QMouseEvent* e) override;
    void mouseMoveEvent(QMouseEvent* e) override;
    void mouseReleaseEvent(QMouseEvent* e) override;
    void wheelEvent(QWheelEvent* e) override;
    void keyPressEvent(QKeyEvent* e) override;
private:
    bool mMouseEnabled=true;
    bool mPressed=false;
    float mPressedValue=0;
    QPointF mPressedPoint;
    bool mValueShowArrow=true;
    bool mValueShowCircle=true;
    bool mValueShowText=true;
    bool mShowInfinity=false;
    bool mShowInfinitesimal=false;
    float mValue=0;
    float mValueDefault=0;
    float mValueMin=-30;
    float mValueMax=0;
    int mValueStart=0;
    int mValueEnd05=-10;
    int mValueEnd10=-20;
    int mValueEnd20=-30;
    int mSensitivity=5;
    QRect mRectDial;
    QFont mFont;
    int mPenWidth=0;
    QColor mColorBG=QColor(22, 22, 22);
    QColor mColorDial=QColor(53, 53, 53);
    QColor mColorCircleBG=QColor(46, 46, 46);
    QColor mColorCircleValue=QColor(67, 207, 124);
    QColor mColorHandle=QColor(67, 207, 124);
    QColor mColorText=QColor(67, 207, 124);
    void drawBG(QPainter* painter);
    void drawElement(QPainter* painter);
signals:
    void valueChanged(float value);
};


#endif // DialS1M4_H


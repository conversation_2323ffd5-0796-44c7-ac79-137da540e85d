#ifndef AUTOSTARTMANAGER_H
#define AUTOSTARTMANAGER_H

#include <QObject>
#include "singleton.h"

class AutoStartManager : public QObject
{
    Q_OBJECT
    BE_SINGLETON(AutoStartManager)
public:
    void setAutoStart(bool enable);
    bool isAutoStartEnabled() const;

private:
    explicit AutoStartManager(QObject *parent = nullptr);
    ~AutoStartManager();
    void setAutoStartForWindows(bool enable);
    void setAutoStartForMac(bool enable);
    bool isAutoStartEnabledForWindows() const;
    bool isAutoStartEnabledForMac() const;
};

#define ATSMHandle AutoStartManager::instance()

#endif // AUTOSTARTMANAGER_H

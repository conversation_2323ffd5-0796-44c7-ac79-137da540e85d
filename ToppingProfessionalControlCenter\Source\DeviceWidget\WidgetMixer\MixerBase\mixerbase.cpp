#include "mixerbase.h"


MixerBase::MixerBase(QWidget* parent)
    : Solo(parent)
{
    connect(this, SIGNAL(soloStateChanged(QString, bool)), this, SLOT(in_widgetBase_soloStateChanged(QString, bool)), Qt::UniqueConnection);
}


// slot
void MixerBase::in_widgetBase_soloStateChanged(QString objectName, bool state)
{
    emit attributeChanged(objectName, "Solo", QString::number(static_cast<int>(state)));
}


// setter & getter
MixerBase& MixerBase::assignMixer(QString* mixer)
{
    mMixer = mixer;
    return *this;
}
MixerBase& MixerBase::assignMixerList(QVector<QString>* mixerList)
{
    mMixerList = mixerList;
    return *this;
}
MixerBase& MixerBase::assignOriginSoloState(QHash<QString, int>* originSoloState)
{
    mOriginSoloState = originSoloState;
    return *this;
}
MixerBase& MixerBase::assignOriginVisibleList(QHash<QString, QString>* originVisibleList)
{
    mOriginVisibleList = originVisibleList;
    return *this;
}
MixerBase& MixerBase::handleFieldSoloStateChanged(int state)
{
    doGlobalSoloChanged(state);
    return *this;
}
MixerBase& MixerBase::setChannelName(QString name)
{
    mChannelName = name;
    return *this;
}
MixerBase& MixerBase::setWidgetEnable(bool state)
{
    mEnable = state;
    return *this;
}
MixerBase& MixerBase::setWidgetEnableWithUpdate(bool state)
{
    if(mEnable != state)
    {
        if(state)
        {
            if(getLinkState())
            {
                if(getSoloState()) in_widgetBase_soloStateChanged(objectName(), true);
            }
            else
            {
                if(getSoloStateLeft()) in_widgetBase_soloStateChanged(objectName(), true);
                if(getSoloStateRight()) in_widgetBase_soloStateChanged(objectName(), true);
            }
        }
        else
        {
            if(getLinkState())
            {
                if(getSoloState()) in_widgetBase_soloStateChanged(objectName(), false);
            }
            else
            {
                if(getSoloStateLeft()) in_widgetBase_soloStateChanged(objectName(), false);
                if(getSoloStateRight()) in_widgetBase_soloStateChanged(objectName(), false);
            }
        }
    }
    mEnable = state;
    if(mEmitAction)
    {
        emit attributeChanged(this->objectName(), "Save_" + *mMixer + "_Enable", QString::number(mEnable));
    }
    updateAttribute();
    return *this;
}
MixerBase& MixerBase::setWidgetMovable(bool state)
{
    mMovable = state;
    return *this;
}
MixerBase& MixerBase::setWidgetReady(bool state)
{
    mReady = state;
    return *this;
}
MixerBase& MixerBase::setWidgetEmitAction(bool state)
{
    mEmitAction = state;
    return *this;
}
int MixerBase::getOriginSoloState(QString mixer)
{
    return mOriginSoloState->value(mixer);
}
QString MixerBase::getOriginVisibleList(QString mixer)
{
    return mOriginVisibleList->value(mixer);
}


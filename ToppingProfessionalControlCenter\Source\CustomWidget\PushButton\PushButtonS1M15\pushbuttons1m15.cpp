#include "pushbuttons1m15.h"
#include <QGraphicsDropShadowEffect>
PushButtonS1M15::PushButtonS1M15(QWidget* parent)
    : QPushButton(parent), m_shadowEffect(new QGraphicsDropShadowEffect(this))
{
    setGraphicsEffect(m_shadowEffect);
    setShadowOffset({0, 0});
    setShadowVisible(false);
}

void PushButtonS1M15::setShadowColor(QColor color)
{
    m_shadowEffect->setColor(color);
}

void PushButtonS1M15::setShadowOffset(QPoint offset)
{
    m_shadowEffect->setOffset(offset);
}

void PushButtonS1M15::setShadowRadius(int radius)
{
    m_shadowEffect->setBlurRadius(radius);
}

void PushButtonS1M15::setShadowVisible(bool isVisible){
    m_shadowEffect->setEnabled(isVisible);
}

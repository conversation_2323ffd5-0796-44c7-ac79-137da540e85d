#include <QScrollBar>

#include "fieldoriginbase1.h"


FieldOriginBase1::FieldOriginBase1(QWidget* parent)
    : QWidget(parent)
{
    setStyleSheet("background: rgb(31, 31, 31);");
    mScrollArea.setParent(this);
    mScrollArea.setWidget(&mWidget);
    mScrollArea.setWidgetResizable(true);
    mScrollArea.setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOn);
    mScrollArea.setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea.horizontalScrollBar()->setContextMenuPolicy(Qt::NoContextMenu);
    mScrollArea.verticalScrollBar()->setContextMenuPolicy(Qt::NoContextMenu);
    mScrollArea.setFrameShape(QFrame::Shape::NoFrame);
    mScrollArea.setFrameShadow(QFrame::Shadow::Plain);
    mScrollArea.setLineWidth(0);
    connect(mScrollArea.horizontalScrollBar(), SIGNAL(valueChanged(int)), this, SLOT(in_mScrollArea_valueChanged(int)), Qt::UniqueConnection);
    QString style;
    style = "QScrollArea {"
            "   background-color: transparent;"
            "}"
            "QScrollBar::sub-line:horizontal {"
            "   width: 0px;"
            "}"
            "QScrollBar::add-line:horizontal {"
            "   width: 0px;"
            "}"
            "QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {"
            "   background: none;"
            "}";
    mScrollArea.setStyleSheet(style);
    style = "QWidget {"
            "   background-color: transparent;"
            "}";
    mWidget.setStyleSheet(style);
    mWidgetAddition.setParent(&mWidget);
    connect(&mWidgetAddition, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mWidgetAddition_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
}
FieldOriginBase1::~FieldOriginBase1()
{
    for(auto element : mWidgetList)
    {
        delete element;
    }
}


// override
void FieldOriginBase1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    float hPixelPerRatio=height() / 100.0;
    mRectBody = rect();
    mScrollArea.setGeometry(mRectBody);
    hPixelPerRatio=mRectBody.height() / 100.0;
    int marginTop = hPixelPerRatio * 0.6;
    int marginBottom = hPixelPerRatio * 3;
    int marginLeft = hPixelPerRatio * 2;
    int marginRight = hPixelPerRatio * 2;
    int spacing = hPixelPerRatio * 1.2;
    int x=marginLeft, h=mRectBody.height() - marginTop - marginBottom, w=0;
    for(auto element : mWidgetList)
    {
        if(element->visible)
        {
            w = qMax(h, element->widget->minimumHeight()) / ((float) element->widget->minimumHeight()) * element->widget->minimumWidth();
            element->widget->setGeometry(x, marginTop, w, h);
            x += w;
            x += spacing;
        }
    }
    w = qMax(h, mWidgetAddition.minimumHeight()) / ((float) mWidgetAddition.minimumHeight()) * mWidgetAddition.minimumWidth();
    if(mAdditionVisible && mWidgetAddition.getVisibleCount())
    {
        mWidgetAddition.setGeometry(x, marginTop, w, h);
        x += w;
        x += spacing;
    }
    else
    {
        mWidgetAddition.setGeometry(x + marginRight, marginTop, w, h);
    }
    x -= spacing;
    x += marginRight;
    mWidget.setFixedWidth(x);
    w = qMax((int) (x), (int) (mRectBody.height() / 2));
    mRectWidgetArea.setRect(hPixelPerRatio, mRectBody.y() + marginTop - hPixelPerRatio, w - hPixelPerRatio * 1.6, h + hPixelPerRatio * 2.2);
    setMinimumWidth(mRectBody.height());
    QString style;
    if(mScrollArea.horizontalScrollBar()->maximum() > 0)
    {
        style += QString("QScrollBar::handle:horizontal {"
                         "   background: rgb(161, 161, 161);"
                         "   min-width: 20px;"
                         "   border-radius: %1px;"
                         "}"
                         "QScrollBar::handle:horizontal:hover {"
                         "   background: rgb(224, 224, 224);"
                         "}"
                         "QScrollBar::handle:horizontal:pressed {"
                         "   background: rgb(224, 224, 224);"
                         "}").arg(marginBottom * 0.2);
    }
    else
    {
        style += "QScrollBar::handle:horizontal { background: transparent; }";
    }
    style += QString("QScrollBar:horizontal {"
                     "   background: rgb(22, 22, 22);"
                     "   height: %1px;"
                     "   border-radius: %2px;"
                     "   margin-top: 0px;"
                     "   margin-bottom: %3px;"
                     "   margin-left: %4px;"
                     "   margin-right: %4px;"
                     "}").arg(marginBottom * 0.7).arg(marginBottom * 0.2).arg(marginBottom * 0.25).arg(marginLeft);
    mScrollArea.horizontalScrollBar()->setStyleSheet(style);
}
void FieldOriginBase1::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawWidgetArea(&painter);
}
void FieldOriginBase1::drawBG(QPainter *painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(mColorBG));
    painter->drawRect(rect());
    painter->restore();
}
void FieldOriginBase1::drawWidgetArea(QPainter *painter)
{
    if(mWidgetAreaVisible)
    {
        painter->save();
        painter->setPen(Qt::NoPen);
        painter->setBrush(QBrush(mColorWidgetArea));
        QRect rect(mRectWidgetArea);
        rect.setX(rect.x() - mScrollBarValue);
        rect.setWidth(rect.width() - mScrollBarValue);
        painter->drawRoundedRect(rect, rect.height() / 50, rect.height() / 50);
        painter->restore();
    }
}


// slot
void FieldOriginBase1::in_mScrollArea_valueChanged(int value)
{
    mScrollBarValue = value;
    update();
}
void FieldOriginBase1::in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(value);
    if(attribute == "Hide")
    {
        for(auto element : mWidgetList)
        {
            if(element->widget->getChannelName() == objectName)
            {
                element->visible = false;
                element->widget->setHidden(true);
                element->widget->setWidgetEnableWithUpdate(false);
                mWidgetAddition.setButtonVisible(objectName);
                QResizeEvent e(size(), size());
                resizeEvent(&e);
                update();
                emit attributeChanged(objectName, "Visible", QString::number(0));
                break;
            }
        }
    }
    else if(attribute == "Resize")
    {
        QResizeEvent e(size(), size());
        resizeEvent(&e);
        update();
    }
}
void FieldOriginBase1::in_mWidgetAddition_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(attribute);
    Q_UNUSED(value);
    for(auto element : mWidgetList)
    {
        if(element->widget->getChannelName() == objectName)
        {
            element->visible = true;
            element->widget->setHidden(false);
            element->widget->setWidgetEnableWithUpdate(true);
            QResizeEvent e(size(), size());
            resizeEvent(&e);
            update();
            emit attributeChanged(objectName, "Visible", QString::number(1));
            break;
        }
    }
}


// setter & getter
FieldOriginBase1& FieldOriginBase1::modifyWidgetList(QVector<OriginBase*> list)
{
    QVector<QString> nameList;
    for(auto element : mWidgetList)
    {
        delete element;
    }
    mWidgetList.clear();
    for(auto element : list)
    {
        nameList.append(element->getChannelName());
        element->setParent(&mWidget);
        element->setHidden(true);
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mWidgetListAll_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
        MixerWidget* newWidget=new MixerWidget();
        newWidget->visible = false;
        newWidget->widget = element;
        mWidgetList.append(newWidget);
    }
    mWidgetAddition.modifyButtonList(nameList);
    return *this;
}
FieldOriginBase1& FieldOriginBase1::setFont(QFont font)
{
    mFont = font;
    mWidgetAddition.setFont(mFont);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase1& FieldOriginBase1::setVisibleList(QVector<QString> list)
{
    QVector<QString> nameList;
    for(auto element : mWidgetList)
    {
        if(list.contains(element->widget->getChannelName()))
        {
            element->visible = true;
            element->widget->setHidden(false);
            element->widget->setWidgetEnable(true);
        }
        else
        {
            nameList.append(element->widget->getChannelName());
            element->visible = false;
            element->widget->setHidden(true);
            element->widget->setWidgetEnable(false);
        }
    }
    mWidgetAddition.setVisibleList(nameList);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase1& FieldOriginBase1::setFieldColor(QColor color)
{
    mColorBG = color;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase1& FieldOriginBase1::setWidgetAreaColor(QColor color)
{
    mColorWidgetArea = color;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase1& FieldOriginBase1::setWidgetAreaVisible(bool state)
{
    mWidgetAreaVisible = state;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase1& FieldOriginBase1::setAdditionVisible(bool state)
{
    mAdditionVisible = state;
    mWidgetAddition.setHidden(!mAdditionVisible);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
FieldOriginBase1& FieldOriginBase1::setAdditionButtonWeight(int weightWidth, int weightHeight)
{
    mWidgetAddition.setButtonWeight(weightWidth, weightHeight);
    return *this;
}


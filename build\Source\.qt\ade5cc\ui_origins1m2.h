/********************************************************************************
** Form generated from reading UI file 'origins1m2.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ORIGINS1M2_H
#define UI_ORIGINS1M2_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <pushbuttons1m9.h>
#include <volumemeters1m1.h>
#include <vsliders1m1.h>

QT_BEGIN_NAMESPACE

class Ui_OriginS1M2
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    QLineEdit *lineEdit;
    VSliderS1M1 *widgetVSlider;
    VolumeMeterS1M1 *widgetVolumeMeter;
    PushButtonS1M9 *widgetPushButtonGroup1;

    void setupUi(QWidget *OriginS1M2)
    {
        if (OriginS1M2->objectName().isEmpty())
            OriginS1M2->setObjectName("OriginS1M2");
        OriginS1M2->resize(140, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(OriginS1M2->sizePolicy().hasHeightForWidth());
        OriginS1M2->setSizePolicy(sizePolicy);
        OriginS1M2->setMinimumSize(QSize(80, 220));
        gridLayout = new QGridLayout(OriginS1M2);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(OriginS1M2);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 20));
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(9, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetVSlider = new VSliderS1M1(frame);
        widgetVSlider->setObjectName("widgetVSlider");
        widgetVSlider->setGeometry(QRect(80, 40, 21, 131));
        sizePolicy.setHeightForWidth(widgetVSlider->sizePolicy().hasHeightForWidth());
        widgetVSlider->setSizePolicy(sizePolicy);
        widgetVolumeMeter = new VolumeMeterS1M1(frame);
        widgetVolumeMeter->setObjectName("widgetVolumeMeter");
        widgetVolumeMeter->setGeometry(QRect(10, 40, 21, 131));
        sizePolicy.setHeightForWidth(widgetVolumeMeter->sizePolicy().hasHeightForWidth());
        widgetVolumeMeter->setSizePolicy(sizePolicy);
        widgetPushButtonGroup1 = new PushButtonS1M9(frame);
        widgetPushButtonGroup1->setObjectName("widgetPushButtonGroup1");
        widgetPushButtonGroup1->setGeometry(QRect(10, 210, 90, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup1->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup1->setSizePolicy(sizePolicy);
        widgetPushButtonGroup1->setMinimumSize(QSize(0, 0));

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(OriginS1M2);

        QMetaObject::connectSlotsByName(OriginS1M2);
    } // setupUi

    void retranslateUi(QWidget *OriginS1M2)
    {
        OriginS1M2->setWindowTitle(QCoreApplication::translate("OriginS1M2", "Form", nullptr));
        pushButtonClose->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class OriginS1M2: public Ui_OriginS1M2 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ORIGINS1M2_H

#include "globalfont.h"
#include "pushbuttons1m7.h"


PushButtonS1M7::PushButtonS1M7(QWidget* parent)
    : QWidget(parent)
{
    mPushButtonMUTE.setParent(this);
    mPushButtonMUTE.setText("MUTE");
    connect(&mPushButtonMUTE, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTE_clicked()), Qt::UniqueConnection);
}
PushButtonS1M7::~PushButtonS1M7()
{

}


// override
void PushButtonS1M7::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wPushButton=wPixelPerRatio * mWeightWidth;
    int xPushButton=(size().width() - wPushButton) / 2;
    // H
    float hPushButton=size().height() / 2.0;
    mPushButtonMUTE.setGeometry(xPushButton, 0, wPushButton, hPushButton * 2);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonMUTE.text(), mPushButtonMUTE.rect()));
    mPushButtonMUTE.setFont(mFont);
    mRadius = hPushButton * 0.8;
    setPushButtonStateMUTE(mPushButtonStateMUTE);
}


// slot
void PushButtonS1M7::in_mPushButtonMUTE_clicked()
{
    QString style;
    mPushButtonStateMUTE = !mPushButtonStateMUTE;
    if(mPushButtonStateMUTE)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(149, 40, 37);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonMUTE.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTE, mPushButtonStateMUTE);
}


// setter & getter
PushButtonS1M7& PushButtonS1M7::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M7& PushButtonS1M7::setPushButtonWeightWidth(int weight)
{
    mWeightWidth = weight;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M7& PushButtonS1M7::setPushButtonStateMUTE(bool state)
{
    QString style;
    mPushButtonStateMUTE = state;
    if(mPushButtonStateMUTE)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(149, 40, 37);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonMUTE.setStyleSheet(style);
    return *this;
}
PushButtonS1M7& PushButtonS1M7::setPushButtonClickedMUTE(bool state)
{
    mPushButtonStateMUTE = !state;
    in_mPushButtonMUTE_clicked();
    return *this;
}
bool PushButtonS1M7::getPushButtonStateMUTE()
{
    return mPushButtonStateMUTE;
}


#ifndef OUTPUTS1M3_H
#define OUTPUTS1M3_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "outputbase.h"
#include "appsettings.h"


namespace Ui {
class OutputS1M3;
}


class OutputS1M3 : public OutputBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OutputS1M3(QWidget* parent=nullptr, QString name="");
    ~OutputS1M3();
    OutputS1M3& setName(QString name);
    OutputS1M3& setFont(QFont font);
    OutputS1M3& setVolumeMeterLeft(int value);
    OutputS1M3& setVolumeMeterLeftClear();
    OutputS1M3& setVolumeMeterLeftSlip();
    OutputS1M3& setVolumeMeterRight(int value);
    OutputS1M3& setVolumeMeterRightClear();
    OutputS1M3& setVolumeMeterRightSlip();
    OutputS1M3& setGain(float value);
    OutputS1M3& setGainLock(bool state=true);
    OutputS1M3& setMuteAffectGain(bool state=true);
    OutputS1M3& setGainAffectMute(bool state=true);
    OutputS1M3& setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    OutputS1M3& setGainDefault(float value);
    OutputS1M3& setGainWidgetDisable(float value);
    OutputS1M3& setAudioSourceDefault(QString audioSourceDefault);
    OutputS1M3& setAudioSourceColor(QColor color);
    OutputS1M3& setChannelNameEditable(bool state=true);
    OutputS1M3& setValueGAIN(float value);
    OutputS1M3& setValueMUTE(bool state=true);
    OutputS1M3& setOverlay(bool state=true);
    OutputS1M3& addAudioSource(QString audioClass, QVector<QString>& audioSourceList);
    QString getAudioSource();
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OutputS1M3* ui;
    QTimer mTimer;
    QFont mFont;
    QString mAudioSourceDefault="";
    QString mPreAudioSource="";
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(float value);
    void in_widgetToolButton_actionChanged(QString actionName);
    void in_widgetPushButtonGroup1_stateChanged(QString button, QString state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // OUTPUTS1M3_H


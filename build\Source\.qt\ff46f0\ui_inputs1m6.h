/********************************************************************************
** Form generated from reading UI file 'inputs1m6.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_INPUTS1M6_H
#define UI_INPUTS1M6_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <dials1m6.h>
#include <pushbuttongroups1m3.h>
#include <volumemeters1m2.h>

QT_BEGIN_NAMESPACE

class Ui_InputS1M6
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    VolumeMeterS1M2 *widgetVolumeMeter;
    DialS1M6 *widgetDial;
    PushButtonGroupS1M3 *widgetPushButtonGroup2;
    QLineEdit *lineEdit;
    QWidget *widgetOverlay;

    void setupUi(QWidget *InputS1M6)
    {
        if (InputS1M6->objectName().isEmpty())
            InputS1M6->setObjectName("InputS1M6");
        InputS1M6->resize(140, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(InputS1M6->sizePolicy().hasHeightForWidth());
        InputS1M6->setSizePolicy(sizePolicy);
        InputS1M6->setMinimumSize(QSize(50, 220));
        gridLayout = new QGridLayout(InputS1M6);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(InputS1M6);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 21));
        widgetVolumeMeter = new VolumeMeterS1M2(frame);
        widgetVolumeMeter->setObjectName("widgetVolumeMeter");
        widgetVolumeMeter->setGeometry(QRect(10, 40, 91, 131));
        sizePolicy.setHeightForWidth(widgetVolumeMeter->sizePolicy().hasHeightForWidth());
        widgetVolumeMeter->setSizePolicy(sizePolicy);
        widgetDial = new DialS1M6(frame);
        widgetDial->setObjectName("widgetDial");
        widgetDial->setGeometry(QRect(10, 180, 91, 41));
        sizePolicy.setHeightForWidth(widgetDial->sizePolicy().hasHeightForWidth());
        widgetDial->setSizePolicy(sizePolicy);
        widgetDial->setMinimumSize(QSize(0, 0));
        widgetPushButtonGroup2 = new PushButtonGroupS1M3(frame);
        widgetPushButtonGroup2->setObjectName("widgetPushButtonGroup2");
        widgetPushButtonGroup2->setGeometry(QRect(10, 230, 91, 41));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup2->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup2->setSizePolicy(sizePolicy);
        widgetPushButtonGroup2->setMinimumSize(QSize(6, 7));
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(10, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetOverlay = new QWidget(frame);
        widgetOverlay->setObjectName("widgetOverlay");
        widgetOverlay->setGeometry(QRect(110, 40, 21, 21));

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(InputS1M6);

        QMetaObject::connectSlotsByName(InputS1M6);
    } // setupUi

    void retranslateUi(QWidget *InputS1M6)
    {
        InputS1M6->setWindowTitle(QCoreApplication::translate("InputS1M6", "Form", nullptr));
        pushButtonClose->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class InputS1M6: public Ui_InputS1M6 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_INPUTS1M6_H

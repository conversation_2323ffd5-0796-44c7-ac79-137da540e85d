#ifndef INPUTS1M6_H
#define INPUTS1M6_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "inputbase.h"
#include "workspace.h"
#include "appsettings.h"


namespace Ui {
class InputS1M6;
}


class InputS1M6 : public InputBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit InputS1M6(QWidget* parent=nullptr, QString name="");
    ~InputS1M6();
    InputS1M6& setName(QString name);
    InputS1M6& setFont(QFont font);
    InputS1M6& setVolumeMeterLeft(int value);
    InputS1M6& setVolumeMeterLeftClear();
    InputS1M6& setVolumeMeterLeftSlip();
    InputS1M6& setVolumeMeterRight(int value);
    InputS1M6& setVolumeMeterRightClear();
    InputS1M6& setVolumeMeterRightSlip();
    InputS1M6& setGain(float value);
    InputS1M6& setGainLock(bool state=true);
    InputS1M6& setMuteAffectGain(bool state=true);
    InputS1M6& setGainAffectMute(bool state=true);
    InputS1M6& setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    InputS1M6& setGainDefault(float value);
    InputS1M6& setGainWidgetDisable(float value);
    InputS1M6& setChannelNameEditable(bool state=true);
    InputS1M6& setValueGAIN(float value);
    InputS1M6& setValueMUTE(bool state=true);
    InputS1M6& setOverlay(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void setSoloState(bool state) override;
    void setSoloStateLeft(bool state) override;
    void setSoloStateRight(bool state) override;
    void setMuteState(bool state) override;
    void setMuteStateLeft(bool state) override;
    void setMuteStateRight(bool state) override;
    void setSoloClicked(bool state) override;
    void setSoloClickedLeft(bool state) override;
    void setSoloClickedRight(bool state) override;
    void setMuteClicked(bool state) override;
    void setMuteClickedLeft(bool state) override;
    void setMuteClickedRight(bool state) override;
    bool getSoloState() override;
    bool getSoloStateLeft() override;
    bool getSoloStateRight() override;
    bool getMuteState() override;
    bool getMuteStateLeft() override;
    bool getMuteStateRight() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::InputS1M6* ui;
    QTimer mTimer;
    QFont mFont;
    int mPreMUTE=-2147483648;
    int mPreANTI=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetDial_valueChanged(float value);
    void in_widgetPushButtonGroup2_stateChanged(QString button, QString state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // INPUTS1M6_H


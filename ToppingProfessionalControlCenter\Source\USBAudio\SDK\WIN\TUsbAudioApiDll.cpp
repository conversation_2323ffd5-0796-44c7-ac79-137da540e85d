/************************************************************************
 *  Long name:    TUsbAudioApi.dll wrapper
 *  Description:  Implements an interface to load and unload the
 *                TUsbAudioApi.dll and to access its API.
 *  Author(s):    Ren?M鰈ler
 *  Company:      Thesycon GmbH, Ilmenau
 ************************************************************************/

#ifdef TUSBAUDIOAPI_USES_LIBTL

// include our OS library which pulls in windows.h
#include "libtlwin.h"

// open our namespaces
using namespace thesycon;

// disable WNTRACE macro
#define WNTRACE(bitnb,x)

#else

// Exclude rarely-used stuff from Windows headers
#ifndef VC_EXTRALEAN
#define VC_EXTRALEAN
#endif
// libwn_min, pulls in windows.h
#include "libwn.h"

// disable TLOG_MSG macro
#define TLOG_MSG(lvl, ...)

#endif


#include "TUsbAudioApiDll.h"




TUsbAudioApiDll::TUsbAudioApiDll()
{
    InitFunctionPointers();
}


TUsbAudioApiDll::~TUsbAudioApiDll()
{
    Unload();
}


bool
TUsbAudioApiDll::IsApiVersionEqualOrGreater(
    unsigned int major,
    unsigned int minor
    ) const
{
    if (ApiMajorVersion() > major) {
        return true;
    }
    if (ApiMajorVersion() == major) {
        if (ApiMinorVersion() >= minor) {
            return true;
        }
    }
    return false;
}



// helper
template <typename FP>
inline
DWORD
GetFunctionPointer(FP& fptr, HMODULE h, const char* fctName)
{
    fptr = (FP)::GetProcAddress(h, fctName);
    if ( nullptr == fptr ) {
        DWORD status = GetLastError();
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetProcAddress(%S) failed with 0x%08X.\n"), fctName, status));
        TLOG_MSG(TLERROR, TLFUNC, "GetProcAddress({}) failed with {:#x}.", AsStr(fctName), status);
        return status;
    }
    return ERROR_SUCCESS;
}

// helper macro
#define GET_PROC_ADDRESS( dllFctName ) \
    status = GetFunctionPointer(mFunctionPointers.m##dllFctName, mDllHandle, #dllFctName); \
    if ( ERROR_SUCCESS != status ) { \
        Unload(); \
        return status; \
    }

DWORD
TUsbAudioApiDll::LoadByPathAndName(
    const TCHAR* pathAndName,
    bool checkApiCompatibility,
    unsigned int expectedMajorApiVersion,
    unsigned int expectedMinorApiVersion
    )
{
    DWORD status;

    //error check
    if ( mDllHandle != nullptr ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": DLL is already loaded.\n")));
        TLOG_MSG(TLERROR, TLFUNC, "DLL is already loaded.");
        return ERROR_INVALID_FUNCTION;
    }

    //check whether the DLL exists, this allows us to return a more specific error code
    if ( ::GetFileAttributes( pathAndName ) == INVALID_FILE_ATTRIBUTES ) {
        status = ::GetLastError();
        if (status == ERROR_FILE_NOT_FOUND || status == ERROR_PATH_NOT_FOUND) {
            WNTRACE(TRCERR, tprint(_T(__FUNCTION__) _T(": DLL '%s' doesn't exist.\n"), pathAndName) );
            TLOG_MSG(TLERROR, TLFUNC, "DLL '{}' doesn't exist.", AsStr(pathAndName));
            return ERROR_FILE_NOT_FOUND;
        } else {
            WNTRACE(TRCERR, tprint(_T(__FUNCTION__) _T(": GetFileAttributes(%s) failed with 0x%08X\n"), pathAndName, status) );
            TLOG_MSG(TLERROR, TLFUNC, "GetFileAttributes({}) failed with {:#x}.", AsStr(pathAndName), status);
            //We cannot determine whether it exists. We continue and try to load it.
        }
    }

    //load the DLL
    mDllHandle = ::LoadLibrary( pathAndName );
    if ( mDllHandle == nullptr ) {
        status = ::GetLastError();
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": LoadLibrary(%s) failed with 0x%08X.\n"), pathAndName, status));
        TLOG_MSG(TLERROR, TLFUNC, "LoadLibrary({}) failed with {:#x}.", AsStr(pathAndName), status);
        return status;
    }

    //get the pointer to the DLL functions related to the API version
    GET_PROC_ADDRESS( TUSBAUDIO_GetApiVersion );
    GET_PROC_ADDRESS( TUSBAUDIO_CheckApiVersion );

    mDllVersion = TUSBAUDIO_GetApiVersion();

    if ( checkApiCompatibility ) {
        //Is the API version of the loaded DLL supported?
        if ( TUSBAUDIO_CheckApiVersion(expectedMajorApiVersion, expectedMinorApiVersion) == 0 ) {
            //NO
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": DLL API version is %d.%d. Expected version is %d.x (x>=%d).\n"),
                                      ApiMajorVersion(), ApiMinorVersion(), expectedMajorApiVersion, expectedMinorApiVersion ));
            TLOG_MSG(TLERROR, TLFUNC, "DLL API version is {}.{}. Expected version is {}.x (x>={}).",
                                      ApiMajorVersion(), ApiMinorVersion(), expectedMajorApiVersion, expectedMinorApiVersion );
            return TSTATUS_VERSION_MISMATCH;
        }
    }

    WNTRACE(TRCINF,tprint(_T(__FUNCTION__) _T(": DLL API version is %d.%d. Expected version is %d.x (x>=%d).\n"),
                                ApiMajorVersion(), ApiMinorVersion(), expectedMajorApiVersion, expectedMinorApiVersion ));
    TLOG_MSG(TLINFO, TLFUNC, "DLL API version is {}.{}. Expected version is {}.x (x>={}).",
                                ApiMajorVersion(), ApiMinorVersion(), expectedMajorApiVersion, expectedMinorApiVersion );

    // get functions which are available in API version 5.0 and higher
    if ( IsApiVersionEqualOrGreater(5, 0) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_RegisterPnpNotification );
        GET_PROC_ADDRESS( TUSBAUDIO_UnregisterPnpNotification );
        GET_PROC_ADDRESS( TUSBAUDIO_EnumerateDevices );
        GET_PROC_ADDRESS( TUSBAUDIO_GetDeviceCount );
        GET_PROC_ADDRESS( TUSBAUDIO_OpenDeviceByIndex );
        GET_PROC_ADDRESS( TUSBAUDIO_OpenDeviceByChannelIdString );
        GET_PROC_ADDRESS( TUSBAUDIO_CloseDevice );
        GET_PROC_ADDRESS( TUSBAUDIO_GetDeviceProperties );
        GET_PROC_ADDRESS( TUSBAUDIO_GetDeviceInstanceIdString );
        GET_PROC_ADDRESS( TUSBAUDIO_GetDeviceContainerIdString );
        GET_PROC_ADDRESS( TUSBAUDIO_GetChannelIndexForChannelIdString );
        GET_PROC_ADDRESS( TUSBAUDIO_GetChannelIdString );
        GET_PROC_ADDRESS( TUSBAUDIO_RegisterDeviceNotification );
        GET_PROC_ADDRESS( TUSBAUDIO_ReadDeviceNotification );
        GET_PROC_ADDRESS( TUSBAUDIO_GetSupportedSampleRates );
        GET_PROC_ADDRESS( TUSBAUDIO_GetCurrentSampleRate );
        GET_PROC_ADDRESS( TUSBAUDIO_SetSampleRate );
        GET_PROC_ADDRESS( TUSBAUDIO_GetSupportedClockSources );
        GET_PROC_ADDRESS( TUSBAUDIO_GetClockSourceStatus );
        GET_PROC_ADDRESS( TUSBAUDIO_GetCurrentClockSource );
        GET_PROC_ADDRESS( TUSBAUDIO_SetCurrentClockSource );
        GET_PROC_ADDRESS( TUSBAUDIO_GetSupportedStreamFormats );
        GET_PROC_ADDRESS( TUSBAUDIO_GetCurrentStreamFormat );
        GET_PROC_ADDRESS( TUSBAUDIO_SetCurrentStreamFormat );
        GET_PROC_ADDRESS( TUSBAUDIO_GetStreamFormatSelectionMode );
        GET_PROC_ADDRESS( TUSBAUDIO_GetChannelProperties );
        GET_PROC_ADDRESS( TUSBAUDIO_GetChannelInfo );
        GET_PROC_ADDRESS( TUSBAUDIO_GetVolumeMuteInfo );
        GET_PROC_ADDRESS( TUSBAUDIO_GetVolume );
        GET_PROC_ADDRESS( TUSBAUDIO_SetVolume );
        GET_PROC_ADDRESS( TUSBAUDIO_GetMute );
        GET_PROC_ADDRESS( TUSBAUDIO_SetMute );
        GET_PROC_ADDRESS( TUSBAUDIO_GetUsbConfigDescriptor );
        GET_PROC_ADDRESS( TUSBAUDIO_GetUsbStringDescriptorString );
        GET_PROC_ADDRESS( TUSBAUDIO_AudioControlRequestSet );
        GET_PROC_ADDRESS( TUSBAUDIO_AudioControlRequestGet );
        GET_PROC_ADDRESS( TUSBAUDIO_ClassVendorRequestOut );
        GET_PROC_ADDRESS( TUSBAUDIO_ClassVendorRequestIn );
        GET_PROC_ADDRESS( TUSBAUDIO_GetDriverInfo );
        GET_PROC_ADDRESS( TUSBAUDIO_GetASIOInstanceInfo );
        GET_PROC_ADDRESS( TUSBAUDIO_SetASIOBufferPreferredSize );
        GET_PROC_ADDRESS( TUSBAUDIO_GetDeviceUsbMode );
        GET_PROC_ADDRESS( TUSBAUDIO_GetClientInfo );
        GET_PROC_ADDRESS( TUSBAUDIO_GetDeviceStreamingMode );
        GET_PROC_ADDRESS( TUSBAUDIO_SetDeviceStreamingMode );
        GET_PROC_ADDRESS( TUSBAUDIO_LoadFirmwareImageFromBuffer );
        GET_PROC_ADDRESS( TUSBAUDIO_LoadFirmwareImageFromFile );
        GET_PROC_ADDRESS( TUSBAUDIO_GetFirmwareImageSize );
        GET_PROC_ADDRESS( TUSBAUDIO_UnloadFirmwareImage );
        GET_PROC_ADDRESS( TUSBAUDIO_GetFirmwareImage );
        GET_PROC_ADDRESS( TUSBAUDIO_StartDfuDownload );
        GET_PROC_ADDRESS( TUSBAUDIO_StartDfuUpload );
        GET_PROC_ADDRESS( TUSBAUDIO_StartDfuRevertToFactoryImage );
        GET_PROC_ADDRESS( TUSBAUDIO_GetDfuStatus );
        GET_PROC_ADDRESS( TUSBAUDIO_EndDfuProc );
        GET_PROC_ADDRESS( TUSBAUDIO_GetDspProperty );
        GET_PROC_ADDRESS( TUSBAUDIO_SetDspProperty );
        GET_PROC_ADDRESS( TUSBAUDIO_QueryDriverStatistics );
        GET_PROC_ADDRESS( TUSBAUDIO_ResetDriverStatistics );
        GET_PROC_ADDRESS( TUSBAUDIO_StatusCodeStringW );
        GET_PROC_ADDRESS( TUSBAUDIO_StatusCodeStringA );
    }
    // get functions which are available in API version 5.1 and higher
    if ( IsApiVersionEqualOrGreater(5, 1) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_SetPreferredASIODevice );
        GET_PROC_ADDRESS( TUSBAUDIO_ClearPreferredASIODevice );
    }
    // get functions which are available in API version 5.2 and higher
    if ( IsApiVersionEqualOrGreater(5, 2) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_QueryDeviceStatistics );
    }
    // get functions which are available in API version 5.4 and higher
    if ( IsApiVersionEqualOrGreater(5, 4) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_GetControlPanelOptions );
    }
    // get functions which are available in API version 5.5 and higher
    if ( IsApiVersionEqualOrGreater(5, 5) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_GetExtendedInfo );
    }
    // get functions which are available in API version 5.6 and higher
    if ( IsApiVersionEqualOrGreater(5, 6) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_SetSoundDeviceProfile );
    }
    // get functions which are available in API version 5.7 and higher
    if ( IsApiVersionEqualOrGreater(5, 7) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_SetHwChannelHiddenFlags );
    }
    // get functions which are available in API version 5.8 and higher
    if ( IsApiVersionEqualOrGreater(5, 8) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_GetCustomString );
    }
    // get functions which are available in API version 5.9 and higher
    if ( IsApiVersionEqualOrGreater(5, 9) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_GetASIOInstanceCount );
        GET_PROC_ADDRESS( TUSBAUDIO_GetASIOInstanceDetails );
        GET_PROC_ADDRESS( TUSBAUDIO_GetASIORelation );
        GET_PROC_ADDRESS( TUSBAUDIO_GetASIOInstanceAllowed );
    }
    // get functions which are available in API version 5.10 and higher
    if ( IsApiVersionEqualOrGreater(5, 10) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_GetStreamChannelCount );
        GET_PROC_ADDRESS( TUSBAUDIO_GetStreamChannelIds );
        GET_PROC_ADDRESS( TUSBAUDIO_EnablePeakMeters );
        GET_PROC_ADDRESS( TUSBAUDIO_DisablePeakMeters );
        GET_PROC_ADDRESS( TUSBAUDIO_GetPeakMeters );
    }
    // get functions which are available in API version 5.12 and higher
    if ( IsApiVersionEqualOrGreater(5, 12) ) {
        GET_PROC_ADDRESS( TUSBAUDIO_SetVirtualSoundDeviceProfile );
    }

    //OK
    return ERROR_SUCCESS;
}


void
TUsbAudioApiDll::Unload()
{
    // unload the DLL if necessary
    if ( mDllHandle != nullptr ) {
        ::FreeLibrary( mDllHandle );
        mDllHandle = nullptr;
    }

    mDllVersion = 0;

    // reset all function pointers
    InitFunctionPointers();
}


void
TUsbAudioApiDll::InitFunctionPointers()
{
    memset((void*)&mFunctionPointers, 0, sizeof(mFunctionPointers));
}




//registry keys and value names used by the driver API DLL
#define REGKEY_CLSID                                        _T("CLSID")
#define REGKEY_INPROCSERVER32                               _T("InprocServer32")


DWORD
TUsbAudioApiDll::LoadByName(
    const TCHAR* dllName,
    bool checkApiCompatibility,
    unsigned int expectedMajorApiVersion,
    unsigned int expectedMinorApiVersion
    )
{
#ifdef TUSBAUDIOAPI_USES_LIBTL
    TLSTATUS st;

    // get the path of this application's .exe
    TLWinModuleFileName modFileName;
    st = modFileName.InitWithModuleHandle();
    if ( !SUCC(st) ) {
        TLOG_MSG(TLERROR, TLFUNC, "InitWithModuleHandle failed, st={}", AsStatus(st));
        return st;
    }

    // create path and name of the DLL
    WCHAR dllPathAndName[_MAX_PATH] = { 0 };
    TLStringNCatToArray(dllPathAndName, modFileName.Drive());  // e.g. "C:"
    TLStringNCatToArray(dllPathAndName, modFileName.Directory());    // e.g. "\Program Files\TUSBAudio\"
    TLStringNCatToArray(dllPathAndName, dllName);     // e.g. tusbaudioapi.dll

    // load the DLL
    st = LoadByPathAndName(dllPathAndName, checkApiCompatibility, expectedMajorApiVersion, expectedMinorApiVersion);
    if ( !SUCC(st) ) {
        TLOG_MSG(TLERROR, TLFUNC, "LoadByPathAndName failed, st={}", AsStatus(st));
        return st;
    }

    return ERROR_SUCCESS;

#else
    WNERR status;

    // get the path of this application's .exe
    WnModuleFileName modFileName;
    status = modFileName.InitWithModuleHandle();
    if (status != ERROR_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": InitWithModuleHandle failed with 0x%08X.\n"), status));
        return status;
    }

    // create path and name of the DLL
    TCHAR dllPathAndName[_MAX_PATH] = { 0 };
    TbStringNCatToArray(dllPathAndName, modFileName.GetDrive());  // e.g. "C:"
    TbStringNCatToArray(dllPathAndName, modFileName.GetDir());    // e.g. "\Program Files\TUSBAudio\"
    TbStringNCatToArray(dllPathAndName, dllName);     // e.g. tusbaudioapi.dll

    // load the DLL
    status = LoadByPathAndName(dllPathAndName, checkApiCompatibility, expectedMajorApiVersion, expectedMinorApiVersion);
    if ( status != ERROR_SUCCESS ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": Load(%s) failed with 0x%08X.\n"), dllPathAndName, status));
        return status;
    }

    return ERROR_SUCCESS;
#endif
}


DWORD
TUsbAudioApiDll::LoadByGUID(
    const TCHAR* guidString,
    bool checkApiCompatibility,
    unsigned int expectedMajorApiVersion,
    unsigned int expectedMinorApiVersion
    )
{
#ifdef TUSBAUDIOAPI_USES_LIBTL
    TLSTATUS st;

    // open the CLSID key
    TLWinRegistryKey clsidKey;
    st = clsidKey.OpenKey(HKEY_CLASSES_ROOT, REGKEY_CLSID, KEY_READ);
    if ( !SUCC(st) ) {
        TLOG_MSG(TLERROR, TLFUNC, "OpenKey({}) failed, st={}", AsStr(REGKEY_CLSID), AsStatus(st));
        return st;
    }

    // open GUID subkey which is the key where the DLL registration information is stored
    TLWinRegistryKey dllKey;
    st = dllKey.OpenKey(clsidKey, guidString, KEY_READ );
    if ( !SUCC(st) ) {
        TLOG_MSG(TLERROR, TLFUNC, "OpenKey({}) failed, st={}", AsStr(guidString), AsStatus(st));
        return st;
    }

    // open the InProcServer32 subkey
    TLWinRegistryKey ipsKey;
    st = ipsKey.OpenKey(dllKey, REGKEY_INPROCSERVER32, KEY_READ);
    if ( !SUCC(st) ) {
        TLOG_MSG(TLERROR, TLFUNC, "OpenKey({}) failed, st={}", AsStr(REGKEY_INPROCSERVER32), AsStatus(st));
        return st;
    }

    // get the default value of this key that should contain the path and name of the DLL
    std::wstring dllPathAndName;
    st = ipsKey.QueryString(nullptr, dllPathAndName);
    if ( !SUCC(st) ) {
        TLOG_MSG(TLERROR, TLFUNC, "QueryString failed, st={}", AsStatus(st));
        return st;
    }

    // load the DLL
    st = LoadByPathAndName(dllPathAndName.c_str(), checkApiCompatibility, expectedMajorApiVersion, expectedMinorApiVersion);
    if ( !SUCC(st) ) {
        TLOG_MSG(TLERROR, TLFUNC, "LoadByPathAndName failed, st={}", AsStatus(st));
        return st;
    }

    return ERROR_SUCCESS;

#else
    WNERR status;

    // open the CLSID key
    WnRegistryKey clsidKey;
    status = clsidKey.OpenKey(HKEY_CLASSES_ROOT, REGKEY_CLSID, KEY_READ);
    if ( status != ERROR_SUCCESS ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": OpenKey(%s) failed with 0x%08X.\n"), REGKEY_CLSID, status));
        return status;
    }

    // open GUID subkey which is the key where the DLL registration information is stored
    WnRegistryKey dllKey;
    status = dllKey.OpenKey(clsidKey, guidString, KEY_READ );
    if ( status != ERROR_SUCCESS ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": OpenKey(%s) failed with 0x%08X.\n"), guidString, status));
        return status;
    }

    // open the InProcServer32 subkey
    WnRegistryKey ipsKey;
    status = ipsKey.OpenKey(dllKey, REGKEY_INPROCSERVER32, KEY_READ);
    if ( status != ERROR_SUCCESS ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": OpenKey(%s) failed with 0x%08X.\n"), REGKEY_INPROCSERVER32, status));
        return status;
    }

    // get the default value of this key that should contain the path and name of the DLL
    TCHAR dllPathAndName[_MAX_PATH] = { 0 };
    status = ipsKey.QueryString(nullptr, dllPathAndName, _countof(dllPathAndName));
    if ( status != ERROR_SUCCESS ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": QueryString failed with 0x%08X.\n"), status));
        return status;
    }

    // load the DLL
    status = LoadByPathAndName(dllPathAndName, checkApiCompatibility, expectedMajorApiVersion, expectedMinorApiVersion);
    if ( status != ERROR_SUCCESS ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": Load(%s) failed with 0x%08X.\n"), dllPathAndName, status));
        return status;
    }

    return ERROR_SUCCESS;
#endif
}



/********************************************************************************
** Form generated from reading UI file 'origins1m4.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ORIGINS1M4_H
#define UI_ORIGINS1M4_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <dials1m1.h>

QT_BEGIN_NAMESPACE

class Ui_OriginS1M4
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QLabel *labelNC;
    QWidget *widget;
    DialS1M1 *DialReverb;
    QLabel *labelReverb;
    QLabel *labelDialLeft;
    QLabel *labelDialRight;
    QPushButton *buttonNCOFF;
    QPushButton *buttonNC1;
    QPushButton *buttonNC2;
    QPushButton *buttonReverbOFF;
    QPushButton *buttonLive;
    QPushButton *buttonHALL;
    QPushButton *buttonSTU;
    QPushButton *buttonNC3;

    void setupUi(QWidget *OriginS1M4)
    {
        if (OriginS1M4->objectName().isEmpty())
            OriginS1M4->setObjectName("OriginS1M4");
        OriginS1M4->resize(168, 270);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(OriginS1M4->sizePolicy().hasHeightForWidth());
        OriginS1M4->setSizePolicy(sizePolicy);
        OriginS1M4->setMinimumSize(QSize(80, 220));
        gridLayout = new QGridLayout(OriginS1M4);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(OriginS1M4);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        labelNC = new QLabel(frame);
        labelNC->setObjectName("labelNC");
        labelNC->setGeometry(QRect(80, 10, 18, 16));
        labelNC->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widget = new QWidget(frame);
        widget->setObjectName("widget");
        widget->setGeometry(QRect(10, 49, 141, 201));
        DialReverb = new DialS1M1(widget);
        DialReverb->setObjectName("DialReverb");
        DialReverb->setGeometry(QRect(50, 120, 41, 16));
        sizePolicy.setHeightForWidth(DialReverb->sizePolicy().hasHeightForWidth());
        DialReverb->setSizePolicy(sizePolicy);
        DialReverb->setMinimumSize(QSize(0, 0));
        labelReverb = new QLabel(widget);
        labelReverb->setObjectName("labelReverb");
        labelReverb->setGeometry(QRect(40, 90, 41, 16));
        labelReverb->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelDialLeft = new QLabel(widget);
        labelDialLeft->setObjectName("labelDialLeft");
        labelDialLeft->setGeometry(QRect(10, 130, 20, 16));
        labelDialLeft->setAlignment(Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter);
        labelDialRight = new QLabel(widget);
        labelDialRight->setObjectName("labelDialRight");
        labelDialRight->setGeometry(QRect(100, 140, 23, 16));
        labelDialRight->setAlignment(Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter);
        buttonNCOFF = new QPushButton(widget);
        buttonNCOFF->setObjectName("buttonNCOFF");
        buttonNCOFF->setGeometry(QRect(40, 10, 75, 23));
        buttonNCOFF->setCheckable(true);
        buttonNC1 = new QPushButton(widget);
        buttonNC1->setObjectName("buttonNC1");
        buttonNC1->setGeometry(QRect(30, 30, 75, 23));
        buttonNC1->setCheckable(true);
        buttonNC2 = new QPushButton(widget);
        buttonNC2->setObjectName("buttonNC2");
        buttonNC2->setGeometry(QRect(30, 50, 75, 23));
        buttonNC2->setCheckable(true);
        buttonReverbOFF = new QPushButton(widget);
        buttonReverbOFF->setObjectName("buttonReverbOFF");
        buttonReverbOFF->setGeometry(QRect(10, 160, 41, 16));
        buttonReverbOFF->setCheckable(true);
        buttonLive = new QPushButton(widget);
        buttonLive->setObjectName("buttonLive");
        buttonLive->setGeometry(QRect(70, 160, 41, 16));
        buttonLive->setCheckable(true);
        buttonHALL = new QPushButton(widget);
        buttonHALL->setObjectName("buttonHALL");
        buttonHALL->setGeometry(QRect(70, 180, 41, 16));
        buttonHALL->setCheckable(true);
        buttonSTU = new QPushButton(widget);
        buttonSTU->setObjectName("buttonSTU");
        buttonSTU->setGeometry(QRect(10, 180, 41, 16));
        buttonSTU->setCheckable(true);
        buttonNC3 = new QPushButton(widget);
        buttonNC3->setObjectName("buttonNC3");
        buttonNC3->setGeometry(QRect(30, 70, 75, 23));
        buttonNC3->setCheckable(true);

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(OriginS1M4);

        QMetaObject::connectSlotsByName(OriginS1M4);
    } // setupUi

    void retranslateUi(QWidget *OriginS1M4)
    {
        OriginS1M4->setWindowTitle(QCoreApplication::translate("OriginS1M4", "Form", nullptr));
        labelNC->setText(QCoreApplication::translate("OriginS1M4", "NC", nullptr));
        labelReverb->setText(QCoreApplication::translate("OriginS1M4", "Reverb", nullptr));
        labelDialLeft->setText(QCoreApplication::translate("OriginS1M4", "Dry", nullptr));
        labelDialRight->setText(QCoreApplication::translate("OriginS1M4", "Wet", nullptr));
        buttonNCOFF->setText(QCoreApplication::translate("OriginS1M4", "OFF", nullptr));
        buttonNC1->setText(QCoreApplication::translate("OriginS1M4", "NC1", nullptr));
        buttonNC2->setText(QCoreApplication::translate("OriginS1M4", "NC2", nullptr));
        buttonReverbOFF->setText(QCoreApplication::translate("OriginS1M4", "OFF", nullptr));
        buttonLive->setText(QCoreApplication::translate("OriginS1M4", "LIVE", nullptr));
        buttonHALL->setText(QCoreApplication::translate("OriginS1M4", "HALL", nullptr));
        buttonSTU->setText(QCoreApplication::translate("OriginS1M4", "STU", nullptr));
        buttonNC3->setText(QCoreApplication::translate("OriginS1M4", "NC3", nullptr));
    } // retranslateUi

};

namespace Ui {
    class OriginS1M4: public Ui_OriginS1M4 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ORIGINS1M4_H

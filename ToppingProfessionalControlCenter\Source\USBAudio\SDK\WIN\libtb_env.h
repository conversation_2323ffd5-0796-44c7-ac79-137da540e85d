/************************************************************************
 *
 *  Module:       libtb_env.h
 *  Description:
 *  libtb adaptations specific to the individual environment
 *
 *  Runtime Env.: any
 *  Author(s):    <PERSON><PERSON>, <PERSON>
 *  Company:      Thesycon GmbH, Ilmenau
 ************************************************************************/

#ifndef __libtb_env_h__
#define __libtb_env_h__

// for arg_list, va_arg
#include <stdarg.h>
#include <string.h>


#if defined(TBASE_COMPILER_MICROSOFT)
// Microsoft compiler, Windows

// for size_t, ptrdiff_t, ...
#include <stddef.h>
// for memcpy, memmove, ...
#include <memory.h>

#define LIBTB_NEW_DELETE_DECL __cdecl

#if defined(TBASE_COMPILER_MICROSOFT_KERNEL_MODE)
// Windows kernel mode
#ifndef LIBTB_USE_DEFAULT_MEMTYPE
#define LIBTB_USE_DEFAULT_MEMTYPE 0
#endif

#elif defined(TBASE_COMPILER_MICROSOFT_USER_MODE)
// Windows user mode
#ifndef LIBTB_USE_DEFAULT_MEMTYPE
#define LIBTB_USE_DEFAULT_MEMTYPE 1
#endif
// stdlib is available
#ifndef LIBTB_STDLIB_SUPPORTED
#define LIBTB_STDLIB_SUPPORTED  1
#endif
// for new, delete
#include <memory>

#else
#error One of TBASE_COMPILER_MICROSOFT_xxx must be defined.
#endif


#elif defined(TBASE_COMPILER_APPLE)
// Apple compiler, MacOS X
#define LIBTB_NEW_DELETE_DECL
#define LIBTB_USE_DEFAULT_MEMTYPE 1
// stdlib is available
#ifndef LIBTB_STDLIB_SUPPORTED
#define LIBTB_STDLIB_SUPPORTED  1
#endif
#else

// default, e.g. Linux
#define LIBTB_NEW_DELETE_DECL
#define LIBTB_USE_DEFAULT_MEMTYPE 1
// stdlib is available
#ifndef LIBTB_STDLIB_SUPPORTED
#define LIBTB_STDLIB_SUPPORTED  1
#endif

#endif



#endif // __libtb_env_h__

/******************************** EOF ***********************************/

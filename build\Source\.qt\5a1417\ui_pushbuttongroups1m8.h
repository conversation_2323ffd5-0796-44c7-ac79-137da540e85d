/********************************************************************************
** Form generated from reading UI file 'pushbuttongroups1m8.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PUSHBUTTONGROUPS1M8_H
#define UI_PUSHBUTTONGROUPS1M8_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_PushButtonGroupS1M8
{
public:
    QGridLayout *gridLayout_2;
    QFrame *frame;
    QGridLayout *gridLayout;
    QSpacerItem *verticalSpacer;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *PushButtonMUTE;
    QSpacerItem *horizontalSpacer;
    QSpacerItem *verticalSpacer_4;

    void setupUi(QWidget *PushButtonGroupS1M8)
    {
        if (PushButtonGroupS1M8->objectName().isEmpty())
            PushButtonGroupS1M8->setObjectName("PushButtonGroupS1M8");
        PushButtonGroupS1M8->resize(80, 70);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(PushButtonGroupS1M8->sizePolicy().hasHeightForWidth());
        PushButtonGroupS1M8->setSizePolicy(sizePolicy);
        PushButtonGroupS1M8->setMinimumSize(QSize(8, 7));
        gridLayout_2 = new QGridLayout(PushButtonGroupS1M8);
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName("gridLayout_2");
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(PushButtonGroupS1M8);
        frame->setObjectName("frame");
        sizePolicy.setHeightForWidth(frame->sizePolicy().hasHeightForWidth());
        frame->setSizePolicy(sizePolicy);
        frame->setFrameShape(QFrame::Shape::NoFrame);
        frame->setFrameShadow(QFrame::Shadow::Plain);
        frame->setLineWidth(0);
        gridLayout = new QGridLayout(frame);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer, 0, 1, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer_2, 1, 0, 1, 1);

        PushButtonMUTE = new QPushButton(frame);
        PushButtonMUTE->setObjectName("PushButtonMUTE");
        sizePolicy.setHeightForWidth(PushButtonMUTE->sizePolicy().hasHeightForWidth());
        PushButtonMUTE->setSizePolicy(sizePolicy);
        PushButtonMUTE->setMinimumSize(QSize(1, 1));

        gridLayout->addWidget(PushButtonMUTE, 1, 1, 1, 1);

        horizontalSpacer = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer, 1, 2, 1, 1);

        verticalSpacer_4 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_4, 2, 1, 1, 1);

        gridLayout->setRowStretch(0, 11);
        gridLayout->setRowStretch(1, 14);
        gridLayout->setRowStretch(2, 10);
        gridLayout->setColumnStretch(0, 7);
        gridLayout->setColumnStretch(1, 26);
        gridLayout->setColumnStretch(2, 7);

        gridLayout_2->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(PushButtonGroupS1M8);

        QMetaObject::connectSlotsByName(PushButtonGroupS1M8);
    } // setupUi

    void retranslateUi(QWidget *PushButtonGroupS1M8)
    {
        PushButtonGroupS1M8->setWindowTitle(QCoreApplication::translate("PushButtonGroupS1M8", "Form", nullptr));
        PushButtonMUTE->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class PushButtonGroupS1M8: public Ui_PushButtonGroupS1M8 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PUSHBUTTONGROUPS1M8_H

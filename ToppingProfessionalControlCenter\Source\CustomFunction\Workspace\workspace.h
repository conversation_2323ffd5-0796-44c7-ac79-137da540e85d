#ifndef WORKSPACE_H
#define WORKSPACE_H


#include <QString>
#include <QVector>
#include <QVariant>
#include <QSettings>
#include <QAnyStringView>


class WorkspaceObserver
{
public:
    WorkspaceObserver(const QString& name) : mObserverName(name) { }
    virtual ~WorkspaceObserver() = default;
    void setObserverName(QString name) { mObserverName = name; }
    QString getObserverName() { return mObserverName; }
    QSettings* getSettings() { return *mSettings; }
    virtual void setValue(QAnyStringView key, const QVariant& value);
    virtual QVariant value(QAnyStringView key);
    virtual void loadSettings() = 0;
protected:
    void assignSettings(QSettings** settings) { mSettings = settings; }
private:
    QSettings** mSettings=nullptr;
    QString mObserverName;
    friend class WorkspaceSubject;
};


class WorkspaceSubject : public QObject
{
    Q_OBJECT
public:
    enum FilePreprocessing
    {
        onOrigin=0,
        onCopy
    };
    static WorkspaceSubject& instance() { return mInstance; }
    WorkspaceSubject& addObserver(WorkspaceObserver* observer);
    WorkspaceSubject& addObserverList(QVector<WorkspaceObserver*> observerList);
    WorkspaceSubject& removeObserverOne(WorkspaceObserver* observer);
    WorkspaceSubject& removeObserverAll();
    WorkspaceSubject& listObserver();
    // is
    bool isValid(QString file, QString device);
    bool isValid(QString workspaceName);
    // file
    WorkspaceSubject& modifyFile(QString file, FilePreprocessing preprocessing=onOrigin);
    WorkspaceSubject& modifyFilePreprocessing(FilePreprocessing preprocessing);
    WorkspaceSubject& saveFile();
    WorkspaceSubject& clearFile();
    WorkspaceSubject& createFile(QString file, QString device);
    WorkspaceSubject& removeFile(QString file);
    WorkspaceSubject& fileSaveAs(QString file);
    // workspace
    WorkspaceSubject& assignWorkspace(QString device, QString path, QString prefix);
    WorkspaceSubject& modifyWorkspace(QString workspaceName, FilePreprocessing preprocessing=onOrigin);
    WorkspaceSubject& modifyWorkspacePreprocessing(FilePreprocessing preprocessing);
    WorkspaceSubject& saveWorkspace();
    WorkspaceSubject& clearWorkspace();
    WorkspaceSubject& createWorkspace(QString workspaceName);
    WorkspaceSubject& removeWorkspace(QString workspaceName);
    WorkspaceSubject& workspaceSaveAs(QString workspaceName);
    QVector<QString> getWorkspaceList();
private:
    static WorkspaceSubject mInstance;
    QSettings* mSettings=nullptr;
    QVector<WorkspaceObserver*> mObservers;
    QString mPath;
    QString mPathTemp;
    QString mWorkspacePath;
    QString mWorkspacePrefix;
    QString mDevice;
    FilePreprocessing mFilePreprocessing=onOrigin;
    WorkspaceSubject() = default;
    WorkspaceSubject(const WorkspaceSubject&) = delete;
    ~WorkspaceSubject();
    WorkspaceSubject& operator=(const WorkspaceSubject&) = delete;
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#define WKSPHandle WorkspaceSubject::instance()


#endif // WORKSPACE_H


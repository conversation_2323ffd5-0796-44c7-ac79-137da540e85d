#include "eqwidget.h"
#include <qnamespace.h>
#include <QApplication>
#include <QScrollBar>
#include <QSizePolicy>
#include <QTimer>

EqWidgetIem::EqWidgetIem(int index, QWidget* parent)
    : QFrame(parent)
    , mIndex(index)
    , mLayoutSpacing(0)
    , mLayoutMargins(0, 0, 0, 0)
    , mMinimumItemWidth(50)
    , mMainLayout(nullptr)
    , mItemLabel(nullptr)
    , mTypeComboBox(nullptr)
    , mGainDial(nullptr)
    , mFrequencyDial(nullptr)
    , mQValueDial(nullptr)
    , mEnabledCheckBox(nullptr)
{
    mData.type = "high pass";
    mData.gain = 0.0f;
    mData.frequency = 1000.0f;
    mData.qValue = 0.7f;
    mData.enabled = true;

    setupUI();
    setupConnections();
    setStyleSheet(
        "EqWidgetIem {"
        "    background-color: #2a2a2a;"
        "}"
        "QLabel {"
        "    color: #404040;"
        "    font-size: 12px;"
        "}"
        "QCheckBox {"
        "    color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: transparent;"
        "}"
    );
}

EqWidgetIem::~EqWidgetIem()
{
}

void EqWidgetIem::setupUI()
{
    setMinimumWidth(mMinimumItemWidth);

    mMainLayout = new QVBoxLayout(this);
    applyLayoutSettings();

    mItemLabel = new QLabel(QString::number(mIndex + 1), this);
    mItemLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mItemLabel->setAlignment(Qt::AlignCenter);
    mMainLayout->addWidget(mItemLabel, 3);

    mTypeComboBox = new ComboBoxS1M3(nullptr);
    mTypeComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    updateTypeOptions();
    mMainLayout->addWidget(mTypeComboBox, 3);

    mGainDial = new DialS1M1(this);
    mGainDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mGainDial->setRange(-20.0f, 20.0f)
              .setValue(0.0f)
              .setStep(0.1f)
              .setPrecision(1)
              .showSign(true);
    mMainLayout->addWidget(mGainDial, 9);

    mFrequencyDial = new DialS1M1(this);
    mFrequencyDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mFrequencyDial->setRange(20.0f, 20000.0f)
                  .setValue(1000.0f)
                  .setStep(1.0f)
                  .setPrecision(0);
    mMainLayout->addWidget(mFrequencyDial, 9);

    mQValueDial = new DialS1M1(this);
    mQValueDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mQValueDial->setRange(0.1f, 10.0f)
                .setValue(0.7f)
                .setStep(0.1f)
                .setPrecision(1);
    mMainLayout->addWidget(mQValueDial, 9);

    mEnabledCheckBox = new QCheckBox(this);
    mEnabledCheckBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mEnabledCheckBox->setChecked(true);
    mMainLayout->addWidget(mEnabledCheckBox, 3, Qt::AlignCenter);
}

void EqWidgetIem::setupConnections()
{
    connect(mTypeComboBox, &ComboBoxS1M3::currentTextChanged,
            this, &EqWidgetIem::onTypeChanged);
    connect(mGainDial, &DialS1M1::valueChanged,
            this, &EqWidgetIem::onGainChanged);
    connect(mFrequencyDial, &DialS1M1::valueChanged,
            this, &EqWidgetIem::onFrequencyChanged);
    connect(mQValueDial, &DialS1M1::valueChanged,
            this, &EqWidgetIem::onQValueChanged);
    connect(mEnabledCheckBox, &QCheckBox::toggled,
            this, &EqWidgetIem::onEnabledChanged);
}

void EqWidgetIem::updateTypeOptions()
{
    QVector<QString> types = {
        "high pass",
        "Low pass",
        "High Shelf",
        "High Shelf"
    };
    
    mTypeComboBox->setCurrentText("high pass");
}

void EqWidgetIem::setItemData(const EqWidgetItemData& data)
{
    mData = data;
    mTypeComboBox->setCurrentText(data.type);
    mGainDial->setValue(data.gain);
    mFrequencyDial->setValue(data.frequency);
    mQValueDial->setValue(data.qValue);
    mEnabledCheckBox->setChecked(data.enabled);
}

EqWidgetItemData EqWidgetIem::getItemData() const
{
    return mData;
}

void EqWidgetIem::setItemIndex(int index)
{
    mIndex = index;
    mItemLabel->setText(QString::number(index + 1));
}

void EqWidgetIem::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqWidgetIem::onTypeChanged(const QString& text)
{
    mData.type = text;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onGainChanged(float value)
{
    mData.gain = value;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onFrequencyChanged(float value)
{
    mData.frequency = value;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onQValueChanged(float value)
{
    mData.qValue = value;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onEnabledChanged(bool enabled)
{
    mData.enabled = enabled;
    emit dataChanged(mIndex, mData);
}

// 间隔和间隙设置方法
void EqWidgetIem::setLayoutSpacing(int spacing)
{
    mLayoutSpacing = spacing;
    if (mMainLayout) {
        mMainLayout->setSpacing(spacing);
    }
}

void EqWidgetIem::setLayoutMargins(int left, int top, int right, int bottom)
{
    mLayoutMargins = QMargins(left, top, right, bottom);
    if (mMainLayout) {
        mMainLayout->setContentsMargins(left, top, right, bottom);
    }
}

void EqWidgetIem::setLayoutMargins(int margin)
{
    setLayoutMargins(margin, margin, margin, margin);
}

void EqWidgetIem::setMinimumItemWidth(int width)
{
    mMinimumItemWidth = width;
    setMinimumWidth(width);
}

QMargins EqWidgetIem::getLayoutMargins() const
{
    return mLayoutMargins;
}

void EqWidgetIem::applyLayoutSettings()
{
    if (mMainLayout) {
        mMainLayout->setSpacing(mLayoutSpacing);
        mMainLayout->setContentsMargins(mLayoutMargins);
    }
}

EqWidget::EqWidget(QWidget* parent)
    : QWidget(parent)
    , mMainLayoutSpacing(0)
    , mControlLayoutSpacing(0)
    , mItemsLayoutSpacing(0)
    , mMainLayoutMargins(0, 0, 0, 0)
    , mControlLayoutMargins(0, 0, 0, 0)
    , mItemsLayoutMargins(0, 0, 0, 0)
    , mScrollAreaMargins(0, 0, 0, 0)
    , mMainLayout(nullptr)
    , mControlLayout(nullptr)
    , mScrollArea(nullptr)
    , mScrollWidget(nullptr)
    , mItemsLayout(nullptr)
    , mAddItemButton(nullptr)
    , mRemoveItemButton(nullptr)
    , mTitleLabel(nullptr)
    , mStretchFactor(2.0)
{
    setupUI();
    setupConnections();
    createDefaultItems();
    setStyleSheet(
        "QWidget {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #404040;"
        "    font-size: 12px;"
        "}"
        "QPushButton {"
        "    background-color: #43cf7c;"
        "    color: #ffffff;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #52d689;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3bb56e;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #666666;"
        "    color: #999999;"
        "}"
        "QScrollArea {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: #404040;"
        "    height: 12px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background-color: #43cf7c;"
        "    min-width: 20px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #52d689;"
        "}"
    );
}

EqWidget::~EqWidget()
{
    removeAllItems();
}

void EqWidget::setupUI()
{
    // 主布局
    auto vLayout = new QVBoxLayout();
    vLayout->setSpacing(0);
    vLayout->setContentsMargins(0, 0, 0, 0);

    mMainLayout = new QHBoxLayout(this);
    mMainLayout->setContentsMargins(mMainLayoutMargins);
    mMainLayout->setSpacing(mMainLayoutSpacing);
    mMainLayout->addStretch(1);
    mMainLayout->addLayout(vLayout, 20);
    mMainLayout->addStretch(1);

    // 控制区域布局
    mControlLayout = new QHBoxLayout();
    mControlLayout->setSpacing(mControlLayoutSpacing);
    mControlLayout->setContentsMargins(mControlLayoutMargins);
    mControlLayout->addStretch();
    vLayout->addLayout(mControlLayout);

    auto hBoxLayout = new QHBoxLayout();
    vLayout->addLayout(hBoxLayout);

    // 控制按钮
    mAddItemButton = new QPushButton("add", this);
    mAddItemButton->setFixedSize(80, 30);
    mControlLayout->addWidget(mAddItemButton);

    mRemoveItemButton = new QPushButton("remove", this);
    mRemoveItemButton->setFixedSize(80, 30);
    mControlLayout->addWidget(mRemoveItemButton);

    // 滚动区域
    mScrollArea = new QScrollArea(this);
    mScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    mScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea->setWidgetResizable(true);
    mScrollArea->setContentsMargins(mScrollAreaMargins);

    // 项目布局
    mScrollWidget = new QWidget();
    mItemsLayout = new QHBoxLayout(mScrollWidget);
    mItemsLayout->setSpacing(mItemsLayoutSpacing);
    mItemsLayout->setContentsMargins(mItemsLayoutMargins);
    mItemsLayout->addStretch();
    mScrollArea->setWidget(mScrollWidget);

    // 标签布局
    auto vBoxLayout = new QVBoxLayout();
    vBoxLayout->setContentsMargins(0, 0, 0, 0);
    vBoxLayout->setSpacing(0);
    vBoxLayout->addStretch(1);
    auto labelType = new QLabel("Type", this);
    labelType->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    auto labelGain = new QLabel("Gain", this);
    labelGain->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    auto labelFrequency = new QLabel("Freq", this);
    labelFrequency->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    auto labelQ = new QLabel("Q", this);
    labelQ->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    vBoxLayout->addWidget(labelType, 1);
    vBoxLayout->addWidget(labelGain, 1);
    vBoxLayout->addWidget(labelFrequency, 1);
    vBoxLayout->addWidget(labelQ, 1);
    vBoxLayout->addStretch(1);

    hBoxLayout->addLayout(vBoxLayout, 1);
    hBoxLayout->addWidget(mScrollArea, 10);
}

void EqWidget::setupConnections()
{
    connect(mAddItemButton, &QPushButton::clicked,
            this, &EqWidget::onAddItemClicked);
    connect(mRemoveItemButton, &QPushButton::clicked,
            this, &EqWidget::onRemoveItemClicked);
}

void EqWidget::createDefaultItems()
{
    setItemCount(4);
}

void EqWidget::setItemCount(int count)
{
    if (count < 0) count = 0;
    if (count > 32) count = 32;

    int currentCount = mItems.size();

    if (count > currentCount) {
        for (int i = currentCount; i < count; ++i) {
            addItem();
        }
    } else if (count < currentCount) {
        for (int i = currentCount - 1; i >= count; --i) {
            removeItem(i);
        }
    }

    emit ItemCountChanged(count);
}

void EqWidget::addItem()
{
    setUpdatesEnabled(false);

    int index = mItems.size();
    EqWidgetIem* ItemWidget = new EqWidgetIem(index, this);
    ItemWidget->setFixedWidth(mStretchFactor * ItemWidget->minimumWidth());

    connect(ItemWidget, &EqWidgetIem::dataChanged,
            this, &EqWidget::onItemDataChanged);

    mItems.append(ItemWidget);

    ItemWidget->hide();

    mItemsLayout->insertWidget(mItemsLayout->count() - 1, ItemWidget);

    ItemWidget->show();
    setUpdatesEnabled(true);

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeItem(int index)
{
    if (mItems.isEmpty()) return;

    if (index < 0 || index >= mItems.size()) {
        index = mItems.size() - 1;
    }

    EqWidgetIem* ItemWidget = mItems.takeAt(index);
    mItemsLayout->removeWidget(ItemWidget);
    ItemWidget->deleteLater();

    updateItemIndices();

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
    emit itemDataChanged(getEqData());
}

void EqWidget::removeAllItems()
{
    while (!mItems.isEmpty()) {
        removeItem(-1);
    }
}

void EqWidget::updateItemIndices()
{
    for (int i = 0; i < mItems.size(); ++i) {
        mItems[i]->setItemIndex(i);
    }
}

void EqWidget::setEqData(const QVector<EqWidgetItemData>& data)
{
    setItemCount(data.size());

    for (int i = 0; i < data.size() && i < mItems.size(); ++i) {
        mItems[i]->setItemData(data[i]);
    }

    emit itemDataChanged(getEqData());
}

QVector<EqWidgetItemData> EqWidget::getEqData() const
{
    QVector<EqWidgetItemData> data;
    for (const auto& Item : mItems) {
        data.append(Item->getItemData());
    }
    return data;
}

void EqWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqWidget::onItemDataChanged(int ItemIndex, const EqWidgetItemData& data)
{
    if (ItemIndex >= 0 && ItemIndex < mItems.size()) {
        emit itemDataChanged(getEqData());
    }
}

void EqWidget::onAddItemClicked()
{
    addItem();
}

void EqWidget::onRemoveItemClicked()
{
    removeItem(-1);
}

// 主布局间隔和间隙设置方法
void EqWidget::setMainLayoutSpacing(int spacing)
{
    mMainLayoutSpacing = spacing;
    if (mMainLayout) {
        mMainLayout->setSpacing(spacing);
    }
}

void EqWidget::setMainLayoutMargins(int left, int top, int right, int bottom)
{
    mMainLayoutMargins = QMargins(left, top, right, bottom);
    if (mMainLayout) {
        mMainLayout->setContentsMargins(left, top, right, bottom);
    }
}

void EqWidget::setMainLayoutMargins(int margin)
{
    setMainLayoutMargins(margin, margin, margin, margin);
}

// 控制区域布局设置方法
void EqWidget::setControlLayoutSpacing(int spacing)
{
    mControlLayoutSpacing = spacing;
    if (mControlLayout) {
        mControlLayout->setSpacing(spacing);
    }
}

void EqWidget::setControlLayoutMargins(int left, int top, int right, int bottom)
{
    mControlLayoutMargins = QMargins(left, top, right, bottom);
    if (mControlLayout) {
        mControlLayout->setContentsMargins(left, top, right, bottom);
    }
}

void EqWidget::setControlLayoutMargins(int margin)
{
    setControlLayoutMargins(margin, margin, margin, margin);
}

// 项目布局设置方法
void EqWidget::setItemsLayoutSpacing(int spacing)
{
    mItemsLayoutSpacing = spacing;
    if (mItemsLayout) {
        mItemsLayout->setSpacing(spacing);
    }
}

void EqWidget::setItemsLayoutMargins(int left, int top, int right, int bottom)
{
    mItemsLayoutMargins = QMargins(left, top, right, bottom);
    if (mItemsLayout) {
        mItemsLayout->setContentsMargins(left, top, right, bottom);
    }
}

void EqWidget::setItemsLayoutMargins(int margin)
{
    setItemsLayoutMargins(margin, margin, margin, margin);
}

// 滚动区域设置方法
void EqWidget::setScrollAreaMargins(int left, int top, int right, int bottom)
{
    mScrollAreaMargins = QMargins(left, top, right, bottom);
    if (mScrollArea) {
        mScrollArea->setContentsMargins(left, top, right, bottom);
    }
}

void EqWidget::setScrollAreaMargins(int margin)
{
    setScrollAreaMargins(margin, margin, margin, margin);
}

// 批量设置所有项目的间隔方法
void EqWidget::setAllItemsLayoutSpacing(int spacing)
{
    for (auto* item : mItems) {
        if (item) {
            item->setLayoutSpacing(spacing);
        }
    }
}

void EqWidget::setAllItemsLayoutMargins(int left, int top, int right, int bottom)
{
    for (auto* item : mItems) {
        if (item) {
            item->setLayoutMargins(left, top, right, bottom);
        }
    }
}

void EqWidget::setAllItemsLayoutMargins(int margin)
{
    setAllItemsLayoutMargins(margin, margin, margin, margin);
}

void EqWidget::setAllItemsMinimumWidth(int width)
{
    for (auto* item : mItems) {
        if (item) {
            item->setMinimumItemWidth(width);
        }
    }
}

// 获取当前设置的方法
QMargins EqWidget::getMainLayoutMargins() const
{
    return mMainLayoutMargins;
}

QMargins EqWidget::getControlLayoutMargins() const
{
    return mControlLayoutMargins;
}

QMargins EqWidget::getItemsLayoutMargins() const
{
    return mItemsLayoutMargins;
}

QMargins EqWidget::getScrollAreaMargins() const
{
    return mScrollAreaMargins;
}

// 应用布局设置的方法
void EqWidget::applyLayoutSettings()
{
    if (mMainLayout) {
        mMainLayout->setSpacing(mMainLayoutSpacing);
        mMainLayout->setContentsMargins(mMainLayoutMargins);
    }

    if (mControlLayout) {
        mControlLayout->setSpacing(mControlLayoutSpacing);
        mControlLayout->setContentsMargins(mControlLayoutMargins);
    }

    if (mItemsLayout) {
        mItemsLayout->setSpacing(mItemsLayoutSpacing);
        mItemsLayout->setContentsMargins(mItemsLayoutMargins);
    }

    if (mScrollArea) {
        mScrollArea->setContentsMargins(mScrollAreaMargins);
    }
}

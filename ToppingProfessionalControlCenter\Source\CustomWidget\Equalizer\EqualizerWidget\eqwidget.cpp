#include "eqwidget.h"
#include <qnamespace.h>
#include <QApplication>
#include <QScrollBar>
#include <QSizePolicy>
#include <QTimer>

EqWidgetIem::EqWidgetIem(int index, QWidget* parent)
    : Q<PERSON>rame(parent)
    , mIndex(index)
    , mMainLayout(nullptr)
    , mItemLabel(nullptr)
    , mTypeComboBox(nullptr)
    , mGainDial(nullptr)
    , mFrequencyDial(nullptr)
    , mQValueDial(nullptr)
    , mEnabledCheckBox(nullptr)
{
    mData.type = "high pass";
    mData.gain = 0.0f;
    mData.frequency = 1000.0f;
    mData.qValue = 0.7f;
    mData.enabled = true;
    
    setupUI();
    setupConnections();
    setStyleSheet(
        "EqWidgetIem {"
        "    background-color: #2a2a2a;"
        "}"
        "QLabel {"
        "    color: #404040;"
        "    font-size: 12px;"
        "}"
        "QCheckBox {"
        "    color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: transparent;"
        "}"
    );
}

EqWidgetIem::~EqWidgetIem()
{
}

void EqWidgetIem::setupUI()
{
    setMinimumWidth(50);
    
    mMainLayout = new QVBoxLayout(this);
    mMainLayout->setSpacing(0);
    mMainLayout->setContentsMargins(0, 0, 0, 0);
    
    mItemLabel = new QLabel(QString::number(mIndex + 1), this);
    mItemLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mItemLabel->setAlignment(Qt::AlignCenter);
    mMainLayout->addWidget(mItemLabel, 3);
    
    mTypeComboBox = new ComboBoxS1M3(nullptr);
    mTypeComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    updateTypeOptions();
    mMainLayout->addWidget(mTypeComboBox, 3);
    
    mGainDial = new DialS1M1(this);
    mGainDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mGainDial->setRange(-20.0f, 20.0f)
              .setValue(0.0f)
              .setStep(0.1f)
              .setPrecision(1)
              .showSign(true);
    mMainLayout->addWidget(mGainDial, 9);

    mFrequencyDial = new DialS1M1(this);
    mFrequencyDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mFrequencyDial->setRange(20.0f, 20000.0f)
                  .setValue(1000.0f)
                  .setStep(1.0f)
                  .setPrecision(0);
    mMainLayout->addWidget(mFrequencyDial, 9);
    
    mQValueDial = new DialS1M1(this);
    mQValueDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mQValueDial->setRange(0.1f, 10.0f)
                .setValue(0.7f)
                .setStep(0.1f)
                .setPrecision(1);
    mMainLayout->addWidget(mQValueDial, 9);
    
    mEnabledCheckBox = new QCheckBox(this);
    mEnabledCheckBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mEnabledCheckBox->setChecked(true);
    mMainLayout->addWidget(mEnabledCheckBox, 3, Qt::AlignCenter);
}

void EqWidgetIem::setupConnections()
{
    connect(mTypeComboBox, &ComboBoxS1M3::currentTextChanged,
            this, &EqWidgetIem::onTypeChanged);
    connect(mGainDial, &DialS1M1::valueChanged,
            this, &EqWidgetIem::onGainChanged);
    connect(mFrequencyDial, &DialS1M1::valueChanged,
            this, &EqWidgetIem::onFrequencyChanged);
    connect(mQValueDial, &DialS1M1::valueChanged,
            this, &EqWidgetIem::onQValueChanged);
    connect(mEnabledCheckBox, &QCheckBox::toggled,
            this, &EqWidgetIem::onEnabledChanged);
}

void EqWidgetIem::updateTypeOptions()
{
    QVector<QString> types = {
        "high pass",
        "Low pass",
        "High Shelf",
        "High Shelf"
    };
    
    mTypeComboBox->setCurrentText("high pass");
}

void EqWidgetIem::setItemData(const EqWidgetItemData& data)
{
    mData = data;
    mTypeComboBox->setCurrentText(data.type);
    mGainDial->setValue(data.gain);
    mFrequencyDial->setValue(data.frequency);
    mQValueDial->setValue(data.qValue);
    mEnabledCheckBox->setChecked(data.enabled);
}

EqWidgetItemData EqWidgetIem::getItemData() const
{
    return mData;
}

void EqWidgetIem::setItemIndex(int index)
{
    mIndex = index;
    mItemLabel->setText(QString::number(index + 1));
}

void EqWidgetIem::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqWidgetIem::onTypeChanged(const QString& text)
{
    mData.type = text;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onGainChanged(float value)
{
    mData.gain = value;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onFrequencyChanged(float value)
{
    mData.frequency = value;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onQValueChanged(float value)
{
    mData.qValue = value;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onEnabledChanged(bool enabled)
{
    mData.enabled = enabled;
    emit dataChanged(mIndex, mData);
}

EqWidget::EqWidget(QWidget* parent)
    : QWidget(parent)
    , mScrollArea(nullptr)
    , mScrollWidget(nullptr)
    , mItemsLayout(nullptr)
    , mAddItemButton(nullptr)
    , mRemoveItemButton(nullptr)
    , mTitleLabel(nullptr)
    , mStretchFactor(2.0)
{
    setupUI();
    setupConnections();
    createDefaultItems();
    setStyleSheet(
        "QWidget {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #404040;"
        "    font-size: 12px;"
        "}"
        "QPushButton {"
        "    background-color: #43cf7c;"
        "    color: #ffffff;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #52d689;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3bb56e;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #666666;"
        "    color: #999999;"
        "}"
        "QScrollArea {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: #404040;"
        "    height: 12px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background-color: #43cf7c;"
        "    min-width: 20px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #52d689;"
        "}"
    );
}

EqWidget::~EqWidget()
{
    removeAllItems();
}

void EqWidget::setupUI()
{
    auto vLayout = new QVBoxLayout();
    vLayout->setSpacing(0);
    vLayout->setContentsMargins(0, 0, 0, 0);
    auto mMainLayout = new QHBoxLayout(this);
    mMainLayout->setContentsMargins(0, 0, 0, 0);
    mMainLayout->addStretch(1);
    mMainLayout->addLayout(vLayout, 20);
    mMainLayout->addStretch(1);
    auto controlLayout = new QHBoxLayout();
    controlLayout->addStretch();
    vLayout->addLayout(controlLayout);
    auto hBoxLayout = new QHBoxLayout();
    vLayout->addLayout(hBoxLayout);

    mAddItemButton = new QPushButton("add", this);
    mAddItemButton->setFixedSize(80, 30);
    controlLayout->addWidget(mAddItemButton);

    mRemoveItemButton = new QPushButton("remove", this);
    mRemoveItemButton->setFixedSize(80, 30);
    controlLayout->addWidget(mRemoveItemButton);

    mScrollArea = new QScrollArea(this);
    mScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    mScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea->setWidgetResizable(true);

    mScrollWidget = new QWidget();
    mItemsLayout = new QHBoxLayout(mScrollWidget);
    mItemsLayout->setSpacing(0);
    mItemsLayout->setContentsMargins(0, 0, 0, 0);
    mItemsLayout->addStretch();
    mScrollArea->setWidget(mScrollWidget);

    auto vBoxLayout = new QVBoxLayout();
    vBoxLayout->setContentsMargins(0, 0, 0, 0);
    vBoxLayout->setSpacing(0);
    vBoxLayout->addStretch(1);
    auto labelType = new QLabel("Type", this);
    labelType->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    auto labelGain = new QLabel("Gain", this);
    labelGain->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    auto labelFrequency = new QLabel("Freq", this);
    labelFrequency->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    auto labelQ = new QLabel("Q", this);
    labelQ->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    vBoxLayout->addWidget(labelType, 1);
    vBoxLayout->addWidget(labelGain, 1);
    vBoxLayout->addWidget(labelFrequency, 1);
    vBoxLayout->addWidget(labelQ, 1);
    vBoxLayout->addStretch(1);

    hBoxLayout->addLayout(vBoxLayout, 1);
    hBoxLayout->addWidget(mScrollArea, 10);
}

void EqWidget::setupConnections()
{
    connect(mAddItemButton, &QPushButton::clicked,
            this, &EqWidget::onAddItemClicked);
    connect(mRemoveItemButton, &QPushButton::clicked,
            this, &EqWidget::onRemoveItemClicked);
}

void EqWidget::createDefaultItems()
{
    setItemCount(4);
}

void EqWidget::setItemCount(int count)
{
    if (count < 0) count = 0;
    if (count > 32) count = 32;

    int currentCount = mItems.size();

    if (count > currentCount) {
        for (int i = currentCount; i < count; ++i) {
            addItem();
        }
    } else if (count < currentCount) {
        for (int i = currentCount - 1; i >= count; --i) {
            removeItem(i);
        }
    }

    emit ItemCountChanged(count);
}

void EqWidget::addItem()
{
    setUpdatesEnabled(false);

    int index = mItems.size();
    EqWidgetIem* ItemWidget = new EqWidgetIem(index, this);
    ItemWidget->setFixedWidth(mStretchFactor * ItemWidget->minimumWidth());

    connect(ItemWidget, &EqWidgetIem::dataChanged,
            this, &EqWidget::onItemDataChanged);

    mItems.append(ItemWidget);

    ItemWidget->hide();

    mItemsLayout->insertWidget(mItemsLayout->count() - 1, ItemWidget);

    ItemWidget->show();
    setUpdatesEnabled(true);

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeItem(int index)
{
    if (mItems.isEmpty()) return;

    if (index < 0 || index >= mItems.size()) {
        index = mItems.size() - 1;
    }

    EqWidgetIem* ItemWidget = mItems.takeAt(index);
    mItemsLayout->removeWidget(ItemWidget);
    ItemWidget->deleteLater();

    updateItemIndices();

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
    emit itemDataChanged(getEqData());
}

void EqWidget::removeAllItems()
{
    while (!mItems.isEmpty()) {
        removeItem(-1);
    }
}

void EqWidget::updateItemIndices()
{
    for (int i = 0; i < mItems.size(); ++i) {
        mItems[i]->setItemIndex(i);
    }
}

void EqWidget::setEqData(const QVector<EqWidgetItemData>& data)
{
    setItemCount(data.size());

    for (int i = 0; i < data.size() && i < mItems.size(); ++i) {
        mItems[i]->setItemData(data[i]);
    }

    emit itemDataChanged(getEqData());
}

QVector<EqWidgetItemData> EqWidget::getEqData() const
{
    QVector<EqWidgetItemData> data;
    for (const auto& Item : mItems) {
        data.append(Item->getItemData());
    }
    return data;
}

void EqWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqWidget::onItemDataChanged(int ItemIndex, const EqWidgetItemData& data)
{
    if (ItemIndex >= 0 && ItemIndex < mItems.size()) {
        emit itemDataChanged(getEqData());
    }
}

void EqWidget::onAddItemClicked()
{
    addItem();
}

void EqWidget::onRemoveItemClicked()
{
    removeItem(-1);
}

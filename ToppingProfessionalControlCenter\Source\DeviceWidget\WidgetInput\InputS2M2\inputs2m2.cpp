#include "inputs2m2.h"
#include "globalfont.h"
#include "ui_InputS2M2.h"
#include <QButtonGroup>

InputS2M2::InputS2M2(QWidget* parent, QString name)
    : OriginBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::InputS2M2)
{
    ui->setupUi(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "   border-radius: 5px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);

    connect(ui->buttonMute, SIGNAL(clicked(bool)), this, SLOT(on_buttonMute_clicked(bool)));
    connect(ui->slider, SIGNAL(valueChanged(int)), this, SLOT(on_slider_valueChanged(int)));
}
InputS2M2::~InputS2M2()
{
    delete ui;
}


void InputS2M2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    float wPixelPerRatio=size().width() / 100.0;
    float hPixelPerRatio = size().height() / 100.0;
    ui->lineEdit->setGeometry(0,0, 13*wPixelPerRatio, height());
    ui->buttonMute->setGeometry(ui->lineEdit->geometry().right(), 31.25*hPixelPerRatio, 12*wPixelPerRatio, 37.5*hPixelPerRatio);
    ui->volumeTop->setGeometry(ui->buttonMute->geometry().right() + 5*wPixelPerRatio, 7.5*hPixelPerRatio, 70*wPixelPerRatio, 25*hPixelPerRatio);
    ui->slider->setGeometry(ui->buttonMute->geometry().right() + 5*wPixelPerRatio, 32.5*hPixelPerRatio, 70*wPixelPerRatio, 35*hPixelPerRatio);
    ui->volumeBottom->setGeometry(ui->buttonMute->geometry().right() + 5*wPixelPerRatio, 67.5*hPixelPerRatio, 70*wPixelPerRatio, 25*hPixelPerRatio);

    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->width())*0.4);
    ui->lineEdit->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->buttonMute->height())*0.7);
    ui->buttonMute->setFont(mFont);
    QString style = QString(R"(
        QPushButton {
            border-radius: %1px;
            color: rgb(161, 161, 161);
            background-color: rgb(60, 60, 60);
            }
        QPushButton:checked {
            color: rgb(229, 229, 229);
            background-color: rgb(149, 40, 37);
        }
        QPushButton:hover {
            border: 2px solid gray;
        };
    )").arg(hPixelPerRatio* 5);
    ui->buttonMute->setStyleSheet(style);
}
void InputS2M2::updateAttribute()
{
    if(isWidgetReady())
    {
        if(isWidgetEnable())
        {
            if(mPreMUTE != static_cast<int>(ui->buttonMute->isChecked()))
            {
                mPreMUTE = static_cast<int>(ui->buttonMute->isChecked());
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
            if(mPreGAIN != static_cast<int>(ui->slider->getValue()))
            {
                mPreGAIN = static_cast<int>(ui->slider->getValue());
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
        }
    }
}
void InputS2M2::loadSettings()
{
    mPreMUTE = -2147483648;
    mPreGAIN = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    { 
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("MUTE", false);
        WorkspaceObserver::setValue("GAIN", 0);
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    
    
    ui->buttonMute->setChecked(WorkspaceObserver::value("MUTE").toBool());
    ui->slider->setValue(WorkspaceObserver::value("GAIN").toInt());
    
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
        emit attributeChanged(this->objectName(), "Save_GAIN", WorkspaceObserver::value("GAIN").toString());
    }
    setWidgetReady(true);
    updateAttribute();
}
void InputS2M2::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}

// slot
void InputS2M2::in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M2::ButtonID button, bool state)
{
    switch(button)
    {
        default:
            break;
    }
}
void InputS2M2::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 12))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void InputS2M2::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}

void InputS2M2::on_buttonMute_clicked(bool checked)
{
    save("MUTE", checked);
    updateAttribute();
}

void InputS2M2::on_slider_valueChanged(int value)
{
    save("GAIN", value);
    updateAttribute();
}

// setter & getter
void InputS2M2::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
InputS2M2& InputS2M2::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
InputS2M2& InputS2M2::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}

void InputS2M2::setTopVolumeMeter(int value)
{
    ui->volumeTop->setValue(value);
}

void InputS2M2::setBottomVolumeMeter(int value)
{
    ui->volumeBottom->setValue(value);
}

void InputS2M2::setMute(bool state)
{
    ui->buttonMute->setChecked(state);
    save("MUTE", state);
    updateAttribute();
}

void InputS2M2::setGain(float value)
{
    ui->slider->setValue(value);
}
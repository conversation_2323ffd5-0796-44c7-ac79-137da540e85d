#include "menus1m1.h"
#include <QLabel>
#include <QToolButton>
#include <QMenu>
#include <QWidgetAction>
#include <QVBoxLayout>
#include <QStack>
#include "globalfont.h"
#include <QPainter>
#include <QStyleOption>
#include <QButtonGroup>
#include <qwindowdefs.h>

MenuS1M1::MenuS1M1(QWidget *parent, const QString& title)
    : QWidget(parent),
      m_menuPosition(MenuPosition::Left), m_titleHeightRatio(25), m_buttonsHeightRatio(75),
      m_buttonBackgroundColor(QColor(22, 22, 22)), m_titleBackgroundColor(45, 45, 45), 
      m_titleTextColor(161, 161, 161), m_buttonTextColor(161, 161, 161),
    m_buttonGroup(new QButtonGroup(this)), m_widgetBorderRadius(0), m_labelBorderRadius(0), m_buttonFontSizeRatio(0.6)
{
    m_title = new QLabel(this);
    m_title->setMinimumHeight(20);
    m_title->setAlignment(Qt::AlignmentFlag::AlignCenter);
    m_title->setText(title);
    setMinimumHeight(50);
    m_buttonGroup->setExclusive(false);
}

void MenuS1M1::setFont(const QFont &font)
{
    QStack<QWidget*> stack;
    stack.push(this);

    while (!stack.isEmpty())
    {
        QWidget* widget = stack.pop();
        widget->setFont(font);

        for (auto child : widget->children())
        {
            if (QWidget* childWidget = qobject_cast<QWidget*>(child))
            {
                stack.push(childWidget);
            }
        }
    }
}

void MenuS1M1::setTitle(const QString &title)
{
    m_title->setText(title);
}

void MenuS1M1::setTitleBackground(const QColor &background)
{
    m_titleBackgroundColor = background;
}

void MenuS1M1::setTitleTextColor(const QColor &color)
{
    m_titleTextColor = color;
}

void MenuS1M1::setButtonBackground(const QColor &background)
{
    m_buttonBackgroundColor = background;
}

void MenuS1M1::setButtonTextColor(const QColor &color)
{
    m_buttonTextColor = color;
}

void MenuS1M1::addButton(const ButtonData& buttonData, const QVector<ButtonAction> &buttonActions)
{
    if(!m_buttons.empty())
    {
        QWidget *line = new QWidget(this);
        m_lines.push_back(line);
        line->setStyleSheet("border-radius: 1px;"
                            "background: rgba(46, 46, 46, 1);");
    }
    QToolButton *button = new QToolButton(this);
    button->setMinimumHeight(20);
    button->setObjectName(buttonData.buttonData);
    button->setText(buttonData.buttonText);
    button->setCheckable(buttonData.isCheckable);
    m_buttonGroup->addButton(button);
    m_buttons.push_back(button);
    m_buttonMap.insert(buttonData.buttonData, button);
    if(!buttonActions.empty())
    {
        QMenu *menu = new QMenu(this);
        button->setProperty("menu", QVariant::fromValue(menu));
        for (const auto &buttonAction : buttonActions)
        {
            QWidget* widget = new QWidget;
            QVBoxLayout* vLayout = new QVBoxLayout(widget);
            vLayout->setContentsMargins(3, 5, 5, 3);
            QLabel* label = new QLabel(buttonAction.actionText, menu);
            label->setWordWrap(true);
            label->setAlignment(Qt::AlignCenter);
            vLayout->addWidget(label);
            QWidgetAction* widgetAction = new QWidgetAction(menu);
            widgetAction->setData(buttonAction.actionData);
            widgetAction->setEnabled(buttonAction.isEnable);
            widgetAction->setDefaultWidget(widget);
            menu->addAction(widgetAction);
        }
        connect(menu, &QMenu::triggered, this, [this, button](QAction *action){
            emit attributeChanged(objectName(), button->objectName(), action->data().toString());
        });
    }
    connect(button, &QToolButton::clicked, this, [this, button, buttonData](bool checked){
        auto menu = button->property("menu").value<QMenu*>();
        if(menu == nullptr)
        {
            if(!buttonData.isCheckable){
                checked = true;
            }
            emit attributeChanged(objectName(), button->objectName(), checked?"true":"false");
            return;
        }

        setMenuStyle(menu, button);
        int xSpace = 10;
        QPoint gPos = button->mapToGlobal(QPoint(0,0));
        int x = m_menuPosition == MenuPosition::Right ? gPos.x()+button->width()+xSpace : gPos.x()-menu->width()-xSpace;
        menu->move(x, gPos.y());
        menu->exec();
        button->setChecked(false);
    });
}

void MenuS1M1::setButtonExlusive(bool exclusive)
{
    m_buttonGroup->setExclusive(exclusive);
}

void MenuS1M1::setStretchFactor(int titleHeightRatio, int buttonsHeightRatio)
{
    m_titleHeightRatio = titleHeightRatio;
    m_buttonsHeightRatio = buttonsHeightRatio;
}

void MenuS1M1::setButtonChecked(const QString &buttonData, bool checked)
{

    if(m_buttonMap.contains(buttonData))
    {
        m_buttonMap[buttonData]->setChecked(checked);
    }
}

void MenuS1M1::setButtonText(const QString &buttonData, const QString &text)
{
    if(m_buttonMap.contains(buttonData))
    {
        m_buttonMap[buttonData]->setText(text);
    }
}

void MenuS1M1::setWidgetBorderRadius(int radius)
{
    m_widgetBorderRadius = radius;
}

void MenuS1M1::setLabelBorderRadius(int radius)
{
    m_labelBorderRadius = radius;
}

void MenuS1M1::setButtonFontSizeRatio(double ratio)
{
    m_buttonFontSizeRatio = ratio;
}

void MenuS1M1::resizeEvent(QResizeEvent *event)
{
    double hPixelPerRatio = height() / 100.0;
    double wPixelPerRatio = width() / 100.0;
    double lineHeightRatio = 1.5;
    double totalLineHeight = m_lines.size() * lineHeightRatio;
    double buttonHeightRatio = (m_buttonsHeightRatio - totalLineHeight) / m_buttons.size();

    m_title->setGeometry(0, 0, 100 * wPixelPerRatio, m_titleHeightRatio * hPixelPerRatio);
    for(int i = 0; i < m_lines.size(); i++)
    {
        m_lines[i]->setGeometry(10*wPixelPerRatio, (m_titleHeightRatio + (i + 1) * buttonHeightRatio + i * lineHeightRatio) * hPixelPerRatio, 80 * wPixelPerRatio, lineHeightRatio * hPixelPerRatio);
    }

    for(int i = 0; i < m_buttons.size(); i++)
    {
        m_buttons[i]->setGeometry(0, (m_titleHeightRatio + i * (buttonHeightRatio + lineHeightRatio)) * hPixelPerRatio, 100 * wPixelPerRatio, buttonHeightRatio * hPixelPerRatio);
    }

    setWidgetStyle(this);
    setTitleStyle(m_title);
    for(auto button : m_buttons)
    {
        setButtonStyle(button);
    }
}

void MenuS1M1::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    QPainter painter(this);
    QStyleOption opt;
    opt.initFrom(this);
    style()->drawPrimitive(QStyle::PE_Widget, &opt, &painter, this);
}

void MenuS1M1::setWidgetStyle(QWidget* widget)
{
    QFont font = widget->font();
    QString style = QString("background: %1;"
                            "border-radius: %2px;").arg(m_buttonBackgroundColor.name()).arg(0.2*m_title->height());
    widget->setStyleSheet(style);
}

void MenuS1M1::setTitleStyle(QWidget* widget)
{
    QFont font = widget->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, widget->height())*70/100);
    widget->setFont(font);

    QString style = QString("background: %1;"
                            "color: %2;"
                            "border-top-left-radius:%3px;border-top-right-radius:%3px;" 
                            "border-bottom-left-radius: 0px;"
                            "border-bottom-right-radius: 0px;").arg(m_titleBackgroundColor.name()).arg(m_titleTextColor.name()).arg(0.2*m_title->height());
    widget->setStyleSheet(style);
}

void MenuS1M1::setButtonStyle(QToolButton* button)
{
    QFont font = button->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, button->height())*m_buttonFontSizeRatio);
    button->setFont(font);

    QString style = QString("QToolButton{color: %1;"
                            "border: none;"
                            "background:%2;}"
                            "QToolButton:pressed{color:rgba(255, 255, 255, 1);}"
                            "QToolButton:checked{color: rgba(67, 207, 124, 1);}")
                            .arg(m_buttonTextColor.name()).arg(m_buttonBackgroundColor.name());
    button->setStyleSheet(style);
}

void MenuS1M1::setMenuStyle(QMenu* menu, QToolButton* button)
{
    QFont font = button->font();
    menu->setStyleSheet(QString(R"(
        QMenu {
            background-color: %1;
            border: 1px solid rgb(172,172,172);
            padding: 10px;
            border-radius:1px;
        }
        QLabel{
            color:rgb(255, 255, 255);
            font-size:%2px;
            background-color: transparent;
        }
        QLabel:hover {
            color:rgb(120,120,120);
        }
        QLabel:disabled{
            color:rgb(255,255,255);
        }
    )").arg(m_buttonBackgroundColor.name()).arg(GLBFHandle.getSuitablePixelSize(font, button->height())*m_buttonFontSizeRatio));
    menu->adjustSize();
}

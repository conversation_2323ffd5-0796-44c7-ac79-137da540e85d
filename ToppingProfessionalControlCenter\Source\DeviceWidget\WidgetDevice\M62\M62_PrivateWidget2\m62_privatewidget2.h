#ifndef M62_PrivateWidget2_H
#define M62_PrivateWidget2_H

#include <QWidget>
#include "workspace.h"
#include "appsettings.h"

class MenuS1M1;
class M62_PrivateWidget2 : public QWidget, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT

public:
    explicit M62_PrivateWidget2(QWidget *parent = nullptr, const QString& name = {});
    ~M62_PrivateWidget2();
    M62_PrivateWidget2& setName(const QString& name);
    M62_PrivateWidget2& setFont(const QFont& font);

protected:
    void paintEvent(QPaintEvent* e)override;
    void resizeEvent(QResizeEvent* e)override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;;
    void setAllChildFont(QWidget* widget, const QFont& font);

private:
    void initSigConnect();

signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
    
private:
    QWidget* mWidget;
    MenuS1M1* mMenuScene = nullptr;
    MenuS1M1* mMenuPresets = nullptr;
    QString mLineEditText;
    double mThreshold;
    double mAttack;
    double mReduction;
    double mRelease;
    int mOFF;
    int mIN1AUX;
    int mIN2AUX;
    int mIN1BT;
    int mIN2BT;
    int mIN1OTGIN;
    int mIN2OTGIN;
    int mIN1Playback1_2;
    int mIN2Playback1_2;
    int mIN1Playback3_4;
    int mIN2Playback3_4;
    int mIN1Playback5_6;
    int mIN2Playback5_6;
    int mIN1Playback7_8;
    int mIN2Playback7_8;
    int mIn1;
    int mIn2;
};

#endif // M62_PrivateWidget2_H

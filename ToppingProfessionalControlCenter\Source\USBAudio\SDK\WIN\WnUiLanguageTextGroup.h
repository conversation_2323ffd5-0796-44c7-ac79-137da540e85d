/************************************************************************
 *  Group of UI language texts.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnUiLanguageTextGroup_h__
#define __WnUiLanguageTextGroup_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// Wrapper class for an UI language.
//
class WnUiLanguageTextGroup
{
//construction/destruction/assignment
public:    
    WnUiLanguageTextGroup();
    WnUiLanguageTextGroup(const wchar_t* idStr, bool isOptional = false);
    ~WnUiLanguageTextGroup();
    WnUiLanguageTextGroup( const WnUiLanguageTextGroup& src );
    WnUiLanguageTextGroup& operator =( const WnUiLanguageTextGroup& src );

//interface
public:

    //
    // clear any information 
    //
    void Clear();    

    //
    // find the UI language text with the given identifier, or return nullptr if the text doesn't exist
    //
    UiLanguageTextHandle
    FindUILanguageText(
        const wchar_t* idStr
    ) const;

    //
    // add an UI language text
    //
    // parameters:
    //      idStr           Identifier string of the text. Must be unique among all managed texts, otherwise adding fails. The parameter is mandatory. 
    //      defaultText     Default UI text, in English. The parameter is mandatory.
    //      comment         optional comment which describes the usage of the text in the UI or contains some hints for translation
    //   
    UiLanguageTextHandle
    AddUiLanguageText(
        const wchar_t* idStr,
        const wchar_t* defaultText,
        const WString& comment = L""
    )
    {
        UiLanguageTextHandle handle{new WnUiLanguageText(idStr, defaultText, comment)};
        mUiLanguageTexts.push_back(handle);
        return handle;
    }

    //
    // add an UI language text
    //    
    void
    AddUiLanguageText(
        const WnUiLanguageText& uiText
    )
    {
        UiLanguageTextHandle handle{new WnUiLanguageText(uiText)};
        mUiLanguageTexts.push_back(handle);
    }

    //
    // get/set the identifier string of the group
    //
    const wchar_t* GetIdentifierStr() const
    {
        return mIdentifierStr;
    }

    void SetIdentifierStr(const wchar_t* idStr)
    {
        mIdentifierStr = idStr;
    }

    //
    // return true if the texts of the group are optional, false if not
    //
    bool IsOptional() const
    {
        return mIsOptional;
    }

    //
    // set the flag, that defines whether or not the texts of the group are optional
    //
    void SetOptionalFlag(bool value)
    {
        mIsOptional = value;
    }

    //
    //get the list of UI language texts managed by the group
    //
    const WnUiLanguageTextVector& GetUiLanguageTexts() const {
        return mUiLanguageTexts;
    }

    WnUiLanguageTextVector& GetUiLanguageTexts() {
        return mUiLanguageTexts;
    }

    //
    //get the number of contained UI language texts 
    //
    size_t GetUiLanguageTextCount() const {
        return mUiLanguageTexts.size();
    }

private:   

private:
    //identifier string of the group
    const wchar_t* mIdentifierStr{nullptr};

    //true if the texts of the group are optional, false if not
    bool mIsOptional{false};

    //group of UI language texts
    WnUiLanguageTextVector mUiLanguageTexts;
};

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguageTextGroup_h__

/*************************** EOF **************************************/

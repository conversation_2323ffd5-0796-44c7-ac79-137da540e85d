/************************************************************************

    Description:
        helper functions

    Author(s):
        <PERSON><PERSON>
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#include "helpers.h"


std::string
ConvertToCharString(
    const std::wstring& wstr
    )
{
    std::string s;
    s.reserve(wstr.size());

    for ( std::wstring::value_type c : wstr ) {
        s.push_back(static_cast<std::string::value_type>(c));
    }

    return s;
}


void
LeftTrimString(
    std::string& str
    )
{
    while ( str.size() > 0 && std::isspace(*str.begin()) ) {
        str.erase(str.begin());
    }
}


void
RightTrimString(
    std::string& str
    )
{
    while ( str.size() > 0 && std::isspace(*(str.end()-1)) ) {
        str.erase(str.end()-1);
    }
}


void
TrimString(
    std::string& str
    )
{
    LeftTrimString(str);
    RightTrimString(str);
}


bool
StringToUint(
    unsigned int& value,
    const std::string& str
    )
{
    const std::string hexPrefix{"0x"};

    try {
       
        if ( 0 == str.compare(0, 2, hexPrefix) ) {
            // hex format
            value = static_cast<unsigned int>(std::stoul(str, nullptr, 16));
        } else {
            // dec format
            value = static_cast<unsigned int>(std::stoul(str, nullptr, 10));
        }

    } catch(...) {
        return false;
    }

    return true;
}



bool
ParseVidPidTuple(
    VidPidTuple& vp,
    const std::string& str
    )
{
    // split string
    auto pos = str.find(':');
    if ( std::string::npos == pos ) {
        return false;
    }
    std::string vidstr = str.substr(0,pos);
    std::string pidstr = str.substr(pos+1);

    // convert to integer
    if ( !StringToUint(vp.vid, vidstr) ) {
        return false;
    }
    if ( !StringToUint(vp.pid, pidstr) ) {
        return false;
    }

    return true;
}


bool
ParseVidPidTupleList(
    VidPidTupleList& vpList,
    const std::string& str
    )
{
    std::string::size_type start = 0;

    for (;;) {

        // extract substring up to the first comma
        std::string::size_type pos = str.find(',', start);
        std::string vpstr { str.substr(start, pos) };
        
        // convert
        VidPidTuple vp;
        if ( !ParseVidPidTuple(vp, vpstr) ) {
            return false;
        }
        vpList.push_back(vp);

        if ( std::string::npos == pos ) {
            // done
            break;
        }
        
        // continue after the ','
        start = pos + 1;

    } //for

    return true;
}


std::string
BuildDeviceFilterDescription(
    const VidPidTuple& vp
    )
{
    char buf[40];
    std::snprintf(buf, sizeof(buf), "VID=0x%04X,PID=0x%04X\n", vp.vid, vp.pid);

    return std::string{buf};
}


std::string
BuildDeviceFilterDescription(
    const VidPidTupleList& vpList
    )
{
    std::string str;

    for ( const VidPidTuple& vp : vpList ) {
        str += BuildDeviceFilterDescription(vp);
    }

    return str;
}




/******************************** EOF ***********************************/

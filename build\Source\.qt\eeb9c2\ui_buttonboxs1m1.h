/********************************************************************************
** Form generated from reading UI file 'buttonboxs1m1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_BUTTONBOXS1M1_H
#define UI_BUTTONBOXS1M1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ButtonBoxS1M1
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;

    void setupUi(QWidget *ButtonBoxS1M1)
    {
        if (ButtonBoxS1M1->objectName().isEmpty())
            ButtonBoxS1M1->setObjectName("ButtonBoxS1M1");
        ButtonBoxS1M1->resize(140, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(ButtonBoxS1M1->sizePolicy().hasHeightForWidth());
        ButtonBoxS1M1->setSizePolicy(sizePolicy);
        ButtonBoxS1M1->setMinimumSize(QSize(60, 220));
        gridLayout = new QGridLayout(ButtonBoxS1M1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(ButtonBoxS1M1);
        frame->setObjectName("frame");
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(ButtonBoxS1M1);

        QMetaObject::connectSlotsByName(ButtonBoxS1M1);
    } // setupUi

    void retranslateUi(QWidget *ButtonBoxS1M1)
    {
        ButtonBoxS1M1->setWindowTitle(QCoreApplication::translate("ButtonBoxS1M1", "Form", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ButtonBoxS1M1: public Ui_ButtonBoxS1M1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_BUTTONBOXS1M1_H

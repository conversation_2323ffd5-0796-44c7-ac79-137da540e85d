/************************************************************************

    Description:
        TLDFU API library
        Represents the dynamically loaded API library

    Author(s):
        <PERSON>in<PERSON> Huck
        Udo Eberhardt
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __TLDfuApi_h__
#define __TLDfuApi_h__

//
// This class dynamically loads the API library 
// and provides function pointer wrappers for all API functions.
//
class TLDfuApi
{
    //
    // Constructor
    // Note: This class is a singleton and should be accessed through Instance(). 
    // We don't allow construction on stack or heap.
    //
private:
    TLDfuApi();

public:
    //
    // Destructor
    //
    ~TLDfuApi();


    // disable copy and move operations
    TLDfuApi(const TLDfuApi&) = delete;
    TLDfuApi& operator=(const TLDfuApi&) = delete;
    TLDfuApi(TLDfuApi&&) = delete;
    TLDfuApi& operator=(TLDfuApi&&) = delete;


/////////////////////////////////////////
// Interface
//
public:

    // This class is a singleton. Return the one and only instance.
    static
    TLDfuApi&
    Instance()
        {
            return mInstance;
        }


    //
    // Load the API library (DLL) by file name.
    //
    // By default the function checks whether the API version of
    // the loaded library is compatible with the application.
    //
    TLSTATUS
    LoadByName(
        const T_UNICHAR* fileName,  // absolute or relative path
        bool checkApiCompatibility = true,
        unsigned int majorApiVersion = TLDFU_API_VERSION_MJ,
        unsigned int minorApiVersion = TLDFU_API_VERSION_MN
        );

    //
    // Unload the API library.
    // It's safe to call this function if no library is loaded currently.
    //
    void
    Unload();

    //
    // Returns true if a library is loaded currently.
    //
    bool
    IsLoaded() const
        {
            return mDynlib.IsDynlibLoaded();
        }


    //
    // Returns the cached API version number.
    //
    unsigned int
    ApiVersion() const
        {
            return mApiVersion;
        }

    unsigned int
    MajorApiVersion() const
        {
            return TLDFU_API_EXTRACT_MJ_VERSION(mApiVersion);
        }

    unsigned int
    MinorApiVersion() const
        {
            return TLDFU_API_EXTRACT_MN_VERSION(mApiVersion);
        }


    //
    // API compatibility check
    // 
    bool
    IsApiVersionEqualOrGreater(
        unsigned int major,
        unsigned int minor
        ) const;


/////////////////////////////////////////
// API wrappers - see reference manual or documentation in tlusbdfu_api.h 
//
public:

    unsigned int
    GetApiVersion()
            {
                if ( nullptr == mFunc.GetApiVersion ) return 0;
                return mFunc.GetApiVersion();
            }


    int
    CheckApiVersion(
        unsigned int majorVersion,
        unsigned int minorVersion
        )
            {
                if ( nullptr == mFunc.CheckApiVersion ) return 0;
                return mFunc.CheckApiVersion(majorVersion, minorVersion);
            }


    unsigned int
    GetUniCharSize()
            {
                if ( nullptr == mFunc.GetUniCharSize ) return 0;
                return mFunc.GetUniCharSize();
            }

    
    const char*
    GetErrorText(
        TLSTATUS statusCode
        )
            {
                if ( nullptr == mFunc.GetErrorText ) return "";
                return mFunc.GetErrorText(statusCode);
            }


    TLSTATUS
    StartFileLogging(
        const T_UNICHAR* logFileName,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.StartFileLogging ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.StartFileLogging(
                                logFileName,
                                flags
                                );
            
            }


    TLSTATUS
    EnumerateUsbDfuDevices(
        const char* deviceFilterDescription,
        TLDfuEnumerationHandle* enumerationHandle,
        unsigned int* deviceCount,
        unsigned int retryCount,
        unsigned int retryDelay,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.EnumerateUsbDfuDevices ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.EnumerateUsbDfuDevices(
                                deviceFilterDescription,
                                enumerationHandle,
                                deviceCount,
                                retryCount,
                                retryDelay,
                                flags
                                );
            
            }

    
    TLSTATUS
    CloseEnumeration(
        TLDfuEnumerationHandle enumerationHandle
        )
            {
                if ( nullptr == mFunc.CloseEnumeration ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.CloseEnumeration(enumerationHandle);
            }


    TLSTATUS
    CompareDeviceInstance(
        TLDfuEnumerationHandle enumerationHandle,
        unsigned int deviceIndex,
        TLDfuDeviceHandle deviceHandle
        )
            {
                if ( nullptr == mFunc.CompareDeviceInstance ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.CompareDeviceInstance(
                                enumerationHandle,
                                deviceIndex,
                                deviceHandle
                                );
            }



    TLSTATUS
    OpenDevice(
        TLDfuEnumerationHandle enumerationHandle,
        unsigned int deviceIndex,
        TLDfuDeviceHandle* deviceHandle,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.OpenDevice ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.OpenDevice(
                                enumerationHandle,
                                deviceIndex,
                                deviceHandle,
                                flags
                                );
            }


    TLSTATUS
    CloseDevice(
        TLDfuDeviceHandle deviceHandle
        )
            {
                if ( nullptr == mFunc.CloseDevice ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.CloseDevice(deviceHandle);
            }


    TLSTATUS
    CheckDeviceConnection(
        TLDfuDeviceHandle deviceHandle
        )
            {
                if ( nullptr == mFunc.CheckDeviceConnection ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.CheckDeviceConnection(deviceHandle);
            }


    TLSTATUS
    GetDevicePropertyUint(
        TLDfuDeviceHandle deviceHandle,
        TLDfuDeviceProperty propertyId,
        unsigned int* propertyValue
        )
            {
                if ( nullptr == mFunc.GetDevicePropertyUint ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetDevicePropertyUint(
                                deviceHandle,
                                propertyId,
                                propertyValue
                                );
            }


    TLSTATUS
    GetDevicePropertyString(
        TLDfuDeviceHandle deviceHandle,
        TLDfuDeviceProperty propertyId,
        T_UNICHAR* stringBuffer,
        unsigned int stringBufferMaxItems
        )
            {
                if ( nullptr == mFunc.GetDevicePropertyString ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetDevicePropertyString(
                                deviceHandle,
                                propertyId,
                                stringBuffer,
                                stringBufferMaxItems
                                );
            }

    
    TLSTATUS
    GetTargetImagePropertyUint(
        TLDfuDeviceHandle deviceHandle,
        unsigned int targetId,
        TLDfuImageProperty propertyId,
        unsigned int* propertyValue,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.GetTargetImagePropertyUint ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetTargetImagePropertyUint(
                                deviceHandle,
                                targetId,
                                propertyId,
                                propertyValue,
                                flags
                                );
            }

    TLSTATUS
    GetTargetImagePropertyUint64(
        TLDfuDeviceHandle deviceHandle,
        unsigned int targetId,
        TLDfuImageProperty propertyId,
        unsigned long long* propertyValue,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.GetTargetImagePropertyUint64 ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetTargetImagePropertyUint64(
                                deviceHandle,
                                targetId,
                                propertyId,
                                propertyValue,
                                flags
                                );
            }

    TLSTATUS
    GetTargetImagePropertyString(
        TLDfuDeviceHandle deviceHandle,
        unsigned int targetId,
        TLDfuImageProperty propertyId,
        T_UNICHAR* stringBuffer,
        unsigned int stringBufferMaxItems,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.GetTargetImagePropertyString ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetTargetImagePropertyString(
                                deviceHandle,
                                targetId,
                                propertyId,
                                stringBuffer,
                                stringBufferMaxItems,
                                flags
                                );
            }


    TLSTATUS
    InterfaceVendorInRequest(
        TLDfuDeviceHandle deviceHandle,
        unsigned int bRequest,     
        unsigned int wValue,
        void* buffer,
        unsigned int bufferSize,
        unsigned int* bytesTransferred,
        TLTimeoutInterval timeout
        )
            {
                if ( nullptr == mFunc.InterfaceVendorInRequest ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.InterfaceVendorInRequest(
                                deviceHandle,
                                bRequest,
                                wValue,
                                buffer,
                                bufferSize,
                                bytesTransferred,
                                timeout
                                );
            }

    TLSTATUS
    InterfaceVendorOutRequest(
        TLDfuDeviceHandle deviceHandle,
        unsigned int bRequest,     
        unsigned int wValue,
        const void* data,                // in, optional
        unsigned int dataLength,
        unsigned int* bytesTransferred,  // out, optional
        TLTimeoutInterval timeout
        )
            {
                if ( nullptr == mFunc.InterfaceVendorOutRequest ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.InterfaceVendorOutRequest(
                                deviceHandle,
                                bRequest,
                                wValue,
                                data,
                                dataLength,
                                bytesTransferred,
                                timeout
                                );
            }


    TLSTATUS
    RebootDevice(
        TLDfuDeviceHandle deviceHandle,
        unsigned int runMode,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.RebootDevice ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.RebootDevice(
                                deviceHandle,
                                runMode,
                                flags
                                );
            }


    TLSTATUS
    StartUpgrade(
        TLDfuDeviceHandle deviceHandle,
        TLDfuImageHandle imageHandle,
        unsigned int targetId,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.StartUpgrade ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.StartUpgrade(
                                deviceHandle,
                                imageHandle,
                                targetId,
                                flags
                                );
            }


    TLSTATUS
    GetUpgradeStatus(
        TLDfuDeviceHandle deviceHandle,
        TLDfuUpgradeState* upgradeState,
        unsigned long long* currentBytes,
        unsigned long long* totalBytes,
        TLSTATUS* completionStatus
        )
            {
                if ( nullptr == mFunc.GetUpgradeStatus ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetUpgradeStatus(
                                deviceHandle,
                                upgradeState,
                                currentBytes,
                                totalBytes,
                                completionStatus
                                );
            }


    TLSTATUS
    FinishUpgrade(
        TLDfuDeviceHandle deviceHandle,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.FinishUpgrade ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.FinishUpgrade(
                                deviceHandle,
                                flags
                                );
            }


    TLSTATUS
    StartReadout(
        TLDfuDeviceHandle deviceHandle,
        unsigned int targetId,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.StartReadout ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.StartReadout(
                                deviceHandle,
                                targetId,
                                flags
                                );
            }


    TLSTATUS
    GetReadoutStatus(
        TLDfuDeviceHandle deviceHandle,
        TLDfuReadoutState* readoutState,
        unsigned long long* currentBytes,
        TLSTATUS* completionStatus
        )
            {
                if ( nullptr == mFunc.GetReadoutStatus ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetReadoutStatus(
                                deviceHandle,
                                readoutState,
                                currentBytes,
                                completionStatus
                                );
            }


    TLSTATUS
    FinishReadout(
        TLDfuDeviceHandle deviceHandle,
        TLDfuImageHandle* imageHandle,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.FinishReadout ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.FinishReadout(
                                deviceHandle,
                                imageHandle,
                                flags
                                );
            }


    TLSTATUS
    LoadFirmwareImageFromFile(
        const T_UNICHAR* filePathAndName,
        TLDfuImageType imageType,
        TLDfuImageHandle* imageHandle,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.LoadFirmwareImageFromFile ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.LoadFirmwareImageFromFile(
                                filePathAndName,
                                imageType,
                                imageHandle,
                                flags
                                );
            }


    TLSTATUS
    LoadFirmwareFromBuffer(
        const void* imageData,
        unsigned long long imageDataSize,
        TLDfuImageType imageType,
        TLDfuImageHandle* imageHandle,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.LoadFirmwareFromBuffer ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.LoadFirmwareFromBuffer(
                                imageData,
                                imageDataSize,
                                imageType,
                                imageHandle,
                                flags
                                );
            }


    TLSTATUS
    StoreFirmwareInBuffer(
        TLDfuImageHandle imageHandle,
        void* buffer,
        unsigned long long bufferSize,
        unsigned long long* bytesCopied,
        unsigned int flags
        )
            {
                if ( nullptr == mFunc.StoreFirmwareInBuffer ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.StoreFirmwareInBuffer(
                                imageHandle,
                                buffer,
                                bufferSize,
                                bytesCopied,
                                flags
                                );
            }


    TLSTATUS
    UnloadFirmwareImage(
        TLDfuImageHandle imageHandle
        )
            {
                if ( nullptr == mFunc.UnloadFirmwareImage ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.UnloadFirmwareImage(imageHandle);
            }



    TLSTATUS
    GetImagePropertyUint(
        TLDfuImageHandle imageHandle,
        TLDfuImageProperty propertyId,
        unsigned int* propertyValue
        )
            {
                if ( nullptr == mFunc.GetImagePropertyUint ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetImagePropertyUint(
                                imageHandle,
                                propertyId, 
                                propertyValue
                                );
            }


    TLSTATUS
    GetImagePropertyUint64(
        TLDfuImageHandle imageHandle,
        TLDfuImageProperty propertyId,
        unsigned long long* propertyValue
        )
            {
                if ( nullptr == mFunc.GetImagePropertyUint64 ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetImagePropertyUint64(
                                imageHandle,
                                propertyId,
                                propertyValue
                                );
            }


    TLSTATUS
    GetImagePropertyString(
        TLDfuImageHandle imageHandle,
        TLDfuImageProperty propertyId,
        T_UNICHAR* stringBuffer,
        unsigned int stringBufferMaxItems
        )
            {
                if ( nullptr == mFunc.GetDevicePropertyString ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.GetImagePropertyString(
                                imageHandle,
                                propertyId,
                                stringBuffer,
                                stringBufferMaxItems
                                );
            }


    TLSTATUS
    RegisterDeviceChangeCallback(
        const char* deviceFilterDescription,
        TLDFU_DeviceChangeCallback callback,
        void* callbackContext,
        TLDfuNotificationHandle* notificationHandle
        )
            {
                if ( nullptr == mFunc.RegisterDeviceChangeCallback ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.RegisterDeviceChangeCallback(
                                deviceFilterDescription,
                                callback,
                                callbackContext,
                                notificationHandle
                                );
            }


    TLSTATUS
    UnregisterDeviceChangeCallback(
        TLDfuNotificationHandle notificationHandle
        )
            {
                if ( nullptr == mFunc.UnregisterDeviceChangeCallback ) return TLSTATUS_NOT_AVAILABLE;
                return mFunc.UnregisterDeviceChangeCallback(
                                notificationHandle
                                );
            }


/////////////////////////////////////////
// Implementation
//
protected:

    // pointers to API functions
    struct FunctionPointers
    {
        F_TLDFU_GetApiVersion GetApiVersion;
        F_TLDFU_CheckApiVersion CheckApiVersion;

        F_TLDFU_GetUniCharSize GetUniCharSize;
        F_TLDFU_GetErrorText GetErrorText;

        F_TLDFU_StartFileLogging StartFileLogging;

        F_TLDFU_EnumerateUsbDfuDevices EnumerateUsbDfuDevices;
        F_TLDFU_CloseEnumeration CloseEnumeration;
        F_TLDFU_CompareDeviceInstance CompareDeviceInstance;

        F_TLDFU_OpenDevice OpenDevice; 
        F_TLDFU_CloseDevice CloseDevice;
        F_TLDFU_CheckDeviceConnection CheckDeviceConnection;
        F_TLDFU_GetDevicePropertyUint GetDevicePropertyUint;
        F_TLDFU_GetDevicePropertyString GetDevicePropertyString;
        F_TLDFU_InterfaceVendorInRequest InterfaceVendorInRequest;
        F_TLDFU_InterfaceVendorOutRequest InterfaceVendorOutRequest;
        F_TLDFU_RebootDevice RebootDevice;
        F_TLDFU_StartUpgrade StartUpgrade;
        F_TLDFU_GetUpgradeStatus GetUpgradeStatus;
        F_TLDFU_FinishUpgrade FinishUpgrade;
        F_TLDFU_StartReadout StartReadout;
        F_TLDFU_GetReadoutStatus GetReadoutStatus;
        F_TLDFU_FinishReadout FinishReadout;
        F_TLDFU_GetTargetImagePropertyUint GetTargetImagePropertyUint;
        F_TLDFU_GetTargetImagePropertyUint64 GetTargetImagePropertyUint64;
        F_TLDFU_GetTargetImagePropertyString GetTargetImagePropertyString;

        F_TLDFU_LoadFirmwareImageFromFile LoadFirmwareImageFromFile;
        F_TLDFU_LoadFirmwareFromBuffer LoadFirmwareFromBuffer;
        F_TLDFU_StoreFirmwareInBuffer StoreFirmwareInBuffer;
        F_TLDFU_UnloadFirmwareImage UnloadFirmwareImage;
        F_TLDFU_GetImagePropertyUint GetImagePropertyUint;
        F_TLDFU_GetImagePropertyUint64 GetImagePropertyUint64;
        F_TLDFU_GetImagePropertyString GetImagePropertyString;

        F_TLDFU_RegisterDeviceChangeCallback RegisterDeviceChangeCallback;
        F_TLDFU_UnregisterDeviceChangeCallback UnregisterDeviceChangeCallback;
    };


    void
    ClearPointers();

    TLSTATUS
    LoadApiFunctions(
        bool checkApiCompatibility,
        unsigned int majorApiVersion,
        unsigned int minorApiVersion
        );
        
////////////////////////////////////////
// Data
//
protected:

    // the dynamic link library
    TLDynlibLoader mDynlib;

    // cached API version
    unsigned int mApiVersion {0};

    // API functions
    FunctionPointers mFunc;

    // The one and only instance.
    static TLDfuApi mInstance;
};


//
// map C function names to API loader
//
#define TLDFU_GetApiVersion                     TLDfuApi::Instance().GetApiVersion
#define TLDFU_CheckApiVersion                   TLDfuApi::Instance().CheckApiVersion
#define TLDFU_GetUniCharSize                    TLDfuApi::Instance().GetUniCharSize
#define TLDFU_GetErrorText                      TLDfuApi::Instance().GetErrorText
#define TLDFU_StartFileLogging                  TLDfuApi::Instance().StartFileLogging
#define TLDFU_EnumerateUsbDfuDevices            TLDfuApi::Instance().EnumerateUsbDfuDevices
#define TLDFU_CloseEnumeration                  TLDfuApi::Instance().CloseEnumeration
#define TLDFU_CompareDeviceInstance             TLDfuApi::Instance().CompareDeviceInstance
#define TLDFU_OpenDevice                        TLDfuApi::Instance().OpenDevice
#define TLDFU_CloseDevice                       TLDfuApi::Instance().CloseDevice
#define TLDFU_CheckDeviceConnection             TLDfuApi::Instance().CheckDeviceConnection
#define TLDFU_GetDevicePropertyUint             TLDfuApi::Instance().GetDevicePropertyUint
#define TLDFU_GetDevicePropertyString           TLDfuApi::Instance().GetDevicePropertyString
#define TLDFU_GetTargetImagePropertyUint        TLDfuApi::Instance().GetTargetImagePropertyUint
#define TLDFU_GetTargetImagePropertyUint64      TLDfuApi::Instance().GetTargetImagePropertyUint64
#define TLDFU_GetTargetImagePropertyString      TLDfuApi::Instance().GetTargetImagePropertyString
#define TLDFU_InterfaceVendorInRequest          TLDfuApi::Instance().InterfaceVendorInRequest
#define TLDFU_InterfaceVendorOutRequest         TLDfuApi::Instance().InterfaceVendorOutRequest
#define TLDFU_RebootDevice                      TLDfuApi::Instance().RebootDevice
#define TLDFU_StartUpgrade                      TLDfuApi::Instance().StartUpgrade
#define TLDFU_GetUpgradeStatus                  TLDfuApi::Instance().GetUpgradeStatus
#define TLDFU_FinishUpgrade                     TLDfuApi::Instance().FinishUpgrade
#define TLDFU_StartReadout                      TLDfuApi::Instance().StartReadout
#define TLDFU_GetReadoutStatus                  TLDfuApi::Instance().GetReadoutStatus
#define TLDFU_FinishReadout                     TLDfuApi::Instance().FinishReadout
#define TLDFU_LoadFirmwareImageFromFile         TLDfuApi::Instance().LoadFirmwareImageFromFile
#define TLDFU_LoadFirmwareFromBuffer            TLDfuApi::Instance().LoadFirmwareFromBuffer
#define TLDFU_StoreFirmwareInBuffer             TLDfuApi::Instance().StoreFirmwareInBuffer
#define TLDFU_UnloadFirmwareImage               TLDfuApi::Instance().UnloadFirmwareImage
#define TLDFU_GetImagePropertyUint              TLDfuApi::Instance().GetImagePropertyUint
#define TLDFU_GetImagePropertyUint64            TLDfuApi::Instance().GetImagePropertyUint64
#define TLDFU_GetImagePropertyString            TLDfuApi::Instance().GetImagePropertyString
#define TLDFU_RegisterDeviceChangeCallback      TLDfuApi::Instance().RegisterDeviceChangeCallback
#define TLDFU_UnregisterDeviceChangeCallback    TLDfuApi::Instance().UnregisterDeviceChangeCallback



#endif 

/*** EOF ***/

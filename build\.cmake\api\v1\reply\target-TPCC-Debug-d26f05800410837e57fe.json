{"artifacts": [{"path": "Source/M Control Center.exe"}, {"path": "Source/M Control Center.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "add_dependencies", "qt6_add_ui", "qt_add_ui", "target_compile_definitions", "include_directories", "add_include", "target_include_directories", "target_sources"], "files": ["D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "Source/CMakeLists.txt", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "D:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 84, "parent": 0}, {"command": 2, "file": 0, "line": 938, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 688, "parent": 3}, {"command": 4, "file": 1, "line": 158, "parent": 0}, {"command": 5, "file": 1, "line": 146, "parent": 0}, {"command": 8, "file": 1, "line": 80, "parent": 0}, {"file": 4, "parent": 7}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 3, "parent": 9}, {"command": 7, "file": 3, "line": 55, "parent": 10}, {"file": 2, "parent": 11}, {"command": 6, "file": 2, "line": 61, "parent": 12}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 6, "parent": 14}, {"command": 7, "file": 6, "line": 55, "parent": 15}, {"file": 5, "parent": 16}, {"command": 6, "file": 5, "line": 61, "parent": 17}, {"command": 5, "file": 0, "line": 640, "parent": 2}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 8, "parent": 20}, {"command": 7, "file": 8, "line": 57, "parent": 21}, {"file": 7, "parent": 22}, {"command": 6, "file": 7, "line": 61, "parent": 23}, {"command": 7, "file": 8, "line": 45, "parent": 21}, {"file": 13, "parent": 25}, {"command": 10, "file": 13, "line": 46, "parent": 26}, {"command": 9, "file": 12, "line": 137, "parent": 27}, {"command": 8, "file": 11, "line": 76, "parent": 28}, {"file": 10, "parent": 29}, {"command": 7, "file": 10, "line": 55, "parent": 30}, {"file": 9, "parent": 31}, {"command": 6, "file": 9, "line": 61, "parent": 32}, {"command": 6, "file": 9, "line": 83, "parent": 32}, {"command": 7, "file": 6, "line": 43, "parent": 15}, {"file": 16, "parent": 35}, {"command": 10, "file": 16, "line": 45, "parent": 36}, {"command": 9, "file": 12, "line": 137, "parent": 37}, {"command": 8, "file": 11, "line": 76, "parent": 38}, {"file": 15, "parent": 39}, {"command": 7, "file": 15, "line": 55, "parent": 40}, {"file": 14, "parent": 41}, {"command": 6, "file": 14, "line": 61, "parent": 42}, {"command": 13, "file": 1, "line": 90, "parent": 0}, {"command": 12, "file": 17, "line": 340, "parent": 44}, {"command": 11, "file": 17, "line": 325, "parent": 45}, {"command": 14, "file": 1, "line": 154, "parent": 0}, {"command": 16, "file": 1, "line": 51, "parent": 0}, {"command": 15, "file": 1, "line": 16, "parent": 48}, {"command": 16, "file": 1, "line": 17, "parent": 48}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 16, "file": 1, "line": 17, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 54}, {"command": 15, "file": 1, "line": 16, "parent": 54}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 16, "file": 1, "line": 17, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 64}, {"command": 15, "file": 1, "line": 16, "parent": 64}, {"command": 15, "file": 1, "line": 16, "parent": 64}, {"command": 15, "file": 1, "line": 16, "parent": 64}, {"command": 15, "file": 1, "line": 16, "parent": 50}, {"command": 15, "file": 1, "line": 16, "parent": 48}, {"command": 16, "file": 1, "line": 17, "parent": 48}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 73}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 76}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 79}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 82}, {"command": 15, "file": 1, "line": 16, "parent": 82}, {"command": 15, "file": 1, "line": 16, "parent": 82}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 87}, {"command": 15, "file": 1, "line": 16, "parent": 87}, {"command": 15, "file": 1, "line": 16, "parent": 87}, {"command": 15, "file": 1, "line": 16, "parent": 87}, {"command": 15, "file": 1, "line": 16, "parent": 87}, {"command": 15, "file": 1, "line": 16, "parent": 87}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 96}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 99}, {"command": 15, "file": 1, "line": 16, "parent": 99}, {"command": 15, "file": 1, "line": 16, "parent": 99}, {"command": 15, "file": 1, "line": 16, "parent": 99}, {"command": 16, "file": 1, "line": 17, "parent": 99}, {"command": 15, "file": 1, "line": 16, "parent": 104}, {"command": 15, "file": 1, "line": 16, "parent": 104}, {"command": 15, "file": 1, "line": 16, "parent": 104}, {"command": 15, "file": 1, "line": 16, "parent": 104}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 110}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 127}, {"command": 15, "file": 1, "line": 16, "parent": 127}, {"command": 15, "file": 1, "line": 16, "parent": 127}, {"command": 15, "file": 1, "line": 16, "parent": 127}, {"command": 15, "file": 1, "line": 16, "parent": 127}, {"command": 15, "file": 1, "line": 16, "parent": 127}, {"command": 15, "file": 1, "line": 16, "parent": 127}, {"command": 15, "file": 1, "line": 16, "parent": 127}, {"command": 15, "file": 1, "line": 16, "parent": 127}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 16, "file": 1, "line": 17, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 140}, {"command": 15, "file": 1, "line": 16, "parent": 140}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 16, "file": 1, "line": 17, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 144}, {"command": 15, "file": 1, "line": 16, "parent": 144}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 148}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 151}, {"command": 15, "file": 1, "line": 16, "parent": 71}, {"command": 16, "file": 1, "line": 17, "parent": 71}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 48}, {"command": 16, "file": 1, "line": 17, "parent": 48}, {"command": 15, "file": 1, "line": 16, "parent": 165}, {"command": 16, "file": 1, "line": 17, "parent": 165}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 48}, {"command": 16, "file": 1, "line": 17, "parent": 48}, {"command": 15, "file": 1, "line": 16, "parent": 171}, {"command": 16, "file": 1, "line": 17, "parent": 171}, {"command": 15, "file": 1, "line": 16, "parent": 173}, {"command": 15, "file": 1, "line": 16, "parent": 173}, {"command": 15, "file": 1, "line": 16, "parent": 171}, {"command": 16, "file": 1, "line": 17, "parent": 171}, {"command": 15, "file": 1, "line": 16, "parent": 177}, {"command": 15, "file": 1, "line": 16, "parent": 177}, {"command": 15, "file": 1, "line": 16, "parent": 177}, {"command": 15, "file": 1, "line": 16, "parent": 177}, {"command": 15, "file": 1, "line": 16, "parent": 171}, {"command": 16, "file": 1, "line": 17, "parent": 171}, {"command": 15, "file": 1, "line": 16, "parent": 183}, {"command": 15, "file": 1, "line": 16, "parent": 183}, {"command": 15, "file": 1, "line": 16, "parent": 171}, {"command": 16, "file": 1, "line": 17, "parent": 171}, {"command": 15, "file": 1, "line": 16, "parent": 187}, {"command": 15, "file": 1, "line": 16, "parent": 187}, {"command": 15, "file": 1, "line": 16, "parent": 171}, {"command": 16, "file": 1, "line": 17, "parent": 171}, {"command": 15, "file": 1, "line": 16, "parent": 191}, {"command": 15, "file": 1, "line": 16, "parent": 191}, {"command": 15, "file": 1, "line": 16, "parent": 171}, {"command": 16, "file": 1, "line": 17, "parent": 171}, {"command": 15, "file": 1, "line": 16, "parent": 195}, {"command": 15, "file": 1, "line": 16, "parent": 195}, {"command": 15, "file": 1, "line": 16, "parent": 195}, {"command": 15, "file": 1, "line": 16, "parent": 195}, {"command": 15, "file": 1, "line": 16, "parent": 171}, {"command": 16, "file": 1, "line": 17, "parent": 171}, {"command": 15, "file": 1, "line": 16, "parent": 201}, {"command": 15, "file": 1, "line": 16, "parent": 201}, {"command": 15, "file": 1, "line": 16, "parent": 48}, {"command": 16, "file": 1, "line": 17, "parent": 48}, {"command": 15, "file": 1, "line": 16, "parent": 205}, {"command": 15, "file": 1, "line": 16, "parent": 205}, {"command": 15, "file": 1, "line": 16, "parent": 48}, {"command": 16, "file": 1, "line": 17, "parent": 48}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 16, "file": 1, "line": 17, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 211}, {"command": 16, "file": 1, "line": 17, "parent": 211}, {"command": 15, "file": 1, "line": 16, "parent": 213}, {"command": 16, "file": 1, "line": 17, "parent": 213}, {"command": 15, "file": 1, "line": 16, "parent": 215}, {"command": 15, "file": 1, "line": 16, "parent": 213}, {"command": 15, "file": 1, "line": 16, "parent": 213}, {"command": 15, "file": 1, "line": 16, "parent": 213}, {"command": 15, "file": 1, "line": 16, "parent": 211}, {"command": 16, "file": 1, "line": 17, "parent": 211}, {"command": 15, "file": 1, "line": 16, "parent": 221}, {"command": 15, "file": 1, "line": 16, "parent": 221}, {"command": 15, "file": 1, "line": 16, "parent": 221}, {"command": 15, "file": 1, "line": 16, "parent": 221}, {"command": 15, "file": 1, "line": 16, "parent": 221}, {"command": 15, "file": 1, "line": 16, "parent": 221}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 16, "file": 1, "line": 17, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 229}, {"command": 15, "file": 1, "line": 16, "parent": 229}, {"command": 15, "file": 1, "line": 16, "parent": 229}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 16, "file": 1, "line": 17, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 234}, {"command": 15, "file": 1, "line": 16, "parent": 234}, {"command": 15, "file": 1, "line": 16, "parent": 234}, {"command": 15, "file": 1, "line": 16, "parent": 234}, {"command": 15, "file": 1, "line": 16, "parent": 234}, {"command": 15, "file": 1, "line": 16, "parent": 234}, {"command": 15, "file": 1, "line": 16, "parent": 234}, {"command": 15, "file": 1, "line": 16, "parent": 234}, {"command": 15, "file": 1, "line": 16, "parent": 234}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 16, "file": 1, "line": 17, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 245}, {"command": 15, "file": 1, "line": 16, "parent": 245}, {"command": 15, "file": 1, "line": 16, "parent": 245}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 16, "file": 1, "line": 17, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 250}, {"command": 15, "file": 1, "line": 16, "parent": 250}, {"command": 15, "file": 1, "line": 16, "parent": 250}, {"command": 15, "file": 1, "line": 16, "parent": 250}, {"command": 15, "file": 1, "line": 16, "parent": 250}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 16, "file": 1, "line": 17, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 16, "file": 1, "line": 17, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 268}, {"command": 15, "file": 1, "line": 16, "parent": 268}, {"command": 15, "file": 1, "line": 16, "parent": 268}, {"command": 15, "file": 1, "line": 16, "parent": 268}, {"command": 15, "file": 1, "line": 16, "parent": 48}, {"command": 16, "file": 1, "line": 17, "parent": 48}, {"command": 15, "file": 1, "line": 16, "parent": 274}, {"command": 15, "file": 1, "line": 16, "parent": 274}, {"command": 15, "file": 1, "line": 16, "parent": 274}, {"command": 16, "file": 1, "line": 17, "parent": 274}, {"command": 15, "file": 1, "line": 16, "parent": 278}, {"command": 15, "file": 1, "line": 16, "parent": 48}, {"command": 16, "file": 1, "line": 17, "parent": 48}, {"command": 15, "file": 1, "line": 16, "parent": 281}, {"command": 15, "file": 1, "line": 16, "parent": 281}, {"command": 16, "file": 1, "line": 17, "parent": 281}, {"command": 15, "file": 1, "line": 16, "parent": 284}, {"command": 15, "file": 1, "line": 16, "parent": 284}, {"command": 15, "file": 1, "line": 16, "parent": 48}, {"command": 16, "file": 1, "line": 17, "parent": 48}, {"command": 15, "file": 1, "line": 16, "parent": 288}, {"command": 15, "file": 1, "line": 16, "parent": 288}, {"command": 16, "file": 1, "line": 17, "parent": 288}, {"command": 15, "file": 1, "line": 16, "parent": 291}, {"command": 15, "file": 1, "line": 16, "parent": 291}, {"command": 15, "file": 1, "line": 16, "parent": 288}, {"command": 16, "file": 1, "line": 17, "parent": 288}, {"command": 15, "file": 1, "line": 16, "parent": 295}, {"command": 15, "file": 1, "line": 16, "parent": 295}, {"command": 15, "file": 1, "line": 16, "parent": 295}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 17, "file": 17, "line": 174, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}, {"command": 18, "file": 17, "line": 229, "parent": 45}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 19, "fragment": "-Zc:__cplusplus"}, {"backtrace": 19, "fragment": "-permissive-"}, {"backtrace": 19, "fragment": "-utf-8"}], "defines": [{"backtrace": 47, "define": "APP_VERSION=\"1.1.8\""}, {"backtrace": 19, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_NETWORK_LIB"}, {"backtrace": 6, "define": "QT_SVG_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 19, "define": "UNICODE"}, {"backtrace": 19, "define": "WIN32"}, {"backtrace": 19, "define": "WIN64"}, {"backtrace": 19, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 19, "define": "_UNICODE"}, {"backtrace": 19, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Documents/TMP/build/Source/TPCC_autogen/include"}, {"backtrace": 49, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction"}, {"backtrace": 51, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/AppSettings"}, {"backtrace": 52, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/AutoStartManager"}, {"backtrace": 53, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/CTL"}, {"backtrace": 55, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/BlockingQueue"}, {"backtrace": 56, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/Singleton"}, {"backtrace": 57, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/DebugManager"}, {"backtrace": 58, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/GlobalFont"}, {"backtrace": 59, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/SingleInstanceManager"}, {"backtrace": 60, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Solo"}, {"backtrace": 61, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/TrialManager"}, {"backtrace": 62, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/USBAudioManager"}, {"backtrace": 63, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater"}, {"backtrace": 65, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterBase"}, {"backtrace": 66, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFactory"}, {"backtrace": 67, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFirmwareM1"}, {"backtrace": 68, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterSoftware"}, {"backtrace": 69, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Workspace"}, {"backtrace": 70, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget"}, {"backtrace": 72, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Battery"}, {"backtrace": 74, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1"}, {"backtrace": 75, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox"}, {"backtrace": 77, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox/ButtonBoxS1M1"}, {"backtrace": 78, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Circle"}, {"backtrace": 80, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Circle/CircleS1M1"}, {"backtrace": 81, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox"}, {"backtrace": 83, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M1"}, {"backtrace": 84, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M2"}, {"backtrace": 85, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M3"}, {"backtrace": 86, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial"}, {"backtrace": 88, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M1"}, {"backtrace": 89, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M2"}, {"backtrace": 90, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M3"}, {"backtrace": 91, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M4"}, {"backtrace": 92, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M5"}, {"backtrace": 93, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M6"}, {"backtrace": 94, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/FramelessWindow"}, {"backtrace": 95, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Menu"}, {"backtrace": 97, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Menu/MenuS1M1"}, {"backtrace": 98, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox"}, {"backtrace": 100, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS1M1"}, {"backtrace": 101, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS2M1"}, {"backtrace": 102, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS3M1"}, {"backtrace": 103, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget"}, {"backtrace": 105, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1"}, {"backtrace": 106, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2"}, {"backtrace": 107, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3"}, {"backtrace": 108, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4"}, {"backtrace": 109, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton"}, {"backtrace": 111, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M1"}, {"backtrace": 112, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10"}, {"backtrace": 113, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M11"}, {"backtrace": 114, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M12"}, {"backtrace": 115, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M13"}, {"backtrace": 116, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M14"}, {"backtrace": 117, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M15"}, {"backtrace": 118, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M2"}, {"backtrace": 119, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M3"}, {"backtrace": 120, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M4"}, {"backtrace": 121, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M5"}, {"backtrace": 122, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M6"}, {"backtrace": 123, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M7"}, {"backtrace": 124, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M8"}, {"backtrace": 125, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M9"}, {"backtrace": 126, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup"}, {"backtrace": 128, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1"}, {"backtrace": 129, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2"}, {"backtrace": 130, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3"}, {"backtrace": 131, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4"}, {"backtrace": 132, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5"}, {"backtrace": 133, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6"}, {"backtrace": 134, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7"}, {"backtrace": 135, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8"}, {"backtrace": 136, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9"}, {"backtrace": 137, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider"}, {"backtrace": 139, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider"}, {"backtrace": 141, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS1M1"}, {"backtrace": 142, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS2M1"}, {"backtrace": 143, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider"}, {"backtrace": 145, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M1"}, {"backtrace": 146, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M2"}, {"backtrace": 147, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget"}, {"backtrace": 149, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget/TabWidgetS1M1"}, {"backtrace": 150, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton"}, {"backtrace": 152, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton/ToolButtonS1M1"}, {"backtrace": 153, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter"}, {"backtrace": 155, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M1"}, {"backtrace": 156, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M2"}, {"backtrace": 157, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M3"}, {"backtrace": 158, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M4"}, {"backtrace": 159, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M5"}, {"backtrace": 160, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M6"}, {"backtrace": 161, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M7"}, {"backtrace": 162, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M8"}, {"backtrace": 163, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS2M1"}, {"backtrace": 164, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector"}, {"backtrace": 166, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView"}, {"backtrace": 168, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase"}, {"backtrace": 169, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1"}, {"backtrace": 170, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField"}, {"backtrace": 172, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect"}, {"backtrace": 174, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectBase1"}, {"backtrace": 175, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectS1M1"}, {"backtrace": 176, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead"}, {"backtrace": 178, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase1"}, {"backtrace": 179, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase2"}, {"backtrace": 180, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M1"}, {"backtrace": 181, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M2"}, {"backtrace": 182, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput"}, {"backtrace": 184, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputBase1"}, {"backtrace": 185, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputS1M1"}, {"backtrace": 186, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback"}, {"backtrace": 188, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackBase1"}, {"backtrace": 189, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackS1M1"}, {"backtrace": 190, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer"}, {"backtrace": 192, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerBase1"}, {"backtrace": 193, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerS1M1"}, {"backtrace": 194, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin"}, {"backtrace": 196, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase1"}, {"backtrace": 197, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase2"}, {"backtrace": 198, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M1"}, {"backtrace": 199, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS2M1"}, {"backtrace": 200, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput"}, {"backtrace": 202, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputBase1"}, {"backtrace": 203, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputS1M1"}, {"backtrace": 204, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceMainWindow"}, {"backtrace": 206, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_Base"}, {"backtrace": 207, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62"}, {"backtrace": 208, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget"}, {"backtrace": 210, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice"}, {"backtrace": 212, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll"}, {"backtrace": 214, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/AutoGain"}, {"backtrace": 216, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/AutoGain/AutoGainS1M1"}, {"backtrace": 217, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAbout1"}, {"backtrace": 218, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAudio1"}, {"backtrace": 219, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetSystem1"}, {"backtrace": 220, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62"}, {"backtrace": 222, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget1"}, {"backtrace": 223, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget2"}, {"backtrace": 224, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget3"}, {"backtrace": 225, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget5"}, {"backtrace": 226, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget6"}, {"backtrace": 227, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget7"}, {"backtrace": 228, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect"}, {"backtrace": 230, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectBase"}, {"backtrace": 231, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M1"}, {"backtrace": 232, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2"}, {"backtrace": 233, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput"}, {"backtrace": 235, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputBase"}, {"backtrace": 236, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M1"}, {"backtrace": 237, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2"}, {"backtrace": 238, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M3"}, {"backtrace": 239, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M4"}, {"backtrace": 240, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M5"}, {"backtrace": 241, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M6"}, {"backtrace": 242, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M1"}, {"backtrace": 243, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M2"}, {"backtrace": 244, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback"}, {"backtrace": 246, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackBase"}, {"backtrace": 247, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M1"}, {"backtrace": 248, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M2"}, {"backtrace": 249, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer"}, {"backtrace": 251, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerBase"}, {"backtrace": 252, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M1"}, {"backtrace": 253, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M2"}, {"backtrace": 254, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M3"}, {"backtrace": 255, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M4"}, {"backtrace": 256, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin"}, {"backtrace": 258, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginBase"}, {"backtrace": 259, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M1"}, {"backtrace": 260, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M2"}, {"backtrace": 261, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M3"}, {"backtrace": 262, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M4"}, {"backtrace": 263, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M6"}, {"backtrace": 264, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M7"}, {"backtrace": 265, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M8"}, {"backtrace": 266, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M9"}, {"backtrace": 267, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput"}, {"backtrace": 269, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputBase"}, {"backtrace": 270, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M1"}, {"backtrace": 271, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M2"}, {"backtrace": 272, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M3"}, {"backtrace": 273, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource"}, {"backtrace": 275, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Font"}, {"backtrace": 276, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Icon"}, {"backtrace": 277, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image"}, {"backtrace": 279, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image/PushButtonGroup"}, {"backtrace": 280, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio"}, {"backtrace": 282, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/API"}, {"backtrace": 283, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/SDK"}, {"backtrace": 285, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/SDK/MAC"}, {"backtrace": 286, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/SDK/WIN"}, {"backtrace": 287, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID"}, {"backtrace": 289, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/API"}, {"backtrace": 290, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/Device"}, {"backtrace": 292, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceBase"}, {"backtrace": 293, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceM62"}, {"backtrace": 294, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK"}, {"backtrace": 296, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK/linux"}, {"backtrace": 297, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK/mac"}, {"backtrace": 298, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK/win"}, {"backtrace": 299, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b3fe1d"}, {"backtrace": 300, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/82f9cd"}, {"backtrace": 301, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/472976"}, {"backtrace": 302, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b0b655"}, {"backtrace": 303, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/7be222"}, {"backtrace": 304, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ea8b46"}, {"backtrace": 305, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/9fcd8a"}, {"backtrace": 306, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f530e6"}, {"backtrace": 307, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f0adbc"}, {"backtrace": 308, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/8d9aaf"}, {"backtrace": 309, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/0de7ed"}, {"backtrace": 310, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/6115e1"}, {"backtrace": 311, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/48f683"}, {"backtrace": 312, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/8ab9a5"}, {"backtrace": 313, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a3ef9a"}, {"backtrace": 314, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/57705c"}, {"backtrace": 315, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ff46f0"}, {"backtrace": 316, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2c1934"}, {"backtrace": 317, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/46d670"}, {"backtrace": 318, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/e40100"}, {"backtrace": 319, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/343d8f"}, {"backtrace": 320, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fff6ec"}, {"backtrace": 321, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/335dd8"}, {"backtrace": 322, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b53517"}, {"backtrace": 323, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/d1d245"}, {"backtrace": 324, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5102ab"}, {"backtrace": 325, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ade5cc"}, {"backtrace": 326, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/27a07f"}, {"backtrace": 327, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a555d1"}, {"backtrace": 328, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/4e73cc"}, {"backtrace": 329, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/84036a"}, {"backtrace": 330, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f5cab1"}, {"backtrace": 331, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/e7c615"}, {"backtrace": 332, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/48c050"}, {"backtrace": 333, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fc3e57"}, {"backtrace": 334, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/eeb9c2"}, {"backtrace": 335, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ddc477"}, {"backtrace": 336, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/339435"}, {"backtrace": 337, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a0067f"}, {"backtrace": 338, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2ff45e"}, {"backtrace": 339, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fa7ae3"}, {"backtrace": 340, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/4a7476"}, {"backtrace": 341, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/447ecf"}, {"backtrace": 342, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5981a4"}, {"backtrace": 343, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/d5c746"}, {"backtrace": 344, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/c85f3d"}, {"backtrace": 345, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5554d4"}, {"backtrace": 346, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5a1417"}, {"backtrace": 347, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2024df"}, {"backtrace": 19, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 19, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 19, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtSvg"}], "language": "CXX", "languageStandard": {"backtraces": [19], "standard": "17"}, "sourceIndexes": [0, 1, 6, 8, 10, 13, 15, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 59, 62, 65, 66, 71, 73, 76, 78, 80, 83, 85, 88, 91, 93, 96, 99, 102, 105, 108, 111, 114, 117, 119, 122, 125, 127, 130, 133, 136, 139, 141, 144, 147, 149, 152, 155, 158, 161, 164, 166, 169, 172, 175, 176, 179, 182, 184, 186, 188, 190, 192, 194, 196, 198, 200, 202, 204, 206, 208, 210, 212, 215, 218, 221, 224, 226, 228, 230, 232, 234, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 257, 260, 263, 266, 269, 272, 275, 278, 281, 283, 285, 287, 289, 291, 293, 295, 297, 299, 301, 303, 305, 307, 309, 313, 315, 319, 321, 323, 325, 327, 329, 331, 334, 336, 338, 340, 341, 344, 346, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 432, 484]}, {"compileCommandFragments": [{"fragment": "-DWIN32 -D_DEBUG"}], "defines": [{"backtrace": 47, "define": "APP_VERSION=\"1.1.8\""}, {"backtrace": 19, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_NETWORK_LIB"}, {"backtrace": 6, "define": "QT_SVG_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 19, "define": "UNICODE"}, {"backtrace": 19, "define": "WIN32"}, {"backtrace": 19, "define": "WIN64"}, {"backtrace": 19, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 19, "define": "_UNICODE"}, {"backtrace": 19, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Documents/TMP/build/Source/TPCC_autogen/include"}, {"backtrace": 49, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction"}, {"backtrace": 51, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/AppSettings"}, {"backtrace": 52, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/AutoStartManager"}, {"backtrace": 53, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/CTL"}, {"backtrace": 55, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/BlockingQueue"}, {"backtrace": 56, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/Singleton"}, {"backtrace": 57, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/DebugManager"}, {"backtrace": 58, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/GlobalFont"}, {"backtrace": 59, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/SingleInstanceManager"}, {"backtrace": 60, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Solo"}, {"backtrace": 61, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/TrialManager"}, {"backtrace": 62, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/USBAudioManager"}, {"backtrace": 63, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater"}, {"backtrace": 65, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterBase"}, {"backtrace": 66, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFactory"}, {"backtrace": 67, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFirmwareM1"}, {"backtrace": 68, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterSoftware"}, {"backtrace": 69, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Workspace"}, {"backtrace": 70, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget"}, {"backtrace": 72, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Battery"}, {"backtrace": 74, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1"}, {"backtrace": 75, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox"}, {"backtrace": 77, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox/ButtonBoxS1M1"}, {"backtrace": 78, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Circle"}, {"backtrace": 80, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Circle/CircleS1M1"}, {"backtrace": 81, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox"}, {"backtrace": 83, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M1"}, {"backtrace": 84, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M2"}, {"backtrace": 85, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M3"}, {"backtrace": 86, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial"}, {"backtrace": 88, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M1"}, {"backtrace": 89, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M2"}, {"backtrace": 90, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M3"}, {"backtrace": 91, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M4"}, {"backtrace": 92, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M5"}, {"backtrace": 93, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M6"}, {"backtrace": 94, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/FramelessWindow"}, {"backtrace": 95, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Menu"}, {"backtrace": 97, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Menu/MenuS1M1"}, {"backtrace": 98, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox"}, {"backtrace": 100, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS1M1"}, {"backtrace": 101, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS2M1"}, {"backtrace": 102, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS3M1"}, {"backtrace": 103, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget"}, {"backtrace": 105, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1"}, {"backtrace": 106, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2"}, {"backtrace": 107, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3"}, {"backtrace": 108, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4"}, {"backtrace": 109, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton"}, {"backtrace": 111, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M1"}, {"backtrace": 112, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10"}, {"backtrace": 113, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M11"}, {"backtrace": 114, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M12"}, {"backtrace": 115, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M13"}, {"backtrace": 116, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M14"}, {"backtrace": 117, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M15"}, {"backtrace": 118, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M2"}, {"backtrace": 119, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M3"}, {"backtrace": 120, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M4"}, {"backtrace": 121, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M5"}, {"backtrace": 122, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M6"}, {"backtrace": 123, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M7"}, {"backtrace": 124, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M8"}, {"backtrace": 125, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M9"}, {"backtrace": 126, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup"}, {"backtrace": 128, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1"}, {"backtrace": 129, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2"}, {"backtrace": 130, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3"}, {"backtrace": 131, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4"}, {"backtrace": 132, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5"}, {"backtrace": 133, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6"}, {"backtrace": 134, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7"}, {"backtrace": 135, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8"}, {"backtrace": 136, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9"}, {"backtrace": 137, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider"}, {"backtrace": 139, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider"}, {"backtrace": 141, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS1M1"}, {"backtrace": 142, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS2M1"}, {"backtrace": 143, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider"}, {"backtrace": 145, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M1"}, {"backtrace": 146, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M2"}, {"backtrace": 147, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget"}, {"backtrace": 149, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget/TabWidgetS1M1"}, {"backtrace": 150, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton"}, {"backtrace": 152, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton/ToolButtonS1M1"}, {"backtrace": 153, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter"}, {"backtrace": 155, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M1"}, {"backtrace": 156, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M2"}, {"backtrace": 157, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M3"}, {"backtrace": 158, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M4"}, {"backtrace": 159, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M5"}, {"backtrace": 160, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M6"}, {"backtrace": 161, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M7"}, {"backtrace": 162, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M8"}, {"backtrace": 163, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS2M1"}, {"backtrace": 164, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector"}, {"backtrace": 166, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView"}, {"backtrace": 168, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase"}, {"backtrace": 169, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1"}, {"backtrace": 170, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField"}, {"backtrace": 172, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect"}, {"backtrace": 174, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectBase1"}, {"backtrace": 175, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectS1M1"}, {"backtrace": 176, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead"}, {"backtrace": 178, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase1"}, {"backtrace": 179, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase2"}, {"backtrace": 180, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M1"}, {"backtrace": 181, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M2"}, {"backtrace": 182, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput"}, {"backtrace": 184, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputBase1"}, {"backtrace": 185, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputS1M1"}, {"backtrace": 186, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback"}, {"backtrace": 188, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackBase1"}, {"backtrace": 189, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackS1M1"}, {"backtrace": 190, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer"}, {"backtrace": 192, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerBase1"}, {"backtrace": 193, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerS1M1"}, {"backtrace": 194, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin"}, {"backtrace": 196, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase1"}, {"backtrace": 197, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase2"}, {"backtrace": 198, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M1"}, {"backtrace": 199, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS2M1"}, {"backtrace": 200, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput"}, {"backtrace": 202, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputBase1"}, {"backtrace": 203, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputS1M1"}, {"backtrace": 204, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceMainWindow"}, {"backtrace": 206, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_Base"}, {"backtrace": 207, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62"}, {"backtrace": 208, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget"}, {"backtrace": 210, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice"}, {"backtrace": 212, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll"}, {"backtrace": 214, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/AutoGain"}, {"backtrace": 216, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/AutoGain/AutoGainS1M1"}, {"backtrace": 217, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAbout1"}, {"backtrace": 218, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAudio1"}, {"backtrace": 219, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetSystem1"}, {"backtrace": 220, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62"}, {"backtrace": 222, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget1"}, {"backtrace": 223, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget2"}, {"backtrace": 224, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget3"}, {"backtrace": 225, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget5"}, {"backtrace": 226, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget6"}, {"backtrace": 227, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget7"}, {"backtrace": 228, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect"}, {"backtrace": 230, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectBase"}, {"backtrace": 231, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M1"}, {"backtrace": 232, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2"}, {"backtrace": 233, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput"}, {"backtrace": 235, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputBase"}, {"backtrace": 236, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M1"}, {"backtrace": 237, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2"}, {"backtrace": 238, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M3"}, {"backtrace": 239, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M4"}, {"backtrace": 240, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M5"}, {"backtrace": 241, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M6"}, {"backtrace": 242, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M1"}, {"backtrace": 243, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M2"}, {"backtrace": 244, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback"}, {"backtrace": 246, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackBase"}, {"backtrace": 247, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M1"}, {"backtrace": 248, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M2"}, {"backtrace": 249, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer"}, {"backtrace": 251, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerBase"}, {"backtrace": 252, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M1"}, {"backtrace": 253, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M2"}, {"backtrace": 254, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M3"}, {"backtrace": 255, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M4"}, {"backtrace": 256, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin"}, {"backtrace": 258, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginBase"}, {"backtrace": 259, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M1"}, {"backtrace": 260, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M2"}, {"backtrace": 261, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M3"}, {"backtrace": 262, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M4"}, {"backtrace": 263, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M6"}, {"backtrace": 264, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M7"}, {"backtrace": 265, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M8"}, {"backtrace": 266, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M9"}, {"backtrace": 267, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput"}, {"backtrace": 269, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputBase"}, {"backtrace": 270, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M1"}, {"backtrace": 271, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M2"}, {"backtrace": 272, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M3"}, {"backtrace": 273, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource"}, {"backtrace": 275, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Font"}, {"backtrace": 276, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Icon"}, {"backtrace": 277, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image"}, {"backtrace": 279, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image/PushButtonGroup"}, {"backtrace": 280, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio"}, {"backtrace": 282, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/API"}, {"backtrace": 283, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/SDK"}, {"backtrace": 285, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/SDK/MAC"}, {"backtrace": 286, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/SDK/WIN"}, {"backtrace": 287, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID"}, {"backtrace": 289, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/API"}, {"backtrace": 290, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/Device"}, {"backtrace": 292, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceBase"}, {"backtrace": 293, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceM62"}, {"backtrace": 294, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK"}, {"backtrace": 296, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK/linux"}, {"backtrace": 297, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK/mac"}, {"backtrace": 298, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK/win"}, {"backtrace": 299, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b3fe1d"}, {"backtrace": 300, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/82f9cd"}, {"backtrace": 301, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/472976"}, {"backtrace": 302, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b0b655"}, {"backtrace": 303, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/7be222"}, {"backtrace": 304, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ea8b46"}, {"backtrace": 305, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/9fcd8a"}, {"backtrace": 306, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f530e6"}, {"backtrace": 307, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f0adbc"}, {"backtrace": 308, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/8d9aaf"}, {"backtrace": 309, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/0de7ed"}, {"backtrace": 310, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/6115e1"}, {"backtrace": 311, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/48f683"}, {"backtrace": 312, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/8ab9a5"}, {"backtrace": 313, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a3ef9a"}, {"backtrace": 314, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/57705c"}, {"backtrace": 315, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ff46f0"}, {"backtrace": 316, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2c1934"}, {"backtrace": 317, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/46d670"}, {"backtrace": 318, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/e40100"}, {"backtrace": 319, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/343d8f"}, {"backtrace": 320, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fff6ec"}, {"backtrace": 321, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/335dd8"}, {"backtrace": 322, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b53517"}, {"backtrace": 323, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/d1d245"}, {"backtrace": 324, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5102ab"}, {"backtrace": 325, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ade5cc"}, {"backtrace": 326, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/27a07f"}, {"backtrace": 327, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a555d1"}, {"backtrace": 328, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/4e73cc"}, {"backtrace": 329, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/84036a"}, {"backtrace": 330, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f5cab1"}, {"backtrace": 331, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/e7c615"}, {"backtrace": 332, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/48c050"}, {"backtrace": 333, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fc3e57"}, {"backtrace": 334, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/eeb9c2"}, {"backtrace": 335, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ddc477"}, {"backtrace": 336, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/339435"}, {"backtrace": 337, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a0067f"}, {"backtrace": 338, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2ff45e"}, {"backtrace": 339, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fa7ae3"}, {"backtrace": 340, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/4a7476"}, {"backtrace": 341, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/447ecf"}, {"backtrace": 342, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5981a4"}, {"backtrace": 343, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/d5c746"}, {"backtrace": 344, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/c85f3d"}, {"backtrace": 345, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5554d4"}, {"backtrace": 346, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5a1417"}, {"backtrace": 347, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2024df"}, {"backtrace": 19, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 19, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 19, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtSvg"}], "language": "RC", "sourceIndexes": [312]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /Zi /Ob0 /Od /RTC1 -MDd"}, {"backtrace": 19, "fragment": "-utf-8"}], "defines": [{"backtrace": 47, "define": "APP_VERSION=\"1.1.8\""}, {"backtrace": 19, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_NETWORK_LIB"}, {"backtrace": 6, "define": "QT_SVG_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 19, "define": "UNICODE"}, {"backtrace": 19, "define": "WIN32"}, {"backtrace": 19, "define": "WIN64"}, {"backtrace": 19, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 19, "define": "_UNICODE"}, {"backtrace": 19, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Documents/TMP/build/Source/TPCC_autogen/include"}, {"backtrace": 49, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction"}, {"backtrace": 51, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/AppSettings"}, {"backtrace": 52, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/AutoStartManager"}, {"backtrace": 53, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/CTL"}, {"backtrace": 55, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/BlockingQueue"}, {"backtrace": 56, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/Singleton"}, {"backtrace": 57, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/DebugManager"}, {"backtrace": 58, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/GlobalFont"}, {"backtrace": 59, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/SingleInstanceManager"}, {"backtrace": 60, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Solo"}, {"backtrace": 61, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/TrialManager"}, {"backtrace": 62, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/USBAudioManager"}, {"backtrace": 63, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater"}, {"backtrace": 65, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterBase"}, {"backtrace": 66, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFactory"}, {"backtrace": 67, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFirmwareM1"}, {"backtrace": 68, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterSoftware"}, {"backtrace": 69, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomFunction/Workspace"}, {"backtrace": 70, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget"}, {"backtrace": 72, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Battery"}, {"backtrace": 74, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1"}, {"backtrace": 75, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox"}, {"backtrace": 77, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox/ButtonBoxS1M1"}, {"backtrace": 78, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Circle"}, {"backtrace": 80, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Circle/CircleS1M1"}, {"backtrace": 81, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox"}, {"backtrace": 83, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M1"}, {"backtrace": 84, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M2"}, {"backtrace": 85, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M3"}, {"backtrace": 86, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial"}, {"backtrace": 88, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M1"}, {"backtrace": 89, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M2"}, {"backtrace": 90, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M3"}, {"backtrace": 91, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M4"}, {"backtrace": 92, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M5"}, {"backtrace": 93, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M6"}, {"backtrace": 94, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/FramelessWindow"}, {"backtrace": 95, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Menu"}, {"backtrace": 97, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Menu/MenuS1M1"}, {"backtrace": 98, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox"}, {"backtrace": 100, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS1M1"}, {"backtrace": 101, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS2M1"}, {"backtrace": 102, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS3M1"}, {"backtrace": 103, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget"}, {"backtrace": 105, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1"}, {"backtrace": 106, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2"}, {"backtrace": 107, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3"}, {"backtrace": 108, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4"}, {"backtrace": 109, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton"}, {"backtrace": 111, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M1"}, {"backtrace": 112, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10"}, {"backtrace": 113, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M11"}, {"backtrace": 114, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M12"}, {"backtrace": 115, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M13"}, {"backtrace": 116, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M14"}, {"backtrace": 117, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M15"}, {"backtrace": 118, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M2"}, {"backtrace": 119, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M3"}, {"backtrace": 120, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M4"}, {"backtrace": 121, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M5"}, {"backtrace": 122, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M6"}, {"backtrace": 123, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M7"}, {"backtrace": 124, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M8"}, {"backtrace": 125, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M9"}, {"backtrace": 126, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup"}, {"backtrace": 128, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1"}, {"backtrace": 129, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2"}, {"backtrace": 130, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3"}, {"backtrace": 131, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4"}, {"backtrace": 132, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5"}, {"backtrace": 133, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6"}, {"backtrace": 134, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7"}, {"backtrace": 135, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8"}, {"backtrace": 136, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9"}, {"backtrace": 137, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider"}, {"backtrace": 139, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider"}, {"backtrace": 141, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS1M1"}, {"backtrace": 142, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS2M1"}, {"backtrace": 143, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider"}, {"backtrace": 145, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M1"}, {"backtrace": 146, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M2"}, {"backtrace": 147, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget"}, {"backtrace": 149, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget/TabWidgetS1M1"}, {"backtrace": 150, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton"}, {"backtrace": 152, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton/ToolButtonS1M1"}, {"backtrace": 153, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter"}, {"backtrace": 155, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M1"}, {"backtrace": 156, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M2"}, {"backtrace": 157, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M3"}, {"backtrace": 158, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M4"}, {"backtrace": 159, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M5"}, {"backtrace": 160, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M6"}, {"backtrace": 161, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M7"}, {"backtrace": 162, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M8"}, {"backtrace": 163, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS2M1"}, {"backtrace": 164, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector"}, {"backtrace": 166, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView"}, {"backtrace": 168, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase"}, {"backtrace": 169, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1"}, {"backtrace": 170, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField"}, {"backtrace": 172, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect"}, {"backtrace": 174, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectBase1"}, {"backtrace": 175, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectS1M1"}, {"backtrace": 176, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead"}, {"backtrace": 178, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase1"}, {"backtrace": 179, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase2"}, {"backtrace": 180, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M1"}, {"backtrace": 181, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M2"}, {"backtrace": 182, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput"}, {"backtrace": 184, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputBase1"}, {"backtrace": 185, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputS1M1"}, {"backtrace": 186, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback"}, {"backtrace": 188, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackBase1"}, {"backtrace": 189, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackS1M1"}, {"backtrace": 190, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer"}, {"backtrace": 192, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerBase1"}, {"backtrace": 193, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerS1M1"}, {"backtrace": 194, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin"}, {"backtrace": 196, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase1"}, {"backtrace": 197, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase2"}, {"backtrace": 198, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M1"}, {"backtrace": 199, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS2M1"}, {"backtrace": 200, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput"}, {"backtrace": 202, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputBase1"}, {"backtrace": 203, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputS1M1"}, {"backtrace": 204, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceMainWindow"}, {"backtrace": 206, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_Base"}, {"backtrace": 207, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62"}, {"backtrace": 208, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget"}, {"backtrace": 210, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice"}, {"backtrace": 212, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll"}, {"backtrace": 214, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/AutoGain"}, {"backtrace": 216, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/AutoGain/AutoGainS1M1"}, {"backtrace": 217, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAbout1"}, {"backtrace": 218, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAudio1"}, {"backtrace": 219, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetSystem1"}, {"backtrace": 220, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62"}, {"backtrace": 222, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget1"}, {"backtrace": 223, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget2"}, {"backtrace": 224, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget3"}, {"backtrace": 225, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget5"}, {"backtrace": 226, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget6"}, {"backtrace": 227, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget7"}, {"backtrace": 228, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect"}, {"backtrace": 230, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectBase"}, {"backtrace": 231, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M1"}, {"backtrace": 232, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2"}, {"backtrace": 233, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput"}, {"backtrace": 235, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputBase"}, {"backtrace": 236, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M1"}, {"backtrace": 237, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2"}, {"backtrace": 238, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M3"}, {"backtrace": 239, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M4"}, {"backtrace": 240, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M5"}, {"backtrace": 241, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M6"}, {"backtrace": 242, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M1"}, {"backtrace": 243, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M2"}, {"backtrace": 244, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback"}, {"backtrace": 246, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackBase"}, {"backtrace": 247, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M1"}, {"backtrace": 248, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M2"}, {"backtrace": 249, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer"}, {"backtrace": 251, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerBase"}, {"backtrace": 252, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M1"}, {"backtrace": 253, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M2"}, {"backtrace": 254, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M3"}, {"backtrace": 255, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M4"}, {"backtrace": 256, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin"}, {"backtrace": 258, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginBase"}, {"backtrace": 259, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M1"}, {"backtrace": 260, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M2"}, {"backtrace": 261, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M3"}, {"backtrace": 262, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M4"}, {"backtrace": 263, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M6"}, {"backtrace": 264, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M7"}, {"backtrace": 265, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M8"}, {"backtrace": 266, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M9"}, {"backtrace": 267, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput"}, {"backtrace": 269, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputBase"}, {"backtrace": 270, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M1"}, {"backtrace": 271, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M2"}, {"backtrace": 272, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M3"}, {"backtrace": 273, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource"}, {"backtrace": 275, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Font"}, {"backtrace": 276, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Icon"}, {"backtrace": 277, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image"}, {"backtrace": 279, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image/PushButtonGroup"}, {"backtrace": 280, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio"}, {"backtrace": 282, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/API"}, {"backtrace": 283, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/SDK"}, {"backtrace": 285, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/SDK/MAC"}, {"backtrace": 286, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBAudio/SDK/WIN"}, {"backtrace": 287, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID"}, {"backtrace": 289, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/API"}, {"backtrace": 290, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/Device"}, {"backtrace": 292, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceBase"}, {"backtrace": 293, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceM62"}, {"backtrace": 294, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK"}, {"backtrace": 296, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK/linux"}, {"backtrace": 297, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK/mac"}, {"backtrace": 298, "path": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter/Source/USBHID/SDK/win"}, {"backtrace": 299, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b3fe1d"}, {"backtrace": 300, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/82f9cd"}, {"backtrace": 301, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/472976"}, {"backtrace": 302, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b0b655"}, {"backtrace": 303, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/7be222"}, {"backtrace": 304, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ea8b46"}, {"backtrace": 305, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/9fcd8a"}, {"backtrace": 306, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f530e6"}, {"backtrace": 307, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f0adbc"}, {"backtrace": 308, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/8d9aaf"}, {"backtrace": 309, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/0de7ed"}, {"backtrace": 310, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/6115e1"}, {"backtrace": 311, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/48f683"}, {"backtrace": 312, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/8ab9a5"}, {"backtrace": 313, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a3ef9a"}, {"backtrace": 314, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/57705c"}, {"backtrace": 315, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ff46f0"}, {"backtrace": 316, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2c1934"}, {"backtrace": 317, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/46d670"}, {"backtrace": 318, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/e40100"}, {"backtrace": 319, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/343d8f"}, {"backtrace": 320, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fff6ec"}, {"backtrace": 321, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/335dd8"}, {"backtrace": 322, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b53517"}, {"backtrace": 323, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/d1d245"}, {"backtrace": 324, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5102ab"}, {"backtrace": 325, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ade5cc"}, {"backtrace": 326, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/27a07f"}, {"backtrace": 327, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a555d1"}, {"backtrace": 328, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/4e73cc"}, {"backtrace": 329, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/84036a"}, {"backtrace": 330, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f5cab1"}, {"backtrace": 331, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/e7c615"}, {"backtrace": 332, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/48c050"}, {"backtrace": 333, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fc3e57"}, {"backtrace": 334, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/eeb9c2"}, {"backtrace": 335, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ddc477"}, {"backtrace": 336, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/339435"}, {"backtrace": 337, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a0067f"}, {"backtrace": 338, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2ff45e"}, {"backtrace": 339, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fa7ae3"}, {"backtrace": 340, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/4a7476"}, {"backtrace": 341, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/447ecf"}, {"backtrace": 342, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5981a4"}, {"backtrace": 343, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/d5c746"}, {"backtrace": 344, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/c85f3d"}, {"backtrace": 345, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5554d4"}, {"backtrace": 346, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5a1417"}, {"backtrace": 347, "isSystem": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2024df"}, {"backtrace": 19, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 19, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 19, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtSvg"}], "language": "C", "sourceIndexes": [356]}], "dependencies": [{"backtrace": 46, "id": "TPCC_ui_property_check::@43690dd2fb94c8e45e84"}, {"id": "TPCC_autogen_timestamp_deps::@43690dd2fb94c8e45e84"}, {"backtrace": 0, "id": "TPCC_autogen::@43690dd2fb94c8e45e84"}], "id": "TPCC::@43690dd2fb94c8e45e84", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/TPCC"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Networkd.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Svgd.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 19, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6EntryPointd.lib", "role": "libraries"}, {"backtrace": 34, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 43, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 43, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 43, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 43, "fragment": "d3d12.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "TPCC", "nameOnDisk": "M Control Center.exe", "paths": {"build": "Source", "source": "Source"}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0, 483, 484]}, {"name": "Source Files", "sourceIndexes": [1, 6, 8, 10, 13, 15, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 59, 62, 65, 66, 71, 73, 76, 78, 80, 83, 85, 88, 91, 93, 96, 99, 102, 105, 108, 111, 114, 117, 119, 122, 125, 127, 130, 133, 136, 139, 141, 144, 147, 149, 152, 155, 158, 161, 164, 166, 169, 172, 175, 176, 179, 182, 184, 186, 188, 190, 192, 194, 196, 198, 200, 202, 204, 206, 208, 210, 212, 215, 218, 221, 224, 226, 228, 230, 232, 234, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 257, 260, 263, 266, 269, 272, 275, 278, 281, 283, 285, 287, 289, 291, 293, 295, 297, 299, 301, 303, 305, 307, 309, 312, 313, 315, 319, 321, 323, 325, 327, 329, 331, 334, 336, 338, 340, 341, 344, 346, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 432]}, {"name": "", "sourceIndexes": [2, 3, 4, 5, 12, 17, 58, 61, 64, 69, 70, 75, 82, 87, 90, 95, 98, 101, 104, 107, 110, 113, 116, 121, 124, 129, 132, 135, 138, 143, 146, 151, 154, 157, 160, 163, 168, 171, 174, 181, 214, 217, 220, 223, 256, 259, 262, 265, 268, 271, 274, 277, 280, 311, 431]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [7, 9, 11, 14, 16, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 60, 63, 67, 68, 72, 74, 77, 79, 81, 84, 86, 89, 92, 94, 97, 100, 103, 106, 109, 112, 115, 118, 120, 123, 126, 128, 131, 134, 137, 140, 142, 145, 148, 150, 153, 156, 159, 162, 165, 167, 170, 173, 177, 178, 180, 183, 185, 187, 189, 191, 193, 195, 197, 199, 201, 203, 205, 207, 209, 211, 213, 216, 219, 222, 225, 227, 229, 231, 233, 235, 237, 239, 241, 243, 245, 247, 249, 251, 253, 255, 258, 261, 264, 267, 270, 273, 276, 279, 282, 284, 286, 288, 290, 292, 294, 296, 298, 300, 302, 304, 306, 308, 310, 314, 316, 317, 318, 320, 322, 324, 326, 328, 330, 332, 333, 335, 337, 339, 342, 343, 345, 347, 348, 349, 350, 351, 352, 353, 354, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482]}, {"name": "CMake Rules", "sourceIndexes": [485, 486]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/TPCC_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/main.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Updater/SeriesM/UpdaterSeriesM.json", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Updater/SeriesM/UpdaterSeriesMTest.json", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Updater/SeriesS/UpdaterSeriesS.json", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Updater/SeriesS/UpdaterSeriesSTest.json", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceConnector/deviceconnector.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceConnector/deviceconnector.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase/deviceconnectorviewbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase/deviceconnectorviewbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceMainWindow/MainWindow_Base/mainwindow_base.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceMainWindow/MainWindow_Base/mainwindow_base.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldEffect/FieldEffectBase1/fieldeffectbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldEffect/FieldEffectBase1/fieldeffectbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldEffect/FieldEffectS1M1/fieldeffects1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldEffect/FieldEffectS1M1/fieldeffects1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadBase1/fieldheadbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadBase1/fieldheadbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadBase2/fieldheadbase2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadBase2/fieldheadbase2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadS1M1/fieldheads1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadS1M1/fieldheads1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadS1M2/fieldheads1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadS1M2/fieldheads1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldInput/FieldInputBase1/fieldinputbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldInput/FieldInputBase1/fieldinputbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldInput/FieldInputS1M1/fieldinputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldInput/FieldInputS1M1/fieldinputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackBase1/fieldloopbackbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackBase1/fieldloopbackbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackS1M1/fieldloopbacks1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackS1M1/fieldloopbacks1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldMixer/FieldMixerBase1/fieldmixerbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldMixer/FieldMixerBase1/fieldmixerbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldMixer/FieldMixerS1M1/fieldmixers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldMixer/FieldMixerS1M1/fieldmixers1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase1/fieldoriginbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase1/fieldoriginbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase2/fieldoriginbase2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase2/fieldoriginbase2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M1/fieldorigins1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M1/fieldorigins1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginS2M1/fieldorigins2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginS2M1/fieldorigins2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOutput/FieldOutputBase1/fieldoutputbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOutput/FieldOutputBase1/fieldoutputbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOutput/FieldOutputS1M1/fieldoutputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOutput/FieldOutputS1M1/fieldoutputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/AutoGain/AutoGainS1M1/autogains1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/AutoGain/AutoGainS1M1/autogains1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAbout1/widgetabout1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAbout1/widgetabout1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAbout1/widgetabout1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAudio1/widgetaudio1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAudio1/widgetaudio1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetAudio1/widgetaudio1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetSystem1/widgetsytem1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetSystem1/widgetsytem1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/DeviceAll/WidgetSystem1/widgetsytem1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget1/m62_privatewidget1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget1/m62_privatewidget1_1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget1/m62_privatewidget1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget1/m62_privatewidget1_1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget1/m62_privatewidget1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget1/m62_privatewidget1_1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget2/m62_privatewidget2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget2/m62_privatewidget2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget3/m62_privatewidget3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget3/m62_privatewidget3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget3/m62_privatewidget3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget5/m62_privatewidget5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget5/m62_privatewidget5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget6/m62_privatewidget6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget6/m62_privatewidget6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget7/m62_privatewidget7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget7/m62_privatewidget7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetDevice/M62/M62_PrivateWidget7/m62_privatewidget7.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectBase/effectbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectBase/effectbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M1/effects1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M1/effects1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M1/effects1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputBase/inputbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputBase/inputbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M1/inputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M1/inputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M1/inputs1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M3/inputs1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M3/inputs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M3/inputs1m3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M4/inputs1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M4/inputs1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M4/inputs1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M5/inputs1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M5/inputs1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M5/inputs1m5.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M6/inputs1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M6/inputs1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M6/inputs1m6.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS2M1/inputs2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M1/inputs2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M1/inputs2m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS2M2/inputs2m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M2/inputs2m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M2/inputs2m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackBase/loopbackbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackBase/loopbackbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M1/loopbacks1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M1/loopbacks1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M1/loopbacks1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M2/loopbacks1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M2/loopbacks1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M2/loopbacks1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerBase/mixerbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerBase/mixerbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M1/mixers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M1/mixers1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M1/mixers1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M2/mixers1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M2/mixers1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M2/mixers1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M3/mixers1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M3/mixers1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M3/mixers1m3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M4/mixers1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M4/mixers1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M4/mixers1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginBase/originbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginBase/originbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M1/origins1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M1/origins1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M1/origins1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M2/origins1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M2/origins1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M2/origins1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M3/origins1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M3/origins1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M4/origins1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M4/origins1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M4/origins1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M6/origins1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M6/origins1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M6/origins1m6.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M7/origins1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M7/origins1m7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M7/origins1m7.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M8/origins1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M8/origins1m8.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M8/origins1m8.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M9/origins1m9.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M9/origins1m9.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M9/origins1m9.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputBase/outputbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputBase/outputbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M1/outputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M1/outputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M1/outputs1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M2/outputs1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M2/outputs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M2/outputs1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M3/outputs1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M3/outputs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M3/outputs1m3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterydrawstrategy.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterys1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterydrawstrategy.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterys1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ButtonBox/ButtonBoxS1M1/buttonboxs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ButtonBox/ButtonBoxS1M1/buttonboxs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/ButtonBox/ButtonBoxS1M1/buttonboxs1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Circle/CircleS1M1/circles1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Circle/CircleS1M1/circles1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M1/comboboxs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M1/comboboxs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M2/comboboxs1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M2/comboboxs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M3/comboboxs1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M3/comboboxs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M1/dials1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M1/dials1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M2/dials1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M2/dials1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M3/dials1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M3/dials1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M4/dials1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M4/dials1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M5/dials1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M5/dials1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M6/dials1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M6/dials1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/FramelessWindow/framelesswindow.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/FramelessWindow/framelesswindow.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Menu/MenuS1M1/menus1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Menu/MenuS1M1/menus1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxS1M1/messageboxs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxS1M1/messageboxs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxS2M1/messageboxs2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxS2M1/messageboxs2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxS3M1/messageboxs3m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxS3M1/messageboxs3m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1/messageboxwidget1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1/messageboxwidget1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1/messageboxwidget1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2/messageboxwidget2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2/messageboxwidget2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2/messageboxwidget2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3/messageboxwidget3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3/messageboxwidget3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3/messageboxwidget3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4/messageboxwidget4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4/messageboxwidget4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4/messageboxwidget4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M1/pushbuttons1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M1/pushbuttons1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M10/pushbuttons1m10.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M10/pushbuttons1m10.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M11/pushbuttons1m11.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M11/pushbuttons1m11.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M12/pushbuttons1m12.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M12/pushbuttons1m12.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M13/pushbuttons1m13.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M13/pushbuttons1m13.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M14/pushbuttons1m14.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M14/pushbuttons1m14.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M15/pushbuttons1m15.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M15/pushbuttons1m15.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M2/pushbuttons1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M2/pushbuttons1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M3/pushbuttons1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M3/pushbuttons1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M4/pushbuttons1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M4/pushbuttons1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M5/pushbuttons1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M5/pushbuttons1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M6/pushbuttons1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M6/pushbuttons1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M7/pushbuttons1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M7/pushbuttons1m7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M8/pushbuttons1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M8/pushbuttons1m8.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M9/pushbuttons1m9.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M9/pushbuttons1m9.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1/pushbuttongroups1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1/pushbuttongroups1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1/pushbuttongroups1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2/pushbuttongroups1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2/pushbuttongroups1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2/pushbuttongroups1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3/pushbuttongroups1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3/pushbuttongroups1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3/pushbuttongroups1m3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4/pushbuttongroups1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4/pushbuttongroups1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4/pushbuttongroups1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5/pushbuttongroups1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5/pushbuttongroups1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5/pushbuttongroups1m5.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6/pushbuttongroups1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6/pushbuttongroups1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6/pushbuttongroups1m6.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7/pushbuttongroups1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7/pushbuttongroups1m7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7/pushbuttongroups1m7.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8/pushbuttongroups1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8/pushbuttongroups1m8.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8/pushbuttongroups1m8.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9/pushbuttongroups1m9.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9/pushbuttongroups1m9.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9/pushbuttongroups1m9.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/HSlider/HSliderS1M1/hsliders1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/HSlider/HSliderS1M1/hsliders1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/HSlider/HSliderS2M1/hsliders2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/HSlider/HSliderS2M1/hsliders2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M1/vsliders1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M1/vsliders1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M2/vsliders1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M2/vsliders1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/TabWidget/TabWidgetS1M1/tabwidgets1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/TabWidget/TabWidgetS1M1/tabwidgets1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ToolButton/ToolButtonS1M1/toolbuttons1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ToolButton/ToolButtonS1M1/toolbuttons1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M1/volumemeters1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M1/volumemeters1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M2/volumemeters1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M2/volumemeters1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M3/volumemeters1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M3/volumemeters1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M4/volumemeters1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M4/volumemeters1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M5/volumemeters1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M5/volumemeters1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M6/volumemeters1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M6/volumemeters1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M7/volumemeters1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M7/volumemeters1m7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M8/volumemeters1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M8/volumemeters1m8.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS2M1/volumemeters2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS2M1/volumemeters2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/ThirdPartyResource/ThirdPartyResource.qrc", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 1, "path": "Source/ThirdPartyResource/AppIconWin.rc", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/AppSettings/appsettings.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/AppSettings/appsettings.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/AutoStartManager/autostartmanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/AutoStartManager/autostartmanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomFunction/CTL/BlockingQueue/blockingqueue.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomFunction/CTL/Singleton/singleton.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/DebugManager/debugmanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/DebugManager/debugmanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/GlobalFont/globalfont.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/GlobalFont/globalfont.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/SingleInstanceManager/singleinstancemanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/SingleInstanceManager/singleinstancemanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Solo/solo.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Solo/solo.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/TrialManager/trialmanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/TrialManager/trialmanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/USBAudioManager/usbaudiomanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/USBAudioManager/usbaudiomanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Updater/UpdaterBase/updaterbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterBase/updaterbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterFactory/updaterfactory.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Updater/UpdaterFirmwareM1/updaterfirmwarem1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterFirmwareM1/updaterfirmwarem1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Updater/UpdaterSoftware/updatersoftware.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterSoftware/updatersoftware.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Workspace/workspace.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Workspace/workspace.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/Device/DeviceBase/devicebase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/Device/DeviceBase/devicetype1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBHID/Device/DeviceBase/devicebase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/Device/DeviceBase/devicetype1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/Device/DeviceM62/devicem62.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBHID/Device/DeviceM62/devicem62.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/API/usbhidapi.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBHID/API/usbhidapi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_cfgmgr32.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_darwin.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_hidclass.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_hidpi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_hidsdi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_winapi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Packager/Win/PackageManager.bat", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 2, "path": "Source/USBHID/SDK/win/hid.c", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiDll.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiExtendedInfo.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TUsbAudioMixer.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TbStdStringUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TbStringUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnModuleFileName.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnRegistryKey.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnStringUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnThread.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTrace.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTraceLogContext.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTraceLogFile.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTraceLogSettings.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguage.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageFile.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageMgr.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageText.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageTextGroup.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUserModeCrashDump.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnWow64.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/libtb_OSEnv_impl.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/CommonPluginProperties.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/MixerPluginProperties.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiDll.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiExtendedInfo.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TUsbAudioMixer.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbOSEnv.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbStdStringUtils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbStringUtils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbUtils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnCriticalSection.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnEvent.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnHandle.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnLibrary.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnModuleFileName.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnRegistryKey.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnStringUtils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnStringUtils_impl.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnThread.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTrace.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTraceLogContext.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTraceLogFile.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTraceLogSettings.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTypes.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguage.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageFile.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageMgr.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageText.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageTextGroup.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUserModeCrashDump.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnWow64.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/dsp_types.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libtb.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libtb_env.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libwn.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libwn_min_global.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_al.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_al_impl.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_al_impl_generic.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_pack1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_packrestore.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_platform.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_types.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_utils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tstatus_codes.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tstatus_codes_ex.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusb_cls_audio.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusb_cls_audio20.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusb_spec.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudio_defs.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudioapi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudioapi_defs.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/win_targetver.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudioapi.lib", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/API/usbaudioapi.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBAudio/API/usbaudioapi.h", "sourceGroupIndex": 3}, {"backtrace": 348, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b3fe1d/ui_deviceconnectorviews1m1.h", "sourceGroupIndex": 3}, {"backtrace": 349, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/82f9cd/ui_mainwindow_m62.h", "sourceGroupIndex": 3}, {"backtrace": 350, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/472976/ui_widgetabout1.h", "sourceGroupIndex": 3}, {"backtrace": 351, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b0b655/ui_widgetaudio1.h", "sourceGroupIndex": 3}, {"backtrace": 352, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/7be222/ui_widgetsytem1.h", "sourceGroupIndex": 3}, {"backtrace": 353, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ea8b46/ui_m62_privatewidget1.h", "sourceGroupIndex": 3}, {"backtrace": 354, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/9fcd8a/ui_m62_privatewidget1_1.h", "sourceGroupIndex": 3}, {"backtrace": 355, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f530e6/ui_m62_privatewidget3.h", "sourceGroupIndex": 3}, {"backtrace": 356, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f0adbc/ui_m62_privatewidget7.h", "sourceGroupIndex": 3}, {"backtrace": 357, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/8d9aaf/ui_effects1m1.h", "sourceGroupIndex": 3}, {"backtrace": 358, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/0de7ed/ui_effects1m2.h", "sourceGroupIndex": 3}, {"backtrace": 359, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/6115e1/ui_inputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 360, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/48f683/ui_inputs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 361, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/8ab9a5/ui_inputs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 362, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a3ef9a/ui_inputs1m4.h", "sourceGroupIndex": 3}, {"backtrace": 363, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/57705c/ui_inputs1m5.h", "sourceGroupIndex": 3}, {"backtrace": 364, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ff46f0/ui_inputs1m6.h", "sourceGroupIndex": 3}, {"backtrace": 365, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2c1934/ui_inputs2m1.h", "sourceGroupIndex": 3}, {"backtrace": 366, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/46d670/ui_inputs2m2.h", "sourceGroupIndex": 3}, {"backtrace": 367, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/e40100/ui_loopbacks1m1.h", "sourceGroupIndex": 3}, {"backtrace": 368, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/343d8f/ui_loopbacks1m2.h", "sourceGroupIndex": 3}, {"backtrace": 369, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fff6ec/ui_mixers1m1.h", "sourceGroupIndex": 3}, {"backtrace": 370, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/335dd8/ui_mixers1m2.h", "sourceGroupIndex": 3}, {"backtrace": 371, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/b53517/ui_mixers1m3.h", "sourceGroupIndex": 3}, {"backtrace": 372, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/d1d245/ui_mixers1m4.h", "sourceGroupIndex": 3}, {"backtrace": 373, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5102ab/ui_origins1m1.h", "sourceGroupIndex": 3}, {"backtrace": 374, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ade5cc/ui_origins1m2.h", "sourceGroupIndex": 3}, {"backtrace": 375, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/27a07f/ui_origins1m4.h", "sourceGroupIndex": 3}, {"backtrace": 376, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a555d1/ui_origins1m6.h", "sourceGroupIndex": 3}, {"backtrace": 377, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/4e73cc/ui_origins1m7.h", "sourceGroupIndex": 3}, {"backtrace": 378, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/84036a/ui_origins1m8.h", "sourceGroupIndex": 3}, {"backtrace": 379, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/f5cab1/ui_origins1m9.h", "sourceGroupIndex": 3}, {"backtrace": 380, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/e7c615/ui_outputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 381, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/48c050/ui_outputs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 382, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fc3e57/ui_outputs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 383, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/eeb9c2/ui_buttonboxs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 384, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/ddc477/ui_messageboxwidget1.h", "sourceGroupIndex": 3}, {"backtrace": 385, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/339435/ui_messageboxwidget2.h", "sourceGroupIndex": 3}, {"backtrace": 386, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/a0067f/ui_messageboxwidget3.h", "sourceGroupIndex": 3}, {"backtrace": 387, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2ff45e/ui_messageboxwidget4.h", "sourceGroupIndex": 3}, {"backtrace": 388, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/fa7ae3/ui_pushbuttongroups1m1.h", "sourceGroupIndex": 3}, {"backtrace": 389, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/4a7476/ui_pushbuttongroups1m2.h", "sourceGroupIndex": 3}, {"backtrace": 390, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/447ecf/ui_pushbuttongroups1m3.h", "sourceGroupIndex": 3}, {"backtrace": 391, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5981a4/ui_pushbuttongroups1m4.h", "sourceGroupIndex": 3}, {"backtrace": 392, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/d5c746/ui_pushbuttongroups1m5.h", "sourceGroupIndex": 3}, {"backtrace": 393, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/c85f3d/ui_pushbuttongroups1m6.h", "sourceGroupIndex": 3}, {"backtrace": 394, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5554d4/ui_pushbuttongroups1m7.h", "sourceGroupIndex": 3}, {"backtrace": 395, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/5a1417/ui_pushbuttongroups1m8.h", "sourceGroupIndex": 3}, {"backtrace": 396, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/.qt/2024df/ui_pushbuttongroups1m9.h", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/TPCC_autogen/timestamp", "sourceGroupIndex": 0}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/TPCC_autogen/QFVAREFS7T/qrc_ThirdPartyResource.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/TPCC_autogen/timestamp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "C:/Users/<USER>/Documents/TMP/build/Source/TPCC_autogen/QFVAREFS7T/qrc_ThirdPartyResource.cpp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}
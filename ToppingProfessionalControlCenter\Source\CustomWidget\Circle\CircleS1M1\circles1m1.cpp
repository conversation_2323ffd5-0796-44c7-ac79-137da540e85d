#include "circles1m1.h"
#include <QPainter>

CircleS1M1::CircleS1M1(QWidget *parent)
    : QWidget{parent}
{
}

void CircleS1M1::setStatus(Status status)
{
    if(mStatus!=status){
        mStatus = status;
        update();
    }
}

void CircleS1M1::setFont(const QFont &font)
{
    mFont = font;
}

void CircleS1M1::setValue(float value)
{
    if (mCurValue != value) {
        mCurValue = value;
        update();
    }
}

void CircleS1M1::paintEvent(QPaintEvent *e)
{
    QPainter painter(this);
    qreal outDiameter = qMin(width(),height());
    qreal outX = (width()-outDiameter)/2.0;
    qreal outY = (height()-outDiameter)/2.0;
    qreal innerDiameter = outDiameter*0.9;
    qreal innerX = outX+outDiameter*0.05;
    qreal innerY = outY+outDiameter*0.05;
    QRectF innerRect{innerX, innerY, innerDiameter, innerDiameter};
    QRectF outRect{outX, outY, outDiameter, outDiameter};
    QColor outBg = getOutBg();
    double hRatio = height()/60.0;
    painter.setRenderHints(QPainter::Antialiasing);

    painter.setPen(Qt::NoPen);
    painter.setBrush(getOutBg());
    painter.drawEllipse(outRect);

    painter.setBrush(getInnerBg());
    painter.drawEllipse(innerRect);

    if (mStatus == detect) {
        qreal percent = (qreal)(mTotalValue - mCurValue) / mTotalValue;
        qreal penWidth = innerDiameter*0.05;
        QPen fgPen(QColor(60, 220, 130), penWidth, Qt::SolidLine, Qt::RoundCap);
        painter.setPen(fgPen);
        QRectF arcRect = outRect.adjusted(penWidth / 2.0, penWidth / 2.0, -penWidth / 2.0, -penWidth / 2.0);
        painter.drawArc(arcRect, 90 * 16, -percent * 360 * 16);
    }

    painter.setPen(mTextColor);
    mFont.setPixelSize(17 * hRatio);
    painter.setFont(mFont);
    if (mStatus == start || mStatus == detect) {
        painter.drawText(innerX, innerY + innerDiameter / 7, innerDiameter, innerDiameter / 3, Qt::AlignCenter, QString::number(mCurValue));
        mFont.setPixelSize(9 * hRatio);
        painter.setFont(mFont);
        painter.drawText(innerX, innerY + innerDiameter / 3, innerDiameter, innerDiameter * 2 / 3, Qt::AlignCenter, "Second");
    } else if (mStatus == complete) {
        int width = innerDiameter * 0.6;
        int height = innerDiameter * 0.4;
        QPixmap scaledPixmap = QPixmap(":/Icon/tick.png").scaled(width, height, Qt::KeepAspectRatio, Qt::SmoothTransformation);
        painter.drawPixmap(innerX + (innerDiameter - scaledPixmap.width()) / 2,innerY + (innerDiameter - scaledPixmap.height()) / 2,scaledPixmap);
    } else if (mStatus == error) {
        mFont.setPixelSize(10 * hRatio);
        painter.setFont(mFont);
        painter.setPen(QColor(255, 87, 51));
        painter.drawText(innerX + innerDiameter / 5, innerY + innerDiameter / 5, innerDiameter*0.6, innerDiameter*0.6, Qt::AlignCenter, "Error");
    }
}

QColor CircleS1M1::getOutBg()
{
    QColor outCircleBg;
    if(mStatus==start){
        outCircleBg = mStartOutBG;
    }else if(mStatus==detect){
        outCircleBg = mStartOutBG;
    }else if(mStatus==complete){
        outCircleBg = mCompleteOutBG;
    }else if(mStatus==error){
        outCircleBg = mErrorOutBG;
    }
    return outCircleBg;
}

QColor CircleS1M1::getInnerBg()
{
    QColor innerCircleBg;
    if(mStatus==start){
        innerCircleBg = mStartInnerBG;
    }else if(mStatus==detect){
        innerCircleBg = mDetectInnerBG;
    }else if(mStatus==complete){
        innerCircleBg = mCompleteInnerBG;
    }else if(mStatus==error){
        innerCircleBg = mErrorInnerBG;
    }
    return innerCircleBg;
}

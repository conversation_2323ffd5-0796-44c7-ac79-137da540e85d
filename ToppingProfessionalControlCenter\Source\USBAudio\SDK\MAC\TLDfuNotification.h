/************************************************************************

    Description:
        TLDFU device change notification
        Encapsulation of a TLDfuNotificationHandle

    Author(s):
        <PERSON><PERSON><PERSON> Huck
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __TLDfuNotification_h__
#define __TLDfuNotification_h__


class TLDfuNotification
{
public:

    //
    // Constructor
    //
    TLDfuNotification()
            {
                // empty
            }


    //
    // Destructor
    //
    ~TLDfuNotification()
            {
                // make sure current handle is closed (ignore possibly error)
                UnregisterDeviceChangeCallback();
            }

    // disable copy and move operations
    TLDfuNotification(const TLDfuNotification&) = delete;
    TLDfuNotification& operator=(const TLDfuNotification&) = delete;
    TLDfuNotification(TLDfuNotification&&) = delete;
    TLDfuNotification& operator=(TLDfuNotification&&) = delete;

/////////////////////////////////////////
// Interface
//
public:

    TLSTATUS
    RegisterDeviceChangeCallback(
        const char* deviceFilterDescription,
        TLDFU_DeviceChangeCallback callback,
        void* callbackContext
        )
            {
                // make sure current handle is closed (ignore possible error)
                UnregisterDeviceChangeCallback();
            
                return TLDFU_RegisterDeviceChangeCallback(
                                deviceFilterDescription,
                                callback,
                                callbackContext,
                                &mHandle
                                );
            }


    TLSTATUS
    UnregisterDeviceChangeCallback()
            {
                TLSTATUS st = TLSTATUS_SUCCESS;
                
                if ( TLDFU_INVALID_HANDLE != mHandle ) {
                    st = TLDFU_UnregisterDeviceChangeCallback(mHandle);
                    mHandle = TLDFU_INVALID_HANDLE;
                }

                return st;
            }


    //
    // Access to the encapsulated handle.
    //
    TLDfuEnumerationHandle
    Handle() const
            {
                return mHandle;
            }


/////////////////////////////////////////
// Implementation
//
protected:



////////////////////////////////////////
// Data
//
protected:

    // handle of the enumerator
    TLDfuNotificationHandle mHandle {TLDFU_INVALID_HANDLE};
};


#endif 

/*** EOF ***/

#include "globalfont.h"
#include "pushbuttongroups1m1.h"
#include "ui_pushbuttongroups1m1.h"


PushButtonGroupS1M1::PushButtonGroupS1M1(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PushButtonGroupS1M1)
{
    ui->setupUi(this);
    ui->PushButton48V->setCheckable(true);
    ui->PushButtonAUTO->setCheckable(true);
    ui->PushButtonDUCKING->setCheckable(true);
    ui->PushButton48V->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonAUTO->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonDUCKING->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    mStyle[0] = "QFrame { image: url(:/Image/PushButtonGroup/PBG7_0.png); }";
    mStyle[1] = "QFrame { image: url(:/Image/PushButtonGroup/PBG7_1.png); }";
    mStyle[2] = "QFrame { image: url(:/Image/PushButtonGroup/PBG7_2.png); }";
    mStyle[3] = "QFrame { image: url(:/Image/PushButtonGroup/PBG7_4.png); }";
    mStyle[4] = "QFrame { image: url(:/Image/PushButtonGroup/PBG7_3.png); }";
    mStyle[5] = "QFrame { image: url(:/Image/PushButtonGroup/PBG7_7.png); }";
    mStyle[6] = "QFrame { image: url(:/Image/PushButtonGroup/PBG7_5.png); }";
    mStyle[7] = "QFrame { image: url(:/Image/PushButtonGroup/PBG7_6.png); }";
    setState("48V", "0", false);
    setState("AUTO", "0", false);
    setState("DUCKING", "0", false);
    setLanguage("English");
}
PushButtonGroupS1M1::~PushButtonGroupS1M1()
{
    delete ui;
}


// override
void PushButtonGroupS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->PushButtonDUCKING->text(), ui->PushButtonDUCKING->rect()) - 1);
    ui->PushButton48V->setFont(mFont);
    ui->PushButtonAUTO->setFont(mFont);
    ui->PushButtonDUCKING->setFont(mFont);
}


// slot
void PushButtonGroupS1M1::on_PushButton48V_clicked(bool checked)
{
    setState("48V", QString::number(checked));
}
void PushButtonGroupS1M1::on_PushButtonAUTO_clicked(bool checked)
{
    setState("AUTO", QString::number(checked));
}
void PushButtonGroupS1M1::on_PushButtonDUCKING_clicked(bool checked)
{
    Q_UNUSED(checked);
    emit stateChanged("DUCKING", "1");
}


// setter & getter
PushButtonGroupS1M1& PushButtonGroupS1M1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonGroupS1M1& PushButtonGroupS1M1::setLanguage(QString language)
{
    if(language == "English")
    {
        // ui->PushButton48V->setText("48V");
        // ui->PushButtonAUTO->setText("AUTO");
        // ui->PushButtonDUCKING->setText("DUCKING");
    }
    else if(language == "Chinese")
    {
        // ui->PushButton48V->setText("48V");
        // ui->PushButtonAUTO->setText("AUTO");
        // ui->PushButtonDUCKING->setText("闪避");
    }
    return *this;
}
PushButtonGroupS1M1& PushButtonGroupS1M1::setState(QString button, QString state, bool needEmit)
{
    unsigned int bitMask=0;
    QPushButton* currentButton=nullptr;
    if(button == "48V")
    {
        bitMask = 0x0001;
        currentButton = ui->PushButton48V;
    }
    else if(button == "AUTO")
    {
        bitMask = 0x0002;
        currentButton = ui->PushButtonAUTO;
    }
    else if(button == "DUCKING")
    {
        bitMask = 0x0004;
        currentButton = ui->PushButtonDUCKING;
    }
    if(currentButton != nullptr)
    {
        mBitmap &= ~bitMask;
        if(state.toInt()) mBitmap |= bitMask;
        ui->frame->setStyleSheet(mStyle.value(mBitmap));
        currentButton->setChecked(state.toInt());
        currentButton->setStyleSheet(state.toInt() ? "QPushButton { color: rgb(222, 222, 222); }" : "QPushButton { color: rgb(161, 161, 161); }");
        if(needEmit) emit stateChanged(button, state);
    }
    return *this;
}
QString PushButtonGroupS1M1::getState(QString button)
{
    QPushButton* currentButton=nullptr;
    if(button == "48V")
    {
        currentButton = ui->PushButton48V;
    }
    else if(button == "AUTO")
    {
        currentButton = ui->PushButtonAUTO;
    }
    else if(button == "DUCKING")
    {
        currentButton = ui->PushButtonDUCKING;
    }
    if(currentButton != nullptr)
    {
        return QString::number(currentButton->isChecked());
    }
    return QString::number(false);
}


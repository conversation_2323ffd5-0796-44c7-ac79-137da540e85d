#ifndef MESSAGEBOXWIDGET3_H
#define MESSAGEBOXWIDGET3_H


#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class MessageBoxWidget3;
}


class MessageBoxWidget3 : public QWidget
{
    Q_OBJECT
public:
    explicit MessageBoxWidget3(QWidget* parent=nullptr);
    ~MessageBoxWidget3();
    MessageBoxWidget3& setFont(QFont font);
    MessageBoxWidget3& setLanguage(QString language);
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    Ui::MessageBoxWidget3* ui;
    QFont mFont;
    QString mSelectedItem="";
private slots:
    void on_CheckBox1_checkStateChanged(const Qt::CheckState &arg1);
    void on_CheckBox2_checkStateChanged(const Qt::CheckState &arg1);
    void on_CheckBox3_checkStateChanged(const Qt::CheckState &arg1);
    void on_PushButton1_clicked();
    void on_PushButton2_clicked();
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // MESSAGEBOXWIDGET3_H


/*******************************************************************************
 *
 *  Module:         tusb_cls_audio20.h
 *  Description:    Audio class 2.0 specific structures, defines, etc.
 *
 *  Runtime Env.:   any
 *  Author(s):      Rein<PERSON>
 *  Company:        Thesycon GmbH, Ilmenau
 *  Copyright:      (c) 2010 Thesycon Systemsoftware and Consulting GmbH
 *
 ******************************************************************************/

#ifndef __tusb_cls_audio20_h__
#define __tusb_cls_audio20_h__

#include "tusb_spec.h"


// pack the following structures
#include "tbase_pack1.h"


//////////////////////////////////////////////////////////////////////////
//
// Audio Function Category (FCAT) Codes (USB Audio 2.0 only)
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO20_FCAT_FUNCTION_SUBCLASS_UNDEFINED   0x00
#define TUSB_CLS_AUDIO20_FCAT_DESKTOP_SPEAKER               0x01
#define TUSB_CLS_AUDIO20_FCAT_HOME_THEATER                  0x02
#define TUSB_CLS_AUDIO20_FCAT_MICROPHONE                    0x03
#define TUSB_CLS_AUDIO20_FCAT_HEADSET                       0x04
#define TUSB_CLS_AUDIO20_FCAT_TELEPHONE                     0x05
#define TUSB_CLS_AUDIO20_FCAT_CONVERTER                     0x06
#define TUSB_CLS_AUDIO20_FCAT_VOICE_SOUND_RECORDER          0x07
#define TUSB_CLS_AUDIO20_FCAT_IO_BOX                        0x08
#define TUSB_CLS_AUDIO20_FCAT_MUSICAL_INSTRUMENT            0x09
#define TUSB_CLS_AUDIO20_FCAT_PRO_AUDIO                     0x0A
#define TUSB_CLS_AUDIO20_FCAT_AUDIO_VIDEO                   0x0B
#define TUSB_CLS_AUDIO20_FCAT_CONTROL_PANEL                 0x0C
#define TUSB_CLS_AUDIO20_FCAT_OTHER                         0xFF


//////////////////////////////////////////////////////////////////////////
//
// Audio effect unit types (USB Audio 2.0 only)
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO20_ET_EFFECT_UNDEFINED          0x00
#define TUSB_CLS_AUDIO20_ET_PARAM_EQ_SECTION_EFFECT   0x01
#define TUSB_CLS_AUDIO20_ET_REVERBERATION_EFFECT      0x02
#define TUSB_CLS_AUDIO20_ET_MOD_DELAY_EFFECT          0x03
#define TUSB_CLS_AUDIO20_ET_DYN_RANGE_COMP_EFFECT     0x04



//////////////////////////////////////////////////////////////////////////
//
// bmControls field bit encodings (2 bits per control)
//
//////////////////////////////////////////////////////////////////////////

// encodings of the 2-bit field
#define TUSB_CLS_AUDIO20_BM_CONTROL_NOT_PRESENT   0x0U    // 00b: control not present
#define TUSB_CLS_AUDIO20_BM_CONTROL_READ_ONLY     0x1U    // 01b: present but read-only
#define TUSB_CLS_AUDIO20_BM_CONTROL_READ_WRITE    0x3U    // 11b: present and read-write
#define TUSB_CLS_AUDIO20_BM_CONTROL_RESERVED      0x2U    // 10b: not allowed
// mask value
#define TUSB_CLS_AUDIO20_BM_CONTROL_MASK          0x3U


//////////////////////////////////////////////////////////////////////////
//
// Helper macros
//
//////////////////////////////////////////////////////////////////////////

// helper macros for defining the bmControls bit mask (in the descriptor)
#define TUSB_CLS_AUDIO20_CONTROL_PRESENT(bitnb)       ( 0x1U<<(bitnb) )
#define TUSB_CLS_AUDIO20_CONTROL_PROGRAMMABLE(bitnb)  ( 0x3U<<(bitnb) )

// helper macros for checking the bmControls bit mask (for host use)
#define TUSB_CLS_AUDIO20_IS_CONTROL_PRESENT(bm,bitnb)       ( 0 != ((bm) & (0x1U<<(bitnb))) )
#define TUSB_CLS_AUDIO20_IS_CONTROL_PROGRAMMABLE(bm,bitnb)  ( 0 != ((bm) & (0x2U<<(bitnb))) )




//////////////////////////////////////////////////////////////////////////
//
// Audio 2.0 request parameter block layout
//
//////////////////////////////////////////////////////////////////////////

//
// Layout 1 Parameter Block - unsigned
//
typedef struct tagT_UsbClsAudio20_AC_CurBlock1ByteU
{
    T_UINT8 bCur;
} T_UsbClsAudio20_AC_CurBlock1ByteU;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_CurBlock1ByteU, 1);

typedef struct tagT_UsbClsAudio20_AC_Range1ByteU
{
    T_UINT8 bMin;
    T_UINT8 bMax;
    T_UINT8 bRes;
} T_UsbClsAudio20_AC_Range1ByteU;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_Range1ByteU, 3);

typedef struct tagT_UsbClsAudio20_AC_RangeBlock1ByteU
{
    T_LE_UINT16 wNumSubRanges;
    T_UsbClsAudio20_AC_Range1ByteU Range[1];   /* variable size */
} T_UsbClsAudio20_AC_RangeBlock1ByteU;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_RangeBlock1ByteU, 5);


//
// Layout 2 Parameter Block - unsigned
//
typedef struct tagT_UsbClsAudio20_AC_CurBlock2ByteU
{
    T_LE_UINT16 wCur;
} T_UsbClsAudio20_AC_CurBlock2ByteU;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_CurBlock2ByteU, 2);

typedef struct tagT_UsbClsAudio20_AC_Range2ByteU
{
    T_LE_UINT16 wMin;
    T_LE_UINT16 wMax;
    T_LE_UINT16 wRes;
} T_UsbClsAudio20_AC_Range2ByteU;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_Range2ByteU, 6);

typedef struct tagT_UsbClsAudio20_AC_RangeBlock2ByteU
{
    T_LE_UINT16 wNumSubRanges;
    T_UsbClsAudio20_AC_Range2ByteU Range[1];   /* variable size */
} T_UsbClsAudio20_AC_RangeBlock2ByteU;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_RangeBlock2ByteU, 8);


//
// Layout 2 Parameter Block - signed
//
typedef struct tagT_UsbClsAudio20_AC_CurBlock2ByteS
{
    T_LE_INT16 wCur;
} T_UsbClsAudio20_AC_CurBlock2ByteS;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_CurBlock2ByteS, 2);

typedef struct tagT_UsbClsAudio20_AC_Range2ByteS
{
    T_LE_INT16 wMin;
    T_LE_INT16 wMax;
    T_LE_INT16 wRes;
} T_UsbClsAudio20_AC_Range2ByteS;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_Range2ByteS, 6);

typedef struct tagT_UsbClsAudio20_AC_RangeBlock2ByteS
{
    T_LE_UINT16 wNumSubRanges;
    T_UsbClsAudio20_AC_Range2ByteS Range[1];    /* variable size */
} T_UsbClsAudio20_AC_RangeBlock2ByteS;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_RangeBlock2ByteS, 8);


//
// Layout 3 Parameter Block - unsigned
//
typedef struct tagT_UsbClsAudio20_AC_CurBlock4ByteU
{
    T_LE_UINT32 dCur;
} T_UsbClsAudio20_AC_CurBlock4ByteU;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_CurBlock4ByteU, 4);

typedef struct tagT_UsbClsAudio20_AC_Range4ByteU
{
    T_LE_UINT32 dMin; /* unaligned */
    T_LE_UINT32 dMax; /* unaligned */
    T_LE_UINT32 dRes; /* unaligned */
} T_UsbClsAudio20_AC_Range4ByteU;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_Range4ByteU, 12);

typedef struct tagT_UsbClsAudio20_AC_RangeBlock4ByteU
{
    T_LE_UINT16 wNumSubRanges;
    T_UsbClsAudio20_AC_Range4ByteU Range[1];   /* variable size */
} T_UsbClsAudio20_AC_RangeBlock4ByteU;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_RangeBlock4ByteU, 14);


//
// Layout 3 Parameter Block - signed
//
typedef struct tagT_UsbClsAudio20_AC_CurBlock4ByteS
{
    T_LE_INT32 dCur;
} T_UsbClsAudio20_AC_CurBlock4ByteS;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_CurBlock4ByteS, 4);

typedef struct tagT_UsbClsAudio20_AC_Range4ByteS
{
    T_LE_INT32 dMin; /* unaligned */
    T_LE_INT32 dMax; /* unaligned */
    T_LE_INT32 dRes; /* unaligned */
} T_UsbClsAudio20_AC_Range4ByteS;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_Range4ByteS, 12);

typedef struct tagT_UsbClsAudio20_AC_RangeBlock4ByteS
{
    T_LE_UINT16 wNumSubRanges;
    T_UsbClsAudio20_AC_Range4ByteS Range[1];   /* variable size */
} T_UsbClsAudio20_AC_RangeBlock4ByteS;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_RangeBlock4ByteS, 14);


//
// Parameter Block for Valid Alternate Settings Control
//
typedef struct tagT_UsbClsAudio20_AC_ValidASBlock
{
    T_UINT8 bControlSize;           // size of bmValidAltSettings in bytes
    T_UINT8 bmValidAltSettings[1];  // variable size
} T_UsbClsAudio20_AC_ValidASBlock;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_ValidASBlock, 2);



//////////////////////////////////////////////////////////////////////////
//
// class 2.0 interrupt endpoint data message
//
//////////////////////////////////////////////////////////////////////////

typedef struct tagT_UsbClsAudio20_AC_InterruptDataMessage {
    T_UINT8    bInfo;
    T_UINT8    bAttribute;
    T_LE_UINT16 wValue;   // control selector (high) and channel (low)
    T_LE_UINT16 wIndex;   // entity (high) and IF/EP (low)
} T_UsbClsAudio20_AC_InterruptDataMessage;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_InterruptDataMessage, 6);




//////////////////////////////////////////////////////////////////////////
//
// audio spec 2.0 descriptors
//
//////////////////////////////////////////////////////////////////////////

typedef struct tagT_UsbClsAudio20_ChannelClusterDescriptor
{
    T_UINT8      bNrChannels;
    T_LE_UINT32  bmChannelConfig; /* unaligned */
    T_UINT8      iChannelNames;
} T_UsbClsAudio20_ChannelClusterDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_ChannelClusterDescriptor, 6);

/* audio control descriptors */

typedef struct tagT_UsbClsAudio20_AC_InterfaceHeaderDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;     /* TUSB_CLS_AUDIO_INTERFACE_DESCRIPTOR */
    T_UINT8      bDescriptorSubtype;  /* TUSB_CLS_AUDIO_AC_HEADER_DESCRIPTOR */
    T_LE_UINT16  bcdADC;        /* unaligned */
    T_UINT8      bCategory;
    T_LE_UINT16  wTotalLength;  /* unaligned */
    T_UINT8      bmControls;
} T_UsbClsAudio20_AC_InterfaceHeaderDescriptor;

#define TUSB_SIZEOF_CLS_AUDIO20_AC_IF_HEAD_DESC               9
TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_InterfaceHeaderDescriptor, 9);



/* AC Clock Source Descriptor */
typedef struct tagT_UsbClsAudio20_AC_ClockSourceDescriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;     /* TUSB_CLS_AUDIO_INTERFACE_DESCRIPTOR */
    T_UINT8   bDescriptorSubtype;  /* TUSB_CLS_AUDIO20_AC_CLOCK_SOURCE */
    T_UINT8   bClockID;
    T_UINT8   bmAttributes;
    T_UINT8   bmControls;
    T_UINT8   bAssocTerminal;
    T_UINT8   iClockSource;
} T_UsbClsAudio20_AC_ClockSourceDescriptor;

#define TUSB_SIZEOF_CLS_AUDIO20_AC_CLK_SRC_DESC           8
TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_ClockSourceDescriptor, 8);

// bmAttributes field: clock source type
#define TUSB_CLS_AUDIO20_CSTYPE_EXT         0x00
#define TUSB_CLS_AUDIO20_CSTYPE_INT_FIX     0x01
#define TUSB_CLS_AUDIO20_CSTYPE_INT_VAR     0x02
#define TUSB_CLS_AUDIO20_CSTYPE_INT_PROG    0x03
// bmAttributes field: SOF-synchronized
#define TUSB_CLS_AUDIO20_CS_SOF_SYNCHED     0x04

// helper macros (for host use)
#define TUSB_CLS_AUDIO20_CSTYPE_FROM_ATTR(bmAttributes)   ( (bmAttributes) & 0x03 )
#define TUSB_CLS_AUDIO20_CSTYPE_IS_EXT(bmAttributes)      ( TUSB_CLS_AUDIO20_CSTYPE_EXT == TUSB_CLS_AUDIO20_CSTYPE_FROM_ATTR(bmAttributes) )
#define TUSB_CLS_AUDIO20_CSTYPE_IS_INT_FIX(bmAttributes)  ( TUSB_CLS_AUDIO20_CSTYPE_INT_FIX == TUSB_CLS_AUDIO20_CSTYPE_FROM_ATTR(bmAttributes) )
#define TUSB_CLS_AUDIO20_CSTYPE_IS_INT_VAR(bmAttributes)  ( TUSB_CLS_AUDIO20_CSTYPE_INT_VAR == TUSB_CLS_AUDIO20_CSTYPE_FROM_ATTR(bmAttributes) )
#define TUSB_CLS_AUDIO20_CSTYPE_IS_INT_PROG(bmAttributes) ( TUSB_CLS_AUDIO20_CSTYPE_INT_PROG == TUSB_CLS_AUDIO20_CSTYPE_FROM_ATTR(bmAttributes) )
#define TUSB_CLS_AUDIO20_CS_IS_SOF_SYNCH(bmAttributes) ( 0 != ((bmAttributes) & TUSB_CLS_AUDIO20_CS_SOF_SYNCHED) )

// bmControls bit number assignments for controls
// see also TUSB_CLS_AUDIO20_CONTROL_PRESENT and TUSB_CLS_AUDIO20_CONTROL_PROGRAMMABLE macros
#define TUSB_CLS_AUDIO20_CSCTRL_FREQUENCY     0
#define TUSB_CLS_AUDIO20_CSCTRL_VALIDITY      2



/* AC Clock Selector Descriptor */
typedef struct tagT_UsbClsAudio20_AC_ClockSelectorDescriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;     /* TUSB_CLS_AUDIO_INTERFACE_DESCRIPTOR */
    T_UINT8   bDescriptorSubtype;  /* TUSB_CLS_AUDIO20_AC_CLOCK_SELECTOR */
    T_UINT8   bClockID;
    T_UINT8   bNrInPins;
    T_UINT8   baCSourceID[1];    /* variable size */
//   T_UINT8   bmControls;
//   T_UINT8   iClockSelector;
} T_UsbClsAudio20_AC_ClockSelectorDescriptor;

#define TUSB_SIZEOF_CLS_AUDIO20_AC_CLK_SEL_DESC_MIN       8

// bmControls bit number assignments for controls
// see also TUSB_CLS_AUDIO20_CONTROL_PRESENT and TUSB_CLS_AUDIO20_CONTROL_PROGRAMMABLE macros
#define TUSB_CLS_AUDIO20_CXCTRL_SELECTOR    0



/* AC Clock Multiplier Descriptor */
typedef struct tagT_UsbClsAudio20_AC_ClockMultiplierDescriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;
    T_UINT8   bDescriptorSubtype;
    T_UINT8   bClockID;
    T_UINT8   bCSourceID;
    T_UINT8   bmControls;
    T_UINT8   iClockMultiplier;
} T_UsbClsAudio20_AC_ClockMultiplierDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_ClockMultiplierDescriptor, 7);



/* AC Input Terminal Descriptor */
typedef struct tagT_UsbClsAudio20_AC_InputTerminalDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bTerminalID;
    T_LE_UINT16  wTerminalType;   /* unaligned */
    T_UINT8      bAssocTerminal;
    T_UINT8      bCSourceID;
    T_UINT8      bNrChannels;
    T_LE_UINT32  bmChannelConfig; /* unaligned */
    T_UINT8      iChannelNames;
    T_LE_UINT16  bmControls;      /* unaligned */
    T_UINT8      iTerminal;
} T_UsbClsAudio20_AC_InputTerminalDescriptor;

#define TUSB_SIZEOF_CLS_AUDIO20_AC_IN_TERM_DESC             17
TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_InputTerminalDescriptor, 17);



/* AC Output Terminal Descriptor */
typedef struct tagT_UsbClsAudio20_AC_OutputTerminalDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bTerminalID;
    T_LE_UINT16  wTerminalType;   /* unaligned */
    T_UINT8      bAssocTerminal;
    T_UINT8      bSourceID;
    T_UINT8      bCSourceID;
    T_LE_UINT16  bmControls;    /* unaligned */
    T_UINT8      iTerminal;
} T_UsbClsAudio20_AC_OutputTerminalDescriptor;

#define TUSB_SIZEOF_CLS_AUDIO20_AC_OUT_TERM_DESC             12
TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_OutputTerminalDescriptor, 12);



/* AC Mixer Unit Descriptor */
typedef struct tagT_UsbClsAudio20_AC_MixerUnitDescriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;
    T_UINT8   bDescriptorSubtype;
    T_UINT8   bUnitID;
    T_UINT8   bNrInPins;
    T_UINT8   baSourceID[1];             /* variable size */
//   T_UINT8   bNrChannels;
//   T_LE_UINT32  bmChannelConfig;
//   T_UINT8   iChannelNames;
//   T_UINT8   bmMixerControls[1];     /* variable size */
//   T_UINT8   bmControls;
//   T_UINT8   iMixer;
} T_UsbClsAudio20_AC_MixerUnitDescriptor;

/* AC Selector Unit Descriptor */
typedef struct tagT_UsbClsAudio20_AC_SelectorUnitDescriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;
    T_UINT8   bDescriptorSubtype;
    T_UINT8   bUnitID;
    T_UINT8   bNrInPins;
    T_UINT8   baSourceID[1];            /* variable size */
//   T_UINT8   bmControls;
//   T_UINT8   iSelector;
} T_UsbClsAudio20_AC_SelectorUnitDescriptor;

typedef struct tagT_UsbClsAudio20_AC_FeatureUnitDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bUnitID;
    T_UINT8      bSourceID;
    T_LE_UINT32  bmaControls[1];  /* unaligned */  /* variable size */
//   T_UINT8   iFeature;
} T_UsbClsAudio20_AC_FeatureUnitDescriptor;

// helper macro that calculates the size, in bytes, of the T_UsbClsAudio20_AC_FeatureUnitDescriptor
// channels does NOT include the master. The master channel must always be present.
#define TUSB_SIZEOF_CLS_AUDIO20_AC_FEATURE_UNIT_DESCRIPTOR(channels)    (6 + (((channels) + 1) * sizeof(T_LE_UINT32)))


// bmaControls bit number assignments for controls
// see also TUSB_CLS_AUDIO20_CONTROL_PRESENT and TUSB_CLS_AUDIO20_CONTROL_PROGRAMMABLE macros
#define TUSB_CLS_AUDIO20_AC_FU_CONTROL_MUTE         0
#define TUSB_CLS_AUDIO20_AC_FU_CONTROL_VOLUME       2
#define TUSB_CLS_AUDIO20_AC_FU_CONTROL_BASS         4
#define TUSB_CLS_AUDIO20_AC_FU_CONTROL_MID          6
#define TUSB_CLS_AUDIO20_AC_FU_CONTROL_TREBLE       8

// USB volume = -INF
#define TUSB_CLS_AUDIO20_VOLUME_SILENCE   (-32768)



/* AC Sampling Rate Converter Unit Descriptor */
typedef struct tagT_UsbClsAudio20_AC_SamplingRateConverterUnitDescriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;
    T_UINT8   bDescriptorSubtype;
    T_UINT8   bUnitID;
    T_UINT8   bSourceID;
    T_UINT8   bCSourceInID;
    T_UINT8   bCSourceOutID;
    T_UINT8   iSRC;
} T_UsbClsAudio20_AC_SamplingRateConverterUnitDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_SamplingRateConverterUnitDescriptor, 8);

/* AC Effect Unit Descriptor */
/* NOTE: The layout of the various effect unit descriptors is always
             the same. Therefor we only have to declare the effect unit
             descriptor once. This struct can be used for:
                    * Parametric Equalizer Section Effect Unit Descriptor
                    * Reverberation Effect Unit Descriptor
                    * Modulation Delay Effect Unit Descriptor
                    * Dynamic Range Compressor Effect Unit Descriptor */
typedef struct tagT_UsbClsAudio20_AC_EffectUnitDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bUnitID;
    T_LE_UINT16  wEffectType;     /* unaligned */
    T_UINT8      bSourceID;
    T_LE_UINT32  bmaControls[1];  /* unaligned */  /* variable size */
//   T_UINT8   iEffects;
} T_UsbClsAudio20_AC_EffectUnitDescriptor;

/* AC Processing Unit Descriptor Common part */
typedef struct tagT_UsbClsAudio20_AC_ProcessingUnitDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bUnitID;
    T_LE_UINT16  wProcessType;  /* unaligned */
    T_UINT8      bNrInPins;
    T_UINT8      baSourceID[1];        /* variable size */
//   T_UINT8      bNrChannels;
//   T_LE_UINT32  bmChannelConfig;
//   T_UINT8      iChannelNames;
//   T_LE_UINT16  bmControls;
//   T_UINT8      iProcessing;
} T_UsbClsAudio20_AC_ProcessingUnitDescriptor;

/* AC Up/Down-mix Processing Unit Descriptor */
typedef struct tagT_UsbClsAudio20_AC_UpDownMixProcessingUnitDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bUnitID;
    T_LE_UINT16  wProcessType;      /* unaligned */
    T_UINT8      bNrInPins;
    T_UINT8      bSourceID;
    T_UINT8      bNrChannels;
    T_LE_UINT32  bmChannelConfig;   /* unaligned */
    T_UINT8      iChannelNames;
    T_LE_UINT16  bmControls;
    T_UINT8      iProcessing;
    T_UINT8      bNrModes;
    //T_LE_UINT32  daModes[1];       /* variable size */
} T_UsbClsAudio20_AC_UpDownMixProcessingUnitDescriptor;

/* AC Dolby Prologic Processing Unit Descriptor */
typedef struct tagT_UsbClsAudio20_AC_DolbyPrologicProcessingUnitDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bUnitID;
    T_LE_UINT16  wProcessType;    /* unaligned */
    T_UINT8      bNrInPins;
    T_UINT8      bSourceID;
    T_UINT8      bNrChannels;
    T_LE_UINT32  bmChannelConfig; /* unaligned */
    T_UINT8      iChannelNames;
    T_LE_UINT16  bmControls;      /* unaligned */
    T_UINT8      iProcessing;
    T_UINT8      bNrModes;
    T_LE_UINT32  daModes[1];      /* unaligned */  /* variable size */
} T_UsbClsAudio20_AC_DolbyPrologicProcessingUnitDescriptor;

/* AC Stereo Extender Processing Unit Descriptor */
typedef struct tagT_UsbClsAudio20_AC_StereoExtenderProcessingUnitDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bUnitID;
    T_LE_UINT16  wProcessType;    /* unaligned */
    T_UINT8      bNrInPins;
    T_UINT8      bSourceID;
    T_UINT8      bNrChannels;
    T_LE_UINT32  bmChannelConfig; /* unaligned */
    T_UINT8      iChannelNames;
    T_LE_UINT16  bmControls;      /* unaligned */
    T_UINT8      iProcessing;
} T_UsbClsAudio20_AC_StereoExtenderProcessingUnitDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_StereoExtenderProcessingUnitDescriptor, 17);

/* AC Extension Unit Descriptor */
typedef struct tagT_UsbClsAudio20_AC_ExtensionUnitDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;      /* TUSB_CLS_AUDIO_INTERFACE_DESCRIPTOR */
    T_UINT8      bDescriptorSubtype;   /* TUSB_CLS_AUDIO20_AC_EXTENSION_UNIT */
    T_UINT8      bUnitID;
    T_LE_UINT16  wExtensionCode;  /* unaligned */
    T_UINT8      bNrInPins;
    T_UINT8      baSourceID[1];      /* variable size */
//   T_UINT8      bNrChannels;
//   T_LE_UINT32  bmChannelConfig;
//   T_UINT8      iChannelNames;
//   T_UINT8      bmControls;
//   T_UINT8      iExtension;
} T_UsbClsAudio20_AC_ExtensionUnitDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AC_ExtensionUnitDescriptor, 8);

// helper macro that calculates the size, in bytes, of the T_UsbClsAudio20_AC_ExtensionUnitDescriptor
#define TUSB_SIZEOF_CLS_AUDIO20_AC_EXTENSION_UNIT_DESCRIPTOR(bNrInPins)  ( 15 + ((bNrInPins) * sizeof(T_UINT8)) )

// bmControls bit number assignments for controls
#define TUSB_CLS_AUDIO20_AC_XU_CONTROL_ENABLE     0


//////////////////////////////////////////////////////
// audio streaming descriptors
//////////////////////////////////////////////////////

/* Class-specific AS Interface Descriptor */
typedef struct tagT_UsbClsAudio20_AS_InterfaceDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;     /* TUSB_CLS_AUDIO_INTERFACE_DESCRIPTOR */
    T_UINT8      bDescriptorSubtype;  /* TUSB_CLS_AUDIO_AS_GENERAL */
    T_UINT8      bTerminalLink;
    T_UINT8      bmControls;
    T_UINT8      bFormatType;
    T_LE_UINT32  bmFormats;       /* unaligned */
    T_UINT8      bNrChannels;
    T_LE_UINT32  bmChannelConfig; /* unaligned */
    T_UINT8      iChannelNames;
} T_UsbClsAudio20_AS_InterfaceDescriptor;

#define TUSB_SIZEOF_CLS_AUDIO20_AS_IF_DESC              16
TB_CHECK_SIZEOF(T_UsbClsAudio20_AS_InterfaceDescriptor, 16);

// bmControls bit number assignments for controls
// see also TUSB_CLS_AUDIO20_CONTROL_PRESENT and TUSB_CLS_AUDIO20_CONTROL_PROGRAMMABLE macros
#define TUSB_CLS_AUDIO20_ASCTRL_ACTIVE_ALT_SETTING      0
#define TUSB_CLS_AUDIO20_ASCTRL_VALID_ALT_SETTINGS      2



/* AS Encoder Descriptor */
typedef struct tagT_UsbClsAudio20_AS_EncoderDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bEncoderID;
    T_UINT8      bEncoder;
    T_LE_UINT32  bmControls;  /* unaligned */
    T_UINT8      iParam1;
    T_UINT8      iParam2;
    T_UINT8      iParam3;
    T_UINT8      iParam4;
    T_UINT8      iParam5;
    T_UINT8      iParam6;
    T_UINT8      iParam7;
    T_UINT8      iParam8;
    T_UINT8      iEncoder;
} T_UsbClsAudio20_AS_EncoderDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AS_EncoderDescriptor, 18);

/* common part for the AS Decoder Descriptors */
typedef struct tagT_UsbClsAudio20_AS_DecoderCommonDescriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;
    T_UINT8   bDescriptorSubtype;
    T_UINT8   bDecoderID;
    T_UINT8   bDecoder;
} T_UsbClsAudio20_AS_DecoderCommonDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AS_DecoderCommonDescriptor, 5);

/* AS MPEG Decoder Descriptor */
typedef struct tagT_UsbClsAudio20_AS_MpegDecoderDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bDecoderID;
    T_UINT8      bDecoder;
    T_LE_UINT16  bmMPEGCapabilities;  /* unaligned */
    T_UINT8      bmMPEGFeatures;
    T_UINT8      bmControls;
    T_UINT8      iDecoder;
} T_UsbClsAudio20_AS_MpegDecoderDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AS_MpegDecoderDescriptor, 10);

/* AS AC-3 Decoder Descriptor */
typedef struct tagT_UsbClsAudio20_AS_Ac3DecoderDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bDecoderID;
    T_UINT8      bDecoder;
    T_LE_UINT32  bmBSID;      /* unaligned */
    T_UINT8      bmAC3Features;
    T_UINT8      bmControls;
    T_UINT8      iDecoder;
} T_UsbClsAudio20_AS_Ac3DecoderDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AS_Ac3DecoderDescriptor, 12);

/* AS WMA Decoder Descriptor */
typedef struct tagT_UsbClsAudio20_AS_WmaDecoderDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bDecoderID;
    T_UINT8      bDecoder;
    T_LE_UINT16  bmWMAProfile;  /* unaligned */
    T_UINT8      bmControls;
    T_UINT8      iDecoder;
} T_UsbClsAudio20_AS_WmaDecoderDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AS_WmaDecoderDescriptor, 9);

/* AS DTS Decoder Descriptor */
typedef struct tagT_UsbClsAudio20_AS_DtsDecoderDescriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;
    T_UINT8   bDescriptorSubtype;
    T_UINT8   bDecoderID;
    T_UINT8   bDecoder;
    T_UINT8   bmCapabilities;
    T_UINT8   bmControls;
    T_UINT8   iDecoder;
} T_UsbClsAudio20_AS_DtsDecoderDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio20_AS_DtsDecoderDescriptor, 8);


/* AS Isochronous Audio Data Endpoint */
typedef struct tagT_UsbClsAudio20_AS_IsoDataEndpointDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;    /* TUSB_CLS_AUDIO_ENDPOINT_DESCRIPTOR */
    T_UINT8      bDescriptorSubtype;
    T_UINT8      bmAttributes;
    T_UINT8      bmControls;
    T_UINT8      bLockDelayUnits;
    T_LE_UINT16  wLockDelay;    /* unaligned */
} T_UsbClsAudio20_AS_IsoDataEndpointDescriptor;

#define TUSB_SIZEOF_CLS_AUDIO20_AS_ISO_DATA_EP_DESC           8
TB_CHECK_SIZEOF(T_UsbClsAudio20_AS_IsoDataEndpointDescriptor, 8);

#define TUSB_CLS_AUDIO20_AS_bLockDelayUnits_undefined 0
#define TUSB_CLS_AUDIO20_AS_bLockDelayUnits_ms        1
#define TUSB_CLS_AUDIO20_AS_bLockDelayUnits_samples   2


/* format type descriptors */

/* format type 1 descriptor */
typedef struct tagT_UsbClsAudio20_AS_FormatType1Descriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;      /* TUSB_CLS_AUDIO_INTERFACE_DESCRIPTOR */
    T_UINT8   bDescriptorSubtype;   /* TUSB_CLS_AUDIO_AS_FORMAT_TYPE */
    T_UINT8   bFormatType;
    T_UINT8   bSubslotSize;
    T_UINT8   bBitResolution;
} T_UsbClsAudio20_AS_FormatType1Descriptor;

#define TUSB_SIZEOF_CLS_AUDIO20_AS_FMT_TYPE1_DESC         6
TB_CHECK_SIZEOF(T_UsbClsAudio20_AS_FormatType1Descriptor, 6);

//### todo: add other desc. for Audio 2.0 spec.

// restore packing
#include "tbase_packrestore.h"

#endif // __tusb_cls_audio20_h__

/******************************** EOF ****************************************/

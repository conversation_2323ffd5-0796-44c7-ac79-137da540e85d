/************************************************************************
*  Generic utilities for basic_string handling.
*
*  Thesycon GmbH, Germany
*  http://www.thesycon.de
*
************************************************************************/

#include "libwn_min_global.h"

// module is empty if .h file was not included (category turned off)
#ifdef __TbStdStringUtils_h__


#ifdef LIBTB_NAMESPACE
namespace LIBTB_NAMESPACE {
#endif

std::wstring TbStdReplaceAll( const std::wstring src, const std::wstring& oldSub, const std::wstring& newSub)
{
    //initialization
    std::wstring dst;
    dst.reserve(src.length());

    //error check 
    if (oldSub.length() == 0) {
        return dst;
    }
    
    std::wstring::size_type lastPos = 0;
    std::wstring::size_type findPos;

    while (std::string::npos != (findPos = src.find(oldSub, lastPos)))
    {
        dst.append(src, lastPos, findPos - lastPos);
        dst += newSub;
        lastPos = findPos + oldSub.length();
    }

    //care for the rest after last occurrence
    dst += src.substr(lastPos);

    return dst;
}

#ifdef LIBTB_NAMESPACE
}
#endif


#endif

/******************************** EOF ***********************************/

/************************************************************************
 *
 *  Module:       TbStringUtils.h
 *  Description:  generic utilities for char and string handling
 *
 *  Runtime Env.: any
 *  Author(s):    <PERSON><PERSON>, Frank <PERSON>
 *  Company:      Thesycon GmbH, Ilmenau
 ************************************************************************/

#ifndef __TbStringUtils_h__
#define __TbStringUtils_h__

// optionally put everything into a namespace
#ifdef LIBTB_NAMESPACE
namespace LIBTB_NAMESPACE {
#endif

// char <--> wide char conversion by means of cast
inline wchar_t  TbWcharFromChar(char c)     { return ( (unsigned char)c ); }
inline char     TbCharFromWchar(wchar_t wc) { return ( (char)wc ); }


// These enable the compiler to lookup the right function based on source type.
inline char TbConvertToChar(char c)     { return c; }
inline char TbConvertToChar(wchar_t c)  { return TbCharFromWchar(c); }

inline wchar_t TbConvertToWchar(char c)     { return TbWcharFromChar(c); }
inline wchar_t TbConvertToWchar(wchar_t c)  { return c; }


// Some tricky helpers that enable the compiler to lookup the right function
// based on source *and* destination type.
inline char     TbCastCharacter(char    dst, char    c)   { TB_UNUSED_PARAM(dst) return TbConvertToChar(c); }
inline wchar_t  TbCastCharacter(wchar_t dst, wchar_t c)   { TB_UNUSED_PARAM(dst) return TbConvertToWchar(c); }
inline wchar_t  TbCastCharacter(wchar_t dst, char    c)   { TB_UNUSED_PARAM(dst) return TbConvertToWchar(c); }
inline char     TbCastCharacter(char    dst, wchar_t c)   { TB_UNUSED_PARAM(dst) return TbConvertToChar(c); }


// Return pointer to an empty string based on character type.
inline const char*    TbEmptyString(char c)     { TB_UNUSED_PARAM(c) return  ""; }
inline const wchar_t* TbEmptyString(wchar_t c)  { TB_UNUSED_PARAM(c) return L""; }


//
// returns true if c is a lower case character
//
inline bool TbIsLowerCaseChar(char c)     { return ( c >=  'a' && c <=  'z' ); }
inline bool TbIsLowerCaseChar(wchar_t c)  { return ( c >= L'a' && c <= L'z' ); }

//
// returns true if c is an upper case character
//
inline bool TbIsUpperCaseChar(char c)     { return ( c >=  'A' && c <=  'Z' ); }
inline bool TbIsUpperCaseChar(wchar_t c)  { return ( c >= L'A' && c <= L'Z' ); }

//
// returns true if c is a white space character
//
inline bool TbIsWhiteSpaceChar(char c)    { return ( (c == 0x20) || (c >= 0x09 && c <= 0x0D) ); }
inline bool TbIsWhiteSpaceChar(wchar_t c) { return ( (c == 0x20) || (c >= 0x09 && c <= 0x0D) ); }


//
// Convert c to lower case
//
char    TbToLowerCaseChar(char c);
wchar_t TbToLowerCaseChar(wchar_t c);

//
// Convert c to upper case
//
char    TbToUpperCaseChar(char c);
wchar_t TbToUpperCaseChar(wchar_t c);



//
// TbStringLen
// Returns the length of the specified string, in characters,
// not including the terminating null character.
// str points to a null-terminated string.
//
// NOTE: The return value specifies characters, not bytes!
//
unsigned int
TbStringLen(
    const char* str
    );
unsigned int
TbStringLen(
    const wchar_t* str
    );



//
// TbStringLCopy -- String copy support functions.
// Size of destination buffer is specified by means of a limit pointer.
// Destination and source buffer MUST NOT overlap.
//
// There are four functions that behave in the same way:
// source points to a null-terminated string, destination points to a user-provided buffer.
// destinationLimit points to the first character behind the user-provided buffer.
// Thus, (destinationLimit - destination) equals the size of the buffer, in characters.
// The function copies characters from source to destination and in any case
// appends a null character to the destination string.
// The function returns a pointer to the terminating null character that was written
// to the destination buffer.
//
// NOTE: destinationLimit MUST BE greater than destination,
//       that is the destination buffer size must not be zero.
//
char*
TbStringLCopy(
    char* destination,
    char* destinationLimit,
    const char* source
    );
wchar_t*
TbStringLCopy(
    wchar_t* destination,
    wchar_t* destinationLimit,
    const wchar_t* Source
    );
char*
TbStringLCopy(
    char* destination,
    char* destinationLimit,
    const wchar_t* source
    );
wchar_t*
TbStringLCopy(
    wchar_t* destination,
    wchar_t* destinationLimit,
    const char* source
    );
// return value of this function is likely to be ignored
//lint -esym(534, TbStringLCopy)


//
// TbStringNCopy -- String copy support functions.
// Size of destination buffer is specified in terms of characters.
// Destination and source buffer MUST NOT overlap.
//
// There are four functions that behave in the same way:
// source points to a null-terminated string. destination points to a user-provided buffer.
// Copy maxChars characters (including the terminating null) from source to destination.
// In any case, append a null character to the destination string.
// Return the number of characters copied, not counting the terminating null.
//
// NOTE: maxChars specifies characters, not bytes!
//       maxChars MUST NOT be zero.
//
unsigned int
TbStringNCopy(
    char* destination,
    const char* source,
    unsigned int maxChars
    );
unsigned int
TbStringNCopy(
    wchar_t* destination,
    const wchar_t* source,
    unsigned int maxChars
    );
unsigned int
TbStringNCopy(
    char* destination,
    const wchar_t* source,
    unsigned int maxChars
    );
unsigned int
TbStringNCopy(
    wchar_t* destination,
    const char* source,
    unsigned int maxChars
    );
// return value of this function is likely to be ignored
//lint -esym(534, TbStringNCopy)


//
// A helper macro that avoids a potential pitfall when TbStringNCopy is used
// to copy a string to an array of characters, for example:
//
//   WCHAR buf[20];
//   TbStringNCopy(buf, L"Some string", TB_ARRAY_ELEMENTS(buf));
//
// is replaced by:
//
//   TbStringNCopyToArray(buf, L"Some string");
//
// This reduces typing and guarantees that the correct value
// for the maxChars argument is given.
//
#define TbStringNCopyToArray(dst,src) TbStringNCopy((dst),(src),TB_ARRAY_ELEMENTS(dst))



//
// TbStringLCat -- String concatenation
// Size of destination buffer is specified by means of a limit pointer.
//
// Destination and source *both* point to a null-terminated string.
// destinationLimit points to the first character behind the user-provided buffer at destination.
// Thus, (destinationLimit - destination) equals the size of the provided buffer, in characters.
// The function appends the characters in source to the existing string at destination
// and in any case appends a null character to the resulting string.
// The function returns a pointer to the terminating null character that was written
// to the destination buffer at the end of the resulting string.
//
// NOTE: destinationLimit MUST BE greater than destination,
//       that is the destination buffer size must not be zero.
//
char*
TbStringLCat(
    char* destination,
    char* destinationLimit,
    const char* source
    );
wchar_t*
TbStringLCat(
    wchar_t* destination,
    wchar_t* destinationLimit,
    const wchar_t* source
    );
char*
TbStringLCat(
    char* destination,
    char* destinationLimit,
    const wchar_t* source
    );
wchar_t*
TbStringLCat(
    wchar_t* destination,
    wchar_t* destinationLimit,
    const char* source
    );
// return value of this function is likely to be ignored
//lint -esym(534, TbStringLCat)


//
// TbStringNCat -- String concatenation
// Size of destination buffer is specified in terms of characters.
//
// Destination and source *both* point to a null-terminated string.
// The function appends the characters in source to the existing string at destination
// and in any case appends a null character to the resulting string.
// maxChars specifies the maximum number of characters the destination buffer can hold,
// including the terminating null.
// The function returns the length of the resulting string, not counting the terminating null.
//
// NOTE: maxChars specifies characters, not bytes!
//       maxChars MUST NOT be zero.
//
unsigned int
TbStringNCat(
    char* destination,
    const char* source,
    unsigned int maxChars
    );
unsigned int
TbStringNCat(
    wchar_t* destination,
    const wchar_t* source,
    unsigned int maxChars
    );
unsigned int
TbStringNCat(
    char* destination,
    const wchar_t* source,
    unsigned int maxChars
    );
unsigned int
TbStringNCat(
    wchar_t* destination,
    const char* source,
    unsigned int maxChars
    );
// return value of this function is likely to be ignored
//lint -esym(534, TbStringNCat)


//
// A helper macro that avoids a potential pitfall when TbStringNCat is used
// to copy a string to an array of characters, for example:
//
//   WCHAR buf[20];
//   TbStringNCat(buf, L"Some string",  TB_ARRAY_ELEMENTS(buf));
//
// is replaced by:
//
//   TbStringNCatToArray(buf, L"Some string");
//
// This reduces typing and guarantees that the correct value
// for the maxChars argument is given.
//
#define TbStringNCatToArray(dst,src)  TbStringNCat((dst),(src),TB_ARRAY_ELEMENTS(dst))



//
// TbStringEqual
// Returns true if the provided strings are equal.
// Comparison is *case-sensitive*.
// s1 and s2 each point to a null-terminated string.
//
bool
TbStringEqual(
    const char* s1,
    const char* s2
    );
bool
TbStringEqual(
    const wchar_t* s1,
    const wchar_t* s2
    );

//
// TbStringEqualIgnoreCase
// Returns true if the provided strings are equal.
// Comparison is *case-insensitive*.
// s1 and s2 each point to a null-terminated string.
//
bool
TbStringEqualIgnoreCase(
    const char* s1,
    const char* s2
    );
bool
TbStringEqualIgnoreCase(
    const wchar_t* s1,
    const wchar_t* s2
    );



//
// Find the specific character c in the given null-terminated string.
// Returns the pointer to the first occurrence of this character.
// Returns NULL if the character is not contained in the string.
//
const char*
TbFindCharInString(
    const char* str,
    char c
    );
const wchar_t*
TbFindCharInString(
    const wchar_t* str,
    wchar_t c
    );


//
// destination points to a null-terminated string.
// Replace all characters in the string that are in
// the set of characters identified by charSet by newChar.
// Returns the number of characters that have been replaced.
//
// Example: replace '*' and '?' by '_'
//   TbReplaceCharsInString(s, "*?", '_');
//
unsigned int
TbReplaceCharsInString(
    char* destination,
    const char* charSet,
    char newChar
    );
unsigned int
TbReplaceCharsInString(
    wchar_t* destination,
    const wchar_t* charSet,
    wchar_t newChar
    );
// return value of this function is likely to be ignored
//lint -esym(534, TbReplaceCharsInString)


//
// Destination points to a null-terminated string.
// Replace all occurrences of oldChar in the string by newChar.
// Returns the number of characters that have been replaced.
//
unsigned int
TbReplaceCharInString(
    char* destination,
    char oldChar,
    char newChar
    );
unsigned int
TbReplaceCharInString(
    wchar_t* destination,
    wchar_t oldChar,
    wchar_t newChar
    );
// return value of this function is likely to be ignored
//lint -esym(534, TbReplaceCharInString)


//
// Destination points to a null-terminated string.
// Remove all leading white space characters
// by moving all following chars to the beginning of destination.
// See also TbIsWhiteSpaceChar().
// Returns true if at least one white space char has been removed.
// Returns false if the string was not modified.
//
bool
TbStringTrimBegin(
    char* destination
    );
bool
TbStringTrimBegin(
    wchar_t* destination
    );
// return value of this function is likely to be ignored
//lint -esym(534, TbStringTrimBegin)


//
// Destination points to a null-terminated string.
// Remove all trailing white space characters by replacing them with null.
// See also TbIsWhiteSpaceChar().
// Returns true if at least one white space char has been removed.
// Returns false if the string was not modified.
//
bool
TbStringTrimEnd(
    char* destination
    );
bool
TbStringTrimEnd(
    wchar_t* destination
    );
// return value of this function is likely to be ignored
//lint -esym(534, TbStringTrimEnd)


//
// Convert the given string to upper-case chars.
// Conversion is done in-place.
// The function returns the destination pointer.
//
char*
TbStringToUpperCase(
    char* destination
    );
wchar_t*
TbStringToUpperCase(
    wchar_t* destination
    );


//
// Convert the given string to lower-case chars.
// Conversion is done in-place.
// The function returns the destination pointer.
//
char*
TbStringToLowerCase(
    char* destination
    );
wchar_t*
TbStringToLowerCase(
    wchar_t* destination
    );



//
// Destination points to a null-terminated string.
// Remove quote characters if, and only if, the first *and* the last
// character is a quote character as specified by the quoteChar argument.
// Returns true if quote characters have been removed.
// Returns false if the string was not modified.
//
bool
TbStringRemoveQuotes(
    char* destination,
    char quoteChar = '"'
    );
bool
TbStringRemoveQuotes(
    wchar_t* destination,
    wchar_t quoteChar = L'"'
    );




//
// convert digit (range = 0..15) to ASCII character
// The function returns a char from the range '0'..'9' or 'A'..'F' or 'a'..'f'.
//
char
TbDigitToHexChar(
    int digit,  // allowed range 0..15
    bool upperCaseLetters = true  // if true 'A'..'F' is returned, 'a'..'f' otherwise
    );


//
// Convert a character from the range '0'..'9' or 'A'..'F' or 'a'..'f'
// to its corresponding integer value (range 0..15).
// The function returns -1 if an invalid char is passed.
// Can be used for wide chars and chars.
//
int
TbHexCharToDigit(
    int c
    );

#ifdef LIBTB_NAMESPACE
}
#endif

#endif // __TbStringUtils_h__

/******************************** EOF ***********************************/

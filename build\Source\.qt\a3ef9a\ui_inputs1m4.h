/********************************************************************************
** Form generated from reading UI file 'inputs1m4.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_INPUTS1M4_H
#define UI_INPUTS1M4_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <dials1m5.h>
#include <pushbuttongroups1m3.h>
#include <pushbuttongroups1m5.h>
#include <pushbuttongroups1m9.h>
#include <volumemeters1m1.h>

QT_BEGIN_NAMESPACE

class Ui_InputS1M4
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    QLineEdit *lineEdit;
    PushButtonGroupS1M9 *widgetPushButtonGroup1;
    VolumeMeterS1M1 *widgetVolumeMeter;
    DialS1M5 *widgetDial;
    PushButtonGroupS1M3 *widgetPushButtonGroup2;
    QWidget *widgetOverlay;
    PushButtonGroupS1M5 *widgetPushButtonGroup3;

    void setupUi(QWidget *InputS1M4)
    {
        if (InputS1M4->objectName().isEmpty())
            InputS1M4->setObjectName("InputS1M4");
        InputS1M4->resize(140, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(InputS1M4->sizePolicy().hasHeightForWidth());
        InputS1M4->setSizePolicy(sizePolicy);
        InputS1M4->setMinimumSize(QSize(80, 220));
        gridLayout = new QGridLayout(InputS1M4);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(InputS1M4);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 20));
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(9, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetPushButtonGroup1 = new PushButtonGroupS1M9(frame);
        widgetPushButtonGroup1->setObjectName("widgetPushButtonGroup1");
        widgetPushButtonGroup1->setGeometry(QRect(80, 40, 21, 61));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup1->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup1->setSizePolicy(sizePolicy);
        widgetPushButtonGroup1->setMinimumSize(QSize(1, 1));
        widgetVolumeMeter = new VolumeMeterS1M1(frame);
        widgetVolumeMeter->setObjectName("widgetVolumeMeter");
        widgetVolumeMeter->setGeometry(QRect(10, 40, 21, 131));
        sizePolicy.setHeightForWidth(widgetVolumeMeter->sizePolicy().hasHeightForWidth());
        widgetVolumeMeter->setSizePolicy(sizePolicy);
        widgetDial = new DialS1M5(frame);
        widgetDial->setObjectName("widgetDial");
        widgetDial->setGeometry(QRect(10, 180, 90, 41));
        sizePolicy.setHeightForWidth(widgetDial->sizePolicy().hasHeightForWidth());
        widgetDial->setSizePolicy(sizePolicy);
        widgetDial->setMinimumSize(QSize(0, 0));
        widgetPushButtonGroup2 = new PushButtonGroupS1M3(frame);
        widgetPushButtonGroup2->setObjectName("widgetPushButtonGroup2");
        widgetPushButtonGroup2->setGeometry(QRect(10, 230, 90, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup2->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup2->setSizePolicy(sizePolicy);
        widgetPushButtonGroup2->setMinimumSize(QSize(6, 7));
        widgetOverlay = new QWidget(frame);
        widgetOverlay->setObjectName("widgetOverlay");
        widgetOverlay->setGeometry(QRect(110, 40, 21, 21));
        widgetPushButtonGroup3 = new PushButtonGroupS1M5(frame);
        widgetPushButtonGroup3->setObjectName("widgetPushButtonGroup3");
        widgetPushButtonGroup3->setGeometry(QRect(80, 110, 21, 61));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup3->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup3->setSizePolicy(sizePolicy);
        widgetPushButtonGroup3->setMinimumSize(QSize(6, 5));
        pushButtonClose->raise();
        lineEdit->raise();
        widgetPushButtonGroup1->raise();
        widgetVolumeMeter->raise();
        widgetDial->raise();
        widgetPushButtonGroup2->raise();
        widgetPushButtonGroup3->raise();
        widgetOverlay->raise();

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(InputS1M4);

        QMetaObject::connectSlotsByName(InputS1M4);
    } // setupUi

    void retranslateUi(QWidget *InputS1M4)
    {
        InputS1M4->setWindowTitle(QCoreApplication::translate("InputS1M4", "Form", nullptr));
        pushButtonClose->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class InputS1M4: public Ui_InputS1M4 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_INPUTS1M4_H

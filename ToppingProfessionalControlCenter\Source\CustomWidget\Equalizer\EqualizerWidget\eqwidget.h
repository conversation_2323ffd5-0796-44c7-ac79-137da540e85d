#ifndef EQUALIZERWIDGET_H
#define EQUALIZERWIDGET_H

#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QCheckBox>
#include <QVector>
#include <QScrollArea>
#include <QFrame>
#include "comboboxs1m3.h"
#include "dials1m1.h"

struct EqWidgetItemData {
    QString type;
    float gain;
    float frequency;
    float qValue;
    bool enabled;
};

class EqWidgetIem : public QFrame
{
    Q_OBJECT

public:
    explicit EqWidgetIem(int index, QWidget* parent = nullptr);
    ~EqWidgetIem();

    void setItemData(const EqWidgetItemData& data);
    EqWidgetItemData getItemData() const;

    void setItemIndex(int index);
    int getItemIndex() const { return mIndex; }

    // 间隔和间隙设置
    void setLayoutSpacing(int spacing);
    void setLayoutMargins(int left, int top, int right, int bottom);
    void setLayoutMargins(int margin);
    void setMinimumItemWidth(int width);

    // 获取当前设置
    int getLayoutSpacing() const { return mLayoutSpacing; }
    QMargins getLayoutMargins() const;
    int getMinimumItemWidth() const { return mMinimumItemWidth; }

protected:
    void resizeEvent(QResizeEvent* event) override;

private:
    void onTypeChanged(const QString& text);
    void onGainChanged(float value);
    void onFrequencyChanged(float value);
    void onQValueChanged(float value);
    void onEnabledChanged(bool enabled);

private:
    void setupUI();
    void setupConnections();
    void updateTypeOptions();
    void applyLayoutSettings();

    int mIndex;
    EqWidgetItemData mData;

    // 布局设置
    int mLayoutSpacing;
    QMargins mLayoutMargins;
    int mMinimumItemWidth;

    QVBoxLayout* mMainLayout;
    QLabel* mItemLabel;

    ComboBoxS1M3* mTypeComboBox;

    DialS1M1* mGainDial;
    DialS1M1* mFrequencyDial;
    DialS1M1* mQValueDial;

    QCheckBox* mEnabledCheckBox;

signals:
    void dataChanged(int index, const EqWidgetItemData& data);
};

class EqWidget : public QWidget
{
    Q_OBJECT

public:
    explicit EqWidget(QWidget* parent = nullptr);
    ~EqWidget();
    void setStretchFactor(double stretchFactor);

    void setItemCount(int count);
    int getItemCount() const { return mItems.size(); }

    void addItem();
    void removeItem(int index = -1);
    void removeAllItems();

    void setEqData(const QVector<EqWidgetItemData>& data);
    QVector<EqWidgetItemData> getEqData() const;

    // 主布局间隔和间隙设置
    void setMainLayoutSpacing(int spacing);
    void setMainLayoutMargins(int left, int top, int right, int bottom);
    void setMainLayoutMargins(int margin);

    // 控制区域布局设置
    void setControlLayoutSpacing(int spacing);
    void setControlLayoutMargins(int left, int top, int right, int bottom);
    void setControlLayoutMargins(int margin);

    // 项目布局设置
    void setItemsLayoutSpacing(int spacing);
    void setItemsLayoutMargins(int left, int top, int right, int bottom);
    void setItemsLayoutMargins(int margin);

    // 滚动区域设置
    void setScrollAreaMargins(int left, int top, int right, int bottom);
    void setScrollAreaMargins(int margin);

    // 批量设置所有项目的间隔
    void setAllItemsLayoutSpacing(int spacing);
    void setAllItemsLayoutMargins(int left, int top, int right, int bottom);
    void setAllItemsLayoutMargins(int margin);
    void setAllItemsMinimumWidth(int width);

    // 获取当前设置
    int getMainLayoutSpacing() const { return mMainLayoutSpacing; }
    int getControlLayoutSpacing() const { return mControlLayoutSpacing; }
    int getItemsLayoutSpacing() const { return mItemsLayoutSpacing; }
    QMargins getMainLayoutMargins() const;
    QMargins getControlLayoutMargins() const;
    QMargins getItemsLayoutMargins() const;
    QMargins getScrollAreaMargins() const;
    
protected:
    void resizeEvent(QResizeEvent* event) override;

private:
    void onItemDataChanged(int index, const EqWidgetItemData& data);
    void onAddItemClicked();
    void onRemoveItemClicked();

private:
    void setupUI();
    void setupConnections();
    void updateItemIndices();
    void createDefaultItems();
    void applyLayoutSettings();

    void addItemInternal(int index);
    void removeItemInternal(int index);

    // 布局设置
    int mMainLayoutSpacing;
    int mControlLayoutSpacing;
    int mItemsLayoutSpacing;
    QMargins mMainLayoutMargins;
    QMargins mControlLayoutMargins;
    QMargins mItemsLayoutMargins;
    QMargins mScrollAreaMargins;

    // UI 组件
    QHBoxLayout* mMainLayout;
    QHBoxLayout* mControlLayout;
    QScrollArea* mScrollArea;
    QWidget* mScrollWidget;
    QHBoxLayout* mItemsLayout;

    QPushButton* mAddItemButton;
    QPushButton* mRemoveItemButton;
    QLabel* mTitleLabel;

    QVector<EqWidgetIem*> mItems;

    double mStretchFactor;

signals:
    void itemDataChanged(const QVector<EqWidgetItemData>& data);
    void ItemCountChanged(int count);
};

#endif // EQUALIZERWIDGET_H

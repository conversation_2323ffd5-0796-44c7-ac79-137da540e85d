#ifndef EQUALIZERWIDGET_H
#define EQUALIZERWIDGET_H

#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QCheckBox>
#include <QVector>
#include <QScrollArea>
#include <QFrame>
#include "comboboxs1m3.h"
#include "dials1m1.h"

struct EqWidgetItemData {
    QString type;
    float gain;
    float frequency;
    float qValue;
    bool enabled;
};

class EqWidgetIem : public QFrame
{
    Q_OBJECT

public:
    explicit EqWidgetIem(int index, QWidget* parent = nullptr);
    ~EqWidgetIem();

    void setItemData(const EqWidgetItemData& data);
    EqWidgetItemData getItemData() const;
    
    void setItemIndex(int index);
    int getItemIndex() const { return mIndex; }
    
protected:
    void resizeEvent(QResizeEvent* event) override;

private:
    void onTypeChanged(const QString& text);
    void onGainChanged(float value);
    void onFrequencyChanged(float value);
    void onQValueChanged(float value);
    void onEnabledChanged(bool enabled);

private:
    void setupUI();
    void setupConnections();
    void updateTypeOptions();

    int mIndex;
    EqWidgetItemData mData;

    QVBoxLayout* mMainLayout;
    QLabel* mItemLabel;
    
    ComboBoxS1M3* mTypeComboBox;
    
    DialS1M1* mGainDial;
    DialS1M1* mFrequencyDial;
    DialS1M1* mQValueDial;
    
    QCheckBox* mEnabledCheckBox;

signals:
    void dataChanged(int index, const EqWidgetItemData& data);
};

class EqWidget : public QWidget
{
    Q_OBJECT

public:
    explicit EqWidget(QWidget* parent = nullptr);
    ~EqWidget();
    void setStretchFactor(double stretchFactor);

    void setItemCount(int count);
    int getItemCount() const { return mItems.size(); }
    
    void addItem();
    void removeItem(int index = -1);
    void removeAllItems();
    
    void setEqData(const QVector<EqWidgetItemData>& data);
    QVector<EqWidgetItemData> getEqData() const;
    
protected:
    void resizeEvent(QResizeEvent* event) override;

private:
    void onItemDataChanged(int index, const EqWidgetItemData& data);
    void onAddItemClicked();
    void onRemoveItemClicked();

private:
    void setupUI();
    void setupConnections();
    void updateItemIndices();
    void createDefaultItems();

    void addItemInternal(int index);
    void removeItemInternal(int index);

    QScrollArea* mScrollArea;
    QWidget* mScrollWidget;
    QHBoxLayout* mItemsLayout;

    QPushButton* mAddItemButton;
    QPushButton* mRemoveItemButton;
    QLabel* mTitleLabel;

    QVector<EqWidgetIem*> mItems;

    double mStretchFactor;

signals:
    void itemDataChanged(const QVector<EqWidgetItemData>& data);
    void ItemCountChanged(int count);
};

#endif // EQUALIZERWIDGET_H

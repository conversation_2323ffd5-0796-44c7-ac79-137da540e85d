#ifndef PUSHBUTTONS1M2_H
#define PUSHBUTTONS1M2_H


#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M2 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M2(QWidget* parent=nullptr);
    ~PushButtonS1M2();
    enum ButtonID
    {
        buttonSOLO=0,
        buttonMUTE,
    };
    PushButtonS1M2& setFont(QFont font);
    PushButtonS1M2& setPushButtonWeightWidth(int weight);
    PushButtonS1M2& setPushButtonStateSOLO(bool state);
    PushButtonS1M2& setPushButtonStateMUTE(bool state);
    PushButtonS1M2& setPushButtonClickedSOLO(bool state);
    PushButtonS1M2& setPushButtonClickedMUTE(bool state);
    bool getPushButtonStateSOLO();
    bool getPushButtonStateMUTE();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateSOLO=false;
    bool mPushButtonStateMUTE=false;
    QPushButton mPushButtonSOLO;
    QPushButton mPushButtonMUTE;
    int mWeightWidth=40;
    int mRadius=0;
private slots:
    void in_mPushButtonSOLO_clicked();
    void in_mPushButtonMUTE_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M2_H


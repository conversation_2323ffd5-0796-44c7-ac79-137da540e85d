#include "inputs2m1.h"
#include "globalfont.h"
#include "ui_inputs2m1.h"
#include <QButtonGroup>

InputS2M1::InputS2M1(QWidget* parent, QString name)
    : OriginBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::InputS2M1)
{
    ui->setupUi(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "   border-radius: 5px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);

    setMicType("Mic1");
    ui->buttonMic1->setObjectName("Mic1");
    ui->buttonMic35->setObjectName("Mic35");
    ui->buttonMicHP->setObjectName("MicHP");
    mButtonGroup = new QButtonGroup(this);
    mButtonGroup->addButton(ui->buttonMic1);
    mButtonGroup->addButton(ui->buttonMic35);
    mButtonGroup->addButton(ui->buttonMicHP);
    connect(mButtonGroup, &QButtonGroup::buttonClicked, this, [this](QAbstractButton* button) {
        auto micType = button->objectName();
        setMicType(micType);
        save("MIC", micType);
        updateAttribute();
    });
    connect(ui->button48V, SIGNAL(clicked(bool)), this, SLOT(on_button48V_clicked(bool)));
    connect(ui->buttonMute, SIGNAL(clicked(bool)), this, SLOT(on_buttonMute_clicked(bool)));
    connect(ui->slider, SIGNAL(valueChanged(int)), this, SLOT(on_slider_valueChanged(int)));
}
InputS2M1::~InputS2M1()
{
    delete ui;
}


void InputS2M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    float wPixelPerRatio=size().width() / 100.0;
    float hPixelPerRatio = size().height() / 100.0;
    ui->lineEdit->setGeometry(0,0, 13*wPixelPerRatio, height());
    ui->button48V->setGeometry(ui->lineEdit->geometry().right(), 20*hPixelPerRatio, 12*wPixelPerRatio, 25*hPixelPerRatio);
    ui->buttonMute->setGeometry(ui->button48V->geometry().left(), 55*hPixelPerRatio, 12*wPixelPerRatio, 25*hPixelPerRatio);
    ui->slider->setGeometry(ui->buttonMute->geometry().right() + 5*wPixelPerRatio, 10*hPixelPerRatio, 70*wPixelPerRatio, 20*hPixelPerRatio);
    ui->volume->setGeometry(ui->buttonMute->geometry().right() + 5*wPixelPerRatio, 33*hPixelPerRatio, 70*wPixelPerRatio, 35*hPixelPerRatio);
    ui->buttonMic1->setGeometry(ui->buttonMute->geometry().right(), 76*hPixelPerRatio, 20*wPixelPerRatio, 20*hPixelPerRatio);
    ui->buttonMic35->setGeometry(ui->buttonMic1->geometry().right()+ 5*wPixelPerRatio, 76*hPixelPerRatio, 20*wPixelPerRatio, 20*hPixelPerRatio);
    ui->buttonMicHP->setGeometry(ui->buttonMic35->geometry().right()+ 5*wPixelPerRatio, 76*hPixelPerRatio, 20*wPixelPerRatio, 20*hPixelPerRatio);

    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->width())*0.4);
    ui->lineEdit->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->button48V->height())*0.7);
    ui->button48V->setFont(mFont);
    ui->buttonMute->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->buttonMic1->height())*0.8);
    ui->buttonMic1->setFont(mFont);
    ui->buttonMic35->setFont(mFont);
    ui->buttonMicHP->setFont(mFont);
    QString style = QString(R"(
        QPushButton {
            border-radius: %1px;
            color: rgb(161, 161, 161);
            background-color: rgb(60, 60, 60);
            }
        QPushButton:checked {
            color: rgb(229, 229, 229);
            background-color: rgb(149, 40, 37);
        }
        QPushButton:hover {
            border: 2px solid gray;
        };
    )").arg(hPixelPerRatio* 5);
    ui->buttonMute->setStyleSheet(style);
    style = QString(R"(
        QPushButton {
            border-radius: %1px;
            color: rgb(161, 161, 161);
            background-color: rgb(60, 60, 60);
            }
        QPushButton:checked {
            color: rgb(229, 229, 229);
            background-color: rgb(234, 78, 80);
        }
        QPushButton:hover {
            border: 2px solid gray;
        };
    )").arg(hPixelPerRatio* 5);
    ui->button48V->setStyleSheet(style);

    style = QString(R"(
        QPushButton { text-align: left; padding-left: %1px; color: rgb(161, 161, 161);background-color: transparent;}
        QPushButton:hover{color: rgb(255, 255, 255);}
        )").arg(ui->buttonMic1->width() * 0.2);
    ui->buttonMic1->setStyleSheet(style);
    ui->buttonMic35->setStyleSheet(style);
    ui->buttonMicHP->setStyleSheet(style);
    ui->buttonMic1->setIconSize(QSize(ui->buttonMic1->width() * 0.2, ui->buttonMic1->height() * 0.8));
    ui->buttonMic35->setIconSize(QSize(ui->buttonMic35->width() * 0.2, ui->buttonMic35->height() * 0.8));
    ui->buttonMicHP->setIconSize(QSize(ui->buttonMicHP->width() * 0.2, ui->buttonMicHP->height() * 0.8));
}
void InputS2M1::updateAttribute()
{
    if(isWidgetReady())
    {
        if(isWidgetEnable())
        {
            QAbstractButton*buttons = mButtonGroup->checkedButton();
            QString currentMic = buttons->objectName();
            if(mPreMIC != currentMic)
            {
                mPreMIC = currentMic;
                emit attributeChanged(this->objectName(), "MIC", mPreMIC);
            }
            
            if(mPreMUTE != static_cast<int>(ui->buttonMute->isChecked()))
            {
                mPreMUTE = static_cast<int>(ui->buttonMute->isChecked());
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
            if(mPre48V != static_cast<int>(ui->button48V->isChecked()))
            {
                mPre48V = static_cast<int>(ui->button48V->isChecked());
                emit attributeChanged(this->objectName(), "48V", QString::number(mPre48V));
            }
            if(mPreGAIN != static_cast<int>(ui->slider->getValue()))
            {
                mPreGAIN = static_cast<int>(ui->slider->getValue());
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
        }
    }
}
void InputS2M1::loadSettings()
{
    mPreMIC = "";
    mPreMUTE = -2147483648;
    mPre48V = -2147483648;
    mPreGAIN = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    { 
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("MIC", "Mic1");
        WorkspaceObserver::setValue("48V", false);
        WorkspaceObserver::setValue("MUTE", false);
        WorkspaceObserver::setValue("GAIN", 0);
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    
    QString mic = WorkspaceObserver::value("MIC").toString();
    setMicType(mic);
    
    ui->button48V->setChecked(WorkspaceObserver::value("48V").toBool());
    ui->buttonMute->setChecked(WorkspaceObserver::value("MUTE").toBool());
    ui->slider->setValue(WorkspaceObserver::value("GAIN").toInt());
    
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_MIC", WorkspaceObserver::value("MIC").toString());
        emit attributeChanged(this->objectName(), "Save_48V", QString::number(WorkspaceObserver::value("48V").toBool()));
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
        emit attributeChanged(this->objectName(), "Save_GAIN", WorkspaceObserver::value("GAIN").toString());
    }
    setWidgetReady(true);
    updateAttribute();
}
void InputS2M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}

// slot
void InputS2M1::in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M2::ButtonID button, bool state)
{
    switch(button)
    {
        default:
            break;
    }
}
void InputS2M1::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 12))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void InputS2M1::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}

void InputS2M1::on_button48V_clicked(bool checked)
{
    save("48V", checked);
    updateAttribute();
}

void InputS2M1::on_buttonMute_clicked(bool checked)
{
    save("MUTE", checked);
    updateAttribute();
}

void InputS2M1::on_slider_valueChanged(int value)
{
    save("GAIN", value);
    updateAttribute();
}

// setter & getter
void InputS2M1::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
InputS2M1& InputS2M1::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
InputS2M1& InputS2M1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}

InputS2M1& InputS2M1::setHideMic()
{
    ui->buttonMic1->hide();
    ui->buttonMic35->hide();
    ui->buttonMicHP->hide();
    return *this;
}

void InputS2M1::setVolumeMeter(int value)
{
    ui->volume->setValue(value);
}

void InputS2M1::setMute(bool state)
{
    ui->buttonMute->setChecked(state);
    save("MUTE", state);
    updateAttribute();
}
void InputS2M1::set48V(bool state)
{
    ui->button48V->setChecked(state);
    save("48V", state);
    updateAttribute();
}
void InputS2M1::setGain(float value)
{
    ui->slider->setValue(value);
}

void InputS2M1::setMicType(const QString& micType)
{
    QIcon icon = QIcon(":/Icon/VerticalRoundedRectBlack.png");
    ui->buttonMic1->setIcon(icon);
    ui->buttonMic35->setIcon(icon);
    ui->buttonMicHP->setIcon(icon);

    if(micType == "Mic1")
    {
        ui->buttonMic1->setChecked(true);
        ui->buttonMic1->setIcon(QIcon(":/Icon/VerticalRoundedRectGreen.png"));
    }
    else if(micType == "Mic35")
    {
        ui->buttonMic35->setChecked(true);
        ui->buttonMic35->setIcon(QIcon(":/Icon/VerticalRoundedRectGreen.png"));
    }
    else if(micType == "MicHP")
    {
        ui->buttonMicHP->setChecked(true);
        ui->buttonMicHP->setIcon(QIcon(":/Icon/VerticalRoundedRectGreen.png"));
    }
}

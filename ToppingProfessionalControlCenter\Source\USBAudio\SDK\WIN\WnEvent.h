/************************************************************************
 *
 *  Module:       WnEvent.h
 *
 *  Description:  Wrapper for a Win32 Event object
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo<PERSON>@thesycon.de
 *    <PERSON>, <PERSON>@thesycon.de
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnEvent_h__
#define __WnEvent_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// WnEvent
//
// Represents a Win32 Event object
//
class WnEvent : public WnHandle
{
public:
    // constructor
    WnEvent(
        BOOL manualResetType
        )
        {
            mManualResetType = manualResetType;
        }

    // destructor
//  virtual
//   ~WnEvent()
//    {
//    }



    // ------------
    // Interface
public:

    // create event, type is defined by mManualResetType
    // Returns ERROR_ALREADY_EXISTS if an object with the given name already exists,
    // the internal handle is valid in this case and refers to the existing object.
    // lpName is limited to MAX_PATH characters and must not contain backslash characters
    WNERR
    Create(
        BOOL initialState = FALSE,
        LPCTSTR lpName = NULL,
        LPSECURITY_ATTRIBUTES lpEventAttributes = NULL
        )
            {
                WNERR err = ERROR_SUCCESS;
                // destroy current event, if any
                WnEvent::Close();

            HANDLE h = ::CreateEvent(lpEventAttributes,mManualResetType,initialState,lpName);
            if (NULL == h) {
                // failed
                err = ::GetLastError();
                WNTRACE(TRCERR,tprint(__FUNCTION__": CreateEvent failed, err=0x%X\n", err));
            } else {
                mHandle = h;
                if (NULL!=lpName) {
                    // a named event can also return ERROR_ALREADY_EXISTS
                    // in this case mHandle is the existing event
                    err = ::GetLastError();
                }
            }
            return err;
        }


    // wrapper for SetEvent()
    WNERR
    Set()
        {
            WNASSERT(IsValid());
            WNERR err = ERROR_SUCCESS;
            BOOL succ = ::SetEvent(mHandle);
            if (!succ) {
                // failed
                err = ::GetLastError();
                WNTRACE(TRCERR,tprint(__FUNCTION__": SetEvent failed, err=0x%X\n", err));
            }
            return err;
        }
        // return value of this function is likely to be ignored
        //lint -esym(534, WnEvent::Set)


    // wrapper for ResetEvent()
    WNERR
    Reset()
        {
            WNASSERT(IsValid());
            WNERR err = ERROR_SUCCESS;
            BOOL succ = ::ResetEvent(mHandle);
            if (!succ) {
                // failed
                err = ::GetLastError();
                WNTRACE(TRCERR,tprint(__FUNCTION__": ResetEvent failed, err=0x%X\n", err));
            }
            return err;
        }
        // return value of this function is likely to be ignored
        //lint -esym(534, WnEvent::Reset)

#ifdef UNDER_CE
    WNERR
    ShareEvent(
        HANDLE srcProcessHandle,
        HANDLE EventHandle
        )
            {
                // destroy current event, if any
                Close();

            WNERR err = ERROR_SUCCESS;
            BOOL succ = DuplicateHandle(srcProcessHandle,EventHandle,GetCurrentProcess(),&mHandle,0,FALSE,DUPLICATE_SAME_ACCESS);
            if (!succ) {
                // failed
                err = ::GetLastError();
                WNTRACE(TRCERR,tprint(__FUNCTION__": DuplicateHandle failed, err=0x%X\n", err));
            }
            return err;
        }
#endif


    // returns true if this is a manual-reset type event
    bool
    IsManualResetType() const
        { return ( mManualResetType != FALSE ); }

    // returns true if this is an auto-reset type event
    bool
    IsAutoResetType() const
        { return !IsManualResetType(); }

    // returns true if the event is in signaled state and false otherwise
    // Note: (side effect) if called on auto reset event the event is cleared
    bool
    IsSignaled() const
        {
            WNASSERT(IsValid());

          DWORD err = ::WaitForSingleObject(mHandle, 0);
          if (WAIT_OBJECT_0 == err) {
              // event is signaled
              return true;
          }

          return false;
      }



    // ------------
    // Data Members

protected:

    // true for manual reset type, false for auto-reset
    BOOL mManualResetType;


}; // class WnEvent



//
// manual-reset type event
//
class WnManualResetEvent : public WnEvent
{
public:
    // default constructor
    WnManualResetEvent() : WnEvent(TRUE)
        {
        }
};


//
// auto-reset type event
//
class WnAutoResetEvent : public WnEvent
{
public:
    // default constructor
    WnAutoResetEvent() : WnEvent(FALSE)
        {
        }
};

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnEvent_h__

/***************************** EOF **************************************/

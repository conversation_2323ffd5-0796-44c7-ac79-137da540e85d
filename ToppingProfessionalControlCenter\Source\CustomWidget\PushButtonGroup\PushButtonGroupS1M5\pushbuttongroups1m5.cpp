#include "globalfont.h"
#include "pushbuttongroups1m5.h"
#include "ui_pushbuttongroups1m5.h"


PushButtonGroupS1M5::PushButtonGroupS1M5(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PushButtonGroupS1M5)
{
    ui->setupUi(this);
    ui->PushButton48V->setCheckable(true);
    ui->PushButtonAUTO->setCheckable(true);
    ui->PushButton48V->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonAUTO->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    mStyle[0] = "QFrame { image: url(:/Image/PushButtonGroup/PBG12_0.png); }";
    mStyle[1] = "QFrame { image: url(:/Image/PushButtonGroup/PBG12_2.png); }";
    mStyle[2] = "QFrame { image: url(:/Image/PushButtonGroup/PBG12_1.png); }";
    mStyle[3] = "QFrame { image: url(:/Image/PushButtonGroup/PBG12_3.png); }";
    setState("48V", "0", false);
    setState("AUTO", "0", false);
    setLanguage("English");
}
PushButtonGroupS1M5::~PushButtonGroupS1M5()
{
    delete ui;
}


// override
void PushButtonGroupS1M5::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "DUCKING", ui->PushButtonAUTO->rect()) - 1);
    ui->PushButton48V->setFont(mFont);
    ui->PushButtonAUTO->setFont(mFont);
}


// slot
void PushButtonGroupS1M5::on_PushButton48V_clicked(bool checked)
{
    setState("48V", QString::number(checked));
}
void PushButtonGroupS1M5::on_PushButtonAUTO_clicked(bool checked)
{
    setState("AUTO", QString::number(checked));
}


// setter & getter
PushButtonGroupS1M5& PushButtonGroupS1M5::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonGroupS1M5& PushButtonGroupS1M5::setLanguage(QString language)
{
    if(language == "English")
    {
        // ui->PushButton48V->setText("48V");
        // ui->PushButtonAUTO->setText("AUTO");
    }
    else if(language == "Chinese")
    {
        // ui->PushButton48V->setText("48V");
        // ui->PushButtonAUTO->setText("AUTO");
    }
    return *this;
}
PushButtonGroupS1M5& PushButtonGroupS1M5::setState(QString button, QString state, bool needEmit)
{
    unsigned int bitMask=0;
    QPushButton* currentButton=nullptr;
    if(button == "48V")
    {
        bitMask = 0x0001;
        currentButton = ui->PushButton48V;
    }
    else if(button == "AUTO")
    {
        bitMask = 0x0002;
        currentButton = ui->PushButtonAUTO;
    }
    if(currentButton != nullptr)
    {
        mBitmap &= ~bitMask;
        if(state.toInt()) mBitmap |= bitMask;
        ui->frame->setStyleSheet(mStyle.value(mBitmap));
        currentButton->setChecked(state.toInt());
        currentButton->setStyleSheet(state.toInt() ? "QPushButton { color: rgb(222, 222, 222); }" : "QPushButton { color: rgb(161, 161, 161); }");
        if(needEmit) emit stateChanged(button, state);
    }
    return *this;
}
QString PushButtonGroupS1M5::getState(QString button)
{
    QPushButton* currentButton=nullptr;
    if(button == "48V")
    {
        currentButton = ui->PushButton48V;
    }
    else if(button == "AUTO")
    {
        currentButton = ui->PushButtonAUTO;
    }
    if(currentButton != nullptr)
    {
        return QString::number(currentButton->isChecked());
    }
    return QString::number(false);
}


#include "hsliders1m1.h"
#include <QPainter>
#include <QMouseEvent>
#include <cfloat>
#include <qfloat16.h>
#include "globalfont.h"
#include <QLineEdit>
#include <QRegularExpression>
#include <QRegularExpressionValidator>

HSliderS1M1::HSliderS1M1(QWidget *parent)
    : QWidget(parent), m_lineEdit(new QLineEdit(this))
{
    setFocusPolicy(Qt::StrongFocus);
    setAttribute(Qt::WA_MouseTracking);
    resize(38, 19);

    m_defaultValue = median();
    m_value = m_defaultValue;
    m_lineEdit->setText(getTextFromValue(m_value));

    connect(m_lineEdit, &QLineEdit::editingFinished, this, [this](){
        QString text = m_lineEdit->text();
        auto value = getValueFromText(text);
        updateValue(value, true);
        m_lineEdit->clearFocus();
    });
    m_lineEdit->setAlignment(Qt::AlignCenter);
    m_lineEdit->setStyleSheet("background:transparent;border:none;color:rgb(67, 207, 124);selection-color: rgb(0, 121, 107);selection-background-color: rgb(224, 247, 250);");
    QRegularExpression rx("^(C|[LR]([1-9][0-9]?)?)$");
    auto validator = new QRegularExpressionValidator(rx, this);
    m_lineEdit->setValidator(validator);
    m_lineEdit->installEventFilter(this);
    m_lineEdit->setContextMenuPolicy(Qt::NoContextMenu);
}

void HSliderS1M1::setValue(float value, bool emitSignal)
{
    if(updateValue(value ,emitSignal)){
        m_lineEdit->setText(getTextFromValue(m_value));
    }
}

void HSliderS1M1::setDefaultValue(float defaultValue)
{
    m_defaultValue = qBound(m_minValue, defaultValue, m_maxValue);
}

float HSliderS1M1::value() const
{
    return m_value;
}

void HSliderS1M1::setFont(const QFont &font)
{
    m_font = font;
    update();
}

void HSliderS1M1::setBackgroundColor(const QColor &color)
{
    if (m_backgroundColor != color) {
        m_backgroundColor = color;
        update();
    }
}

void HSliderS1M1::setGrooveColor(const QColor &color)
{
    if (m_grooveColor != color) {
        m_grooveColor = color;
        update();
    }
}

void HSliderS1M1::setHandleColor(const QColor &color)
{
    if (m_handleColor != color) {
        m_handleColor = color;
        update();
    }
}

void HSliderS1M1::setTextColor(const QColor &color)
{
    if (m_textColor != color) {
        m_textColor = color;
        update();
    }
}

void HSliderS1M1::paintEvent(QPaintEvent *)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    painter.setPen(Qt::NoPen);
    painter.setBrush(m_backgroundColor);
    qreal cornerRadius = height() * 0.1;
    painter.drawRoundedRect(rect(), cornerRadius, cornerRadius);
    
    painter.setBrush(m_grooveColor);
    qreal grooveCornerRadius = m_grooveRect.height() * 0.3;
    painter.drawRoundedRect(m_grooveRect, grooveCornerRadius, grooveCornerRadius);
    
    painter.setBrush(m_handleColor);
    updateHandleRect();
    painter.drawRect(m_handleRect);
    
    painter.setPen(m_textColor);
    painter.setFont(m_font);
    
    // QString text = getTextFromValue(m_value);
    // painter.drawText(m_textRect, Qt::AlignCenter, text);
}

QPoint HSliderS1M1::posFromValue(float value) const
{
    qreal valueRange = m_maxValue - m_minValue;
    qreal relativePos = (value - m_minValue) / valueRange;
    qreal xPos = m_grooveRect.left() + relativePos * m_grooveRect.width();
    return QPoint((int)(xPos * m_unitValuePixels), 0);
}

void HSliderS1M1::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && m_grooveRect.contains(event->pos())) {
        m_pressed = true;
        m_mousePos = event->pos();
        if(m_handleRect.contains(event->pos())){
            m_mousePosRatio = posFromValue(m_value);
        }
        else if(m_grooveRect.contains(event->pos())){
            m_mousePosRatio = m_unitValuePixels * m_mousePos;
            int value = valueFromPos(m_mousePosRatio);
            float range = m_maxValue - m_minValue;
            if(value < 5/100.0*range){
                value = minimum();
            }else if(value > 95/100.0*range){
                value = maximum();
            }
            setValue(value);
        }
    }
}

void HSliderS1M1::mouseMoveEvent(QMouseEvent *event)
{
    if (m_pressed) {
        QPoint pos = QPoint(qBound((int)m_grooveRect.left()*m_unitValuePixels, m_mousePosRatio.x()+event->pos().x() - m_mousePos.x(),
         (int)(m_grooveRect.right()+m_handleRect.width())*m_unitValuePixels), event->pos().y());
        int value = valueFromPos(pos);
        setValue(value);
    }
}

void HSliderS1M1::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_pressed = false;
    }
    m_mousePos = {0, 0};
    m_mousePosRatio = {0, 0};
}

void HSliderS1M1::mouseDoubleClickEvent(QMouseEvent *event)
{
    setValue(m_defaultValue);
}

void HSliderS1M1::wheelEvent(QWheelEvent *event)
{
    if (event->angleDelta().y() > 0) {
        setValue(m_value - 1);
    } else {
        setValue(m_value + 1);
    }
}

void HSliderS1M1::keyPressEvent(QKeyEvent *event)
{
    if (event->key() == Qt::Key_Left || event->key() == Qt::Key_Up) {
        setValue(m_value - 1);
    } else if (event->key() == Qt::Key_Right || event->key() == Qt::Key_Down) {
        setValue(m_value + 1);
    }
}
void HSliderS1M1::resizeEvent(QResizeEvent *)
{
    qreal grooveHeight = height() * 0.3;
    qreal grooveMargin = height() * 0.1;
    qreal grooveTop = height() * 0.1;
    
    m_grooveRect = QRectF(grooveMargin, 
                         grooveTop,
                         width() - 2 * grooveMargin, 
                         grooveHeight);
    updateHandleRect();

    qreal textY = grooveTop + grooveHeight + height() * 0.1;
    m_textRect = QRectF(0, 
                        textY, 
                        width(), 
                        height() - textY - height() * 0.1);
    m_font.setPointSize(GLBFHandle.getSuitablePointSize(m_font, m_textRect.height()*1.4));
    m_lineEdit->setFont(m_font);
    m_lineEdit->setGeometry(m_textRect.toRect());
}

bool HSliderS1M1::eventFilter(QObject *o, QEvent *e)
{
    if(o==m_lineEdit&& e->type()==QEvent::FocusOut){
        QString text = m_lineEdit->text();
        if(text.isEmpty()){
            m_lineEdit->setText(getTextFromValue(m_value));
        }
    }
    return QWidget::eventFilter(o, e);
}

void HSliderS1M1::updateHandleRect()
{
    qreal valueRange = m_maxValue - m_minValue;
    m_handleWidth = m_grooveRect.width() * 0.08;
    qreal valuePos = ((m_value - m_minValue) / valueRange) * (m_grooveRect.width()-m_handleWidth);
    
    qreal handleX = m_grooveRect.left() + valuePos;
    
    m_handleRect = QRectF(
        handleX,
        m_grooveRect.top(),
        m_handleWidth,
        m_grooveRect.height()
    );
}

float HSliderS1M1::valueFromPos(const QPoint &pos) const
{
    qreal valueRange = m_maxValue - m_minValue;
    qreal xPos = qBound(m_grooveRect.left(), 
                        (qreal)pos.x()/m_unitValuePixels,
                        m_grooveRect.right());
    qreal relativePos = (xPos - m_grooveRect.left()) / m_grooveRect.width();
    
    int rawValue = qRound(m_minValue + relativePos * valueRange);
    return rawValue;
}

QString HSliderS1M1::getTextFromValue(float value) const
{
    QString text = "C";
    auto median = this->median();
    auto displayValue = value - median;
    if (value < median) {
        if(value == m_minValue){
            text = "L";
        }else{
            text = QString("L%1").arg(-displayValue);
        }
    } else if (value > median) {
        if(value == m_maxValue){
            text = "R";
        }else{
            text = QString("R%1").arg(displayValue);
        }
    }
    
    return text;
}

float HSliderS1M1::getValueFromText(const QString &text) const
{
    QChar firstChar = text.at(0);
    auto displayValue = text.mid(1);
    float value = FLT_MAX;;
    if(firstChar == 'C'){
        value = median();
    }else if(firstChar == 'L'){
        if(!displayValue.isEmpty()){
            value = -displayValue.toFloat() + median();
        }else{
            value = m_minValue;
        }
    }else if(firstChar == 'R'){
        if(!displayValue.isEmpty()){
            value = displayValue.toFloat() + median();
        }else{
            value = m_maxValue;
        }
    }
    return value;
}

bool HSliderS1M1::updateValue(float value, bool emitSignal)
{
    value = qBound(m_minValue, value, m_maxValue);
    if(value == m_value){
        return false;
    }
    m_value = value;
    update();
    if(emitSignal)
        emit valueChanged(m_value);
    return true;
}

void HSliderS1M1::setRange(float min, float max)
{
    if (min > max)
        qSwap(min, max);
        
    if (m_minValue != min || m_maxValue != max) {
        m_minValue = min;
        m_maxValue = max;
        setValue(m_value);
        updateHandleRect();
        update();
    }
}

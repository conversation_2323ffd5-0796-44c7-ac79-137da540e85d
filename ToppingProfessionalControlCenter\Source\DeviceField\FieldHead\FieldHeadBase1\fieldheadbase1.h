#ifndef FIELDHEADBASE1_H
#define FIELDHEADBASE1_H


#include <QRect>
#include <QLabel>
#include <QColor>
#include <QWidget>
#include <QString>
#include <QPainter>
#include <QComboBox>
#include <QPushButton>
#include <QPaintEvent>
#include <QResizeEvent>

#include "batterys1m1.h"
#include "comboboxs1m1.h"
#include "comboboxs1m2.h"


class FieldHeadBase1 : public QWidget
{
    Q_OBJECT
public:
    explicit FieldHeadBase1(QWidget* parent=nullptr);
    ~FieldHeadBase1();
    FieldHeadBase1& setFont(QFont font);
    FieldHeadBase1& setFieldColor(QColor color);
    FieldHeadBase1& setLanguage(QString language="Chinese");
    FieldHeadBase1& setBatteryValue(int value);
    FieldHeadBase1& setBatteryCharging(bool charging=true);
    FieldHeadBase1& modifyWorkspaceList(QVector<QString> list, QString defaultItem);
    FieldHeadBase1& modifySampleRateList(QVector<unsigned int> list, unsigned int defaultItem);
    FieldHeadBase1& modifyBufferSizeList(QVector<unsigned int> list, unsigned int defaultItem);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    QFont mFont;
    QString mLanguage="Chinese";
    QColor mColorBG=QColor(31, 31, 31);
    QLabel mLabelLogo;
    QLabel mLabelWorkspace;
    QLabel mLabelSampleRate;
    QLabel mLabelBufferSize;
    ComboBoxS1M1 mComboBoxWorkspace;
    ComboBoxS1M2 mComboBoxSampleRate;
    ComboBoxS1M2 mComboBoxBufferSize;
    BatteryS1M1 mBattery;
    QPushButton mPushButtonWorkspaceSave;
    QPushButton mPushButtonWorkspaceDownload;
    QPushButton mPushButtonSettings;
    void drawBG(QPainter* painter);
private slots:
    void in_mPushButtonAll_clicked();
    void in_mComboBoxAll_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDHEADBASE1_H


/********************************************************************************
** Form generated from reading UI file 'effects1m1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_EFFECTS1M1_H
#define UI_EFFECTS1M1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <dials1m3.h>
#include <pushbuttons1m4.h>
#include <pushbuttons1m7.h>
#include <volumemeters1m1.h>

QT_BEGIN_NAMESPACE

class Ui_EffectS1M1
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    QLineEdit *lineEdit;
    PushButtonS1M4 *widgetPushButtonGroup1;
    VolumeMeterS1M1 *widgetVolumeMeter;
    DialS1M3 *widgetDial;
    PushButtonS1M7 *widgetPushButtonGroup2;

    void setupUi(QWidget *EffectS1M1)
    {
        if (EffectS1M1->objectName().isEmpty())
            EffectS1M1->setObjectName("EffectS1M1");
        EffectS1M1->resize(140, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(EffectS1M1->sizePolicy().hasHeightForWidth());
        EffectS1M1->setSizePolicy(sizePolicy);
        EffectS1M1->setMinimumSize(QSize(80, 220));
        gridLayout = new QGridLayout(EffectS1M1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(EffectS1M1);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 20));
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(9, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetPushButtonGroup1 = new PushButtonS1M4(frame);
        widgetPushButtonGroup1->setObjectName("widgetPushButtonGroup1");
        widgetPushButtonGroup1->setGeometry(QRect(80, 40, 21, 131));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup1->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup1->setSizePolicy(sizePolicy);
        widgetVolumeMeter = new VolumeMeterS1M1(frame);
        widgetVolumeMeter->setObjectName("widgetVolumeMeter");
        widgetVolumeMeter->setGeometry(QRect(10, 40, 21, 131));
        sizePolicy.setHeightForWidth(widgetVolumeMeter->sizePolicy().hasHeightForWidth());
        widgetVolumeMeter->setSizePolicy(sizePolicy);
        widgetDial = new DialS1M3(frame);
        widgetDial->setObjectName("widgetDial");
        widgetDial->setGeometry(QRect(10, 180, 90, 41));
        sizePolicy.setHeightForWidth(widgetDial->sizePolicy().hasHeightForWidth());
        widgetDial->setSizePolicy(sizePolicy);
        widgetDial->setMinimumSize(QSize(0, 0));
        widgetPushButtonGroup2 = new PushButtonS1M7(frame);
        widgetPushButtonGroup2->setObjectName("widgetPushButtonGroup2");
        widgetPushButtonGroup2->setGeometry(QRect(10, 230, 90, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup2->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup2->setSizePolicy(sizePolicy);
        widgetPushButtonGroup2->setMinimumSize(QSize(0, 0));

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(EffectS1M1);

        QMetaObject::connectSlotsByName(EffectS1M1);
    } // setupUi

    void retranslateUi(QWidget *EffectS1M1)
    {
        EffectS1M1->setWindowTitle(QCoreApplication::translate("EffectS1M1", "Form", nullptr));
        pushButtonClose->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class EffectS1M1: public Ui_EffectS1M1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_EFFECTS1M1_H

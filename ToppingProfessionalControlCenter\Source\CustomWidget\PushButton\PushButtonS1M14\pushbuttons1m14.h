#ifndef PUSHBUTTONS1M14_H
#define PUSHBUTTONS1M14_H


#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M14 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M14(QWidget* parent=nullptr);
    ~PushButtonS1M14();
    enum ButtonID
    {
        buttonMUTELeft=0,
        buttonMUTERight
    };
    PushButtonS1M14& setFont(QFont font);
    PushButtonS1M14& setPushButtonWeightWidth(int weight);
    PushButtonS1M14& setPushButtonStateMUTELeft(bool state);
    PushButtonS1M14& setPushButtonStateMUTERight(bool state);
    PushButtonS1M14& setPushButtonClickedMUTELeft(bool state);
    PushButtonS1M14& setPushButtonClickedMUTERight(bool state);
    bool getPushButtonStateMUTELeft();
    bool getPushButtonStateMUTERight();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateMUTELeft=false;
    bool mPushButtonStateMUTERight=false;
    QPushButton mPushButtonMUTELeft;
    QPushButton mPushButtonMUTERight;
    int mWeightWidth=40;
    int mRadius=0;
private slots:
    void in_mPushButtonMUTELeft_clicked();
    void in_mPushButtonMUTERight_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M14_H


# 动态均衡器控件 (EqualizerWidget)

这是一个基于Qt的动态均衡器控件，可以动态添加和删除均衡器段数，每个段包含类型选择、增益、频率、Q值控制和启用开关。

## 功能特性

### 主要功能
- **动态段数管理**: 可以动态添加和删除均衡器段（1-32段）
- **完整的均衡器控制**: 每段包含类型、增益、频率、Q值和启用状态
- **预设管理**: 支持保存和加载均衡器预设
- **深色主题**: 现代化的深色UI设计
- **实时数据更新**: 参数变化时实时发送信号

### 单段控制
每个均衡器段包含以下控制：
- **类型选择**: 下拉菜单选择滤波器类型（高通滤波、High Shelf等）
- **增益控制**: 旋钮控制，范围-20dB到+20dB，精度0.1dB
- **频率控制**: 旋钮控制，范围20Hz到20kHz
- **Q值控制**: 旋钮控制，范围0.1到10.0，精度0.1
- **启用开关**: 复选框控制该段是否启用

## 类结构

### EqualizerBand 结构体
```cpp
struct EqualizerBand {
    QString type;           // 滤波器类型
    float gain;            // 增益值 (-20.0 到 +20.0 dB)
    float frequency;       // 频率值 (20.0 到 20000.0 Hz)
    float qValue;          // Q值 (0.1 到 10.0)
    bool enabled;          // 是否启用
};
```

### EqualizerBandWidget 类
单个均衡器段的控件，包含所有必要的UI元素和控制逻辑。

**主要方法:**
- `setBandData(const EqualizerBand& data)`: 设置段数据
- `getBandData() const`: 获取段数据
- `setBandIndex(int index)`: 设置段号

**信号:**
- `bandDataChanged(int bandIndex, const EqualizerBand& data)`: 段数据变化时发出

### EqualizerWidget 类
主均衡器控件，管理多个均衡器段。

**主要方法:**
- `setBandCount(int count)`: 设置段数
- `addBand()`: 添加一个段
- `removeBand(int index = -1)`: 删除指定段（-1表示最后一个）
- `setEqualizerData(const QVector<EqualizerBand>& data)`: 设置所有段数据
- `getEqualizerData() const`: 获取所有段数据
- `savePreset(const QString& presetName)`: 保存预设
- `loadPreset(const QString& presetName)`: 加载预设

**信号:**
- `equalizerDataChanged(const QVector<EqualizerBand>& data)`: 均衡器数据变化
- `bandCountChanged(int count)`: 段数变化

## 使用示例

### 基本使用
```cpp
#include "equalizerwidget.h"

// 创建均衡器控件
EqualizerWidget* equalizer = new EqualizerWidget(parent);

// 设置段数
equalizer->setBandCount(8);

// 连接信号
connect(equalizer, &EqualizerWidget::equalizerDataChanged,
        this, &MyClass::onEqualizerChanged);

// 设置深色主题
equalizer->setDarkTheme();
```

### 数据操作
```cpp
// 获取当前数据
QVector<EqualizerBand> data = equalizer->getEqualizerData();

// 修改数据
for (auto& band : data) {
    band.gain = 0.0f;  // 重置所有增益
}

// 应用修改后的数据
equalizer->setEqualizerData(data);
```

### 预设管理
```cpp
// 保存当前设置为预设
equalizer->savePreset("我的预设1");

// 加载预设
equalizer->loadPreset("我的预设1");

// 获取所有可用预设
QStringList presets = equalizer->getAvailablePresets();
```

## 编译说明

### 依赖项
- Qt6 (Core, Widgets)
- C++17 或更高版本
- CMake 3.16 或更高版本

### 快速开始（简化版本）

我们提供了一个简化版本的均衡器控件，不依赖于项目中的其他自定义控件，可以独立编译和运行：

#### Windows
```batch
# 运行构建脚本
build_and_run.bat
```

#### Linux/Mac
```bash
# 给脚本执行权限
chmod +x build_and_run.sh

# 运行构建脚本
./build_and_run.sh
```

#### 手动编译简化版本
```bash
# 创建构建目录
mkdir build_simple
cd build_simple

# 配置CMake（使用简化版CMakeLists）
cmake -f ../CMakeLists_simple.txt ..

# 编译
cmake --build . --config Release

# 运行测试程序
./bin/SimpleEqualizerTest
```

### 完整版本编译步骤

如果要编译完整版本（依赖项目中的其他控件）：

```bash
# 创建构建目录
mkdir build
cd build

# 配置CMake
cmake ..

# 编译
cmake --build .

# 运行示例程序
./bin/EqualizerDemo
```

## 自定义和扩展

### 添加新的滤波器类型
在 `EqualizerBandWidget::updateTypeOptions()` 方法中添加新的类型：
```cpp
QVector<QString> types = {
    "高通滤波",
    "低通滤波",
    "带通滤波",
    "带阻滤波",
    "High Shelf",
    "Low Shelf",
    "Peak/Notch"
};
```

### 自定义样式
可以通过修改 `setDarkTheme()` 方法或直接设置样式表来自定义外观：
```cpp
equalizer->setStyleSheet("your custom stylesheet");
```

### 扩展数据结构
如果需要更多参数，可以扩展 `EqualizerBand` 结构体：
```cpp
struct EqualizerBand {
    QString type;
    float gain;
    float frequency;
    float qValue;
    bool enabled;
    float slope;        // 新增：斜率参数
    QString filterMode; // 新增：滤波器模式
};
```

## 注意事项

1. **段数限制**: 最大支持32个均衡器段，最少1个段
2. **内存管理**: 控件会自动管理子控件的内存，无需手动删除
3. **信号连接**: 确保正确连接信号以接收数据变化通知
4. **线程安全**: 控件不是线程安全的，请在主线程中使用

## 许可证

本控件遵循项目的整体许可证。

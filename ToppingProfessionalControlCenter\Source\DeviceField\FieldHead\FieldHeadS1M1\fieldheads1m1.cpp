#include "workspace.h"
#include "usbaudioapi.h"
#include "fieldheads1m1.h"


FieldHeadS1M1::FieldHeadS1M1(QWidget* parent, QString name)
    : FieldHeadBase1(parent)
    , AppSettingsObserver(name)
{
    connect(this, &FieldHeadBase1::attributeChanged, this, &FieldHeadS1M1::in_widgetBase_attributeChanged, Qt::UniqueConnection);
}
FieldHeadS1M1::~FieldHeadS1M1()
{

}


// override
void FieldHeadS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Language")
    {
        setLanguage(value);
    }
}


// slot
void FieldHeadS1M1::in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(objectName == "Workspace")
    {
        if(attribute == "ItemChanged")
        {
            WKSPHandle.modifyWorkspace(value, QSettings().value("AutoSaveWorkspace").toBool() ? (WorkspaceSubject::onOrigin) : (WorkspaceSubject::onCopy));
        }
        else if(attribute == "RemoveItem")
        {
            WKSPHandle.removeWorkspace(value);
        }
        else if(attribute == "RestoreDefault")
        {
            WKSPHandle.clearWorkspace();
            WKSPHandle.modifyWorkspace(value, QSettings().value("AutoSaveWorkspace").toBool() ? (WorkspaceSubject::onOrigin) : (WorkspaceSubject::onCopy));
        }
        else if(attribute == "NewItem")
        {
            WKSPHandle.createWorkspace(value);
        }
        else if(attribute == "ItemSaveAs")
        {
            WKSPHandle.workspaceSaveAs(value);
        }
    }
    else if(objectName == "SampleRate")
    {
        if(attribute == "ItemChanged")
        {
            USBAHandle.setSampleRateOfActiveDevice(value.toUInt());
        }
    }
    else if(objectName == "BufferSize")
    {
        if(attribute == "ItemChanged")
        {
            USBAHandle.setBufferSizeOfActiveDevice(value.toUInt());
        }
    }
    else if(objectName == "WorkspaceSave")
    {
        if(attribute == "Clicked")
        {
            WKSPHandle.saveWorkspace();
        }
    }
    else if(objectName == "WorkspaceDownload")
    {
        if(attribute == "Clicked")
        {
            emit attributeChanged(objectName, attribute, value);
        }
    }
    else if(objectName == "SystemSettings")
    {
        if(attribute == "Clicked")
        {
            emit attributeChanged(objectName, attribute, value);
        }
    }
}


// setter & getter
FieldHeadS1M1& FieldHeadS1M1::setName(QString name)
{
    setObjectName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}


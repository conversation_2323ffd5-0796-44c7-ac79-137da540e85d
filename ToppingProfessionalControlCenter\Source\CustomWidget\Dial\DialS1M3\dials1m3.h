#ifndef DIALS1M3_H
#define DIALS1M3_H

#include <QFont>
#include <QRect>
#include <QColor>
#include <QWidget>
#include <QPointF>
#include <QPainter>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QResizeEvent>

class DialS1M3 : public QWidget
{
    Q_OBJECT
public:
    explicit DialS1M3(QWidget* parent=nullptr);
    ~DialS1M3();
    DialS1M3& setFont(QFont font);
    DialS1M3& setValue(float value);
    DialS1M3& setDefault(float value);
    DialS1M3& setRange(float min, float max);
    DialS1M3& setStep(float step);
    DialS1M3& setPrecision(int precision);
    DialS1M3& setSensitivity(int sensitivity);
    DialS1M3& setMovable(bool status=true);
    DialS1M3& setDoublePercent(bool status=true);
    DialS1M3& setColorBG(QColor color);
    DialS1M3& setColorDial(QColor color);
    DialS1M3& setColorCircleBG(QColor color);
    DialS1M3& setColorCircleValue(QColor color);
    DialS1M3& setColorHandle(QColor color);
    DialS1M3& setColorText(QColor color);
    float getValue() { return mValue; }
    float getDefault() { return mValueDefault; }
    DialS1M3& showArrow(bool status=true);
    DialS1M3& showCircle(bool status=true);
    DialS1M3& showText(bool status=true);
    DialS1M3& showSign(bool status=true);
    DialS1M3& showInfinity(bool state=true);
    DialS1M3& showInfinitesimal(bool state=true);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
    void mouseDoubleClickEvent(QMouseEvent* e) override;
    void mousePressEvent(QMouseEvent* e) override;
    void mouseMoveEvent(QMouseEvent* e) override;
    void mouseReleaseEvent(QMouseEvent* e) override;
    void wheelEvent(QWheelEvent* e) override;
    void keyPressEvent(QKeyEvent* e) override;
private:
    bool mMouseEnabled=true;
    bool mPressed=false;
    float mPressedValue=0;
    QPointF mPressedPoint;
    bool mDoublePercent=false;
    bool mValueShowArrow=true;
    bool mValueShowCircle=true;
    bool mValueShowText=true;
    bool mValueShowSign=false;
    bool mShowInfinity=false;
    bool mShowInfinitesimal=false;
    float mValue=25;
    float mValueDefault=25;
    float mValueMin=0;
    float mValueMax=50;
    float mValueStep=1;
    int mPrecision=0;
    int mSensitivity=5;
    QRect mRectDial;
    QFont mFont;
    int mPenWidth=0;
    QColor mColorBG=QColor(22, 22, 22);
    QColor mColorDial=QColor(53, 53, 53);
    QColor mColorCircleBG=QColor(46, 46, 46);
    QColor mColorCircleValue=QColor(67, 207, 124);
    QColor mColorHandle=QColor(67, 207, 124);
    QColor mColorText=QColor(67, 207, 124);
    void drawBG(QPainter* painter);
    void drawElement(QPainter* painter);
signals:
    void valueChanged(float value);
};

#endif // DIALS1M3_H

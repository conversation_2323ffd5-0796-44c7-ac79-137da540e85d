/************************************************************************

    Description:
        Represents a dynamically loaded library

    Author(s):
        <PERSON><PERSON><PERSON>ck
        Udo Eberhardt
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __TLDynlibLoader_h__
#define __TLDynlibLoader_h__

//
// This class dynamically loads the API library and provides function pointer wrappers for all API functions.
//
class TLDynlibLoader
{
public:

    //
    // Constructor
    //
    TLDynlibLoader();
    
    //
    // Destructor
    //
    ~TLDynlibLoader();


    // disable copy and move operations
    TLDynlibLoader(const TLDynlibLoader&) = delete;
    TLDynlibLoader& operator=(const TLDynlibLoader&) = delete;
    TLDynlibLoader(TLDynlibLoader&&) = delete;
    TLDynlibLoader& operator=(TLDynlibLoader&&) = delete;

    // handle for a loaded library
    using DynLibHandleType = void*;

    static constexpr DynLibHandleType InvalidHandleValue = nullptr;


/////////////////////////////////////////
// Interface
//
public:

    //
    // Load the library (DLL) by file name.
    //
    TLSTATUS
    LoadDynlibByName(
        const T_UNICHAR* fileName  // absolute or relative path
        );

    //
    // Unload the library.
    // It's safe to call this function if no library is loaded currently.
    //
    void
    UnloadDynlib();

    //
    // Returns true if a library is loaded currently.
    //
    bool
    IsDynlibLoaded() const
        {
            return ( mDynLibHandle != InvalidHandleValue );
        }

    //
    // Return the address of the given symbol, or nullptr.
    //
    void*
    ResolveDynlibSymbol(
        const char* name
        );


////////////////////////////////////////
// OS specific implementation
//
protected:

    static
    TLSTATUS
    OSLoadDynLib(
        DynLibHandleType& handle,
        const T_UNICHAR* fileName
        );

    static
    void
    OSUnloadDynLib(
        DynLibHandleType handle
        );

    static
    void*
    OSResolveSymbol(
        DynLibHandleType handle, 
        const char* symbol
        );
        
////////////////////////////////////////
// Data
//
protected:

    // handle for the dynamic link library
    DynLibHandleType mDynLibHandle {InvalidHandleValue};

};



#endif 

/*** EOF ***/

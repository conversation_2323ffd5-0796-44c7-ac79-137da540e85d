/***********************************************************************
 *  Wrapper for 64bit support functions.
 *
 *  Thesycon GmbH, Germany
 *  http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

// Module is empty if .h file was not included (category turned off).
#ifdef __WnWow64_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

    // ctor
WnWow64FsRedirection::WnWow64FsRedirection()
{
    mOldRedirVal = NULL;
}

WNERR
WnWow64FsRedirection::DisableWow64FsRedirection()
{
    //initialization
    mOldRedirVal = NULL;

    WnLibrary lib;

    // load the kernel32 DLL
    WNERR err = lib.Load(TEXT("kernel32"));
    if (ERROR_SUCCESS != err) {
        // failed
        return err;
    }

    //Since the Windows function 'Wow64DisableWow64FsRedirection' isn't implemented on
    //32-bit Windows we check whether the Kernel32 DLL exports this function.
    typedef BOOL WINAPI F_Wow64DisableWow64FsRedirection( PVOID* OldValue );

    //check whether the function is exported by the DLL
    F_Wow64DisableWow64FsRedirection* DisableRedir = (F_Wow64DisableWow64FsRedirection*)lib.GetProcAddress("Wow64DisableWow64FsRedirection");
    if ( DisableRedir == NULL ) {
        //Since the caller of the function should already now whether
        //32- or 64-bit Windows is running we return an error.
        err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": GetProcAddress(Wow64DisableWow64FsRedirection) failed, err=0x%X\n", err));
        return err;
    }

    //disable redirection
    if ( !DisableRedir( &mOldRedirVal ) ) {
        err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": DisableRedir failed, err=0x%X\n", err));
        return err;
    }

    //OK
    return ERROR_SUCCESS;
}

WNERR
WnWow64FsRedirection::RevertWow64FsRedirection()
{
    WnLibrary lib;

    // load the kernel32 DLL
    WNERR err = lib.Load(TEXT("kernel32"));
    if (ERROR_SUCCESS != err) {
        // failed
        return err;
    }

    //Since the Windows function 'Wow64RevertWow64FsRedirection' isn't implemented on
    //32-bit Windows we check whether the Kernel32 DLL exports this function.
    typedef BOOL WINAPI F_Wow64RevertWow64FsRedirection( PVOID OldValue );

    //check whether the function is exported by the DLL
    F_Wow64RevertWow64FsRedirection* RevertRedir = (F_Wow64RevertWow64FsRedirection*)lib.GetProcAddress("Wow64RevertWow64FsRedirection");
    if ( RevertRedir == NULL ) {
        //Since the caller of the function should already now whether
        //32- or 64-bit Windows is running we return an error.
        err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": GetProcAddress(Wow64RevertWow64FsRedirection) failed, err=0x%X\n", err));
        return err;
    }

    //disable redirection
    if ( !RevertRedir( mOldRedirVal ) ) {
        err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": RevertRedir failed, err=0x%X\n", err));
        return err;
    }

    //OK
    return ERROR_SUCCESS;
}

//---global functions -------------------------------------------------------------------------------------

WNERR WnIsWow64Process( bool& is )
{
    //initialization
    is = false;

    WnLibrary lib;

    // load the kernel32 DLL
    WNERR err = lib.Load(TEXT("kernel32"));
    if (ERROR_SUCCESS != err) {
        // failed
        return err;
    }

    //Since the Windows function 'IsWow64Process' isn't implemented on earlier
    //operating systems (e.g. Windows 2000) we check whether the Kernel32 DLL
    //exports this function.
    typedef BOOL WINAPI F_IsWow64Process(HANDLE hProcess, PBOOL Wow64Process);

    //check whether the function is exported by the DLL
    F_IsWow64Process* IsWow64Process = (F_IsWow64Process*)lib.GetProcAddress("IsWow64Process");
    if (IsWow64Process != NULL) {

        //YES: check whether 64-bit Windows is running
        BOOL isWow64 = FALSE;
        if (!IsWow64Process(
            GetCurrentProcess(),  //HANDLE hProcess,
            &isWow64              //PBOOL Wow64Process
        )) {
            err = ::GetLastError();
            WNTRACE(TRCERR, tprint(__FUNCTION__": IsWow64Process failed, err=0x%X\n", err));
            return err;
        }

        //return the result
        is = (isWow64 ? true : false);

    }
    else {
        //NO: Since the function is always available on 64-bit Windows
        //we know that 64-bit Windows isn't running.
        is = false;
    }

    //OK
    return TSTATUS_SUCCESS;
}


#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnWow64_h__

/***************************** EOF **************************************/

/************************************************************************
 *
 *  Module:       libtb.h
 *  Description:
 *
 *  Runtime Env.: any
 *  Author(s):    <PERSON><PERSON>, <PERSON>
 *  Company:      Thesycon GmbH, Ilmenau
 ************************************************************************/

#ifndef __libtb_h__
#define __libtb_h__

// basic types and functions
#define NO_TBASE_AL_INLINE
#include "libbase.h"

// environment-specific libtb implementation
#include "libtb_env.h"


#include "TbOSEnv.h"
#include "TbUtils.h"
#include "TbStringUtils.h"
#include "TbStdStringUtils.h"



#endif // __libtb_h__

/******************************** EOF ***********************************/

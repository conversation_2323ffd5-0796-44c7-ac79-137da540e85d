#ifndef OriginS1M4_H
#define OriginS1M4_H

#include "originbase.h"
#include "workspace.h"
#include "appsettings.h"

class QLabel;
class QPushButton;
class QButtonGroup;

namespace Ui {
    class OriginS1M4;
}
    
class OriginS1M4  : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT

public:
    explicit OriginS1M4(QWidget *parent = nullptr, const QString &name = {});
    ~OriginS1M4();
    OriginS1M4& setName(const QString& name);
    OriginS1M4& setFont(const QFont& font);
    OriginS1M4& setStretchFactor(int titleHeightRatio, int widgetHeightRatio);
    OriginS1M4& setValueNCType(QString type);
    OriginS1M4& setValueReverbType(QString type);

protected:
    void paintEvent(QPaintEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    void updateAttribute() override;
    void setAllChildFont(QWidget* widget, const QFont& font);
    void saveAttribute(QString attribute);

private slots:
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);

private:
    void initSigConnect();
    void setWidgetStyle(QWidget* widget);
    void setButtonStyle(QWidget* widget);
    void updateAttributeExtend();
    void reset();

private:
    Ui::OriginS1M4* ui;
    int m_titleHeightRatio;
    int m_widgetHeightRatio;
    QButtonGroup* m_buttonGroupNC;
    QButtonGroup* m_buttonGroupReverb;
    QString mNCType;
    QString mReverbType;
    double mDryWet;
};

#endif // OriginS1M4_H

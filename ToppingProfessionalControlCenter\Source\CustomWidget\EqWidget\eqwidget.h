#ifndef EQUALIZERWIDGET_H
#define EQUALIZERWIDGET_H

#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QCheckBox>
#include <QVector>
#include <QScrollArea>
#include <QFrame>
#include "comboboxs1m3.h"
#include "dials1m1.h"

struct EqWidgetItemData {
    QString type;
    float gain;
    float frequency;
    float qValue;
    bool enabled;
};

class EqWidgetIem : public QFrame
{
    Q_OBJECT

public:
    explicit EqWidgetIem(int index, QWidget* parent = nullptr);
    ~EqWidgetIem();

    void setItemData(const EqWidgetItemData& data);
    EqWidgetItemData getItemData() const;

    void setItemIndex(int index);
    int getItemIndex() const { return mIndex; }

    void setLayoutSpacing(int spacing);
    void setLayoutMargins(int left, int top, int right, int bottom);
    void setLayoutMargins(int margin);
    void setMinimumItemWidth(int width);
    void setWidgetStretchFactors(int labelStretch, int comboStretch, int gainStretch,
                                int freqStretch, int qStretch, int checkStretch);
    void getWidgetStretchFactors(int& labelStretch, int& comboStretch, int& gainStretch,
                                int& freqStretch, int& qStretch, int& checkStretch) const;
    int getLayoutSpacing() const { return mLayoutSpacing; }
    QMargins getLayoutMargins() const;
    int getMinimumItemWidth() const { return mMinimumItemWidth; }

protected:
    void resizeEvent(QResizeEvent* event) override;

private:
    void onTypeChanged(const QString& text);
    void onGainChanged(float value);
    void onFrequencyChanged(float value);
    void onQValueChanged(float value);
    void onEnabledChanged(bool enabled);

private:
    void setupUI();
    void setupConnections();
    void updateTypeOptions();
    void applyLayoutSettings();

    int mIndex;
    EqWidgetItemData mData;

    int mLayoutSpacing;
    QMargins mLayoutMargins;
    int mMinimumItemWidth;
    int mLabelStretch;
    int mComboStretch;
    int mGainStretch;
    int mFreqStretch;
    int mQStretch;
    int mCheckStretch;

    QVBoxLayout* mMainLayout;
    QLabel* mItemLabel;

    ComboBoxS1M3* mTypeComboBox;

    DialS1M1* mGainDial;
    DialS1M1* mFrequencyDial;
    DialS1M1* mQValueDial;

    QCheckBox* mEnabledCheckBox;

signals:
    void dataChanged(int index, const EqWidgetItemData& data);
};

class EqWidget : public QWidget
{
    Q_OBJECT

public:
    explicit EqWidget(QWidget* parent = nullptr);
    ~EqWidget();
    void setItemStretchFactor(double stretchFactor);
    double getItemStretchFactor() const { return mItemStretchFactor; }

    void setItemCount(int count);
    int getItemCount() const { return mItems.size(); }

    void addItem();
    void removeItem(int index = -1);
    void removeAllItems();

    void setEqData(const QVector<EqWidgetItemData>& data);
    QVector<EqWidgetItemData> getEqData() const;

    void setMainLayoutSpacing(int spacing);
    void setMainLayoutMargins(int left, int top, int right, int bottom);
    void setMainLayoutMargins(int margin);

    void setTitleLayoutSpacing(int spacing);
    void setTitleLayoutMargins(int left, int top, int right, int bottom);
    void setTitleLayoutMargins(int margin);

    void setItemsLayoutSpacing(int spacing);
    void setItemsLayoutMargins(int left, int top, int right, int bottom);
    void setItemsLayoutMargins(int margin);

    void setScrollAreaMargins(int left, int top, int right, int bottom);
    void setScrollAreaMargins(int margin);

    void setAllItemsLayoutSpacing(int spacing);
    void setAllItemsLayoutMargins(int left, int top, int right, int bottom);
    void setAllItemsLayoutMargins(int margin);
    void setAllItemsMinimumWidth(int width);

    void setAllItemsStretchFactors(int labelStretch, int comboStretch, int gainStretch,
                                  int freqStretch, int qStretch, int checkStretch);

    void setMainLayoutStretchFactors(int leftStretch, int centerStretch, int rightStretch);
    void getMainLayoutStretchFactors(int& leftStretch, int& centerStretch, int& rightStretch) const;

    void setContentLayoutStretchFactors(int labelAreaStretch, int scrollAreaStretch);
    void getContentLayoutStretchFactors(int& labelAreaStretch, int& scrollAreaStretch) const;

    int getMainLayoutSpacing() const { return mMainLayoutSpacing; }
    int getTitleLayoutSpacing() const { return mTitleLayoutSpacing; }
    int getItemsLayoutSpacing() const { return mItemsLayoutSpacing; }
    QMargins getMainLayoutMargins() const;
    QMargins getTitleLayoutMargins() const;
    QMargins getItemsLayoutMargins() const;
    QMargins getScrollAreaMargins() const;
    
protected:
    void resizeEvent(QResizeEvent* event) override;

private:
    void onAddItemClicked();
    void onRemoveItemClicked();

private:
    void setupUI();
    void setupConnections();
    void updateItemIndices();
    void createDefaultItems();
    void applyLayoutSettings();
    void updateItemWidth(EqWidgetIem* item);

    int mMainLayoutSpacing;
    int mTitleLayoutSpacing;
    int mItemsLayoutSpacing;
    QMargins mMainLayoutMargins;
    QMargins mTitleLayoutMargins;
    QMargins mItemsLayoutMargins;
    QMargins mScrollAreaMargins;
    double mItemStretchFactor;
    int mMainLeftStretch;
    int mMainCenterStretch;
    int mMainRightStretch;
    int mLabelAreaStretch;
    int mScrollAreaStretch;

    QHBoxLayout* mMainLayout;
    QVBoxLayout* mTitleLayout;
    QHBoxLayout* mItemsLayout;
    QScrollArea* mScrollArea;
    QWidget* mScrollWidget;

    QPushButton* mAddItemButton;
    QPushButton* mRemoveItemButton;
    QLabel* mTitleLabel;

    QVector<EqWidgetIem*> mItems;

signals:
    void itemDataChanged(int index, const EqWidgetItemData& data);
    void ItemCountChanged(int count);
};

#endif // EQUALIZERWIDGET_H

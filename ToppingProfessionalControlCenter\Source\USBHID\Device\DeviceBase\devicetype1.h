#ifndef DEVICETYPE1_H
#define DEVICETYPE1_H


#include "devicebase.h"


class DeviceType1 : public DeviceBase
{
    Q_OBJECT
public:
    explicit DeviceType1(QObject* parent=nullptr) : DeviceBase(parent) { }
    ~DeviceType1() { }
    enum FrameProtocol
    {
        read_ack    = 0x11,
        read_nack   = 0x10,
        r_ack       = 0x1f,
        write_ack   = 0x21,
        write_nack  = 0x20,
        w_ack       = 0x2f
    };
    struct FrameInfo
    {
        unsigned int cmd;
        int data;
        unsigned char total;
        unsigned char current;
        FrameProtocol protocol;
    };
    unsigned int makeCMD(unsigned int t, unsigned int c, unsigned int a) { return t | c | a; }
    void sendFrame(FrameInfo frame);
protected:
    int readFrame() override;
signals:
    void newFrameReceived(FrameInfo frame);
};


#endif // DEVICETYPE1_H


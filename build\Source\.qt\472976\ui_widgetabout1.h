/********************************************************************************
** Form generated from reading UI file 'widgetabout1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_WIDGETABOUT1_H
#define UI_WIDGETABOUT1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_WidgetAbout1
{
public:
    QGridLayout *gridLayout;
    QSpacerItem *verticalSpacer_7;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer;
    QVBoxLayout *verticalLayout;
    QWidget *widget1;
    QLabel *label1;
    QLabel *labelModel;
    QSpacerItem *verticalSpacer_3;
    QWidget *widget2;
    QLabel *label2;
    QLabel *labelHard;
    QSpacerItem *verticalSpacer_2;
    QWidget *widget3;
    QLabel *label3;
    QLabel *labelFirmwareTip;
    QPushButton *button3;
    QSpacerItem *verticalSpacer_4;
    QWidget *widget4;
    QLabel *label4;
    QPushButton *button4;
    QLabel *labelSoftwareTip;
    QSpacerItem *verticalSpacer_5;
    QWidget *widget5;
    QLabel *label5;
    QPushButton *button5;
    QSpacerItem *horizontalSpacer;

    void setupUi(QWidget *WidgetAbout1)
    {
        if (WidgetAbout1->objectName().isEmpty())
            WidgetAbout1->setObjectName("WidgetAbout1");
        WidgetAbout1->resize(712, 410);
        gridLayout = new QGridLayout(WidgetAbout1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_7 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_7, 0, 0, 1, 3);

        horizontalSpacer_2 = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer_2, 1, 2, 1, 1);

        verticalSpacer = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer, 2, 0, 1, 3);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName("verticalLayout");
        widget1 = new QWidget(WidgetAbout1);
        widget1->setObjectName("widget1");
        label1 = new QLabel(widget1);
        label1->setObjectName("label1");
        label1->setGeometry(QRect(30, 1, 57, 16));
        labelModel = new QLabel(widget1);
        labelModel->setObjectName("labelModel");
        labelModel->setGeometry(QRect(480, 10, 53, 15));
        labelModel->setAlignment(Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter);

        verticalLayout->addWidget(widget1);

        verticalSpacer_3 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_3);

        widget2 = new QWidget(WidgetAbout1);
        widget2->setObjectName("widget2");
        label2 = new QLabel(widget2);
        label2->setObjectName("label2");
        label2->setGeometry(QRect(30, 1, 47, 16));
        labelHard = new QLabel(widget2);
        labelHard->setObjectName("labelHard");
        labelHard->setGeometry(QRect(490, 0, 53, 15));
        labelHard->setAlignment(Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter);

        verticalLayout->addWidget(widget2);

        verticalSpacer_2 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_2);

        widget3 = new QWidget(WidgetAbout1);
        widget3->setObjectName("widget3");
        label3 = new QLabel(widget3);
        label3->setObjectName("label3");
        label3->setGeometry(QRect(30, 1, 201, 16));
        labelFirmwareTip = new QLabel(widget3);
        labelFirmwareTip->setObjectName("labelFirmwareTip");
        labelFirmwareTip->setGeometry(QRect(400, 0, 53, 15));
        labelFirmwareTip->setAlignment(Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter);
        button3 = new QPushButton(widget3);
        button3->setObjectName("button3");
        button3->setGeometry(QRect(470, 0, 80, 23));
        button3->setStyleSheet(QString::fromUtf8(""));

        verticalLayout->addWidget(widget3);

        verticalSpacer_4 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_4);

        widget4 = new QWidget(WidgetAbout1);
        widget4->setObjectName("widget4");
        label4 = new QLabel(widget4);
        label4->setObjectName("label4");
        label4->setGeometry(QRect(30, 1, 124, 16));
        button4 = new QPushButton(widget4);
        button4->setObjectName("button4");
        button4->setGeometry(QRect(480, 0, 80, 23));
        button4->setStyleSheet(QString::fromUtf8(""));
        labelSoftwareTip = new QLabel(widget4);
        labelSoftwareTip->setObjectName("labelSoftwareTip");
        labelSoftwareTip->setGeometry(QRect(400, 0, 53, 15));
        labelSoftwareTip->setAlignment(Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter);

        verticalLayout->addWidget(widget4);

        verticalSpacer_5 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_5);

        widget5 = new QWidget(WidgetAbout1);
        widget5->setObjectName("widget5");
        label5 = new QLabel(widget5);
        label5->setObjectName("label5");
        label5->setGeometry(QRect(30, 1, 136, 16));
        button5 = new QPushButton(widget5);
        button5->setObjectName("button5");
        button5->setGeometry(QRect(470, 0, 80, 23));
        button5->setStyleSheet(QString::fromUtf8("text-decoration: underline;\n"
"background:transparent;\n"
"text-align: right;"));

        verticalLayout->addWidget(widget5);

        verticalLayout->setStretch(0, 26);
        verticalLayout->setStretch(1, 22);
        verticalLayout->setStretch(2, 26);
        verticalLayout->setStretch(3, 22);
        verticalLayout->setStretch(4, 26);
        verticalLayout->setStretch(5, 22);
        verticalLayout->setStretch(6, 26);
        verticalLayout->setStretch(7, 22);
        verticalLayout->setStretch(8, 26);

        gridLayout->addLayout(verticalLayout, 1, 1, 1, 1);

        horizontalSpacer = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer, 1, 0, 1, 1);

        gridLayout->setRowStretch(0, 24);
        gridLayout->setRowStretch(1, 266);
        gridLayout->setRowStretch(2, 83);
        gridLayout->setColumnStretch(0, 60);
        gridLayout->setColumnStretch(1, 480);
        gridLayout->setColumnStretch(2, 60);

        retranslateUi(WidgetAbout1);

        QMetaObject::connectSlotsByName(WidgetAbout1);
    } // setupUi

    void retranslateUi(QWidget *WidgetAbout1)
    {
        WidgetAbout1->setWindowTitle(QCoreApplication::translate("WidgetAbout1", "WidgetSytem1", nullptr));
        label1->setText(QCoreApplication::translate("WidgetAbout1", "Model", nullptr));
        labelModel->setText(QCoreApplication::translate("WidgetAbout1", "M62", nullptr));
        label2->setText(QCoreApplication::translate("WidgetAbout1", "Hardware version", nullptr));
        labelHard->setText(QCoreApplication::translate("WidgetAbout1", "V1.00", nullptr));
        label3->setText(QCoreApplication::translate("WidgetAbout1", "Firmware version", nullptr));
        labelFirmwareTip->setText(QCoreApplication::translate("WidgetAbout1", "TextLabel", nullptr));
        button3->setText(QString());
        label4->setText(QCoreApplication::translate("WidgetAbout1", "Software version", nullptr));
        button4->setText(QString());
        labelSoftwareTip->setText(QCoreApplication::translate("WidgetAbout1", "TextLabel", nullptr));
        label5->setText(QCoreApplication::translate("WidgetAbout1", "Official website", nullptr));
        button5->setText(QCoreApplication::translate("WidgetAbout1", "Topping.pro", nullptr));
    } // retranslateUi

};

namespace Ui {
    class WidgetAbout1: public Ui_WidgetAbout1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_WIDGETABOUT1_H

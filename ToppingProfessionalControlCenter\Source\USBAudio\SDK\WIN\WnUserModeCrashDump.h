/************************************************************************
 *  Module:       WnUserModeCrashDump.h
 *  Description:  Creates a dump if the user mode module crashes.
 *
 *  Runtime Env.: 32-/64-bit Windows
 *  Author(s):    Rene <PERSON>
 *  Company:      Thesycon GmbH, Germany      http://www.thesycon.de
 ************************************************************************/

#ifndef __WnUserModeCrashDump_h__
#define __WnUserModeCrashDump_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

class WnUserModeCrashDump
{
// construction/destruction
protected:
    //
    // constructor
    //
    WnUserModeCrashDump();

    //
    // destructor
    //
    ~WnUserModeCrashDump();

public:
    static
    void
    Init(
        const TCHAR* prefix,
        const TCHAR* filePath,
        int filePathRootId
        );


//static methods
private:
    //
    //This function is called whenever the UnhandledExceptionFilter function gets control,
    //and the process is not being debugged.
    //
    // parameters:
    //  ExceptionInfo       A pointer to an EXCEPTION_POINTERS structure that specifies a description
    //                      of the exception and the processor context at the time of the exception.
    //                      This pointer is the return value of a call to the GetExceptionInformation
    //                      function.
    // return:
    //   EXCEPTION_EXECUTE_HANDLER, EXCEPTION_CONTINUE_EXECUTION or EXCEPTION_CONTINUE_SEARCH
    static LONG WINAPI
    TopLevelExceptionHandler(
        struct _EXCEPTION_POINTERS* exceptionPointers
        );

// data
protected:
    TCHAR mPrefix[MAX_PATH];
    TCHAR mFilePath[MAX_PATH];
    int mFilePathRootId;
    bool mIsInitialized;

    // singleton
    static WnUserModeCrashDump sWnUserModeCrashDump;

// make copy constructor and assignment operator unavailable
PRIVATE_COPY_CONSTRUCTOR(WnUserModeCrashDump)
PRIVATE_ASSIGNMENT_OPERATOR(WnUserModeCrashDump)

}; // class WnUserModeCrashDump



//
// prefix
//
//  The created dump file will be named "[prefix]_[DATETIME].dmp".
//  Examples:
//    prefix = TEXT("MyModuleName")
//
// filePath
//
//  Full path or sub-path of the location of the created dump file depending on the
//  value of filePathRootId. If set to an empty string the dump file is
//  created in the root folder defined by filePathRootId. If set to an empty
//  string while filePathRootId is set to -1 the current folder of the running
//  application is used (Note: The current application folder may be write-protected,
//  e.g. if the application is executed from a CD.)
//  Usually the dump file of an application should be created in the TEMP directory
//  while services may use the system32 directory or any other user-independent directory.
//  Note: Consider possible file redirection for 32-bit modules running under 64-bit
//  Windows.
//  Examples:
//    filePath = TEXT("MySubfolder")
//    filePath = TEXT("%SystemRoot%\\system32")
//    filePath = TEXT("%TEMP%")
//
// filePathRootId
//
//  CSIDL of the root folder of the dump file to create (see MSDN: CSIDL). If set
//  to -1 it is ignored and the filePath parameter is expected to contain the
//  full path of the dump file to create. Otherwise filePath is expected
//  to contain the sub-path located under the root folder.
//  Examples:
//    filePathRootId = -1
//    filePathRootId = CSIDL_LOCAL_APPDATA
//
inline
void
WnInitUserModeCrashDump(
    const TCHAR* prefix = NULL,   // defaults to .exe base name
    const TCHAR* filePath = NULL, // defaults to %TEMP%
    int filePathRootId = -1
    )
{
    WnUserModeCrashDump::Init(prefix, filePath, filePathRootId);
}

#ifdef LIBWN_NAMESPACE
}
#endif

#endif //__WnUserModeCrashDump_h__

/***************************** EOF **************************************/

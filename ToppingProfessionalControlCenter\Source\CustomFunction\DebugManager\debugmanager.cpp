#include "debugmanager.h"


#define ANSI_COLOR_WHITE    "\x1b[37m"
#define ANSI_COLOR_YELLOW   "\x1b[33m"
#define ANSI_COLOR_GREEN    "\x1b[32m"
#define ANSI_COLOR_ORANGE   "\x1b[38;5;208m"
#define ANSI_COLOR_RED      "\x1b[31m"
#define ANSI_COLOR_RESET    "\x1b[0m"


DebugManager DebugManager::mInstance;


void DebugManager::installMessageHandler()
{
    qInstallMessageHandler(customMessageHandler);
}
void DebugManager::customMessageHandler(QtMsgType type, const QMessageLogContext& context, const QString& msg)
{
    Q_UNUSED(context);
    int level = 0;
    QString coloredMessage;
    switch (type) {
        case QtDebugMsg:
            level = 1;
#ifdef Q_OS_MACOS
            coloredMessage = "[Debug]\t " + msg + "\n";
#else
            coloredMessage = QString(ANSI_COLOR_WHITE) + "[Debug]\t " + msg + ANSI_COLOR_RESET + "\n";
#endif
            break;
        case QtInfoMsg:
            level = 2;
#ifdef Q_OS_MACOS
            coloredMessage = "[Info]\t " + msg + "\n";
#else
            coloredMessage = QString(ANSI_COLOR_YELLOW) + "[Info]\t " + msg + ANSI_COLOR_RESET + "\n";
#endif
            break;
        case QtWarningMsg:
            level = 3;
#ifdef Q_OS_MACOS
            coloredMessage = "[Warning]\t " + msg + "\n";
#else
            coloredMessage = QString(ANSI_COLOR_GREEN) + "[Warning]\t " + msg + ANSI_COLOR_RESET + "\n";
#endif
            break;
        case QtCriticalMsg:
            level = 4;
#ifdef Q_OS_MACOS
            coloredMessage = "[Critical] " + msg + "\n";
#else
            coloredMessage = QString(ANSI_COLOR_ORANGE) + "[Critical] " + msg + ANSI_COLOR_RESET + "\n";
#endif
            break;
        case QtFatalMsg:
            level = 5;
#ifdef Q_OS_MACOS
            coloredMessage = "[Fatal]\t " + msg + "\n";
#else
            coloredMessage = QString(ANSI_COLOR_RED) + "[Fatal]\t " + msg + ANSI_COLOR_RESET + "\n";
#endif
            break;
    }
    if (level <= DebugLevel) {
        fprintf(stderr, "%s", qPrintable(coloredMessage));
    }
}


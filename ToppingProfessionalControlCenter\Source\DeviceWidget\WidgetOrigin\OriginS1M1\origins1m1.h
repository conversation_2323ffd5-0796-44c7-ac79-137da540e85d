#ifndef ORIGINS1M1_H
#define ORIGINS1M1_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"
#include "pushbuttons1m8.h"


namespace Ui {
class OriginS1M1;
}


class OriginS1M1 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M1(QWidget* parent=nullptr, QString name="");
    ~OriginS1M1();
    OriginS1M1& setName(QString name);
    OriginS1M1& setFont(QFont font);
    OriginS1M1& setVolumeMeter(int value);
    OriginS1M1& setVolumeMeterClear();
    OriginS1M1& setVolumeMeterSlip();
    OriginS1M1& setGain(float value);
    OriginS1M1& setGainLock(bool state=true);
    OriginS1M1& setMuteAffectGain(bool state=true);
    OriginS1M1& setGainAffectMute(bool state=true);
    OriginS1M1& setGainRange(float min, float max);
    OriginS1M1& setGainDefault(float value);
    OriginS1M1& setGainWidgetDisable(float value);
    OriginS1M1& setChannelNameEditable(bool state=true);
    OriginS1M1& setValueMIC(QString mic);
    OriginS1M1& setValue48V(bool state=true);
    OriginS1M1& setValueGAIN(float value);
    OriginS1M1& setValueMUTE(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OriginS1M1* ui;
    QTimer mTimer;
    QFont mFont;
    QString mPreMIC="";
    int mPre48V=-2147483648;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetVSlider_valueChanged(int value);
    void in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M8::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // ORIGINS1M1_H


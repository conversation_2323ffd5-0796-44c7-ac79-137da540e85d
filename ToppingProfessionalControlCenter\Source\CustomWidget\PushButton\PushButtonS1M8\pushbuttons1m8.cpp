#include "globalfont.h"
#include "pushbuttons1m8.h"


PushButtonS1M8::PushButtonS1M8(QWidget* parent)
    : QWidget(parent)
{
    mPushButtonMic1.setParent(this);
    mPushButtonMic35.setParent(this);
    mPushButtonMicHP.setParent(this);
    mPushButton48V.setParent(this);
    mPushButtonMUTE.setParent(this);
    mLabelMic1.setParent(this);
    mLabelMic35.setParent(this);
    mLabelMicHP.setParent(this);
    QString style;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelMic1.setStyleSheet(style);
    mLabelMic35.setStyleSheet(style);
    mLabelMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic1.setStyleSheet(style);
    mPushButtonMic35.setStyleSheet(style);
    mPushButtonMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButton48V.setStyleSheet(style);
    mPushButtonMUTE.setStyleSheet(style);
    mPushButtonMic1.setText("Mic 1");
    mPushButtonMic35.setText("Mic-3.5");
    mPushButtonMicHP.setText("Mic-HP");
    mPushButton48V.setText("48V");
    mPushButtonMUTE.setText("MUTE");
    connect(&mPushButtonMic1, SIGNAL(clicked()), this, SLOT(in_mPushButtonMic1_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMic35, SIGNAL(clicked()), this, SLOT(in_mPushButtonMic35_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMicHP, SIGNAL(clicked()), this, SLOT(in_mPushButtonMicHP_clicked()), Qt::UniqueConnection);
    connect(&mPushButton48V, SIGNAL(clicked()), this, SLOT(in_mPushButton48V_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMUTE, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTE_clicked()), Qt::UniqueConnection);
}
PushButtonS1M8::~PushButtonS1M8()
{

}


// override
void PushButtonS1M8::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wLabel=wPixelPerRatio * 10;
    int wSpace1=wPixelPerRatio * 10;
    int wPushButton=42*wPixelPerRatio;
    int xLabel=0;
    int xPushButton=xLabel + wLabel;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hSpace1=hPixelPerRatio * 15;
    int hSpace2=hPixelPerRatio * 2;
    int hSpace3=hPixelPerRatio * 20;
    int hSpace4=hPixelPerRatio * 5;
    int hPushButton=(size().height() - hSpace1 - hSpace2 * 3 - hSpace3 ) / 6;
    mLabelMic1.setGeometry(xLabel, hSpace1, wLabel, hPushButton);
    mPushButtonMic1.setGeometry(xPushButton, hSpace1, wPushButton, hPushButton);
    mLabelMic35.setGeometry(xLabel, hSpace1 + hSpace2 + hPushButton, wLabel, hPushButton);
    mPushButtonMic35.setGeometry(xPushButton, hSpace1 + hPushButton + hSpace2, wPushButton, hPushButton);
    mLabelMicHP.setGeometry(xLabel, hSpace1 + hSpace2 * 2 + hPushButton * 2, wLabel, hPushButton);
    mPushButtonMicHP.setGeometry(xPushButton, hSpace1 + hSpace2 * 2 + hPushButton * 2, wPushButton, hPushButton);
    mPushButton48V.setGeometry(xPushButton + wPushButton + 2*hSpace2, hSpace1, 35*wPixelPerRatio, hPushButton);
    mPushButtonMUTE.setGeometry(xPushButton + wPushButton + 2*hSpace2, hSpace1 + hSpace2 * 2 + hPushButton * 2, 35*wPixelPerRatio, hPushButton);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonMic35.height()) /100.0*80);
    mPushButtonMic1.setFont(mFont);
    mPushButtonMic35.setFont(mFont);
    mPushButtonMicHP.setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonMUTE.text(), mPushButtonMUTE.rect())/100.0*85);
    mPushButton48V.setFont(mFont);
    mPushButtonMUTE.setFont(mFont);
}


// slot
void PushButtonS1M8::in_mPushButtonMic1_clicked()
{
    QString style;
    if(!mPushButtonStateMic1)
    {
        mPushButtonStateMic1 = true;
        mPushButtonStateMic35 = false;
        mPushButtonStateMicHP = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelMic1.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelMic35.setStyleSheet(style);
        mLabelMicHP.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic1.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic35.setStyleSheet(style);
        mPushButtonMicHP.setStyleSheet(style);
        emit buttonStateChanged(buttonMic1, true);
    }
}
void PushButtonS1M8::in_mPushButtonMic35_clicked()
{
    QString style;
    if(!mPushButtonStateMic35)
    {
        mPushButtonStateMic35 = true;
        mPushButtonStateMic1 = false;
        mPushButtonStateMicHP = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelMic35.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelMic1.setStyleSheet(style);
        mLabelMicHP.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic35.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic1.setStyleSheet(style);
        mPushButtonMicHP.setStyleSheet(style);
        emit buttonStateChanged(buttonMic35, true);
    }
}
void PushButtonS1M8::in_mPushButtonMicHP_clicked()
{
    QString style;
    if(!mPushButtonStateMicHP)
    {
        mPushButtonStateMicHP = true;
        mPushButtonStateMic1 = false;
        mPushButtonStateMic35 = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelMicHP.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelMic1.setStyleSheet(style);
        mLabelMic35.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMicHP.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic1.setStyleSheet(style);
        mPushButtonMic35.setStyleSheet(style);
        emit buttonStateChanged(buttonMicHP, true);
    }
}
void PushButtonS1M8::in_mPushButton48V_clicked()
{
    QString style;
    mPushButtonState48V = !mPushButtonState48V;
    if(mPushButtonState48V)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(222, 55, 55);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButton48V.setStyleSheet(style);
    emit buttonStateChanged(button48V, mPushButtonState48V);
}
void PushButtonS1M8::in_mPushButtonMUTE_clicked()
{
    QString style;
    mPushButtonStateMUTE = !mPushButtonStateMUTE;
    if(mPushButtonStateMUTE)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(107, 5, 5);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonMUTE.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTE, mPushButtonStateMUTE);
}


// setter & getter
PushButtonS1M8& PushButtonS1M8::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M8& PushButtonS1M8::setPushButtonStateMic1(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateMic1 = true;
    mPushButtonStateMic35 = false;
    mPushButtonStateMicHP = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelMic1.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelMic35.setStyleSheet(style);
    mLabelMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic1.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic35.setStyleSheet(style);
    mPushButtonMicHP.setStyleSheet(style);
    return *this;
}
PushButtonS1M8& PushButtonS1M8::setPushButtonStateMic35(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateMic35 = true;
    mPushButtonStateMic1 = false;
    mPushButtonStateMicHP = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelMic35.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelMic1.setStyleSheet(style);
    mLabelMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic35.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic1.setStyleSheet(style);
    mPushButtonMicHP.setStyleSheet(style);
    return *this;
}
PushButtonS1M8& PushButtonS1M8::setPushButtonStateMicHP(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateMicHP = true;
    mPushButtonStateMic1 = false;
    mPushButtonStateMic35 = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelMicHP.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelMic1.setStyleSheet(style);
    mLabelMic35.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic1.setStyleSheet(style);
    mPushButtonMic35.setStyleSheet(style);
    return *this;
}
PushButtonS1M8& PushButtonS1M8::setPushButtonState48V(bool state)
{
    QString style;
    mPushButtonState48V = state;
    if(mPushButtonState48V)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(222, 55, 55);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButton48V.setStyleSheet(style);
    return *this;
}
PushButtonS1M8& PushButtonS1M8::setPushButtonStateMUTE(bool state)
{
    QString style;
    mPushButtonStateMUTE = state;
    if(mPushButtonStateMUTE)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(107, 5, 5);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonMUTE.setStyleSheet(style);
    return *this;
}
PushButtonS1M8& PushButtonS1M8::setPushButtonClickedMUTE(bool state)
{
    mPushButtonStateMUTE = !state;
    in_mPushButtonMUTE_clicked();
    return *this;
}
bool PushButtonS1M8::getPushButtonStateMic1()
{
    return mPushButtonStateMic1;
}
bool PushButtonS1M8::getPushButtonStateMic35()
{
    return mPushButtonStateMic35;
}
bool PushButtonS1M8::getPushButtonStateMicHP()
{
    return mPushButtonStateMicHP;
}
bool PushButtonS1M8::getPushButtonState48V()
{
    return mPushButtonState48V;
}
bool PushButtonS1M8::getPushButtonStateMUTE()
{
    return mPushButtonStateMUTE;
}


/********************************************************************************
** Form generated from reading UI file 'inputs2m1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_INPUTS2M1_H
#define UI_INPUTS2M1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <hsliders2m1.h>
#include <volumemeters2m1.h>

QT_BEGIN_NAMESPACE

class Ui_InputS2M1
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QLineEdit *lineEdit;
    QPushButton *button48V;
    QPushButton *buttonMute;
    HSliderS2M1 *slider;
    VolumeMeterS2M1 *volume;
    QPushButton *buttonMic1;
    QPushButton *buttonMic35;
    QPushButton *buttonMicHP;

    void setupUi(QWidget *InputS2M1)
    {
        if (InputS2M1->objectName().isEmpty())
            InputS2M1->setObjectName("InputS2M1");
        InputS2M1->resize(226, 60);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(InputS2M1->sizePolicy().hasHeightForWidth());
        InputS2M1->setSizePolicy(sizePolicy);
        InputS2M1->setMinimumSize(QSize(226, 60));
        gridLayout = new QGridLayout(InputS2M1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(InputS2M1);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(0, 10, 41, 41));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        button48V = new QPushButton(frame);
        button48V->setObjectName("button48V");
        button48V->setGeometry(QRect(50, 10, 41, 21));
        button48V->setCheckable(true);
        buttonMute = new QPushButton(frame);
        buttonMute->setObjectName("buttonMute");
        buttonMute->setGeometry(QRect(50, 30, 41, 21));
        buttonMute->setCheckable(true);
        slider = new HSliderS2M1(frame);
        slider->setObjectName("slider");
        slider->setGeometry(QRect(110, 0, 101, 16));
        volume = new VolumeMeterS2M1(frame);
        volume->setObjectName("volume");
        volume->setGeometry(QRect(110, 20, 101, 16));
        buttonMic1 = new QPushButton(frame);
        buttonMic1->setObjectName("buttonMic1");
        buttonMic1->setGeometry(QRect(90, 41, 51, 20));
        buttonMic1->setCheckable(true);
        buttonMic35 = new QPushButton(frame);
        buttonMic35->setObjectName("buttonMic35");
        buttonMic35->setGeometry(QRect(130, 40, 51, 20));
        buttonMic35->setCheckable(true);
        buttonMicHP = new QPushButton(frame);
        buttonMicHP->setObjectName("buttonMicHP");
        buttonMicHP->setGeometry(QRect(180, 40, 51, 20));
        buttonMicHP->setCheckable(true);

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(InputS2M1);

        QMetaObject::connectSlotsByName(InputS2M1);
    } // setupUi

    void retranslateUi(QWidget *InputS2M1)
    {
        InputS2M1->setWindowTitle(QCoreApplication::translate("InputS2M1", "Form", nullptr));
        lineEdit->setText(QCoreApplication::translate("InputS2M1", "IN 1", nullptr));
        button48V->setText(QCoreApplication::translate("InputS2M1", "48V", nullptr));
        buttonMute->setText(QCoreApplication::translate("InputS2M1", "MUTE", nullptr));
        buttonMic1->setText(QCoreApplication::translate("InputS2M1", "Mic 1", nullptr));
        buttonMic35->setText(QCoreApplication::translate("InputS2M1", "Mic-3.5", nullptr));
        buttonMicHP->setText(QCoreApplication::translate("InputS2M1", "Mic-HP", nullptr));
    } // retranslateUi

};

namespace Ui {
    class InputS2M1: public Ui_InputS2M1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_INPUTS2M1_H

#ifndef MESSAGEBOXS2M1_H
#define MESSAGEBOXS2M1_H

#include <QFont>
#include <QRadioButton>
#include <QPushButton>
#include <QResizeEvent>
#include <QButtonGroup>

#include "framelesswindow.h"

/*test case
    MessageBoxS2M1 msgBox;
    msgBox.setModal(true);
    msgBox.setFont(QFont("Arial", 12));
    msgBox.setMovable(false);
    msgBox.setResizable(false);
    QPoint pointCenter = QApplication::primaryScreen()->availableGeometry().center();
    int w = 600;
    int h = 300;
    msgBox.setGeometry(pointCenter.x() - w/2, pointCenter.y() - h/2, w, h);
    msgBox.setTitle("Downloaded");
    msgBox.setTextRadio1("Save the current workspace and download to the device");
    msgBox.setTextRadio2("Save as workspace and download to device");
    msgBox.setTextRadio3("Download to device only");
    msgBox.setTextButtonYes("OK");
    msgBox.setTextButtonNo("Cancel");
    int result = msgBox.exec();
    if (result != MessageBoxS2M1::None)  {
        qDebug() << "Selected radio button index:" << result;
    }
*/

class MessageBoxS2M1_WidgetS2M1 : public QWidget
{
    Q_OBJECT
public:
    enum Result
    {
        None,
        Option1,
        Option2,
        Option3
    };
    explicit MessageBoxS2M1_WidgetS2M1(QWidget* parent=nullptr);
    ~MessageBoxS2M1_WidgetS2M1() = default;
    MessageBoxS2M1_WidgetS2M1& setFont(QFont font);
    MessageBoxS2M1_WidgetS2M1& setTextRadio1(QString text);
    MessageBoxS2M1_WidgetS2M1& setTextRadio2(QString text);
    MessageBoxS2M1_WidgetS2M1& setTextRadio3(QString text);
    MessageBoxS2M1_WidgetS2M1& setTextButtonYes(QString text);
    MessageBoxS2M1_WidgetS2M1& setTextButtonNo(QString text);
    MessageBoxS2M1_WidgetS2M1& setColorTextRadio1(QColor color);
    MessageBoxS2M1_WidgetS2M1& setColorTextRadio2(QColor color);
    MessageBoxS2M1_WidgetS2M1& setColorTextRadio3(QColor color);
    MessageBoxS2M1_WidgetS2M1& setColorTextButtonYes(QColor color, QColor colorBG);
    MessageBoxS2M1_WidgetS2M1& setColorTextButtonNo(QColor color, QColor colorBG);
    Result getSelectedRadioIndex() const;

protected:
    void resizeEvent(QResizeEvent* e) override;

private:
    QFont mFont;
    QRadioButton mRadioButton1;
    QRadioButton mRadioButton2;
    QRadioButton mRadioButton3;
    QButtonGroup mButtonGroup;
    QPushButton mPushButtonYes;
    QPushButton mPushButtonNo;

private slots:
    void in_mPushButtonAll_clicked();

signals:
    void buttonClicked(int);
};

class MessageBoxS2M1 : public FramelessWindow
{
    Q_OBJECT
public:
    enum Result
    {
        None,
        Option1,
        Option2,
        Option3
    };
    explicit MessageBoxS2M1(FramelessWindow* parent=nullptr);
    ~MessageBoxS2M1() = default;
    MessageBoxS2M1& setFont(QFont font);
    MessageBoxS2M1& setTextRadio1(QString text);
    MessageBoxS2M1& setTextRadio2(QString text);
    MessageBoxS2M1& setTextRadio3(QString text);
    MessageBoxS2M1& setTextButtonYes(QString text);
    MessageBoxS2M1& setTextButtonNo(QString text);
    MessageBoxS2M1& setColorTitle(QColor color, QColor colorBG);
    MessageBoxS2M1& setColorBody(QColor color);
    MessageBoxS2M1& setColorTextRadio1(QColor color);
    MessageBoxS2M1& setColorTextRadio2(QColor color);
    MessageBoxS2M1& setColorTextRadio3(QColor color);
    MessageBoxS2M1& setColorTextButtonYes(QColor color, QColor colorBG);
    MessageBoxS2M1& setColorTextButtonNo(QColor color, QColor colorBG);
    Result getSelectedRadioIndex() const;

private:
    MessageBoxS2M1_WidgetS2M1 mWidget;

private slots:
    void in_mWidget_buttonClicked(int index);
};

#endif // MESSAGEBOXS2M1_H
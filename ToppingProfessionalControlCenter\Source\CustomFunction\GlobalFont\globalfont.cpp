#include <QDebug>
#include <QStringList>
#include <QFontMetrics>
#include <QFontDatabase>

#include "globalfont.h"


GlobalFont GlobalFont::mInstance;


bool GlobalFont::load(const QStringList& files)
{
    int fontIdDefault=-1;
    for(const QString& fontFile : files)
    {
        int fontId=QFontDatabase::addApplicationFont(fontFile);
        if(fontId == -1)
        {
            qWarning() << "GlobalFont::load // failed to add file[" << fontFile << "]";
        }
        else
        {
            if(fontIdDefault == -1) fontIdDefault = fontId;
        }
    }
    if(fontIdDefault != -1)
    {
        QStringList loadedFontFamilies=QFontDatabase::applicationFontFamilies(fontIdDefault);
        if(!loadedFontFamilies.empty())
        {
            mGlobal.setFamily(loadedFontFamilies.at(fontIdDefault));
            mGlobal.setWeight(QFont::Bold);
            mGlobal.setPixelSize(16);
            return true;
        }
    }
    qWarning() << "GlobalFont::load // failed to load font";
    return false;
}
QFont GlobalFont::font()
{
    return mGlobal;
}
QFont GlobalFont::font(QFont::Weight weight)
{
    QFont font(mGlobal);
    font.setWeight(weight);
    return font;
}
QFont GlobalFont::font(QString family, QFont::Weight weight)
{
    QFont font(mGlobal);
    font.setFamily(family);
    font.setWeight(weight);
    return font;
}
GlobalFont& GlobalFont::setPixelSize(int size)
{
    mGlobal.setPixelSize(size);
    return *this;
}
GlobalFont& GlobalFont::setPointSize(int size)
{
    mGlobal.setPointSize(size);
    return *this;
}
int GlobalFont::getPixelSize()
{
    return mGlobal.pixelSize();
}
int GlobalFont::getPointSize()
{
    return mGlobal.pointSize();
}
int GlobalFont::getSuitablePixelSize(QFont font, QString text, QRect rect)
{
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.horizontalAdvance(text) <= rect.width() && fm.height() <= rect.height())
    {
        font.setPixelSize(font.pixelSize() + 1);
        fm = QFontMetrics(font);
        if(font.pixelSize() > 60)
        {
            break;
        }
    }
    return font.pixelSize() - 1;
}
int GlobalFont::getSuitablePixelSize(QString text, QRect rect)
{
    QFont font(mGlobal);
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.horizontalAdvance(text) <= rect.width() && fm.height() <= rect.height())
    {
        font.setPixelSize(font.pixelSize() + 1);
        fm = QFontMetrics(font);
        if(font.pixelSize() > 60)
        {
            break;
        }
    }
    return font.pixelSize() - 1;
}
int GlobalFont::getSuitablePixelSize(QFont font, QString text, int width)
{
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.horizontalAdvance(text) <= width)
    {
        font.setPixelSize(font.pixelSize() + 1);
        fm = QFontMetrics(font);
        if(font.pixelSize() > 60)
        {
            break;
        }
    }
    return font.pixelSize() - 1;
}
int GlobalFont::getSuitablePixelSize(QFont font, int height)
{
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.height() <= height)
    {
        font.setPixelSize(font.pixelSize() + 1);
        fm = QFontMetrics(font);
        if(font.pixelSize() > 60)
        {
            break;
        }
    }
    return font.pixelSize() - 1;
}
int GlobalFont::getSuitablePixelSize(QString text, int width)
{
    QFont font(mGlobal);
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.horizontalAdvance(text) <= width)
    {
        font.setPixelSize(font.pixelSize() + 1);
        fm = QFontMetrics(font);
        if(font.pixelSize() > 60)
        {
            break;
        }
    }
    return font.pixelSize() - 1;
}
int GlobalFont::getSuitablePixelSize(int height)
{
    QFont font(mGlobal);
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.height() <= height)
    {
        font.setPixelSize(font.pixelSize() + 1);
        fm = QFontMetrics(font);
        if(font.pixelSize() > 60)
        {
            break;
        }
    }
    return font.pixelSize() - 1;
}
int GlobalFont::getSuitablePointSize(QFont font, QString text, QRect rect)
{
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.horizontalAdvance(text) <= rect.width() && fm.height() <= rect.height())
    {
        font.setPointSize(font.pointSize() + 1);
        fm = QFontMetrics(font);
        if(font.pointSize() > 60)
        {
            break;
        }
    }
#if defined(Q_OS_MACOS)
    return qCeil(1.2 * (font.pointSize() - 1));
#elif defined(Q_OS_WIN)
    return font.pointSize() - 1;
#endif
}
int GlobalFont::getSuitablePointSize(QString text, QRect rect)
{
    QFont font(mGlobal);
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.horizontalAdvance(text) <= rect.width() && fm.height() <= rect.height())
    {
        font.setPointSize(font.pointSize() + 1);
        fm = QFontMetrics(font);
        if(font.pointSize() > 60)
        {
            break;
        }
    }
#if defined(Q_OS_MACOS)
    return qCeil(1.2 * (font.pointSize() - 1));
#elif defined(Q_OS_WIN)
    return font.pointSize() - 1;
#endif
}
int GlobalFont::getSuitablePointSize(QFont font, QString text, int width)
{
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.horizontalAdvance(text) <= width)
    {
        font.setPointSize(font.pointSize() + 1);
        fm = QFontMetrics(font);
        if(font.pointSize() > 60)
        {
            break;
        }
    }
#if defined(Q_OS_MACOS)
    return qCeil(1.2 * (font.pointSize() - 1));
#elif defined(Q_OS_WIN)
    return font.pointSize() - 1;
#endif
}
int GlobalFont::getSuitablePointSize(QFont font, int height)
{
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.height() <= height)
    {
        font.setPointSize(font.pointSize() + 1);
        fm = QFontMetrics(font);
        if(font.pointSize() > 60)
        {
            break;
        }
    }
#if defined(Q_OS_MACOS)
    return qCeil(1.2 * (font.pointSize() - 1));
#elif defined(Q_OS_WIN)
    return font.pointSize() - 1;
#endif
}
int GlobalFont::getSuitablePointSize(QString text, int width)
{
    QFont font(mGlobal);
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.horizontalAdvance(text) <= width)
    {
        font.setPointSize(font.pointSize() + 1);
        fm = QFontMetrics(font);
        if(font.pointSize() > 60)
        {
            break;
        }
    }
#if defined(Q_OS_MACOS)
    return qCeil(1.2 * (font.pointSize() - 1));
#elif defined(Q_OS_WIN)
    return font.pointSize() - 1;
#endif
}
int GlobalFont::getSuitablePointSize(int height)
{
    QFont font(mGlobal);
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.height() <= height)
    {
        font.setPointSize(font.pointSize() + 1);
        fm = QFontMetrics(font);
        if(font.pointSize() > 60)
        {
            break;
        }
    }
#if defined(Q_OS_MACOS)
    return qCeil(1.2 * (font.pointSize() - 1));
#elif defined(Q_OS_WIN)
    return font.pointSize() - 1;
#endif
}
int GlobalFont::getSuitableWidth(QFont font, QString text, int height)
{
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.height() <= height)
    {
        font.setPointSize(font.pointSize() + 1);
        fm = QFontMetrics(font);
        if(font.pointSize() > 60)
        {
            break;
        }
    }
    return fm.horizontalAdvance(text);
}
int GlobalFont::getSuitableWidth(QString text, int height)
{
    QFont font(mGlobal);
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    while(fm.height() <= height)
    {
        font.setPointSize(font.pointSize() + 1);
        fm = QFontMetrics(font);
        if(font.pointSize() > 60)
        {
            break;
        }
    }
    return fm.horizontalAdvance(text);
}
QString GlobalFont::getLongerInPointSize(QFont font, QString text1, QString text2)
{
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    return fm.horizontalAdvance(text1) > fm.horizontalAdvance(text2) ? (text1) : (text2);
}
QString GlobalFont::getLongerInPointSize(QString text1, QString text2)
{
    QFont font(mGlobal);
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    return fm.horizontalAdvance(text1) > fm.horizontalAdvance(text2) ? (text1) : (text2);
}
QString GlobalFont::getLongerInPixelSize(QFont font, QString text1, QString text2)
{
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    return fm.horizontalAdvance(text1) > fm.horizontalAdvance(text2) ? (text1) : (text2);
}
QString GlobalFont::getLongerInPixelSize(QString text1, QString text2)
{
    QFont font(mGlobal);
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    return fm.horizontalAdvance(text1) > fm.horizontalAdvance(text2) ? (text1) : (text2);
}
QString GlobalFont::getShorterInPointSize(QFont font, QString text1, QString text2)
{
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    return fm.horizontalAdvance(text1) > fm.horizontalAdvance(text2) ? (text2) : (text1);
}
QString GlobalFont::getShorterInPointSize(QString text1, QString text2)
{
    QFont font(mGlobal);
    font.setPointSize(5);
    QFontMetrics fm=QFontMetrics(font);
    return fm.horizontalAdvance(text1) > fm.horizontalAdvance(text2) ? (text2) : (text1);
}
QString GlobalFont::getShorterInPixelSize(QFont font, QString text1, QString text2)
{
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    return fm.horizontalAdvance(text1) > fm.horizontalAdvance(text2) ? (text2) : (text1);
}
QString GlobalFont::getShorterInPixelSize(QString text1, QString text2)
{
    QFont font(mGlobal);
    font.setPixelSize(5);
    QFontMetrics fm=QFontMetrics(font);
    return fm.horizontalAdvance(text1) > fm.horizontalAdvance(text2) ? (text2) : (text1);
}
bool GlobalFont::isSuitable(QFont font, QString text, QRect rect)
{
    QFontMetrics fm=QFontMetrics(font);
    if(fm.horizontalAdvance(text) > rect.width() || fm.height() > rect.height())
    {
        return false;
    }
    return true;
}
bool GlobalFont::isSuitable(QFont font, QString text, int width)
{
    QFontMetrics fm=QFontMetrics(font);
    if(fm.horizontalAdvance(text) > width)
    {
        return false;
    }
    return true;
}
bool GlobalFont::isSuitable(QFont font, int height)
{
    QFontMetrics fm=QFontMetrics(font);
    if(fm.height() > height)
    {
        return false;
    }
    return true;
}
bool GlobalFont::isSuitable(QString text, QRect rect)
{
    QFont font(mGlobal);
    QFontMetrics fm=QFontMetrics(font);
    if(fm.horizontalAdvance(text) > rect.width() || fm.height() > rect.height())
    {
        return false;
    }
    return true;
}
bool GlobalFont::isSuitable(QString text, int width)
{
    QFont font(mGlobal);
    QFontMetrics fm=QFontMetrics(font);
    if(fm.horizontalAdvance(text) > width)
    {
        return false;
    }
    return true;
}
bool GlobalFont::isSuitable(int height)
{
    QFont font(mGlobal);
    QFontMetrics fm=QFontMetrics(font);
    if(fm.height() > height)
    {
        return false;
    }
    return true;
}
GlobalFont& GlobalFont::showSupported()
{
    qInfo() << "";
    qInfo() << "";
    qInfo() << "GlobalFont::showSupported // supported list below";
    foreach(const QString &strFamily, QFontDatabase::families())
    {
        qInfo()<<strFamily.toStdString().c_str();
        foreach(const QString &strStyle, QFontDatabase::styles(strFamily))
        {
            QString strSizes;
            foreach(int points, QFontDatabase::smoothSizes(strFamily, strStyle))
            {
                strSizes += QString::number(points) + " ";
            }
            qInfo()<<"\t"<<strStyle.toStdString().c_str()<<"\t"<<strSizes.toStdString().c_str();
        }
    }
    qInfo() << "";
    qInfo() << "";
    return *this;
}


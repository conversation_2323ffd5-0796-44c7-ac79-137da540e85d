{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.19"}, "projectIndex": 0, "source": "."}, {"build": "Source", "hasInstallRule": true, "jsonFile": "directory-Source-Debug-4ba2e80952501163abf7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "Source", "targetIndexes": [0, 1, 2, 3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "TPCC", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 1, "id": "TPCC::@43690dd2fb94c8e45e84", "jsonFile": "target-TPCC-Debug-d26f05800410837e57fe.json", "name": "TPCC", "projectIndex": 0}, {"directoryIndex": 1, "id": "TPCC_autogen::@43690dd2fb94c8e45e84", "jsonFile": "target-TPCC_autogen-Debug-5c0deed933812c74d154.json", "name": "TPCC_autogen", "projectIndex": 0}, {"directoryIndex": 1, "id": "TPCC_autogen_timestamp_deps::@43690dd2fb94c8e45e84", "jsonFile": "target-TPCC_autogen_timestamp_deps-Debug-957a5ee5098b238bfe67.json", "name": "TPCC_autogen_timestamp_deps", "projectIndex": 0}, {"directoryIndex": 1, "id": "TPCC_ui_property_check::@43690dd2fb94c8e45e84", "jsonFile": "target-TPCC_ui_property_check-Debug-6b6ed33c3d6e5f10aa6d.json", "name": "TPCC_ui_property_check", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Documents/TMP/build", "source": "C:/Users/<USER>/Documents/TMP/ToppingProfessionalControlCenter"}, "version": {"major": 2, "minor": 7}}
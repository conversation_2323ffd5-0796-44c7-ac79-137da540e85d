#ifndef FieldHeadBase2_H
#define FieldHeadBase2_H


#include <QRect>
#include <QLabel>
#include <QColor>
#include <QWidget>
#include <QString>
#include <QPainter>
#include <QComboBox>
#include <QPushButton>
#include <QPaintEvent>
#include <QResizeEvent>

#include "batterys1m1.h"
#include "comboboxs1m2.h"


class FieldHeadBase2 : public QWidget
{
    Q_OBJECT
public:
    explicit FieldHeadBase2(QWidget* parent=nullptr);
    ~FieldHeadBase2();
    FieldHeadBase2& setFont(QFont font);
    FieldHeadBase2& setFieldColor(QColor color);
    FieldHeadBase2& setLanguage(QString language="Chinese");
    FieldHeadBase2& setBatteryValue(int value);
    FieldHeadBase2& setBatteryCharging(bool charging=true);
    FieldHeadBase2& modifySampleRateList(QVector<unsigned int> list, unsigned int defaultItem);
    FieldHeadBase2& modifyBufferSizeList(QVector<unsigned int> list, unsigned int defaultItem);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    QFont mFont;
    QString mLanguage="Chinese";
    QColor mColorBG=QColor(31, 31, 31);
    QLabel mLabelLogo;
    QLabel mLabelSampleRate;
    QLabel mLabelBufferSize;
    ComboBoxS1M2 mComboBoxSampleRate;
    ComboBoxS1M2 mComboBoxBufferSize;
    BatteryS1M1 mBattery;
    QPushButton mPushButtonSettings;
    void drawBG(QPainter* painter);
private slots:
    void in_mPushButtonAll_clicked();
    void in_mComboBoxAll_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FieldHeadBase2_H


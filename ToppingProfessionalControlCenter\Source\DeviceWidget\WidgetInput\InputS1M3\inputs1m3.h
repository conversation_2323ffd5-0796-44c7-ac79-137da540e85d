#ifndef INPUTS1M3_H
#define INPUTS1M3_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "inputbase.h"
#include "workspace.h"
#include "appsettings.h"


namespace Ui {
class InputS1M3;
}


class InputS1M3 : public InputBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit InputS1M3(QWidget* parent=nullptr, QString name="");
    ~InputS1M3();
    InputS1M3& setName(QString name);
    InputS1M3& setFont(QFont font);
    InputS1M3& setVolumeMeterLeft(int value);
    InputS1M3& setVolumeMeterLeftClear();
    InputS1M3& setVolumeMeterLeftSlip();
    InputS1M3& setVolumeMeterRight(int value);
    InputS1M3& setVolumeMeterRightClear();
    InputS1M3& setVolumeMeterRightSlip();
    InputS1M3& setGain(float value);
    InputS1M3& setGainLock(bool state=true);
    InputS1M3& setMuteAffectGain(bool state=true);
    InputS1M3& setGainAffectMute(bool state=true);
    InputS1M3& setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    InputS1M3& setGainDefault(float value);
    InputS1M3& setGainWidgetDisable(float value);
    InputS1M3& setChannelNameEditable(bool state=true);
    InputS1M3& setValueGAIN(float value);
    InputS1M3& setValueMUTE(bool state=true);
    InputS1M3& setOverlay(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void setSoloState(bool state) override;
    void setSoloStateLeft(bool state) override;
    void setSoloStateRight(bool state) override;
    void setMuteState(bool state) override;
    void setMuteStateLeft(bool state) override;
    void setMuteStateRight(bool state) override;
    void setSoloClicked(bool state) override;
    void setSoloClickedLeft(bool state) override;
    void setSoloClickedRight(bool state) override;
    void setMuteClicked(bool state) override;
    void setMuteClickedLeft(bool state) override;
    void setMuteClickedRight(bool state) override;
    bool getSoloState() override;
    bool getSoloStateLeft() override;
    bool getSoloStateRight() override;
    bool getMuteState() override;
    bool getMuteStateLeft() override;
    bool getMuteStateRight() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::InputS1M3* ui;
    QTimer mTimer;
    QFont mFont;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetDial_valueChanged(float value);
    void in_widgetPushButtonGroup2_stateChanged(QString button, QString state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // INPUTS1M3_H


/****************************************************************************
** Meta object code from reading C++ file 'batterydrawstrategy.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1/batterydrawstrategy.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'batterydrawstrategy.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN19BatteryDrawStrategyE_t {};
} // unnamed namespace

template <> constexpr inline auto BatteryDrawStrategy::qt_create_metaobjectdata<qt_meta_tag_ZN19BatteryDrawStrategyE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "BatteryDrawStrategy"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<BatteryDrawStrategy, qt_meta_tag_ZN19BatteryDrawStrategyE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject BatteryDrawStrategy::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN19BatteryDrawStrategyE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN19BatteryDrawStrategyE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN19BatteryDrawStrategyE_t>.metaTypes,
    nullptr
} };

void BatteryDrawStrategy::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<BatteryDrawStrategy *>(_o);
    (void)_t;
    (void)_c;
    (void)_id;
    (void)_a;
}

const QMetaObject *BatteryDrawStrategy::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *BatteryDrawStrategy::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN19BatteryDrawStrategyE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int BatteryDrawStrategy::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    return _id;
}
namespace {
struct qt_meta_tag_ZN22ContinuousDrawStrategyE_t {};
} // unnamed namespace

template <> constexpr inline auto ContinuousDrawStrategy::qt_create_metaobjectdata<qt_meta_tag_ZN22ContinuousDrawStrategyE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ContinuousDrawStrategy"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ContinuousDrawStrategy, qt_meta_tag_ZN22ContinuousDrawStrategyE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ContinuousDrawStrategy::staticMetaObject = { {
    QMetaObject::SuperData::link<BatteryDrawStrategy::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ContinuousDrawStrategyE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ContinuousDrawStrategyE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN22ContinuousDrawStrategyE_t>.metaTypes,
    nullptr
} };

void ContinuousDrawStrategy::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ContinuousDrawStrategy *>(_o);
    (void)_t;
    (void)_c;
    (void)_id;
    (void)_a;
}

const QMetaObject *ContinuousDrawStrategy::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ContinuousDrawStrategy::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ContinuousDrawStrategyE_t>.strings))
        return static_cast<void*>(this);
    return BatteryDrawStrategy::qt_metacast(_clname);
}

int ContinuousDrawStrategy::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = BatteryDrawStrategy::qt_metacall(_c, _id, _a);
    return _id;
}
namespace {
struct qt_meta_tag_ZN22SegmentedBlinkStrategyE_t {};
} // unnamed namespace

template <> constexpr inline auto SegmentedBlinkStrategy::qt_create_metaobjectdata<qt_meta_tag_ZN22SegmentedBlinkStrategyE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "SegmentedBlinkStrategy"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<SegmentedBlinkStrategy, qt_meta_tag_ZN22SegmentedBlinkStrategyE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject SegmentedBlinkStrategy::staticMetaObject = { {
    QMetaObject::SuperData::link<BatteryDrawStrategy::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22SegmentedBlinkStrategyE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22SegmentedBlinkStrategyE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN22SegmentedBlinkStrategyE_t>.metaTypes,
    nullptr
} };

void SegmentedBlinkStrategy::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<SegmentedBlinkStrategy *>(_o);
    (void)_t;
    (void)_c;
    (void)_id;
    (void)_a;
}

const QMetaObject *SegmentedBlinkStrategy::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SegmentedBlinkStrategy::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22SegmentedBlinkStrategyE_t>.strings))
        return static_cast<void*>(this);
    return BatteryDrawStrategy::qt_metacast(_clname);
}

int SegmentedBlinkStrategy::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = BatteryDrawStrategy::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP

#ifndef DEVICECONNECTOR_H
#define DEVICECONNECTOR_H


#include <QTimer>

#include "framelesswindow.h"
#include "mainwindow_base.h"
#include "deviceconnectorviewbase.h"


class DeviceConnector : public FramelessWindow
{
    Q_OBJECT
public:
    DeviceConnector();
    ~DeviceConnector();
    void showSeries(QString series, QHash<QString, QPair<QString, QString>> deviceList);
protected:
    void done(int) override;
private:
    QHash<QString, std::function<DeviceConnectorViewBase*(DeviceConnector*)>> mConnectViewCreators;
    QHash<QString, std::function<MainWindow_Base*(DeviceConnector*)>> mMainWindowCreators;
    DeviceConnectorViewBase* mConnectView=nullptr;
    MainWindow_Base* mMainWindow=nullptr;
    QTimer mTimerConnector;
    QVector<QString> mSeriesDeviceList;
    QString mDFUDevice="";
    QSize mBaseSize;
private slots:
    void in_mTimerConnector_timeout();
    void in_widgetConnectView_attributeChanged(QString objectName, QString attribute, QString value);
    void in_widgetMainWindow_attributeChanged(QString objectName, QString attribute, QString value);
    void in_Updater_UpdatingSoftware(QString key, QString value);
    void in_Updater_UpdatingFirmware(QString key, QString value);
};


#endif // DEVICECONNECTOR_H


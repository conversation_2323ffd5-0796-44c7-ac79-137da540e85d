#ifndef PUSHBUTTONS1M4_H
#define PUSHBUTTONS1M4_H


#include <QLabel>
#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M4 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M4(QWidget* parent=nullptr);
    ~PushButtonS1M4();
    enum ButtonID
    {
        buttonIN1=0,
        buttonIN2,
        buttonIN12,
        buttonOFF,
        buttonNC1,
        buttonNC2,
        buttonNC3
    };
    PushButtonS1M4& setFont(QFont font);
    PushButtonS1M4& setPushButtonStateIN1(bool state);
    PushButtonS1M4& setPushButtonStateIN2(bool state);
    PushButtonS1M4& setPushButtonStateIN12(bool state);
    PushButtonS1M4& setPushButtonStateOFF(bool state);
    PushButtonS1M4& setPushButtonStateNC1(bool state);
    PushButtonS1M4& setPushButtonStateNC2(bool state);
    PushButtonS1M4& setPushButtonStateNC3(bool state);
    bool getPushButtonStateIN1();
    bool getPushButtonStateIN2();
    bool getPushButtonStateIN12();
    bool getPushButtonStateOFF();
    bool getPushButtonStateNC1();
    bool getPushButtonStateNC2();
    bool getPushButtonStateNC3();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateIN1=false;
    bool mPushButtonStateIN2=false;
    bool mPushButtonStateIN12=false;
    bool mPushButtonStateOFF=false;
    bool mPushButtonStateNC1=false;
    bool mPushButtonStateNC2=false;
    bool mPushButtonStateNC3=false;
    QPushButton mPushButtonIN1;
    QPushButton mPushButtonIN2;
    QPushButton mPushButtonIN12;
    QPushButton mPushButtonOFF;
    QPushButton mPushButtonNC1;
    QPushButton mPushButtonNC2;
    QPushButton mPushButtonNC3;
    QLabel mLabelIN1;
    QLabel mLabelIN2;
    QLabel mLabelIN12;
private slots:
    void in_mPushButtonIN1_clicked();
    void in_mPushButtonIN2_clicked();
    void in_mPushButtonIN12_clicked();
    void in_mPushButtonOFF_clicked();
    void in_mPushButtonNC1_clicked();
    void in_mPushButtonNC2_clicked();
    void in_mPushButtonNC3_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M4_H


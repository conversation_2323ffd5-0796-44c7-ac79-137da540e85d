/********************************************************************************
** Form generated from reading UI file 'messageboxwidget2.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MESSAGEBOXWIDGET2_H
#define UI_MESSAGEBOXWIDGET2_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MessageBoxWidget2
{
public:
    QGridLayout *gridLayout;
    QSpacerItem *verticalSpacer_7;
    QLabel *Label1;
    QSpacerItem *verticalSpacer_8;
    QLabel *Label2;
    QSpacerItem *verticalSpacer_9;
    QGridLayout *gridLayout_3;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *PushButton1;
    QSpacerItem *horizontalSpacer_5;
    QPushButton *PushButton2;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *verticalSpacer_2;

    void setupUi(QWidget *MessageBoxWidget2)
    {
        if (MessageBoxWidget2->objectName().isEmpty())
            MessageBoxWidget2->setObjectName("MessageBoxWidget2");
        MessageBoxWidget2->resize(300, 200);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(MessageBoxWidget2->sizePolicy().hasHeightForWidth());
        MessageBoxWidget2->setSizePolicy(sizePolicy);
        gridLayout = new QGridLayout(MessageBoxWidget2);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_7 = new QSpacerItem(20, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_7, 0, 0, 1, 1);

        Label1 = new QLabel(MessageBoxWidget2);
        Label1->setObjectName("Label1");
        sizePolicy.setHeightForWidth(Label1->sizePolicy().hasHeightForWidth());
        Label1->setSizePolicy(sizePolicy);
        Label1->setMinimumSize(QSize(1, 1));
        Label1->setLineWidth(0);
        Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout->addWidget(Label1, 1, 0, 1, 1);

        verticalSpacer_8 = new QSpacerItem(20, 30, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_8, 2, 0, 1, 1);

        Label2 = new QLabel(MessageBoxWidget2);
        Label2->setObjectName("Label2");
        sizePolicy.setHeightForWidth(Label2->sizePolicy().hasHeightForWidth());
        Label2->setSizePolicy(sizePolicy);
        Label2->setMinimumSize(QSize(1, 1));
        Label2->setLineWidth(0);
        Label2->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout->addWidget(Label2, 3, 0, 1, 1);

        verticalSpacer_9 = new QSpacerItem(20, 29, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_9, 4, 0, 1, 1);

        gridLayout_3 = new QGridLayout();
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName("gridLayout_3");
        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_3, 0, 0, 1, 1);

        PushButton1 = new QPushButton(MessageBoxWidget2);
        PushButton1->setObjectName("PushButton1");
        sizePolicy.setHeightForWidth(PushButton1->sizePolicy().hasHeightForWidth());
        PushButton1->setSizePolicy(sizePolicy);
        PushButton1->setMinimumSize(QSize(1, 1));

        gridLayout_3->addWidget(PushButton1, 0, 1, 1, 1);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_5, 0, 2, 1, 1);

        PushButton2 = new QPushButton(MessageBoxWidget2);
        PushButton2->setObjectName("PushButton2");
        sizePolicy.setHeightForWidth(PushButton2->sizePolicy().hasHeightForWidth());
        PushButton2->setSizePolicy(sizePolicy);
        PushButton2->setMinimumSize(QSize(1, 1));

        gridLayout_3->addWidget(PushButton2, 0, 3, 1, 1);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_4, 0, 4, 1, 1);

        gridLayout_3->setColumnStretch(0, 100);
        gridLayout_3->setColumnStretch(1, 80);
        gridLayout_3->setColumnStretch(2, 10);
        gridLayout_3->setColumnStretch(3, 80);
        gridLayout_3->setColumnStretch(4, 100);

        gridLayout->addLayout(gridLayout_3, 5, 0, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 30, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_2, 6, 0, 1, 1);

        gridLayout->setRowStretch(0, 60);
        gridLayout->setRowStretch(1, 20);
        gridLayout->setRowStretch(2, 4);
        gridLayout->setRowStretch(3, 20);
        gridLayout->setRowStretch(4, 20);
        gridLayout->setRowStretch(5, 30);
        gridLayout->setRowStretch(6, 97);

        retranslateUi(MessageBoxWidget2);

        QMetaObject::connectSlotsByName(MessageBoxWidget2);
    } // setupUi

    void retranslateUi(QWidget *MessageBoxWidget2)
    {
        MessageBoxWidget2->setWindowTitle(QCoreApplication::translate("MessageBoxWidget2", "Form", nullptr));
        Label1->setText(QString());
        Label2->setText(QString());
        PushButton1->setText(QString());
        PushButton2->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class MessageBoxWidget2: public Ui_MessageBoxWidget2 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MESSAGEBOXWIDGET2_H

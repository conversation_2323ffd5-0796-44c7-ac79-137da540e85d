/************************************************************************
 *
 *  Module:       TbStringUtils.cpp
 *  Description:  generic utilities for char and string handling
 *
 *  Runtime Env.: any
 *  Author(s):    <PERSON><PERSON>, Frank <PERSON>
 *  Company:      Thesycon GmbH, Ilmenau
 ************************************************************************/

#include "libwn_min_global.h"

// module is empty if .h file was not included (category turned off)
#ifdef __TbStringUtils_h__


#ifdef LIBTB_NAMESPACE
namespace LIBTB_NAMESPACE {
#endif


char
TbToLowerCaseChar(
    char c
    )
{
    return ( TbIsUpperCaseChar(c) ? ((c - 'A') + 'a') : c );
}

wchar_t
TbToLowerCaseChar(
    wchar_t c
    )
{
    return ( TbIsUpperCaseChar(c) ? ((c - L'A') + L'a') : c );
}



char
TbToUpperCaseChar(
    char c
    )
{
    return ( TbIsLowerCaseChar(c) ? ((c - 'a') + 'A') : c );
}

wchar_t
TbToUpperCaseChar(
    wchar_t c
    )
{
    return ( TbIsLowerCaseChar(c) ? ((c - L'a') + L'A') : c );
}





// template that generates implementations
template <typename charT>
inline
unsigned int
TbStringLenT(
    const charT* str
    )
{
    unsigned int n = 0;
    while ( *str++ ) n++;
    return n;
}

unsigned int
TbStringLen(const char* str)  { return TbStringLenT<char>(str); }

unsigned int
TbStringLen(const wchar_t* str) { return TbStringLenT<wchar_t>(str); }




// template that generates implementations
template <typename charTD, typename charTS>
inline
charTD*
TbStringLCopyT(
    charTD* destination,
    charTD* destinationLimit,
    const charTS* source
    )
{
    while ( (*source != 0) && ((destination+1) < destinationLimit) ) {
        *destination = TbCastCharacter(*destination, *source);
        destination++;
        source++;
    }
    *destination = 0;
    return destination;
}

char*
TbStringLCopy(
    char* destination,
    char* destinationLimit,
    const char* source
    )
{
    TBASSERT(destinationLimit>destination);
    return TbStringLCopyT<char,char>(destination, destinationLimit, source);
}

wchar_t*
TbStringLCopy(
    wchar_t* destination,
    wchar_t* destinationLimit,
    const wchar_t* source
    )
{
    TBASSERT(destinationLimit>destination);
    return TbStringLCopyT<wchar_t,wchar_t>(destination, destinationLimit, source);
}

char*
TbStringLCopy(
    char* destination,
    char* destinationLimit,
    const wchar_t* source
    )
{
    TBASSERT(destinationLimit>destination);
    return TbStringLCopyT<char,wchar_t>(destination, destinationLimit, source);
}

wchar_t*
TbStringLCopy(
    wchar_t* destination,
    wchar_t* destinationLimit,
    const char* source
    )
{
    TBASSERT(destinationLimit>destination);
    return TbStringLCopyT<wchar_t,char>(destination, destinationLimit, source);
}




// template that generates implementations
template <typename charTD, typename charTS>
inline
unsigned int
TbStringNCopyT(
    charTD* destination,
    const charTS* source,
    unsigned int maxChars
    )
{
    unsigned int n = 0;
    while ( (*source) && ((n+1)<maxChars) ) {
        *destination = TbCastCharacter(*destination, *source);
        destination++;
        source++;
        n++;
    }
    *destination = 0;
    return n;
}

unsigned int
TbStringNCopy(
    char* destination,
    const char* source,
    unsigned int maxChars
    )
{
    TBASSERT(maxChars>0);
    return TbStringNCopyT<char,char>(destination, source, maxChars);
}

unsigned int
TbStringNCopy(
    wchar_t* destination,
    const wchar_t* source,
    unsigned int maxChars
    )
{
    TBASSERT(maxChars>0);
    return TbStringNCopyT<wchar_t,wchar_t>(destination, source, maxChars);
}

unsigned int
TbStringNCopy(
    char* destination,
    const wchar_t* source,
    unsigned int maxChars
    )
{
    TBASSERT(maxChars>0);
    return TbStringNCopyT<char,wchar_t>(destination, source, maxChars);
}

unsigned int
TbStringNCopy(
    wchar_t* destination,
    const char* source,
    unsigned int maxChars
    )
{
    TBASSERT(maxChars>0);
    return TbStringNCopyT<wchar_t,char>(destination, source, maxChars);
}



// template that generates implementations
template <typename charTD, typename charTS>
inline
charTD*
TbStringLCatT(
    charTD* destination,
    charTD* destinationLimit,
    const charTS* source
    )
{
    while ( *destination ) destination++;

    while ( (*source != 0) && ((destination+1) < destinationLimit) ) {
        *destination = TbCastCharacter(*destination, *source);
        destination++;
        source++;
    }
    *destination = 0;
    return destination;
}

char*
TbStringLCat(
    char* destination,
    char* destinationLimit,
    const char* source
    )
{
    TBASSERT(destinationLimit>destination);
    return TbStringLCatT<char,char>(destination, destinationLimit, source);
}

wchar_t*
TbStringLCat(
    wchar_t* destination,
    wchar_t* destinationLimit,
    const wchar_t* source
    )
{
    TBASSERT(destinationLimit>destination);
    return TbStringLCatT<wchar_t,wchar_t>(destination, destinationLimit, source);
}

char*
TbStringLCat(
    char* destination,
    char* destinationLimit,
    const wchar_t* source
    )
{
    TBASSERT(destinationLimit>destination);
    return TbStringLCatT<char,wchar_t>(destination, destinationLimit, source);
}

wchar_t*
TbStringLCat(
    wchar_t* destination,
    wchar_t* destinationLimit,
    const char* source
    )
{
    TBASSERT(destinationLimit>destination);
    return TbStringLCatT<wchar_t,char>(destination, destinationLimit, source);
}




// template that generates implementations
template <typename charTD, typename charTS>
inline
unsigned int
TbStringNCatT(
    charTD* destination,
    const charTS* source,
    unsigned int maxChars
    )
{
    unsigned int n = TbStringLen(destination);
    destination += n;

    while ( (*source) && ((n+1)<maxChars) ) {
        *destination = TbCastCharacter(*destination, *source);
        destination++;
        source++;
        n++;
    }
    *destination = 0;
    return n;
}

unsigned int
TbStringNCat(
    char* destination,
    const char* source,
    unsigned int maxChars
    )
{
    TBASSERT(maxChars>0);
    return TbStringNCatT<char,char>(destination, source, maxChars);
}

unsigned int
TbStringNCat(
    wchar_t* destination,
    const wchar_t* source,
    unsigned int maxChars
    )
{
    TBASSERT(maxChars>0);
    return TbStringNCatT<wchar_t,wchar_t>(destination, source, maxChars);
}

unsigned int
TbStringNCat(
    char* destination,
    const wchar_t* source,
    unsigned int maxChars
    )
{
    TBASSERT(maxChars>0);
    return TbStringNCatT<char,wchar_t>(destination, source, maxChars);
}

unsigned int
TbStringNCat(
    wchar_t* destination,
    const char* source,
    unsigned int maxChars
    )
{
    TBASSERT(maxChars>0);
    return TbStringNCatT<wchar_t,char>(destination, source, maxChars);
}





// template that generates implementations
template <typename charT>
inline
bool
TbStringEqualT(
    const charT* s1,
    const charT* s2
    )
{
    for (;;) {
        if ( 0 == *s1 && 0 == *s2 ) {
            return true;
        }
        if ( *s1 != *s2 ) {
            break;
        }
        s1++;
        s2++;
    }
    return false;
}

bool
TbStringEqual(
    const char* s1,
    const char* s2
    )
{
    return TbStringEqualT<char>(s1, s2);
}

bool
TbStringEqual(
    const wchar_t* s1,
    const wchar_t* s2
    )
{
    return TbStringEqualT<wchar_t>(s1, s2);
}




// template that generates implementations
template <typename charT>
inline
bool
TbStringEqualIgnoreCaseT(
    const charT* s1,
    const charT* s2
    )
{
    for (;;) {
        if ( 0 == *s1 && 0 == *s2 ) {
            return true;
        }
        if ( TbToUpperCaseChar(*s1) != TbToUpperCaseChar(*s2) ) {
            break;
        }
        s1++;
        s2++;
    }
    return false;
}

bool
TbStringEqualIgnoreCase(
    const char* s1,
    const char* s2
    )
{
    return TbStringEqualIgnoreCaseT<char>(s1, s2);
}

bool
TbStringEqualIgnoreCase(
    const wchar_t* s1,
    const wchar_t* s2
    )
{
    return TbStringEqualIgnoreCaseT<wchar_t>(s1, s2);
}




// template that generates implementations
template <typename charT>
inline
const charT*
TbFindCharInStringT(
    const charT* str,
    charT c
    )
{
    while ( *str ) {
        if ( *str == c ) {
            return str;
        }
        str++;
    }
    return NULL;
}

const char*
TbFindCharInString(
    const char* str,
    char c
    )
{
    return TbFindCharInStringT<char>(str, c);
}

const wchar_t*
TbFindCharInString(
    const wchar_t* str,
    wchar_t c
    )
{
    return TbFindCharInStringT<wchar_t>(str, c);
}




// template that generates implementations
template <typename charT>
inline
unsigned int
TbReplaceCharsInStringT(
    charT* destination,
    const charT* charSet,
    charT newChar
    )
{
    unsigned int n = 0;
    charT c;
    for ( ; 0 != (c = *destination); destination++ ) {
        for ( const charT* p = charSet;  0 != *p;  p++ ) {
            if ( *p == c ) {
                *destination = newChar;
                n++;
            }
        }
    }
    return n;
}

unsigned int
TbReplaceCharsInString(
    char* destination,
    const char* charSet,
    char newChar
    )
{
    return TbReplaceCharsInStringT<char>(destination, charSet, newChar);
}

unsigned int
TbReplaceCharsInString(
    wchar_t* destination,
    const wchar_t* charSet,
    wchar_t newChar
    )
{
    return TbReplaceCharsInStringT<wchar_t>(destination, charSet, newChar);
}



// template that generates implementations
template <typename charT>
inline
unsigned int
TbReplaceCharInStringT(
    charT* destination,
    charT oldChar,
    charT newChar
    )
{
    charT s[2];
    s[0] = oldChar;
    s[1] = 0;
    return TbReplaceCharsInString(destination, s, newChar);
}

unsigned int
TbReplaceCharInString(
    char* destination,
    char oldChar,
    char newChar
    )
{
    return TbReplaceCharInStringT<char>(destination, oldChar, newChar);
}

unsigned int
TbReplaceCharInString(
    wchar_t* destination,
    wchar_t oldChar,
    wchar_t newChar
    )
{
    return TbReplaceCharInStringT<wchar_t>(destination, oldChar, newChar);
}




// template that generates implementations
template <typename charT>
inline
bool
TbStringTrimBeginT(
    charT* destination
    )
{
    charT* p = destination;

    while ( TbIsWhiteSpaceChar(*p) ) p++;

    if ( p != destination ) {
        while ( *p != 0 ) {
            *destination++ = *p++;
        }
        *destination = 0;
        return true;
    }
    return false;
}

bool
TbStringTrimBegin(
    char* destination
    )
{
    return TbStringTrimBeginT<char>(destination);
}

bool
TbStringTrimBegin(
    wchar_t* destination
    )
{
    return TbStringTrimBeginT<wchar_t>(destination);
}



// template that generates implementations
template <typename charT>
inline
bool
TbStringTrimEndT(
    charT* destination
    )
{
    bool modified = false;
    charT* p = destination + TbStringLen(destination) - 1;
    while ( p >= destination && TbIsWhiteSpaceChar(*p) ) {
        *p-- = 0;
        modified = true;
    }
    return modified;
}

bool
TbStringTrimEnd(
    char* destination
    )
{
    return TbStringTrimEndT<char>(destination);
}

bool
TbStringTrimEnd(
    wchar_t* destination
    )
{
    return TbStringTrimEndT<wchar_t>(destination);
}



// template that generates implementations
template <typename charT>
inline
charT*
TbStringToUpperCaseT(
    charT* destination
    )
{
    charT* p = destination;
    while ( *p != 0 ) {
        *p = TbToUpperCaseChar(*p);
        p++;
    }
    return destination;
}

char*
TbStringToUpperCase(
    char* destination
    )
{
    return TbStringToUpperCaseT<char>(destination);
}

wchar_t*
TbStringToUpperCase(
    wchar_t* destination
    )
{
    return TbStringToUpperCaseT<wchar_t>(destination);
}


// template that generates implementations
template <typename charT>
inline
charT*
TbStringToLowerCaseT(
    charT* destination
    )
{
    charT* p = destination;
    while ( *p != 0 ) {
        *p = TbToLowerCaseChar(*p);
        p++;
    }
    return destination;
}

char*
TbStringToLowerCase(
    char* destination
    )
{
    return TbStringToLowerCaseT<char>(destination);
}

wchar_t*
TbStringToLowerCase(
    wchar_t* destination
    )
{
    return TbStringToLowerCaseT<wchar_t>(destination);
}




// template that generates implementations
template <typename charT>
inline
bool
TbStringRemoveQuotesT(
    charT* destination,
    charT quoteChar
    )
{
    unsigned int len = TbStringLen(destination);
    if ( len >= 2 ) {
        if ( destination[0] == quoteChar && destination[len-1] == quoteChar ) {
            charT* p = destination + 1;
            --len;
            while ( 0 != --len ) {
                *destination++ = *p++;
            }
            *destination = 0;
            return true;
        }
    }
    return false;
}

bool
TbStringRemoveQuotes(
    char* destination,
    char quoteChar
    )
{
    return TbStringRemoveQuotesT<char>(destination, quoteChar);
}

bool
TbStringRemoveQuotes(
    wchar_t* destination,
    wchar_t quoteChar
    )
{
    return TbStringRemoveQuotesT<wchar_t>(destination, quoteChar);
}



char
TbDigitToHexChar(
    int digit,  // allowed range 0..15
    bool upperCaseLetters
    )
{
    digit &= 0xF;
    return (char)( (digit <= 9) ? ('0' + digit) : ((upperCaseLetters ? 'A' : 'a') + digit - 10) );  //lint !e734
}


int
TbHexCharToDigit(
    int c
    )
{
    return (
        (c>='0' && c<='9') ? (c - '0') :
        (c>='A' && c<='F') ? ((c - 'A') + 10) :
        (c>='a' && c<='f') ? ((c - 'a') + 10) :  -1
        );
}


#ifdef LIBTB_NAMESPACE
}
#endif


#endif

/******************************** EOF ***********************************/

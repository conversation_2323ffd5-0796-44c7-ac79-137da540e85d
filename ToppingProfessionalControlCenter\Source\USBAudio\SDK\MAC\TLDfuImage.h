/************************************************************************

    Description:
        TLDFU image
        Encapsulation of a TLDfuImageHandle

    Author(s):
        <PERSON><PERSON><PERSON>berhardt
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __TLDfuImage_h__
#define __TLDfuImage_h__


class TLDfuImage
{
public:

    //
    // Constructor
    //
    TLDfuImage()
            {
                // empty
            }


    //
    // Destructor
    //
    ~TLDfuImage()
            {
                // make sure current handle is unloaded (ignore possible error)
                UnloadFirmwareImage();
            }

    // disable copy and move operations
    TLDfuImage(const TLDfuImage&) = delete;
    TLDfuImage& operator=(const TLDfuImage&) = delete;
    TLDfuImage(TLDfuImage&&) = delete;
    TLDfuImage& operator=(TLDfuImage&&) = delete;

/////////////////////////////////////////
// Interface
//
public:

    bool
    IsValid() const
            {
                return ( mHandle != TLDFU_INVALID_HANDLE );
            }

    void
    AttachHandle(
        TLDfuImageHandle handle
        )
            {
                // make sure current handle is unloaded (ignore possible error)
                UnloadFirmwareImage();

                mHandle = handle;
            }


    TLDfuImageHandle
    DetachHandle()
            {
               TLDfuImageHandle h = mHandle;
               mHandle = TLDFU_INVALID_HANDLE;
               return h;
            }


    TLSTATUS
    UnloadFirmwareImage()
            {
                TLSTATUS st = TLSTATUS_SUCCESS;
                
                if ( TLDFU_INVALID_HANDLE != mHandle ) {
                    st = TLDFU_UnloadFirmwareImage(mHandle);
                    mHandle = TLDFU_INVALID_HANDLE;
                }

                return st;
            }


    TLSTATUS
    LoadFirmwareImageFromFile(
        const T_UNICHAR* filePathAndName,
        TLDfuImageType imageType,
        unsigned int flags = 0
        )
            {
                // make sure current handle is unloaded (ignore possible error)
                UnloadFirmwareImage();

                return TLDFU_LoadFirmwareImageFromFile(
                                filePathAndName,
                                imageType,
                                &mHandle,
                                flags
                                );
            }


    TLSTATUS
    LoadFirmwareFromBuffer(
        const void* imageData,
        unsigned long long imageDataSize,
        TLDfuImageType imageType,
        unsigned int flags = 0
        )
            {
                // make sure current handle is unloaded (ignore possible error)
                UnloadFirmwareImage();
            
                return TLDFU_LoadFirmwareFromBuffer(
                                imageData,
                                imageDataSize,
                                imageType,
                                &mHandle,
                                flags
                                );
            }


    TLSTATUS
    StoreFirmwareInBuffer(
        void* buffer,
        unsigned long long bufferSize,
        unsigned long long* bytesCopied,    // optional, can be nullptr
        unsigned int flags = 0
        )
            {
                return TLDFU_StoreFirmwareInBuffer(
                                mHandle,
                                buffer,
                                bufferSize,
                                bytesCopied,
                                flags
                                );
            }


    TLSTATUS
    GetImagePropertyUint(
        TLDfuImageProperty propertyId,
        unsigned int& propertyValue
        )
            {
                return TLDFU_GetImagePropertyUint(
                                mHandle,
                                propertyId,
                                &propertyValue
                                );
            }

    
    TLSTATUS
    GetImagePropertyUint64(
        TLDfuImageProperty propertyId,
        unsigned long long& propertyValue
        )
            {
                return TLDFU_GetImagePropertyUint64(
                                mHandle,
                                propertyId,
                                &propertyValue
                                );
            }


    TLSTATUS
    GetImagePropertyString(
        TLDfuImageProperty propertyId,
        T_UNICHAR* stringBuffer,
        unsigned int stringBufferMaxItems
        )
            {
                return TLDFU_GetImagePropertyString(
                                mHandle,
                                propertyId,
                                stringBuffer,
                                stringBufferMaxItems
                                );
            }


    //
    // Access to the encapsulated handle.
    //
    TLDfuImageHandle
    Handle() const
            {
                return mHandle;
            }


/////////////////////////////////////////
// Implementation
//
protected:



////////////////////////////////////////
// Data
//
protected:

    // handle of the image
    TLDfuImageHandle mHandle {TLDFU_INVALID_HANDLE};

};


#endif 

/*** EOF ***/

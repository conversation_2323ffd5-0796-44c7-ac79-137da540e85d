/************************************************************************
 *
 *  Module:       WnTraceLogContext.cpp
 *
 *  Description:  Trace and log context
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    U<PERSON>,  Udo.E<PERSON><EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"
#include <stdarg.h>

// Module is empty if .h file was not included (category turned off).
#ifdef __WnTraceLogContext_h__

#include "WnStringUtils_impl.h"

#define INITGUID
#include <evntrace.h>
#undef INITGUID

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif


// registry location where the settings are stored
//#define TRACELOG_REGISTRY_ROOT    HKEY_LOCAL_MACHINE
#define TRACELOG_REGISTRY_ROOT    HKEY_CURRENT_USER
    // note: we use HKEY_CURRENT_USER because we want to write to the key
    // which is not possible under HKEY_LOCAL_MACHINE on W7 with UAC

const TCHAR sTraceLogDefaultRegistryPath[] = TEXT("Software\\Thesycon\\Debug");


#define TEMP_BUFFER_LENGTH  1024
#define OUTPUT_STRING_BUFFER_LENGTH WnTraceLogFile::cTranslateTempBufferSize

#define WNTRACELOG_MAGIC_WALUE    TB_FOUR_CHAR_DWORD('B','E','E','F')


#ifndef __FUNCTION__
#pragma message("WARNING: Macro __FUNCTION__ is not supported by the compiler.")
#endif



// ctor
WnTraceLogContext::WnTraceLogContext(const TCHAR* contextName/* = NULL*/)
{
    // default trace mask
    mMask = 0
        |TRCBITMASK(TRCERR)
        |TRCBITMASK(TRCWRN)
        |TRCBITMASK(TRCINF)
        ;

    TB_ZERO_ARRAY(mPrefix);
    TB_ZERO_ARRAY(mTraceFilePathAndName);
    mCol = 0;
    mOutputDebugStringEnabled = false;
    mTraceFileEnable = false;
    mTraceFileAppend = false;
    mTraceFileInsertPid = false;

#ifdef UNDER_CE

#else
    // enable OutputDebugString by default on Windows
    mOutputDebugStringEnabled = true;
#endif

    TbStringNCopyToArray(mContextName, contextName);

    // default settings:
    mDatePrefixEnable = false;
    mTimePrefixEnable = false;
    mProcessIdPrefixEnable = false;
    mThreadIdPrefixEnable = true;
    mBitTagPrefixEnable = true;


    // init event descriptor, same content for all messages
    TbZeroObject(mEventDescripor);
    mEventDescripor.Id = 1;
    mEventDescripor.Version = 1;
    mEventDescripor.Level = TRACE_LEVEL_INFORMATION;
    mEventDescripor.Keyword = 0;

    // default description for each trace bit
    InitTraceBitInfo();

    //
    // constructor is done
    // Mark this instance valid.
    // Because we are creating a global instance of WnTraceLogContext there is a problem if traces
    // are printed from constructors/destructors of other global instances.
    // Using the magic value trick this class rejects print calls before construction and after destruction.
    //
    mMagic = WNTRACELOG_MAGIC_WALUE;
}


// dtor
WnTraceLogContext::~WnTraceLogContext()
{
    UnregisterEtwTracing();
    // mark instance as invalid
    mMagic = 0;
}


void
WnTraceLogContext::InitTraceBitInfo()
{
    // default description for each trace bit
    TB_ZERO_ARRAY(mTraceBitTable);
    for ( int i = 0; i<TB_ARRAY_ELEMENTS(mTraceBitTable); i++ ) {
        mTraceBitTable[i].traceBitTag = "";
        mTraceBitTable[i].traceBitDescription = "";
    }
    mTraceBitTable[TRCERR].traceBitTag = "ERROR";
    mTraceBitTable[TRCERR].traceBitDescription = "Error";

    mTraceBitTable[TRCWRN].traceBitTag = "WARNING";
    mTraceBitTable[TRCWRN].traceBitDescription = "Warning";

    mTraceBitTable[TRCINF].traceBitTag = "INFO";
    mTraceBitTable[TRCINF].traceBitDescription = "Info";

    mTraceBitTable[TRCEXTINF].traceBitTag = "INFO";
    mTraceBitTable[TRCEXTINF].traceBitDescription = "Extended Info";
}


void
WnTraceLogContext::SetTraceBitInfo(
    const WnTraceInfoEntry* infoTable
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    // init with defaults
    InitTraceBitInfo();

    if ( NULL != infoTable ) {

        const WnTraceInfoEntry* e = infoTable;
        // end of table marker is traceBitNb < 0
        while ( e->traceBitNb >= 0 && e->traceBitTag != NULL && e->traceBitDescription != NULL  ) {
            // ignore invalid traceBitNb
            if ( e->traceBitNb < TB_ARRAY_ELEMENTS(mTraceBitTable) ) {
                TraceBitEntry& tbe = mTraceBitTable[e->traceBitNb];
                tbe.traceBitTag = e->traceBitTag;
                tbe.traceBitDescription = e->traceBitDescription;
            }
            ++e;
        }//while

    }

} //SetTraceBitInfo



void
WnTraceLogContext::SetContextName(
    const TCHAR* contextName
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    TbStringNCopyToArray(mContextName, contextName);
}


void
WnTraceLogContext::SetTraceFile(
    const TCHAR* traceFilePathAndName
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    WnAutoCriticalSection lock(mLock);

    TB_ZERO_ARRAY(mTraceFilePathAndName);
    if (NULL != traceFilePathAndName) {
        TbStringNCopyToArray(mTraceFilePathAndName, traceFilePathAndName);
    }
}


WNERR
WnTraceLogContext::SetTraceFileEnable(bool enable)
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return ERROR_FUNCTION_FAILED;
    }

    WnAutoCriticalSection lock(mLock);

    mTraceFile.CloseFile();

    if ( enable ) {
        // enable

        if (0 == TbStringLen(mTraceFilePathAndName)) {
            WnDbgOutput(__FUNCTION__": ERROR: no mTraceFilePathAndName set\n" );
            return ERROR_INVALID_PARAMETER;
        }

        TCHAR tmpStr[MAX_PATH];
        tmpStr[0] = 0;

#ifdef UNDER_CE
        TbStringNCopyToArray(tmpStr, mTraceFilePathAndName);
#else
        // expand environment variables
        ::ExpandEnvironmentStrings(mTraceFilePathAndName, tmpStr, TB_ARRAY_ELEMENTS(tmpStr));
#endif

        // trim string
        TbStringTrimBegin(tmpStr);
        TbStringTrimEnd(tmpStr);

        // check for empty string
        if ( 0 == TbStringLen(tmpStr) ) {
            WnDbgOutput(__FUNCTION__": ERROR: empty file path\n" );
            return ERROR_INVALID_PARAMETER;
        }

        // open trace file
        if (!mTraceFile.OpenFile(tmpStr, mTraceFileAppend, mTraceFileInsertPid)) {
            // failed
            WnDbgOutput(__FUNCTION__": ERROR: failed to open log file\n" );
            return ERROR_OPEN_FAILED;
        }

        mTraceFileEnable = true;

    } else {
        // disable
        mTraceFileEnable = false;
    }

    return ERROR_SUCCESS;
}


#if LIBWN_CAT_TraceLogFileBuffered

bool
WnTraceLogContext::StartThread(unsigned int bufferSizeInKB)
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return false;
    }

    return mTraceFile.StartThread(bufferSizeInKB);
}


void
WnTraceLogContext::StopThread()
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    mTraceFile.StopThread();
}

#endif //#if LIBWN_CAT_TraceLogFileBuffered



void
WnTraceLogContext::SetLogFileSizeLimit(
    unsigned int maxSizeInBytes
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    WnAutoCriticalSection lock(mLock);

    mTraceFile.SetLogFileSizeLimit(maxSizeInBytes);
}


void
WnTraceLogContext::SetFlushAfterEachWrite(
    bool enable
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    WnAutoCriticalSection lock(mLock);

    mTraceFile.SetFlushAfterEachWrite(enable);
}



// called with no lock held
template<typename T>
void
WnTraceLogContext::OutputString(
    const T* str,   // points to null-terminated string
    int bitnb
    )
{
    WnAutoCriticalSection lock(mLock);

    char buffer[OUTPUT_STRING_BUFFER_LENGTH];
    char* p = buffer;
    // reserve 1 character for terminating 0
    char* limit = buffer + OUTPUT_STRING_BUFFER_LENGTH - 1;

    while (*str != 0) {
        if (p >= limit) {
            // buffer full
            // add terminating 0
            *p = 0;
            WriteString(buffer);
            p = buffer;
        }

        if (0 == mCol) {
            // write prefix to buffer
            mCol = GetLinePrefix(bitnb, p, (unsigned int)(limit-p));
            p += mCol;
            if (p >= limit) {
                // should not happen
                // normally it is enough space in buffer
                // add terminating 0
                *p = 0;
                WriteString(buffer);
                p = buffer;
            }
        }

        // it is space in the buffer for at least one char and the terminating 0

        // process 1 character
        char c = TbConvertToChar(*str++);

        if ('\n' == c) {
            // line break
            *p++ = '\n';
            mCol = 0;

          // add terminating 0
          *p = 0;
          WriteString(buffer);
          p = buffer;

          continue;
      }

        *p++ = c;
        mCol++;
    }

    if (p != buffer) {
        // there are characters in the temp buffer that are not written
        // add terminating 0
        *p = 0;
        WriteString(buffer);
    }
}



void
WnTraceLogContext::VPrintfEx(
    int bitnb,
    const char* format,
    va_list args
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    // temp buffer
    char buffer[TEMP_BUFFER_LENGTH];
    unsigned int maxs = TB_ARRAY_ELEMENTS(buffer);

    // we use MS Win32 exception handling to catch invalid arguments
    __try
    {
        // print arguments
        WNERR err = WnStringVPrintf_impl(buffer, maxs, format, args);
        if ( !SUCC(err) ) {
            WnDbgOutput(__FUNCTION__ ": ERROR: WnStringVPrintf_impl(%s) failed, err=%u\n", format, err);
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        WnStringPrintf_impl(buffer, maxs, "\n*** FAULTY TRACE MESSAGE CAUGHT! TraceBitNb=%d. Format string is given in the next line.\n%s\n***\n", bitnb, format);
        // print on error bit
        bitnb = TRCERR;
    }

    // output to targets
    OutputString(buffer, bitnb);

}//VPrintfEx

void
WnTraceLogContext::VPrintfEx(
    int bitnb,
    const WCHAR* format,
    va_list args
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    // temp buffer
    WCHAR buffer[TEMP_BUFFER_LENGTH];
    unsigned int maxs = TB_ARRAY_ELEMENTS(buffer);

    // we use MS Win32 exception handling to catch invalid arguments
    __try
    {
        // print arguments
        WNERR err = WnStringVPrintf_impl(buffer, maxs, format, args);
        if ( !SUCC(err) ) {
            WnDbgOutput(__FUNCTION__ ": ERROR: WnStringVPrintf_impl(%S) failed, err=%u\n", format, err);
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        WnStringPrintf_impl(buffer, maxs, L"\n*** FAULTY TRACE MESSAGE CAUGHT! TraceBitNb=%d. Format string is given in the next line.\n%s\n***\n", bitnb, format);
        // print on error bit
        bitnb = TRCERR;
    }

    // output to targets
    OutputString(buffer, bitnb);

}//VPrintfEx



void
WnTraceLogContext::Print(
    const char* format,
    ...
    )
{
    va_list args;
    va_start(args,format);

    VPrintf(format, args);

    va_end(args);

}//Print

void
WnTraceLogContext::Print(
    const WCHAR* format,
    ...
    )
{
    va_list args;
    va_start(args,format);

    VPrintf(format, args);

    va_end(args);

}//Print



void
WnTraceLogContext::PrintEx(
    int bitnb,
    const char* format,
    ...
    )
{
    va_list args;
    va_start(args,format);

    VPrintfEx(bitnb, format, args);

    va_end(args);

}//PrintEx

void
WnTraceLogContext::PrintEx(
    int bitnb,
    const WCHAR* format,
    ...
    )
{
    va_list args;
    va_start(args,format);

    VPrintfEx(bitnb, format, args);

    va_end(args);

}//PrintEx



// called with lock held
void
WnTraceLogContext::WriteString(
    const char* str   // points to null-terminated string
    )
{
    if (mOutputDebugStringEnabled) {
        // output to targets
#ifdef UNDER_CE
        // temp buffer
        WCHAR buffer[TEMP_BUFFER_LENGTH];
        TbStringNCopyToArray(buffer, str);
        ::OutputDebugStringW(buffer);
#else
        ::OutputDebugStringA(str);
#endif
    }

    if ( mTraceFileEnable ) {
        mTraceFile.TranslateLFandWriteToFile(str, TbStringLen(str));
    }

    if ( mTraceProviderHandle != 0 ) {
        // init EVENT_DATA_DESCRIPTOR
        EVENT_DATA_DESCRIPTOR edd;
        EventDataDescCreate(
            &edd,
            str,
            static_cast<ULONG>(strlen(str)) + 1
            );

        EventWrite(
            mTraceProviderHandle, // _In_ REGHANDLE RegHandle,
            &mEventDescripor,     // _In_ PCEVENT_DESCRIPTOR EventDescriptor,
            1,        // _In_     ULONG UserDataCount,
            &edd      // _In_opt_ PEVENT_DATA_DESCRIPTOR UserData
            );
    }
}


void
WnTraceLogContext::DumpBytes(
    const void* ptr,
    size_t byteCount
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    char line[256];
    char dump[64];
    const unsigned int maxs = TB_ARRAY_ELEMENTS(line);
    unsigned int n = 0;
    int m = 0;
    const char* dataptr = (const char*)ptr;
    unsigned long offs = 0;
    char b;

    if ( ptr==0 ) {
        OutputString("<<NULL>>\n");
        return;
    }

    if ( byteCount==0 ) {
        OutputString("<<no data>>\n");
        return;
    }

    while ( byteCount-- ) {

        b = *dataptr++;

        if ( (offs & 0x000F) == 0 ) {
            n = 0;
            m = 0;
            WnStringPrintf_impl(line,maxs-n, "%08X: ",offs);
        }

        n = TbStringLen(line);
        WnStringPrintf_impl(&line[n],maxs-n, "%02X ",(unsigned char)b);

        /* write byte as ASCII */
        dump[m++] = ( (b>=0x20) && (b<=0x7F) ) ? b : '.';

        offs++;

        if ( (offs & 0x000F) == 0 ) {
            /* end of line */
            dump[m] = 0;
            n = TbStringLen(line);
            WnStringPrintf_impl(&line[n],maxs-n,"| %s\n",dump);
            OutputString(line);
        }

    } // while

    if ( (offs & 0x000F) != 0 ) {
        /* terminate last line */
        char buf[256];
        dump[m] = 0;
        WnStringPrintf_impl(buf,maxs-n,"%-58s| %s\n",line,dump);
        OutputString(buf);
    }
}

void
WnTraceLogContext::DumpDwords(
    const unsigned long* ptr,
    unsigned int dwordCount
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    char line[256];
    const int maxs = TB_ARRAY_ELEMENTS(line);
    int n = 0;
    const long* dataptr = (const long*)ptr;
    unsigned long offs = 0;
    unsigned long l;

    if ( ptr==0 ) {
        OutputString("<<NULL>>\n");
        return;
    }

    if ( dwordCount==0 ) {
        OutputString("<<no data>>\n");
        return;
    }

    while ( dwordCount-- ) {

        l = *dataptr++;

        if ( (offs & 0x0003) == 0 ) {
            n = 0;
            WnStringPrintf_impl(line,maxs-n, "%08X: ",(offs<<2));
        }

        n = TbStringLen(line);
        WnStringPrintf_impl(&line[n],maxs-n, "%08X ",l);
        offs++;

        if ( (offs & 0x0003) == 0 ) {
            /* end of line */
            n = TbStringLen(line);
            WnStringPrintf_impl(&line[n],maxs-n,"\n");
            OutputString(line);
        }

    } // while

    if ( (offs & 0x0003) != 0 ) {
        /* terminate last line */
        n = TbStringLen(line);
        WnStringPrintf_impl(&line[n],maxs-n,"\n");
        OutputString(line);
    }
}


void
WnTraceLogContext::PrintGuid(
    const GUID* guid
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    Print(
            "{%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x}",
            guid->Data1,
            guid->Data2,
            guid->Data3,
            guid->Data4[0], guid->Data4[1],
            guid->Data4[2], guid->Data4[3], guid->Data4[4], guid->Data4[5], guid->Data4[6], guid->Data4[7]
            );
}



void
WnTraceLogContext::SetPrefix(
    const TCHAR* prefix // points to null-terminated string
    )
{
    if ( mMagic != WNTRACELOG_MAGIC_WALUE ) {
        WnDbgOutput(__FUNCTION__ ": INFO: Instance 0x%p not yet constructed or already destructed, ignoring call.\n", this);
        return;
    }

    TB_ZERO_ARRAY(mPrefix);
    if ( NULL != prefix ) {
        if ( 0 != TbStringLen(prefix) ) {
            TbStringNCat(mPrefix, prefix, TB_ARRAY_ELEMENTS(mPrefix)-2);
            TbStringNCat(mPrefix, ": ",   TB_ARRAY_ELEMENTS(mPrefix));
        }
    }
}

// called with lock held
unsigned int
WnTraceLogContext::GetLinePrefix(
    int bitnb,
    char* buffer,
    unsigned int numberOfChars
    )
{
    // null termination
    buffer[0] = 0;

    unsigned int pos = 0;

    // print prefix
    if ( mPrefix[0] != 0 ) {
        pos += TbStringNCopy(&buffer[pos], mPrefix, (numberOfChars - pos));
    }

    if (mDatePrefixEnable || mTimePrefixEnable) {
        SYSTEMTIME st;
        GetLocalTime(&st);

        if ( mDatePrefixEnable ) {
            WnStringPrintf_impl(&buffer[pos], (numberOfChars - pos), "%04u-%02u-%02u ",
                st.wYear,
                st.wMonth,
                st.wDay
                );
            pos = TbStringLen(buffer);
        }
        if ( mTimePrefixEnable ) {
            WnStringPrintf_impl(&buffer[pos], (numberOfChars - pos), "%02u:%02u:%02u.%03u ",
                st.wHour,
                st.wMinute,
                st.wSecond,
                st.wMilliseconds
                );
            pos = TbStringLen(buffer);
        }
    }

    if (mProcessIdPrefixEnable) {
        DWORD procId = GetCurrentProcessId();
        WnStringPrintf_impl(&buffer[pos], (numberOfChars - pos), "[%u] ",
            procId
            );
        pos = TbStringLen(buffer);
    }
    if (mThreadIdPrefixEnable) {
        DWORD threadId = GetCurrentThreadId();
        WnStringPrintf_impl(&buffer[pos], (numberOfChars - pos), "(%u) ",
            threadId
            );
        pos = TbStringLen(buffer);
    }

    if (mBitTagPrefixEnable && bitnb >= 0) {
        WnStringPrintf_impl(&buffer[pos], (numberOfChars - pos), "%s ", GetTraceBitTag(bitnb));
        pos = TbStringLen(buffer);
    }

    return pos;

} //GetLinePrefix


WNERR
WnTraceLogContext::RegisterEtwTracing(const GUID& etwTraceProviderGuid)
{
    if ( mTraceProviderHandle != 0 ) {
        // already registered
        return ERROR_SUCCESS;
    }

    mTraceProviderGuid = etwTraceProviderGuid;

    WNERR err = EventRegister(
            &mTraceProviderGuid, //_In_ LPCGUID ProviderId,
            nullptr,        //_In_opt_ PETWENABLECALLBACK EnableCallback,
            nullptr,        //_In_opt_ PVOID CallbackContext,
            &mTraceProviderHandle //_Out_ PREGHANDLE RegHandle
            );
    if ( !SUCC(err) ) {
        WnDbgOutput(__FUNCTION__ ": ERROR: EventRegister failed, err=%u\n", err);
    } else {
        mOutputDebugStringEnabled = false;
        mThreadIdPrefixEnable = false;
    }

    return err;
}

void
WnTraceLogContext::UnregisterEtwTracing()
{
    if ( mTraceProviderHandle != 0 ) {
        EventUnregister(mTraceProviderHandle);
        mTraceProviderHandle = 0;
    }
}



#ifdef LIBWN_NAMESPACE
}
#endif

#endif

/*************************** EOF **************************************/

#include "trialmanager.h"
#include <QSettings>
#include <QMessageBox>
#include <QCryptographicHash>
#include <QSettings>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QEventLoop>
#include <QStandardPaths>
#include <QDir>

#define BUILD_TIMESTAMP __DATE__ " " __TIME__

QString TrialManager::getRegistryPath() const {
#ifdef Q_OS_WIN
    return QString("HKEY_CURRENT_USER\\Software\\41a4ccd8-bcc0-5710-9eee-0e164da68055");
#else
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    return QDir(appDataPath).filePath("settings.ini");
#endif
}

QDateTime TrialManager::getBuildTime() const {
    QString buildTimeStr = QString(BUILD_TIMESTAMP);
    return QDateTime::fromString(buildTimeStr, "MMM dd yyyy HH:mm:ss");
}

void TrialManager::check(TrialMode mode, int trialDays) {
    if(trialDays >= 0){
        m_trialMode = mode;
        m_trialDays = trialDays;
        checkTrialExpired();
    }
}

TrialManager::TrialManager(QObject *parent)
    : TrialManager(BuildTime, -1, parent)
{

}

TrialManager::TrialManager(TrialMode mode, int trialDays, QObject *parent)
    : QObject(parent),
    m_trialMode(mode),
    m_enabled(true),
    m_trialDays(trialDays),
    m_trialStartTime(QDateTime()),
    m_buildTime(getBuildTime()),
    encryptionKey("ToppingProfessionalControlCenter2025")
{

}

TrialManager::~TrialManager()
{

}

QDateTime TrialManager::getNetworkTime() const
{
    QNetworkAccessManager manager;
    QEventLoop loop;
    QNetworkRequest request(QUrl("https://www.baidu.com"));
    request.setTransferTimeout(3000);
    
    QNetworkReply *reply = manager.get(request);
    QObject::connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
    
    loop.exec();
    
    if (reply->error() == QNetworkReply::NoError) {
        QString dateStr = reply->rawHeader("Date");
        QDateTime networkTime = QDateTime::fromString(dateStr, "ddd, dd MMM yyyy HH:mm:ss 'GMT'").toLocalTime();
        if (networkTime.isValid()) {
            reply->deleteLater();
            return networkTime;
        }
    }
    
    reply->deleteLater();
    QMessageBox::warning(nullptr, tr("警告"), tr("网络异常。"));
    ::exit(0);
}

void TrialManager::checkTrialExpired()
{
    QDateTime currentTime = getNetworkTime();
    
    if (m_trialMode == BuildTime) {
        qint64 daysFromBuild = m_buildTime.daysTo(currentTime);
        if (daysFromBuild >= m_trialDays) {
            QMessageBox::warning(nullptr, tr("试用到期"), 
                               tr("软件试用已到期，请购买正式版。"));
            ::exit(0);
        }
    } else if (m_trialMode == Registry) {
#ifdef Q_OS_WIN
    m_settings = new QSettings(getRegistryPath(), QSettings::NativeFormat, this);
#else
    m_settings = new QSettings(getRegistryPath(), QSettings::IniFormat, this);
#endif
        saveTrialData();
        if(isFirstRun()){
            m_trialStartTime = getNetworkTime();
            saveTrialFirstData();
        }

        loadTrialData();
        
        if (!m_enabled)
            return;

        if (m_trialStartTime.isValid()) {
            qint64 daysFromStart = m_trialStartTime.daysTo(currentTime);
            if (daysFromStart >= m_trialDays) {
                QMessageBox::warning(nullptr, tr("试用到期"), 
                                   tr("软件试用已到期，请购买正式版。"));
                ::exit(0);
            }
        }
    }
}

bool TrialManager::isFirstRun() const
{
    QString firstRun = getValue("Trial/FirstRun");
    return firstRun.isNull();
}

QString TrialManager::encryptData(const QString &data) const
{
    QByteArray input = data.toUtf8();
    QByteArray key = QCryptographicHash::hash(encryptionKey.toUtf8(), QCryptographicHash::Sha256);
    
    QByteArray encrypted;
    for(int i = 0; i < input.length(); i++) {
        encrypted.append(input.at(i) ^ key.at(i % key.length()));
    }
    return encrypted.toBase64();
}

QString TrialManager::decryptData(const QString &data) const
{
    QByteArray input = QByteArray::fromBase64(data.toUtf8());
    QByteArray key = QCryptographicHash::hash(encryptionKey.toUtf8(), QCryptographicHash::Sha256);
    
    QByteArray decrypted;
    for(int i = 0; i < input.length(); i++) {
        decrypted.append(input.at(i) ^ key.at(i % key.length()));
    }
    return QString::fromUtf8(decrypted);
}

QString TrialManager::getEncryptedKeyName(const QString& originalKey) const {
    QByteArray combined = originalKey.toUtf8() + encryptionKey.toUtf8();
    QByteArray hash = QCryptographicHash::hash(combined, QCryptographicHash::Sha256);
    
    QString hexHash = hash.toHex();
    QString guid = QString("%1-%2-%3-%4-%5")
        .arg(hexHash.mid(0, 8))
        .arg(hexHash.mid(8, 4))
        .arg(hexHash.mid(12, 4))
        .arg(hexHash.mid(16, 4))
        .arg(hexHash.mid(20, 12));
        
    return guid;
}

void TrialManager::setValue(const QString& key, const QVariant& value) {
    QString encryptedKey = getEncryptedKeyName(key);
    QString encryptedValue = encryptData(value.toString());
    m_settings->setValue(encryptedKey, encryptedValue);
}

QString TrialManager::getValue(const QString& key) const {
    QString encryptedKey = getEncryptedKeyName(key);
    QString encryptedValue = m_settings->value(encryptedKey, "").toString();
    QString value = decryptData(encryptedValue);
    return value;
}

void TrialManager::saveTrialData()
{
    setValue("Trial/Enabled", QString::number(m_enabled));
    setValue("Trial/Days", QString::number(m_trialDays));
}

void TrialManager::saveTrialFirstData(){
    setValue("Trial/FirstRun", true);
    setValue("Trial/StartTime", m_trialStartTime.toString(Qt::ISODate));
}

void TrialManager::loadTrialData()
{
    QString enabled = getValue("Trial/Enabled");
    QString days = getValue("Trial/Days");
    QString startTime = getValue("Trial/StartTime");
    
    if (enabled.isEmpty() || days.isEmpty() || startTime.isEmpty()) {
        m_enabled = false;
        m_trialDays = 0;
        return;
    }
    
    bool ok = false;
    m_enabled = enabled.toInt(&ok);
    if (!ok) {
        m_enabled = false;
    }
    
    m_trialDays = days.toInt(&ok);
    if (!ok) {
        m_trialDays = 0;
    }
    
    m_trialStartTime = QDateTime::fromString(startTime, Qt::ISODate);
    if (!m_trialStartTime.isValid()) {
        m_enabled = false;
        m_trialDays = 0;
    }
}

#ifndef LOOPBACKS1M1_H
#define LOOPBACKS1M1_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "appsettings.h"
#include "loopbackbase.h"


namespace Ui {
class LoopbackS1M1;
}


class LoopbackS1M1 : public LoopbackBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit LoopbackS1M1(QWidget* parent=nullptr, QString name="");
    ~LoopbackS1M1();
    LoopbackS1M1& setName(QString name);
    LoopbackS1M1& setFont(QFont font);
    LoopbackS1M1& setVolumeMeterLeft(int value);
    LoopbackS1M1& setVolumeMeterLeftClear();
    LoopbackS1M1& setVolumeMeterLeftSlip();
    LoopbackS1M1& setVolumeMeterRight(int value);
    LoopbackS1M1& setVolumeMeterRightClear();
    LoopbackS1M1& setVolumeMeterRightSlip();
    LoopbackS1M1& setGain(float value);
    LoopbackS1M1& setGainLock(bool state=true);
    LoopbackS1M1& setMuteAffectGain(bool state=true);
    LoopbackS1M1& setGainAffectMute(bool state=true);
    LoopbackS1M1& setGainRange(float min, float max);
    LoopbackS1M1& setGainDefault(float value);
    LoopbackS1M1& setGainWidgetDisable(float value);
    LoopbackS1M1& setAudioSourceDefault(QString audioSourceDefault);
    LoopbackS1M1& setAudioSourceColor(QColor color);
    LoopbackS1M1& setChannelNameEditable(bool state=true);
    LoopbackS1M1& addAudioSource(QString audioClass, QVector<QString>& audioSourceList);
    QString getAudioSource();
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::LoopbackS1M1* ui;
    QTimer mTimer;
    QFont mFont;
    QString mAudioSourceDefault="";
    QString mPreAudioSource="";
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(int value);
    void in_widgetToolButton_actionChanged(QString actionName);
    void in_widgetPushButtonGroup1_stateChanged(QString button, QString state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // LOOPBACKS1M1_H


#ifndef COMBOBOXS1M2_H
#define COMBOBOXS1M2_H


#include <QComboBox>
#include <QWheelEvent>
#include <QtWidgets/qstyleoption.h>

#ifdef Q_OS_MACOS
#include <QProxyStyle>
#include <QPainter>
#include <QPainterPath>
#include <QStyleOption>
class MacComboBoxStyle : public QProxyStyle {
public:
    MacComboBoxStyle(QStyle* style) : QProxyStyle(style) {}

    ~MacComboBoxStyle() {}

    void drawComplexControl(ComplexControl control, const QStyleOptionComplex *option, QPainter *painter, const QWidget *widget = nullptr) const override{
        QProxyStyle::drawComplexControl(control, option, painter, widget);
    }

    void drawPrimitive(PrimitiveElement element, const QStyleOption *option, QPainter *painter, const QWidget *widget = nullptr) const override
    {
        if(element == PE_IndicatorMenuCheckMark){
            return;
        }
        QProxyStyle::drawPrimitive(element, option, painter, widget);
    }

    void drawControl(ControlElement element, const QStyleOption *option, QPainter *painter, const QWidget *widget = nullptr) const override
    {
        if (element == CE_MenuItem) {
            painter->save();
            if (option->state & State_Selected) {
                painter->setPen(Qt::NoPen);
                painter->setBrush(QColor(66, 66, 66));
                painter->drawRect(option->rect);
            }
            const QStyleOptionMenuItem* viewOpt = qstyleoption_cast<const QStyleOptionMenuItem*>(option);
            if (viewOpt) {
                QRect textRect = option->rect;
                painter->setPen(QColor(216,216,216));
                painter->setFont(viewOpt->font);
                painter->drawText(textRect, Qt::AlignCenter, viewOpt->text);
            }
            painter->restore();
            return;
        }
        QProxyStyle::drawControl(element, option, painter, widget);
    }
};
#endif

class ComboBoxS1M2 : public QComboBox
{
    Q_OBJECT
public:
    explicit ComboBoxS1M2(QComboBox* parent=nullptr);
    ~ComboBoxS1M2();
    ComboBoxS1M2& setFont(QFont font);
    ComboBoxS1M2& setName(QString name);
    ComboBoxS1M2& modifyItemList(QVector<QString> list);
    ComboBoxS1M2& modifyItemList(QVector<QString> list, QString defaultItem);
    ComboBoxS1M2& setDefaultItem(QString item);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void wheelEvent(QWheelEvent* e) override;
    void hidePopup() override;
    void showPopup() override;
private:
    QFont mFont;
    bool mInitOK=false;
private slots:
    void in_mComboBox_currentTextChanged(QString text);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // COMBOBOXS1M2_H


#ifndef DEVICEBASE_H
#define DEVICEBASE_H


#include <QThread>

#include "usbhidapi.h"
#include "blockingqueue.h"


// Sender
class Sender : public QThread
{
    Q_OBJECT
public:
    void stop() { mQueue.enqueue(mExitValue.toUtf8()); }
    void send(QByteArray packet) { mQueue.enqueue(packet); }
protected:
    void run() override
    {
        while(true)
        {
            QByteArray packet=mQueue.dequeue();
            if(packet == mExitValue) break;
            if(USBHHandle.send(reinterpret_cast<unsigned char*>(packet.data()), packet.size()) == -1)
            {
                emit sendFailed();
                break;
            }
        }
    }
private:
    BlockingQueue<QByteArray> mQueue;
    inline static const QString mExitValue="--##--<<__EXIT__>>--##--";
signals:
    void sendFailed();
};

// DeviceBase
class DeviceBase : public QThread
{
    Q_OBJECT
public:
    explicit DeviceBase(QObject* parent=nullptr) : QThread(parent) { }
    ~DeviceBase();
    bool open() { return USBHHandle.openDevice(mVendorID, mProductID); }
    void start();
    void stop();
    unsigned int getMinimumSupportedVersion() { return mMinimumSupportedVersion; }
    void showSendingFrame(bool state=true) { mShowSendingFrame = state; }
protected:
    void run() override;
    void setDevice(unsigned int deviceVendorID, unsigned int deviceProductID);
    void setMinimumSupportedVersion(unsigned int version) { mMinimumSupportedVersion = version; }
    void sendPacket(QByteArray packet);
    bool showSendingFrameState() { return mShowSendingFrame; }
    virtual int readFrame() = 0;
private:
    int mVendorID=0, mProductID=0;
    unsigned int mMinimumSupportedVersion=0;
    bool mStoped=true;
    Sender mSender;
    bool mShowSendingFrame=false;
signals:
    void deviceDisconnected();
};


#endif // DEVICEBASE_H


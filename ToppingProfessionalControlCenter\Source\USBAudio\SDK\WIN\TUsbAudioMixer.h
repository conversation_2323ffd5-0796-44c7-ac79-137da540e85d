/************************************************************************
 *  Module:       TUsbAudioMixer.h
 *  Description:  TUSBAudio mixer control interface
 *
 *  Runtime Env.: Windows user mode
 *  Author(s):    <PERSON><PERSON>, <PERSON>
 *  Company:      Thesycon GmbH, Germany      http://www.thesycon.de
 ************************************************************************/

#ifndef __TUsbAudioMixer_h__
#define __TUsbAudioMixer_h__

// Win32 support library, includes windows.h
#include "libwn.h"

// TUSBAUDIO driver API
#include "TUsbAudioApiDll.h"
// mixer plugin control interface
#include "MixerPluginProperties.h"

// STL
#include <memory>
#include <vector>


/*
    The TUSBAudio driver supports an optional plugin which implements a mixer. If the mixer plugin is present, 
    all incoming and outgoing audio streams will be routed through the mixer. 
    The data flow is illustrated below. Note that there is one mixer instance per USB device instance.


    INPUT CHANNELS

    Application playback (to physical channels) ->-------#--------#--------#
                                                         |        |        |
                                   Device input ->-------#--------#--------#
                                                         |        |        |
     Application playback (to virtual channels) ->-------#--------#--------#
                                                         |        |        |      OUTPUT CHANNELS
                                                         |        |        |
                                                         |        |        +----> Application recording (from virtual channels)
                                                         |        |
                                                         |        +-------------> Application recording (from physical channels)
                                                         |
                                                         +----------------------> Device output 
    
    Channels:
    An arrow symbol -> represents a stream which consists of multiple audio channels, 
    or zero channels if the given stream is not implemented.
    
    Pins:
    A pin represents a channel bundle.
    An incoming stream (from the mixer's perspective) connects to a mixer input pin.
    An outgoing stream (from the mixer's perspective) connects to a mixer output pin.
    The channel count for a given pin can be zero if the device does not implement the respective channels.
    
    Nodes:
    A number sign # represents a matrix of mixer nodes. There is one node for each intersection of an input channel and an output channel.
    Each node stores a gain value (range 0..1) which defines how much of the input signal will be mixed to the given output channel.
    An output channel receives the sum of all input channels after multiplication with the gain in each node.
    
    Output Gain:
    In addition, the mixer implements an individual output gain for each output channel.
    This gain will be applied after the sum of all nodes has been calculated, and immediately before the output signal leaves the mixer.

    Level Meters:
    The mixer implements a level meter for each single input and output channel.
    For efficiency reasons, level meters are disabled by default and must be enabled by an application before values can be queried.
*/


//
// This class provides access to the mixer's control interface.
//
class TUsbAudioMixer
{
public:
    // ctor
    TUsbAudioMixer(
        TUsbAudioApiDll& api  // Pass an instance of the API DLL loader.
        );

    // dtor
    ~TUsbAudioMixer();


/////////////////////////////////////////////
// Device Instance Association
//
public:
    
    //
    // Associate this API with a device instance previously
    // opened using TUSBAUDIO_OpenDeviceByIndex or similar.
    // This function must be called before any other method can be used.
    // The returned status should be checked.
    //
    TSTATUS
    AttachToDevice(
        TUsbAudioHandle deviceHandle   // handle opened previously
        );
    
    //
    // Forget the device handle association, if any.
    // It's safe to call this function multiple times.
    //
    void
    DetachFromDevice();
    

/////////////////////////////////////////////
// Types
//

    //
    // Identifies a mixer input or output pin (a bundle of channels).
    //
    enum Pin
    {
        // Mixer inputs: The signal enters the mixer.
        AppPlayback = 0,
        DeviceInput,
        AppPlaybackVirt,

        // Mixer outputs: The signal leaves the mixer.
        AppRecording = 4,
        DeviceOutput,
        AppRecordingVirt
    };

    //
    // Identifies a single channel.
    //
    struct Channel
    {
        Pin pin;            // The pin the channel belongs to.
        unsigned int index; // Zero-based index within the pin's channel bundle.

        // ctor
        Channel(Pin p = AppPlayback, unsigned int i = 0)
            : pin{p}, index{i}
            { }
    };

    //
    // Identifies a mixer node (intersection of input channel and output channel).
    //
    struct Node
    {
        Channel inputChannel;   // Input channel this node processes.
        Channel outputChannel;  // Output channel this node belongs to. 

        // ctor
        Node(Channel ichan = Channel{}, Channel ochan = Channel{} )
            : inputChannel{ichan}, outputChannel{ochan}
            { }
        // ctor
        Node(Pin ipin, unsigned int iidx, Pin opin, unsigned int oidx)
            : inputChannel{ipin, iidx}, outputChannel{opin, oidx}
            { }
    };


    //
    // The gain (or weight) ranges from zero to one, inclusive, and is expressed as Q7.24 signed fixed point number.
    // One sign bit, 7 significant bits and 24 fractional bits.
    //
    using Gain = DSP_Q724;

    static constexpr Gain Gain_Zero = 0;             // signal will be multiplied by zero (-inf dB) : node not connected
    static constexpr Gain Gain_One = DSP_Q724_ONE;   // signal will be multiplied by 1.0 (0 dB) : node connected, signal passes unmodified

    static constexpr double Gain_ZeroDotZero = Gain_Zero;
    static constexpr double Gain_OneDotZero = Gain_One;
 

    //
    // Combines node address and gain for this node.
    //
    struct NodeGain
    {
        Node node;
        Gain gain;

        // ctor
        NodeGain(Channel ichan = Channel{}, Channel ochan = Channel{}, Gain g = Gain_Zero )
            : node{ichan, ochan}, gain{g}
            { }
        // ctor
        NodeGain(Pin ipin, unsigned int iidx, Pin opin, unsigned int oidx, Gain g = Gain_Zero )
            : node{ipin, iidx, opin, oidx}, gain{g}
            { }
    };

    //
    // Combines output channel and output gain for this channel.
    //
    struct ChannelGain
    {
        Channel channel;
        Gain gain;

        // ctor
        ChannelGain(Channel chan = Channel{}, Gain g = Gain_Zero )
            : channel{chan}, gain{g}
            { }
        // ctor
        ChannelGain(Pin pin, unsigned int idx, Gain g = Gain_Zero )
            : channel{pin, idx}, gain{g}
            { }
    };


/////////////////////////////////////////////
// Pin and Channel Access
//

    // Pin classification.
    static bool IsInputPin(Pin pin)   { return (pin >= AppPlayback && pin <= AppPlaybackVirt); }
    static bool IsOutputPin(Pin pin)  { return (pin >= AppRecording && pin <= AppRecordingVirt); }

    // Channel classification.
    static bool IsInputChannel(Channel channel)   { return IsInputPin(channel.pin); }
    static bool IsOutputChannel(Channel channel)  { return IsOutputPin(channel.pin); }

    // Returns the channel count for the given pin. Returns zero if the pin is not implemented.
    unsigned int GetChannelCount(Pin pin) const;

    // Shortcut access to input pin channel counts.
    unsigned int ChannelCountAppPlayback() const      { return mChannelCountAppPlayback; }
    unsigned int ChannelCountDeviceInput() const      { return mChannelCountDeviceInput; }
    unsigned int ChannelCountAppPlaybackVirt() const  { return mChannelCountAppPlaybackVirt; }

    // Shortcut access to output pin channel counts.
    unsigned int ChannelCountAppRecording() const     { return mChannelCountAppRecording; }
    unsigned int ChannelCountDeviceOutput() const     { return mChannelCountDeviceOutput; }
    unsigned int ChannelCountAppRecordingVirt() const { return mChannelCountAppRecordingVirt; }

    void
    SetMaxChannelCounts(
        unsigned int maxChannelCountDeviceOutput,
        unsigned int maxChannelCountDeviceInput,
        unsigned int maxChannelCountAppPlayback,
        unsigned int maxChannelCountAppRecording,
        unsigned int maxChannelCountAppPlaybackVirt,
        unsigned int maxChannelCountAppRecordingVirt
        )
            {
                mMaxChannelCountDeviceOutput = maxChannelCountDeviceOutput;
                mMaxChannelCountDeviceInput = maxChannelCountDeviceInput;
                mMaxChannelCountAppPlayback = maxChannelCountAppPlayback;
                mMaxChannelCountAppRecording = maxChannelCountAppRecording;
                mMaxChannelCountAppPlaybackVirt = maxChannelCountAppPlaybackVirt;
                mMaxChannelCountAppRecordingVirt = maxChannelCountAppRecordingVirt;
            }

    // Returns all channels that belong to the given pin.
    std::vector<Channel>
    GetChannels(Pin pin) const;

    // Returns the nodes for all channel intersections of the given pins.
    std::vector<Node>
    GetNodes(Pin inputPin, Pin outputPin) const;


/////////////////////////////////////////////
// Node Gain Control
//

    // Set a new gain for the specified node.
    TSTATUS
    SetNodeGain(
        const Node& node,
        Gain gain
        );

    // Retrieve the current gain from the specified node.
    TSTATUS
    GetNodeGain(
        const Node& node,
        Gain& gain
        );

    // GetNodeGain always succeeds if used correctly. Hence a convenient shortcut is provided.
    Gain
    GetNodeGain(
        const Node& node
        )
            { 
                Gain gain;
                GetNodeGain(node, gain);
                return gain;
            }


    // Set a new gain for the specified set of nodes.
    // Gain modification is an atomic operation and will be applied to all nodes simultaneously.
    TSTATUS
    SetNodesGain(
        const std::vector<Node>& nodes,
        Gain gain  // All nodes will be set to this gain value.
        );

    // Set a new gain for the specified set of nodes.
    // Gain modification is an atomic operation and will be applied to all nodes simultaneously.
    TSTATUS
    SetNodeGains(
        const std::vector<Node>& nodes,
        const std::vector<Gain>& gains  // Specifies one gain value for each node.
        );

    // Set a new gain for the specified set of nodes.
    // Gain modification is an atomic operation and will be applied to all nodes simultaneously.
    TSTATUS
    SetNodeGains(
        const std::vector<NodeGain>& nodeGains  // Node address and gain value combined for each node.
        );


    // Query current gain from the specified set of nodes.
    TSTATUS
    GetNodeGains(
        const std::vector<Node>& nodes,
        std::vector<Gain>& gains // Receives one gain value for each node.
        );

    // GetNodeGains always succeeds if used correctly. Hence a convenient shortcut is provided.
    std::vector<Gain>
    GetNodeGains(
        const std::vector<Node>& nodes
        )
            {
                std::vector<Gain> gains;
                GetNodeGains(nodes, gains);
                return gains;
            }

    // Query current gain from the specified set of nodes.
    TSTATUS
    GetNodeGains(
        std::vector<NodeGain>& nodeGains  // Node addresses must be set before the function is called,
                                          // gain values will be set on function return.
        );
    

/////////////////////////////////////////////
// Output Gain Control
//

    // Set a new gain for the specified output channel.
    TSTATUS
    SetOutputGain(
        Channel outputChannel,
        Gain gain
        );

    // Retrieve the current gain from the specified output channel.
    TSTATUS
    GetOutputGain(
        Channel outputChannel,
        Gain& gain
        );

    // GetOutputGain always succeeds if used correctly. Hence a convenient shortcut is provided.
    Gain
    GetOutputGain(
        Channel outputChannel
        )
            {
                Gain gain;
                GetOutputGain(outputChannel, gain);
                return gain;
            }


    // Set a new gain for the specified output channels.
    // Gain modification is an atomic operation and will be applied to all channels simultaneously.
    TSTATUS
    SetOutputsGain(
        const std::vector<Channel>& outputChannels,
        Gain gain  // All channels will be set to this gain value.
        );

    // Set a new gain for the specified output channels.
    // Gain modification is an atomic operation and will be applied to all channels simultaneously.
    TSTATUS
    SetOutputGains(
        const std::vector<Channel>& outputChannels,
        const std::vector<Gain>& gains  // Specifies one gain value for each channel.
        );

    // Set a new gain for the specified output channels.
    // Gain modification is an atomic operation and will be applied to all channels simultaneously.
    TSTATUS
    SetOutputGains(
        const std::vector<ChannelGain>& outputChannelGains  // Channel address and gain value combined for each channel.
        );


    // Query current gain from the specified set of output channels.
    TSTATUS
    GetOutputGains(
        const std::vector<Channel>& outputChannels,
        std::vector<Gain>& gains  // Receives one gain value for each channel.
        );

    // GetOutputGains always succeeds if used correctly. Hence a convenient shortcut is provided.
    std::vector<Gain>
    GetOutputGains(
        const std::vector<Channel>& outputChannels
        )
            {
                std::vector<Gain> gains;
                GetOutputGains(outputChannels, gains);
                return gains;
            }


/////////////////////////////////////////////
// Gain Conversion Helpers
//
   
    // Convert gain in fixed-point format to percent. Range is 0..100 inclusive.
    static
    unsigned int // 0..100
    GainToPercent(
        Gain gain
        )
            { 
                return ( (gain * 100) / Gain_One );
            }

    // Convert percent to gain in fixed-point format.
    static
    Gain
    PercentToGain(
        unsigned int percent  // 0 .. 100
        )
            { 
                return ( (percent * Gain_One) / 100 );
            }


    // Convert gain in fixed-point format to linear factor. Range is 0.0 .. 1.0 inclusive.
    static
    double // 0.0 .. 1.0
    GainToLinear(
        Gain gain
        )
            { 
                double x = gain;
                return ( x / Gain_OneDotZero );
            }

    // Convert linear factor to gain in fixed-point format.
    static
    Gain
    LinearToGain(
        double attenuation  // 0.0 .. 1.0
        )
            {
                double x = attenuation * Gain_OneDotZero;
                return static_cast<Gain>(x); 
            }


    // Convert gain in fixed-point format to dB. Range is -144dB .. 0.0dB.
    // dB = 20 * log(gain)
    static
    double // -INFINITY for gain=0, or -144dB .. 0.0dB
    GainToLog(
        Gain gain
        );

    // Convert dB to gain in fixed-point format.
    // gain = 10 ^ (dB/20)
    static
    Gain
    LogToGain(
        double dB,                 // -INFINITY .. 0.0dB
        double minusInf = -144.49  // dB <= minusInf results in gain = zero
        );


/////////////////////////////////////////////
// Level Meters
//

    //
    // Signal level is expressed as attenuation in steps of 1/100 dB.
    // Examples:
    //        0 (0dB)    corresponds to full scale
    //     -600 (-6dB)   corresponds to -6dB attenuation
    //    -1200 (-12dB)  corresponds to -12dB attenuation
    //   -12000 (-120dB) corresponds to -120dB attenuation
    //
    using Level = short;
    
    // smallest value is -12000 (-120dB)
    static constexpr Level Level_Min = LEVEL_METER_MINIMUM_VALUE;


    // Enable level meters in the driver.
    // Must be called before UpdateLevelMeterData() and QueryLevelMeter() can be used.
    TSTATUS
    EnableLevelMeters();

    // Disable level meters in the driver.
    TSTATUS
    DisableLevelMeters();


    // Retrieve current values for all level meters from the driver and store values in an internal cache.
    // For efficiency reasons, an application should call this function once 
    // and then issue a sequence of QueryLevelMeter() calls.
    TSTATUS
    UpdateLevelMeterData();

    
    // Fetch the current level value for the given channel from the internal cache.
    // UpdateLevelMeterData() should be called once to update the internal cache.
    Level
    QueryLevelMeter(
        Channel channel
        );

    // Fetch the current level values for all channels of the given pin from the internal cache.
    // UpdateLevelMeterData() should be called once to update the internal cache.
    std::vector<Level>
    QueryLevelMeters(
        Pin pin
        );


///////////////////////////////////////////
// Implementation
//
protected:

    TSTATUS
    QueryProperties();

    TSTATUS
    GetInterfaceVersion(
        unsigned int& majorVersion,
        unsigned int& minorVersion
        );

    TSTATUS
    GetChannelCountProperty(
        MixerProperty propId,
        unsigned int& channelCount
        );

    TSTATUS
    SetLevelMetersEnable(bool enable);

    void
    AllocLevelMetersBuffer();

    void
    FreeLevelMetersBuffer();

    
    struct LevelDataArray
    {
         const LevelMeterValue* val;
         unsigned int count;
    };

    void
    GetLevelDataArray(
        LevelDataArray& lda, 
        Pin pin
        );


    static
    constexpr
    unsigned char
    ChannelTypeFromPin(Pin pin)
            { return static_cast<unsigned char>(pin & 0x3); }
    
    MixerAddress
    MixerAddressFromNode(const Node& node)
            {
                MixerAddress addr;
                addr.inChannelType = ChannelTypeFromPin(node.inputChannel.pin);
                addr.inChannelIndex = static_cast<unsigned char>(node.inputChannel.index);
                addr.outChannelType = ChannelTypeFromPin(node.outputChannel.pin);
                addr.outChannelIndex = static_cast<unsigned char>(node.outputChannel.index);
                return addr;
            }

    MixerLineAddress
    MixerLineAddressFromChannel(Channel channel)
            {
                MixerLineAddress addr;
                addr.channelType = ChannelTypeFromPin(channel.pin);
                addr.channelIndex = static_cast<unsigned char>(channel.index);
                return addr;
            }


    TSTATUS
    GetDspProperty(
        void* propertyBuffer,
        size_t propertySize
        )
            {
                return mDriverApi.TUSBAUDIO_GetDspProperty(mDeviceHandle, propertyBuffer, static_cast<unsigned int>(propertySize));
            }

    TSTATUS
    SetDspProperty(
        const void* propertyBuffer,
        size_t propertySize
        )
            {
                return mDriverApi.TUSBAUDIO_SetDspProperty(mDeviceHandle, propertyBuffer, static_cast<unsigned int>(propertySize));
            }


///////////////////////////////////////////
// Data
//
protected:

    TUsbAudioApiDll& mDriverApi;

    // device instance we are talking to
    TUsbAudioHandle mDeviceHandle {TUSBAUDIO_INVALID_HANDLE_VALUE};

    // interface version reported by the mixer plugin
    unsigned int mInterfaceVersionMajor {0};
    unsigned int mInterfaceVersionMinor {0};

    // channel counts reported by the mixer
    unsigned int mChannelCountDeviceOutput {0};
    unsigned int mChannelCountDeviceInput {0};
    unsigned int mChannelCountAppPlayback {0};
    unsigned int mChannelCountAppRecording {0};
    unsigned int mChannelCountAppPlaybackVirt {0};
    unsigned int mChannelCountAppRecordingVirt {0};

    unsigned int mMaxChannelCountDeviceOutput {0xffffffff};
    unsigned int mMaxChannelCountDeviceInput {0xffffffff};
    unsigned int mMaxChannelCountAppPlayback {0xffffffff};
    unsigned int mMaxChannelCountAppRecording {0xffffffff};
    unsigned int mMaxChannelCountAppPlaybackVirt {0xffffffff};
    unsigned int mMaxChannelCountAppRecordingVirt {0xffffffff};

    // current state
    bool mLevelMetersEnabled {false};

    // transfer buffer for level meters
    std::vector<unsigned char> mLevelMetersData;


}; // class TUsbAudioMixer


#endif

/***************************** EOF **************************************/

#include "fieldorigins1m1.h"


FieldOriginS1M1::FieldOriginS1M1(QWidget* parent, QString name)
    : FieldOriginBase1(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
{
    connect(this, &FieldOriginBase1::attributeChanged, this, &FieldOriginS1M1::in_widgetBase_attributeChanged, Qt::UniqueConnection);
}
FieldOriginS1M1::~FieldOriginS1M1()
{

}


// override
void FieldOriginS1M1::loadSettings()
{
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("Visible", mVisibleListDefault);
    }
    QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
    QVector<QString> list;
    for(auto element : visibleList)
    {
        list.append(element.toString());
    }
    setVisibleList(list);
}
void FieldOriginS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Language")
    {
        if(value == "English")
        {
        }
        else if(value == "Chinese")
        {
        }
    }
}


// slot
void FieldOriginS1M1::in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute == "Visible")
    {
        if(value.toInt())
        {
            QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
            visibleList.append(QVariant(objectName));
            WorkspaceObserver::setValue("Visible", visibleList);
        }
        else
        {
            QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
            visibleList.removeOne(QVariant(objectName));
            WorkspaceObserver::setValue("Visible", visibleList);
        }
    }
}
void FieldOriginS1M1::in_widgetList_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// setter & getter
FieldOriginS1M1& FieldOriginS1M1::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
FieldOriginS1M1& FieldOriginS1M1::modifyWidgetList(QVector<OriginBase*> list)
{
    mWidgetList = list;
    for(auto element : list)
    {
        element->setWidgetMovable(false);
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_widgetList_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    FieldOriginBase1::modifyWidgetList(list);
    return *this;
}
FieldOriginS1M1& FieldOriginS1M1::setVisibleListDefault(QVector<OriginBase*> list)
{
    for(auto widget : list)
    {
        mVisibleListDefault.append(QVariant(widget->getChannelName()));
    }
    return *this;
}


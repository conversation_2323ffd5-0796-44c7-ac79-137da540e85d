#ifndef PUSHBUTTONGROUPS1M6_H
#define PUSHBUTTONGROUPS1M6_H


#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class PushButtonGroupS1M6;
}


class PushButtonGroupS1M6 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonGroupS1M6(QWidget* parent=nullptr);
    ~PushButtonGroupS1M6();
    PushButtonGroupS1M6& setFont(QFont font);
    PushButtonGroupS1M6& setLanguage(QString language);
    PushButtonGroupS1M6& setState(QString button, QString state, bool needEmit=true);
    QString getState(QString button);
protected:
    void resizeEvent(QResizeEvent* e) override;
    bool eventFilter(QObject* watched, QEvent* event) override;
    void showEvent(QShowEvent*) override { resizeEvent(nullptr); }
private:
    Ui::PushButtonGroupS1M6* ui;
    QFont mFont;
    QString mCurrentItem="";
private slots:
    void on_Item1PushButton_clicked(bool checked);
    void on_Item2PushButton_clicked(bool checked);
    void on_Item3PushButton_clicked(bool checked);
signals:
    void stateChanged(QString button, QString state);
};


#endif // PUSHBUTTONGROUPS1M6_H


/****************************************************************************
** Meta object code from reading C++ file 'deviceconnectorviews1m1.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'deviceconnectorviews1m1.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN23DeviceConnectorViewS1M1E_t {};
} // unnamed namespace

template <> constexpr inline auto DeviceConnectorViewS1M1::qt_create_metaobjectdata<qt_meta_tag_ZN23DeviceConnectorViewS1M1E_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "DeviceConnectorViewS1M1",
        "on_Page6PushButton1_clicked",
        "",
        "on_Page6PushButton2_clicked",
        "on_Page6CheckBox_checkStateChanged",
        "Qt::CheckState",
        "arg1",
        "on_Page7PushButton1_clicked",
        "on_Page7PushButton2_clicked",
        "on_Page7CheckBox_checkStateChanged",
        "on_Page9PushButton1_clicked",
        "on_Page9PushButton2_clicked",
        "on_Page9CheckBox_checkStateChanged",
        "on_PageAPushButton1_clicked",
        "on_PageAPushButton2_clicked",
        "on_PageBPushButton_clicked",
        "on_PageCPushButton_clicked"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'on_Page6PushButton1_clicked'
        QtMocHelpers::SlotData<void()>(1, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_Page6PushButton2_clicked'
        QtMocHelpers::SlotData<void()>(3, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_Page6CheckBox_checkStateChanged'
        QtMocHelpers::SlotData<void(const Qt::CheckState &)>(4, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 5, 6 },
        }}),
        // Slot 'on_Page7PushButton1_clicked'
        QtMocHelpers::SlotData<void()>(7, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_Page7PushButton2_clicked'
        QtMocHelpers::SlotData<void()>(8, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_Page7CheckBox_checkStateChanged'
        QtMocHelpers::SlotData<void(const Qt::CheckState &)>(9, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 5, 6 },
        }}),
        // Slot 'on_Page9PushButton1_clicked'
        QtMocHelpers::SlotData<void()>(10, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_Page9PushButton2_clicked'
        QtMocHelpers::SlotData<void()>(11, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_Page9CheckBox_checkStateChanged'
        QtMocHelpers::SlotData<void(const Qt::CheckState &)>(12, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 5, 6 },
        }}),
        // Slot 'on_PageAPushButton1_clicked'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_PageAPushButton2_clicked'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_PageBPushButton_clicked'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_PageCPushButton_clicked'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<DeviceConnectorViewS1M1, qt_meta_tag_ZN23DeviceConnectorViewS1M1E_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject DeviceConnectorViewS1M1::staticMetaObject = { {
    QMetaObject::SuperData::link<DeviceConnectorViewBase::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN23DeviceConnectorViewS1M1E_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN23DeviceConnectorViewS1M1E_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN23DeviceConnectorViewS1M1E_t>.metaTypes,
    nullptr
} };

void DeviceConnectorViewS1M1::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<DeviceConnectorViewS1M1 *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->on_Page6PushButton1_clicked(); break;
        case 1: _t->on_Page6PushButton2_clicked(); break;
        case 2: _t->on_Page6CheckBox_checkStateChanged((*reinterpret_cast< std::add_pointer_t<Qt::CheckState>>(_a[1]))); break;
        case 3: _t->on_Page7PushButton1_clicked(); break;
        case 4: _t->on_Page7PushButton2_clicked(); break;
        case 5: _t->on_Page7CheckBox_checkStateChanged((*reinterpret_cast< std::add_pointer_t<Qt::CheckState>>(_a[1]))); break;
        case 6: _t->on_Page9PushButton1_clicked(); break;
        case 7: _t->on_Page9PushButton2_clicked(); break;
        case 8: _t->on_Page9CheckBox_checkStateChanged((*reinterpret_cast< std::add_pointer_t<Qt::CheckState>>(_a[1]))); break;
        case 9: _t->on_PageAPushButton1_clicked(); break;
        case 10: _t->on_PageAPushButton2_clicked(); break;
        case 11: _t->on_PageBPushButton_clicked(); break;
        case 12: _t->on_PageCPushButton_clicked(); break;
        default: ;
        }
    }
}

const QMetaObject *DeviceConnectorViewS1M1::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DeviceConnectorViewS1M1::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN23DeviceConnectorViewS1M1E_t>.strings))
        return static_cast<void*>(this);
    return DeviceConnectorViewBase::qt_metacast(_clname);
}

int DeviceConnectorViewS1M1::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = DeviceConnectorViewBase::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 13;
    }
    return _id;
}
QT_WARNING_POP

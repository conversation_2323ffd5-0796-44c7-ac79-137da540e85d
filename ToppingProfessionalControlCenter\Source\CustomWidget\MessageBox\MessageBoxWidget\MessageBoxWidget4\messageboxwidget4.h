#ifndef MESSAGEBOXWIDGET4_H
#define MESSAGEBOXWIDGET4_H


#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class MessageBoxWidget4;
}


class MessageBoxWidget4 : public QWidget
{
    Q_OBJECT
public:
    explicit MessageBoxWidget4(QWidget* parent=nullptr);
    ~MessageBoxWidget4();
    MessageBoxWidget4& setFont(QFont font);
    MessageBoxWidget4& setLanguage(QString language);
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    Ui::MessageBoxWidget4* ui;
    QFont mFont;
    QString mSelectedItem="";
private slots:
    void on_CheckBox1_checkStateChanged(const Qt::CheckState &arg1);
    void on_CheckBox2_checkStateChanged(const Qt::CheckState &arg1);
    void on_PushButton1_clicked();
    void on_PushButton2_clicked();
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // MESSAGEBOXWIDGET4_H


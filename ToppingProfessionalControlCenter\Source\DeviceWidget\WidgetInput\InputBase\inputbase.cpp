#include "inputbase.h"


InputBase::InputBase(QWidget* parent)
    : Solo(parent)
{
    connect(this, SIGNAL(soloStateChanged(QString, bool)), this, SLOT(in_widgetBase_soloStateChanged(QString, bool)), Qt::UniqueConnection);
}


// slot
void InputBase::in_widgetBase_soloStateChanged(QString objectName, bool state)
{
    emit attributeChanged(objectName, "Solo", QString::number(static_cast<int>(state)));
}


// setter & getter
InputBase& InputBase::handleFieldSoloStateChanged(int state)
{
    doGlobalSoloChanged(state);
    return *this;
}
InputBase& InputBase::setChannelName(QString name)
{
    mChannelName = name;
    return *this;
}
InputBase& InputBase::setWidgetEnable(bool state)
{
    mEnable = state;
    return *this;
}
InputBase& InputBase::setWidgetEnableWithUpdate(bool state)
{
    if(mEnable != state)
    {
        if(state)
        {
            if(getLinkState())
            {
                if(getSoloState()) in_widgetBase_soloStateChanged(objectName(), true);
            }
            else
            {
                if(getSoloStateLeft()) in_widgetBase_soloStateChanged(objectName(), true);
                if(getSoloStateRight()) in_widgetBase_soloStateChanged(objectName(), true);
            }
        }
        else
        {
            if(getLinkState())
            {
                if(getSoloState()) in_widgetBase_soloStateChanged(objectName(), false);
            }
            else
            {
                if(getSoloStateLeft()) in_widgetBase_soloStateChanged(objectName(), false);
                if(getSoloStateRight()) in_widgetBase_soloStateChanged(objectName(), false);
            }
        }
    }
    mEnable = state;
    if(mEmitAction)
    {
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(mEnable));
    }
    updateAttribute();
    return *this;
}
InputBase& InputBase::setWidgetMovable(bool state)
{
    mMovable = state;
    return *this;
}
InputBase& InputBase::setWidgetReady(bool state)
{
    mReady = state;
    return *this;
}
InputBase& InputBase::setWidgetEmitAction(bool state)
{
    mEmitAction = state;
    return *this;
}


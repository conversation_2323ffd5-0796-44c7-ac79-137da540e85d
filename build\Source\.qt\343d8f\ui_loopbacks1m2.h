/********************************************************************************
** Form generated from reading UI file 'loopbacks1m2.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_LOOPBACKS1M2_H
#define UI_LOOPBACKS1M2_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <pushbuttongroups1m4.h>
#include <pushbuttongroups1m8.h>
#include <toolbuttons1m1.h>
#include <volumemeters1m3.h>
#include <volumemeters1m4.h>
#include <volumemeters1m5.h>
#include <vsliders1m1.h>

QT_BEGIN_NAMESPACE

class Ui_LoopbackS1M2
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    QLineEdit *lineEdit;
    PushButtonGroupS1M4 *widgetPushButtonGroup1;
    VolumeMeterS1M4 *widgetLinkedMeterLeft;
    VolumeMeterS1M5 *widgetLinkedMeterRight;
    VSliderS1M1 *widgetLinkedVSlider;
    ToolButtonS1M1 *widgetToolButton;
    QPushButton *pushButtonLink;
    VSliderS1M1 *widgetUnlinkVSliderLeft;
    VSliderS1M1 *widgetUnlinkVSliderRight;
    VolumeMeterS1M3 *widgetUnlinkMeter;
    PushButtonGroupS1M8 *widgetPushButtonGroup2;
    PushButtonGroupS1M8 *widgetPushButtonGroup3;

    void setupUi(QWidget *LoopbackS1M2)
    {
        if (LoopbackS1M2->objectName().isEmpty())
            LoopbackS1M2->setObjectName("LoopbackS1M2");
        LoopbackS1M2->resize(210, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(LoopbackS1M2->sizePolicy().hasHeightForWidth());
        LoopbackS1M2->setSizePolicy(sizePolicy);
        LoopbackS1M2->setMinimumSize(QSize(80, 220));
        gridLayout = new QGridLayout(LoopbackS1M2);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(LoopbackS1M2);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 21));
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(10, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetPushButtonGroup1 = new PushButtonGroupS1M4(frame);
        widgetPushButtonGroup1->setObjectName("widgetPushButtonGroup1");
        widgetPushButtonGroup1->setGeometry(QRect(10, 230, 51, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup1->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup1->setSizePolicy(sizePolicy);
        widgetPushButtonGroup1->setMinimumSize(QSize(12, 7));
        widgetLinkedMeterLeft = new VolumeMeterS1M4(frame);
        widgetLinkedMeterLeft->setObjectName("widgetLinkedMeterLeft");
        widgetLinkedMeterLeft->setGeometry(QRect(10, 110, 21, 111));
        sizePolicy.setHeightForWidth(widgetLinkedMeterLeft->sizePolicy().hasHeightForWidth());
        widgetLinkedMeterLeft->setSizePolicy(sizePolicy);
        widgetLinkedMeterRight = new VolumeMeterS1M5(frame);
        widgetLinkedMeterRight->setObjectName("widgetLinkedMeterRight");
        widgetLinkedMeterRight->setGeometry(QRect(80, 110, 21, 111));
        sizePolicy.setHeightForWidth(widgetLinkedMeterRight->sizePolicy().hasHeightForWidth());
        widgetLinkedMeterRight->setSizePolicy(sizePolicy);
        widgetLinkedVSlider = new VSliderS1M1(frame);
        widgetLinkedVSlider->setObjectName("widgetLinkedVSlider");
        widgetLinkedVSlider->setGeometry(QRect(40, 110, 31, 111));
        widgetToolButton = new ToolButtonS1M1(frame);
        widgetToolButton->setObjectName("widgetToolButton");
        widgetToolButton->setGeometry(QRect(10, 40, 91, 21));
        pushButtonLink = new QPushButton(frame);
        pushButtonLink->setObjectName("pushButtonLink");
        pushButtonLink->setGeometry(QRect(40, 70, 31, 21));
        widgetUnlinkVSliderLeft = new VSliderS1M1(frame);
        widgetUnlinkVSliderLeft->setObjectName("widgetUnlinkVSliderLeft");
        widgetUnlinkVSliderLeft->setGeometry(QRect(110, 110, 21, 111));
        widgetUnlinkVSliderRight = new VSliderS1M1(frame);
        widgetUnlinkVSliderRight->setObjectName("widgetUnlinkVSliderRight");
        widgetUnlinkVSliderRight->setGeometry(QRect(180, 110, 21, 111));
        widgetUnlinkMeter = new VolumeMeterS1M3(frame);
        widgetUnlinkMeter->setObjectName("widgetUnlinkMeter");
        widgetUnlinkMeter->setGeometry(QRect(140, 110, 31, 111));
        sizePolicy.setHeightForWidth(widgetUnlinkMeter->sizePolicy().hasHeightForWidth());
        widgetUnlinkMeter->setSizePolicy(sizePolicy);
        widgetPushButtonGroup2 = new PushButtonGroupS1M8(frame);
        widgetPushButtonGroup2->setObjectName("widgetPushButtonGroup2");
        widgetPushButtonGroup2->setGeometry(QRect(80, 230, 51, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup2->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup2->setSizePolicy(sizePolicy);
        widgetPushButtonGroup2->setMinimumSize(QSize(8, 7));
        widgetPushButtonGroup3 = new PushButtonGroupS1M8(frame);
        widgetPushButtonGroup3->setObjectName("widgetPushButtonGroup3");
        widgetPushButtonGroup3->setGeometry(QRect(150, 230, 51, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup3->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup3->setSizePolicy(sizePolicy);
        widgetPushButtonGroup3->setMinimumSize(QSize(8, 7));

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(LoopbackS1M2);

        QMetaObject::connectSlotsByName(LoopbackS1M2);
    } // setupUi

    void retranslateUi(QWidget *LoopbackS1M2)
    {
        LoopbackS1M2->setWindowTitle(QCoreApplication::translate("LoopbackS1M2", "Form", nullptr));
        pushButtonClose->setText(QString());
        pushButtonLink->setText(QCoreApplication::translate("LoopbackS1M2", "Link", nullptr));
    } // retranslateUi

};

namespace Ui {
    class LoopbackS1M2: public Ui_LoopbackS1M2 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_LOOPBACKS1M2_H

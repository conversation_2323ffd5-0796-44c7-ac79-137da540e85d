/************************************************************************
 *  The module is a wrapper for properties that describe an UI language.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

 // Module is empty if .h file was not included (category turned off).
#ifdef __WnUiLanguage_h__

 // optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif


WnUiLanguage::WnUiLanguage()
{
}


WnUiLanguage::~WnUiLanguage()
{
}


WnUiLanguage::WnUiLanguage( const WnUiLanguage& src)
{
    *this = src;
}


WnUiLanguage& WnUiLanguage::operator = (const WnUiLanguage& src)
{
    // Self assignment? 
    if (&src == this) return *this;

    mIdentifierStr = src.mIdentifierStr;
    mLanguageDesignator = src.mLanguageDesignator;
    mRegionDesignator = src.mRegionDesignator;
    mScriptDesignator = src.mScriptDesignator;
    mEnglishDescription = src.mEnglishDescription;
    mLocalizedDescription = src.mLocalizedDescription;

    return *this;
}


void WnUiLanguage::Clear()
{
    mIdentifierStr.clear();
    mLanguageDesignator.clear();
    mRegionDesignator.clear();
    mScriptDesignator.clear();
    mEnglishDescription.clear();
    mLocalizedDescription.clear();
}


WNERR WnUiLanguage::SetIdentifierStr( const WString& idStr )
{   
    WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": idStr = '%s'\n"), idStr.c_str()));

    //initialization
    bool parserFinished = false;
    WNERR status = TSTATUS_SUCCESS;
    Clear();
    mIdentifierStr = idStr;

    //The first part of the string is the language designator.
    //Find the first separator, if present. We expect an hyphen
    //or an underscore.
    WString designator;
    size_t idx = 0;
    for (; idx < idStr.length(); ++idx) {
        if ((idStr[idx] == L'-') || (idStr[idx] == L'_'))
        {        
            ++idx;
            break;
        }            
        designator += idStr[idx];
    }    

    if ((designator.length() != 2) && (designator.length() != 3)) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Language identifier '%s' has invalid length %d.\n"), designator.c_str(), designator.length()));
        return TSTATUS_INVALID_FORMAT;
    }

    mLanguageDesignator = designator;
    designator.clear();
    WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Language designator = '%s'\n"), mLanguageDesignator.c_str()));

    //get the next designator, if available
    for (; idx < idStr.length(); ++idx) {
        if ((idStr[idx] == L'-') || (idStr[idx] == L'_'))
        {
            ++idx;
            break;
        }
        designator += idStr[idx];
    }

    //Is it a region or script designator?
    if (designator.length() == 2) {
        //region
        mRegionDesignator = designator;
        designator.clear();
        WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Region designator = '%s'\n"), mRegionDesignator.c_str()));
    }
    else if (designator.length() == 4) {
        //script
        mScriptDesignator = designator;
        designator.clear();
        WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Script designator = '%s'\n"), mScriptDesignator.c_str()));
    }
    else {
        //Maybe this is already an additional information that will be ignored.
        parserFinished = true;
    }

    if (!parserFinished) {

        //get the next designator, if available
        for (; idx < idStr.length(); ++idx) {
            if ((idStr[idx] == L'-') || (idStr[idx] == L'_'))
            {
                ++idx;
                break;
            }
            designator += idStr[idx];
        }

        //Is it a region or script designator?
        if (designator.length() == 2) {
            //region. Is there already one?
            if (mRegionDesignator.length() > 0) {
                if (mRegionDesignator != designator) {
                    WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Two different region designators found in identifier '%s'. This is unexpected.\n"), idStr.c_str()));
                    return TSTATUS_INVALID_FORMAT;
                }
                else {
                    WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Region designator found twice.\n")));
                }
            }
            else {
                mRegionDesignator = designator;
                WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Region designator = '%s'\n"), mRegionDesignator.c_str()));
            }
            designator.clear();
            parserFinished = true;
        }
        else if (designator.length() == 4) {
            //scriptIs there already one?
            if (mScriptDesignator.length() > 0) {
                if (mScriptDesignator != designator) {
                    WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Two different script designators found in identifier '%s'. This is unexpected.\n"), idStr.c_str()));
                    return TSTATUS_INVALID_FORMAT;
                }
                else {
                    WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Script designator found twice.\n")));
                }
            }
            else {
                mScriptDesignator = designator;
                WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Script designator = '%s'\n"), mScriptDesignator.c_str()));
            }
            designator.clear();
            parserFinished = true;
        }
        else {
            //Maybe this is already an additional information that will be ignored.  
            parserFinished = true;
        }
    }

    //get the description for the parsed information, in English and in the current UI language
    status = GetDescription( /*localized*/false, mEnglishDescription);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": GetDescription(English) for identifier '%s' failed with 0x%08X.\n"), idStr.c_str(), status));
        if (status == ERROR_INVALID_PARAMETER) {
            status = TSTATUS_INVALID_PARAMETER;
        }
        return status;
    }

    status = GetDescription( /*localized*/true, mLocalizedDescription);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": GetDescription(localized) for identifier '%s' failed with 0x%08X.\n"), idStr.c_str(), status));
        if (status == ERROR_INVALID_PARAMETER) {
            status = TSTATUS_INVALID_PARAMETER;
        }
        return status;
    }

    //OK
    return TSTATUS_SUCCESS;
}


WNERR WnUiLanguage::GetDescription(bool localized, WString& desc) const
{
    //initialization
    desc.clear();
    WNERR status = TSTATUS_SUCCESS;

    //error check
    if (mLanguageDesignator.length() == 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Language designator not yet set.\n")));
        return TSTATUS_FAILED;
    }

    //create an identifier string for the given UI language information
    WString idStr = mLanguageDesignator;

    if (mScriptDesignator.length() > 0) {
        idStr += L"-";
        idStr += mScriptDesignator;
    }

    if (mRegionDesignator.length() > 0) {
        idStr += L"-";
        idStr += mRegionDesignator;
    }

    //get the required buffer size to retrieve the description
    int res = GetLocaleInfoEx(
        idStr.c_str(),  //_In_opt_  LPCWSTR lpLocaleName,
        (localized ? LOCALE_SLOCALIZEDDISPLAYNAME : LOCALE_SENGLISHDISPLAYNAME),    //_In_      LCTYPE  LCType,
        NULL,           //_Out_opt_ LPWSTR  lpLCData,
        0               //_In_      int     cchData
    );
    if (res == 0) {
        status = GetLastError();
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": GetLocaleInfoEx failed with 0x%08X to get buffer size.\n"), status));
        return status;
    }

    //create the buffer to receive the description
    std::unique_ptr<wchar_t[]> buffer(new wchar_t[res]);

    //get the description
    res = GetLocaleInfoEx(
        idStr.c_str(),  //_In_opt_  LPCWSTR lpLocaleName,
        (localized ? LOCALE_SLOCALIZEDDISPLAYNAME : LOCALE_SENGLISHDISPLAYNAME),    //_In_      LCTYPE  LCType,
        buffer.get(),           //_Out_opt_ LPWSTR  lpLCData,
        res               //_In_      int     cchData
    );
    if (res == 0) {
        status = GetLastError();
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": GetLocaleInfoEx failed with 0x%08X.\n"), status));
        return status;
    }

    desc = buffer.get();
    WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": %s description for '%s' is '%s'.\n"), (localized ? _T("Localized") : _T("English")), idStr.c_str(), desc.c_str()));
    
    //OK
    return TSTATUS_SUCCESS;
}

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguage_h__

/*************************** EOF **************************************/

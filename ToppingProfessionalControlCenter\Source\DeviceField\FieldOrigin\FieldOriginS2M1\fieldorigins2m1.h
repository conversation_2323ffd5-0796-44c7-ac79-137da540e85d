#ifndef FieldOriginS2M1_H
#define FieldOriginS2M1_H


#include <QWidget>
#include <QVector>
#include <QString>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"
#include "fieldoriginbase2.h"


class FieldOriginS2M1 : public FieldOriginBase2, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldOriginS2M1(QWidget* parent=nullptr, QString name="");
    ~FieldOriginS2M1();
    FieldOriginS2M1& setName(QString name);
    FieldOriginS2M1& modifyWidgetList(QVector<OriginBase*> list);
    FieldOriginS2M1& setVisibleListDefault(QVector<OriginBase*> list);
protected:
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    QVector<OriginBase*> mWidgetList;
    QVariantList mVisibleListDefault;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
    void in_widgetList_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FieldOriginS2M1_H


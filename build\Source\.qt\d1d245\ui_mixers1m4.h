/********************************************************************************
** Form generated from reading UI file 'mixers1m4.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MIXERS1M4_H
#define UI_MIXERS1M4_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <dials1m5.h>
#include <pushbuttongroups1m3.h>
#include <pushbuttongroups1m7.h>
#include <volumemeters1m3.h>
#include <volumemeters1m4.h>
#include <volumemeters1m5.h>
#include <vsliders1m1.h>

QT_BEGIN_NAMESPACE

class Ui_MixerS1M4
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    QLineEdit *lineEdit;
    VolumeMeterS1M3 *widgetUnlinkMeter;
    DialS1M5 *widgetDialLeft;
    PushButtonGroupS1M3 *widgetPushButtonGroup1;
    DialS1M5 *widgetDialRight;
    QPushButton *pushButtonLink;
    VSliderS1M1 *widgetUnlinkVSliderRight;
    VSliderS1M1 *widgetUnlinkVSliderLeft;
    PushButtonGroupS1M7 *widgetPushButtonGroup2;
    VolumeMeterS1M4 *widgetLinkedMeterLeft;
    VolumeMeterS1M5 *widgetLinkedMeterRight;
    VSliderS1M1 *widgetLinkedVSlider;
    PushButtonGroupS1M7 *widgetPushButtonGroup3;

    void setupUi(QWidget *MixerS1M4)
    {
        if (MixerS1M4->objectName().isEmpty())
            MixerS1M4->setObjectName("MixerS1M4");
        MixerS1M4->resize(210, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(MixerS1M4->sizePolicy().hasHeightForWidth());
        MixerS1M4->setSizePolicy(sizePolicy);
        MixerS1M4->setMinimumSize(QSize(80, 220));
        gridLayout = new QGridLayout(MixerS1M4);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(MixerS1M4);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 21));
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(10, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetUnlinkMeter = new VolumeMeterS1M3(frame);
        widgetUnlinkMeter->setObjectName("widgetUnlinkMeter");
        widgetUnlinkMeter->setGeometry(QRect(140, 110, 31, 111));
        sizePolicy.setHeightForWidth(widgetUnlinkMeter->sizePolicy().hasHeightForWidth());
        widgetUnlinkMeter->setSizePolicy(sizePolicy);
        widgetDialLeft = new DialS1M5(frame);
        widgetDialLeft->setObjectName("widgetDialLeft");
        widgetDialLeft->setGeometry(QRect(10, 70, 41, 31));
        sizePolicy.setHeightForWidth(widgetDialLeft->sizePolicy().hasHeightForWidth());
        widgetDialLeft->setSizePolicy(sizePolicy);
        widgetDialLeft->setMinimumSize(QSize(0, 0));
        widgetPushButtonGroup1 = new PushButtonGroupS1M3(frame);
        widgetPushButtonGroup1->setObjectName("widgetPushButtonGroup1");
        widgetPushButtonGroup1->setGeometry(QRect(10, 230, 51, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup1->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup1->setSizePolicy(sizePolicy);
        widgetPushButtonGroup1->setMinimumSize(QSize(6, 7));
        widgetDialRight = new DialS1M5(frame);
        widgetDialRight->setObjectName("widgetDialRight");
        widgetDialRight->setGeometry(QRect(60, 70, 41, 31));
        sizePolicy.setHeightForWidth(widgetDialRight->sizePolicy().hasHeightForWidth());
        widgetDialRight->setSizePolicy(sizePolicy);
        widgetDialRight->setMinimumSize(QSize(0, 0));
        pushButtonLink = new QPushButton(frame);
        pushButtonLink->setObjectName("pushButtonLink");
        pushButtonLink->setGeometry(QRect(40, 40, 31, 21));
        widgetUnlinkVSliderRight = new VSliderS1M1(frame);
        widgetUnlinkVSliderRight->setObjectName("widgetUnlinkVSliderRight");
        widgetUnlinkVSliderRight->setGeometry(QRect(180, 110, 21, 111));
        widgetUnlinkVSliderLeft = new VSliderS1M1(frame);
        widgetUnlinkVSliderLeft->setObjectName("widgetUnlinkVSliderLeft");
        widgetUnlinkVSliderLeft->setGeometry(QRect(110, 110, 21, 111));
        widgetPushButtonGroup2 = new PushButtonGroupS1M7(frame);
        widgetPushButtonGroup2->setObjectName("widgetPushButtonGroup2");
        widgetPushButtonGroup2->setGeometry(QRect(80, 230, 51, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup2->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup2->setSizePolicy(sizePolicy);
        widgetPushButtonGroup2->setMinimumSize(QSize(4, 7));
        widgetLinkedMeterLeft = new VolumeMeterS1M4(frame);
        widgetLinkedMeterLeft->setObjectName("widgetLinkedMeterLeft");
        widgetLinkedMeterLeft->setGeometry(QRect(10, 110, 21, 111));
        sizePolicy.setHeightForWidth(widgetLinkedMeterLeft->sizePolicy().hasHeightForWidth());
        widgetLinkedMeterLeft->setSizePolicy(sizePolicy);
        widgetLinkedMeterRight = new VolumeMeterS1M5(frame);
        widgetLinkedMeterRight->setObjectName("widgetLinkedMeterRight");
        widgetLinkedMeterRight->setGeometry(QRect(80, 110, 21, 111));
        sizePolicy.setHeightForWidth(widgetLinkedMeterRight->sizePolicy().hasHeightForWidth());
        widgetLinkedMeterRight->setSizePolicy(sizePolicy);
        widgetLinkedVSlider = new VSliderS1M1(frame);
        widgetLinkedVSlider->setObjectName("widgetLinkedVSlider");
        widgetLinkedVSlider->setGeometry(QRect(40, 110, 31, 111));
        widgetPushButtonGroup3 = new PushButtonGroupS1M7(frame);
        widgetPushButtonGroup3->setObjectName("widgetPushButtonGroup3");
        widgetPushButtonGroup3->setGeometry(QRect(150, 230, 51, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup3->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup3->setSizePolicy(sizePolicy);
        widgetPushButtonGroup3->setMinimumSize(QSize(4, 7));

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(MixerS1M4);

        QMetaObject::connectSlotsByName(MixerS1M4);
    } // setupUi

    void retranslateUi(QWidget *MixerS1M4)
    {
        MixerS1M4->setWindowTitle(QCoreApplication::translate("MixerS1M4", "Form", nullptr));
        pushButtonClose->setText(QString());
        pushButtonLink->setText(QCoreApplication::translate("MixerS1M4", "Link", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MixerS1M4: public Ui_MixerS1M4 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MIXERS1M4_H

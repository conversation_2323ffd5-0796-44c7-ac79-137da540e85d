/************************************************************************
 *  Module:       TUsbAudioMixer.cpp
 *  Description:  TUSBAudio mixer control interface
 *
 *  Runtime Env.: Windows user mode
 *  Author(s):    <PERSON><PERSON>, <PERSON>
 *  Company:      Thesycon GmbH, Germany      http://www.thesycon.de
 ************************************************************************/

 #include "TUsbAudioMixer.h"
 
 #include <math.h>



// ctor
TUsbAudioMixer::TUsbAudioMixer(
    TUsbAudioApiDll& api
    )
    : mDriverApi{api}
{
    static_assert( (ChannelTypeFromPin(AppPlayback) == MIXER_CHANNEL_TYPE_APPLICATION), "enum constant does not match" );
    static_assert( (ChannelTypeFromPin(DeviceInput) == MIXER_CHANNEL_TYPE_DEVICE), "enum constant does not match" );
    static_assert( (ChannelTypeFromPin(AppPlaybackVirt) == MIXER_CHANNEL_TYPE_VIRTUAL), "enum constant does not match" );

    static_assert( (ChannelTypeFromPin(AppRecording) == MIXER_CHANNEL_TYPE_APPLICATION), "enum constant does not match" );
    static_assert( (ChannelTypeFromPin(DeviceOutput) == MIXER_CHANNEL_TYPE_DEVICE), "enum constant does not match" );
    static_assert( (ChannelTypeFromPin(AppRecordingVirt) == MIXER_CHANNEL_TYPE_VIRTUAL), "enum constant does not match" );
}

// dtor
TUsbAudioMixer::~TUsbAudioMixer()
{
    DetachFromDevice();
}


void
TUsbAudioMixer::DetachFromDevice()
{
    if ( mDeviceHandle != TUSBAUDIO_INVALID_HANDLE_VALUE ) {
        SetLevelMetersEnable(false);
        mDeviceHandle = TUSBAUDIO_INVALID_HANDLE_VALUE;
        mInterfaceVersionMajor = 0;
        mInterfaceVersionMinor = 0;
        mChannelCountDeviceOutput = 0;
        mChannelCountDeviceInput = 0;
        mChannelCountAppPlayback = 0;
        mChannelCountAppRecording = 0;
        mChannelCountAppPlaybackVirt = 0;
        mChannelCountAppRecordingVirt = 0;
    }
    FreeLevelMetersBuffer();
}


TSTATUS
TUsbAudioMixer::AttachToDevice(
    TUsbAudioHandle deviceHandle
    )
{
    // make sure we are not already attached
    DetachFromDevice();

    if ( TUSBAUDIO_INVALID_HANDLE_VALUE == deviceHandle ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": Invalid deviceHandle\n")));
        return TSTATUS_INVALID_PARAMETER;
    }

    mDeviceHandle = deviceHandle;

    TSTATUS status = QueryProperties();
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": QueryProperties failed with 0x%08X.\n"), status));
        DetachFromDevice();
        return status;
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::QueryProperties()
{
    TSTATUS status;

    // query interface version
    status = GetInterfaceVersion(mInterfaceVersionMajor, mInterfaceVersionMinor);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetInterfaceVersion failed with 0x%08X.\n"), status));
        return status;
    }

    // check version
    if (mInterfaceVersionMajor != MIXER_MODULE_INTERFACE_VERSION_MJ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": major version mismatch, got %u expected %u\n"), mInterfaceVersionMajor, MIXER_MODULE_INTERFACE_VERSION_MJ));
        return TSTATUS_VERSION_MISMATCH;
    }
    if (mInterfaceVersionMinor < MIXER_MODULE_INTERFACE_VERSION_MN) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": minor version mismatch, got %u expected at least %u\n"), mInterfaceVersionMinor, MIXER_MODULE_INTERFACE_VERSION_MN));
        return TSTATUS_VERSION_MISMATCH;
    }

    // query channel counts
    status = GetChannelCountProperty(MixerProperty_DeviceChannelCountOut, mChannelCountDeviceOutput);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetChannelCountProperty(MixerProperty_DeviceChannelCountOut) failed with 0x%08X.\n"), status));
        return status;
    }
    mChannelCountDeviceOutput = TbMin(mChannelCountDeviceOutput, mMaxChannelCountDeviceOutput);
    status = GetChannelCountProperty(MixerProperty_DeviceChannelCountIn, mChannelCountDeviceInput);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetChannelCountProperty(MixerProperty_DeviceChannelCountIn) failed with 0x%08X.\n"), status));
        return status;
    }
    mChannelCountDeviceInput = TbMin(mChannelCountDeviceInput, mMaxChannelCountDeviceInput);
    status = GetChannelCountProperty(MixerProperty_PlaybackChannelCount, mChannelCountAppPlayback);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetChannelCountProperty(MixerProperty_PlaybackChannelCount) failed with 0x%08X.\n"), status));
        return status;
    }
    mChannelCountAppPlayback = TbMin(mChannelCountAppPlayback, mMaxChannelCountAppPlayback);
    status = GetChannelCountProperty(MixerProperty_RecordChannelCount, mChannelCountAppRecording);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetChannelCountProperty(MixerProperty_RecordChannelCount) failed with 0x%08X.\n"), status));
        return status;
    }
    mChannelCountAppRecording = TbMin(mChannelCountAppRecording, mMaxChannelCountAppRecording);
    status = GetChannelCountProperty(MixerProperty_VirtualPlaybackChannelCount, mChannelCountAppPlaybackVirt);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetChannelCountProperty(MixerProperty_VirtualPlaybackChannelCount) failed with 0x%08X.\n"), status));
        return status;
    }
    mChannelCountAppPlaybackVirt = TbMin(mChannelCountAppPlaybackVirt, mMaxChannelCountAppPlaybackVirt);
    status = GetChannelCountProperty(MixerProperty_VirtualRecordChannelCount, mChannelCountAppRecordingVirt);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetChannelCountProperty(MixerProperty_VirtualRecordChannelCount) failed with 0x%08X.\n"), status));
        return status;
    }
    mChannelCountAppRecordingVirt = TbMin(mChannelCountAppRecordingVirt, mMaxChannelCountAppRecordingVirt);

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::GetInterfaceVersion(
    unsigned int& majorVersion,
    unsigned int& minorVersion
    )
{
    MixerPropData_InterfaceVersion propData;
    TbZeroObject(propData);
    propData.prop.moduleId = MIXER_MODULE_ID;
    propData.prop.propertyId = MixerProperty_InterfaceVersion;

    TSTATUS st = GetDspProperty(&propData, sizeof(propData));
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetDspProperty(MixerProperty_InterfaceVersion) failed with 0x%08X.\n"), st));
        return st;
    }

    majorVersion = propData.majorVersion;
    minorVersion = propData.minorVersion;

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::GetChannelCountProperty(
    MixerProperty propId,
    unsigned int& channelCount
    )
{
    MixerPropData_ChannelCount propData;
    TbZeroObject(propData);
    propData.prop.moduleId = MIXER_MODULE_ID;
    propData.prop.propertyId = propId;

    TSTATUS st = GetDspProperty(&propData, sizeof(propData));
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetDspProperty mDeviceHandle=%u propId=%d failed with 0x%08X.\n"), mDeviceHandle, propId, st));
        return st;
    }

    channelCount = propData.channelCount;

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::SetNodeGain(
    const Node& node,
    Gain gain
    )
{
    if ( !IsInputPin(node.inputChannel.pin) ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid input pin=%d\n"), node.inputChannel.pin));
        return TSTATUS_INVALID_PARAMETER;
    }
    if ( !IsOutputPin(node.outputChannel.pin) ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), node.outputChannel.pin));
        return TSTATUS_INVALID_PARAMETER;
    }

    MixerPropData_Weight propData;
    TbZeroObject(propData);
    propData.prop.moduleId = MIXER_MODULE_ID;
    propData.prop.propertyId = MixerProperty_MixerWeight;
    propData.weightsArray[0].address = MixerAddressFromNode(node);
    propData.weightsArray[0].weight = gain;

    TSTATUS st = SetDspProperty(&propData, sizeof(propData));
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": SetDspProperty(MixerProperty_MixerWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::GetNodeGain(
    const Node& node,
    Gain& gain
    )
{
    gain = Gain_Zero;

    if ( !IsInputPin(node.inputChannel.pin) ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid input pin=%d\n"), node.inputChannel.pin));
        return TSTATUS_INVALID_PARAMETER;
    }
    if ( !IsOutputPin(node.outputChannel.pin) ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), node.outputChannel.pin));
        return TSTATUS_INVALID_PARAMETER;
    }

    MixerPropData_Weight propData;
    TbZeroObject(propData);
    propData.prop.moduleId = MIXER_MODULE_ID;
    propData.prop.propertyId = MixerProperty_MixerWeight;
    propData.weightsArray[0].address = MixerAddressFromNode(node);

    TSTATUS st = GetDspProperty(&propData, sizeof(propData));
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetDspProperty(MixerProperty_MixerWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    gain = propData.weightsArray[0].weight;

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::SetNodesGain(
    const std::vector<Node>& nodes,
    Gain gain
    )
{
    size_t dataSize = (sizeof(MixerPropData_Weight) - sizeof(MixerWeight)) + (nodes.size() * sizeof(MixerWeight));
    std::vector<unsigned char> buffer(dataSize);
    MixerPropData_Weight* propData = reinterpret_cast<MixerPropData_Weight*>(buffer.data());

    propData->prop.moduleId = MIXER_MODULE_ID;
    propData->prop.propertyId = MixerProperty_MixerWeight;

    for ( size_t i = 0; i < nodes.size(); i++ ) {
        const Node& node = nodes[i];

        if ( !IsInputPin(node.inputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid input pin=%d\n"), node.inputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }
        if ( !IsOutputPin(node.outputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), node.outputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }

        propData->weightsArray[i].address = MixerAddressFromNode(node);
        propData->weightsArray[i].weight = gain;
    }

    TSTATUS st = SetDspProperty(propData, dataSize);
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": SetDspProperty(MixerProperty_MixerWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::SetNodeGains(
    const std::vector<Node>& nodes,
    const std::vector<Gain>& gains
    )
{
    if ( gains.size() < nodes.size() ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": gains.size %zu smaller than nodes.size %zu\n"), gains.size(), nodes.size() ));
        return TSTATUS_INVALID_PARAMETER;
    }

    size_t dataSize = (sizeof(MixerPropData_Weight) - sizeof(MixerWeight)) + (nodes.size() * sizeof(MixerWeight));
    std::vector<unsigned char> buffer(dataSize);
    MixerPropData_Weight* propData = reinterpret_cast<MixerPropData_Weight*>(buffer.data());

    propData->prop.moduleId = MIXER_MODULE_ID;
    propData->prop.propertyId = MixerProperty_MixerWeight;

    for ( size_t i = 0; i < nodes.size(); i++ ) {
        const Node& node = nodes[i];

        if ( !IsInputPin(node.inputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid input pin=%d\n"), node.inputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }
        if ( !IsOutputPin(node.outputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), node.outputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }

        propData->weightsArray[i].address = MixerAddressFromNode(node);
        propData->weightsArray[i].weight = gains[i];
    }

    TSTATUS st = SetDspProperty(propData, dataSize);
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": SetDspProperty(MixerProperty_MixerWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::SetNodeGains(
    const std::vector<NodeGain>& nodeGains
    )
{
    size_t dataSize = (sizeof(MixerPropData_Weight) - sizeof(MixerWeight)) + (nodeGains.size() * sizeof(MixerWeight));
    std::vector<unsigned char> buffer(dataSize);
    MixerPropData_Weight* propData = reinterpret_cast<MixerPropData_Weight*>(buffer.data());

    propData->prop.moduleId = MIXER_MODULE_ID;
    propData->prop.propertyId = MixerProperty_MixerWeight;

    for ( size_t i = 0; i < nodeGains.size(); i++ ) {
        const NodeGain& ng = nodeGains[i];
        
        if ( !IsInputPin(ng.node.inputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid input pin=%d\n"), ng.node.inputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }
        if ( !IsOutputPin(ng.node.outputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), ng.node.outputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }

        propData->weightsArray[i].address = MixerAddressFromNode(ng.node);
        propData->weightsArray[i].weight = ng.gain;
    }

    TSTATUS st = SetDspProperty(propData, dataSize);
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": SetDspProperty(MixerProperty_MixerWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    return TSTATUS_SUCCESS;

}


TSTATUS
TUsbAudioMixer::GetNodeGains(
    const std::vector<Node>& nodes,
    std::vector<Gain>& gains
    )
{
    size_t dataSize = (sizeof(MixerPropData_Weight) - sizeof(MixerWeight)) + (nodes.size() * sizeof(MixerWeight));
    std::vector<unsigned char> buffer(dataSize);
    MixerPropData_Weight* propData = reinterpret_cast<MixerPropData_Weight*>(buffer.data());

    propData->prop.moduleId = MIXER_MODULE_ID;
    propData->prop.propertyId = MixerProperty_MixerWeight;

    for ( size_t i = 0; i < nodes.size(); i++ ) {
        const Node& node = nodes[i];

        if ( !IsInputPin(node.inputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid input pin=%d\n"), node.inputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }
        if ( !IsOutputPin(node.outputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), node.outputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }

        propData->weightsArray[i].address = MixerAddressFromNode(node);
    }

    TSTATUS st = GetDspProperty(propData, dataSize);
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetDspProperty(MixerProperty_MixerWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    gains.reserve(nodes.size());
    for ( size_t i = 0; i < nodes.size(); i++ ) {
        gains.push_back(propData->weightsArray[i].weight);
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::GetNodeGains(
    std::vector<NodeGain>& nodeGains
    )
{
    size_t dataSize = (sizeof(MixerPropData_Weight) - sizeof(MixerWeight)) + (nodeGains.size() * sizeof(MixerWeight));
    std::vector<unsigned char> buffer(dataSize);
    MixerPropData_Weight* propData = reinterpret_cast<MixerPropData_Weight*>(buffer.data());

    propData->prop.moduleId = MIXER_MODULE_ID;
    propData->prop.propertyId = MixerProperty_MixerWeight;

    for ( size_t i = 0; i < nodeGains.size(); i++ ) {
        const Node& node = nodeGains[i].node;

        if ( !IsInputPin(node.inputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid input pin=%d\n"), node.inputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }
        if ( !IsOutputPin(node.outputChannel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), node.outputChannel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }

        propData->weightsArray[i].address = MixerAddressFromNode(node);
    }

    TSTATUS st = GetDspProperty(propData, dataSize);
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetDspProperty(MixerProperty_MixerWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    for ( size_t i = 0; i < nodeGains.size(); i++ ) {
        nodeGains[i].gain = propData->weightsArray[i].weight;
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::SetOutputGain(
    Channel outputChannel,
    Gain gain
    )
{
    if ( !IsOutputPin(outputChannel.pin) ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), outputChannel.pin));
        return TSTATUS_INVALID_PARAMETER;
    }
    
    MixerPropData_LineWeight propData;
    TbZeroObject(propData);
    propData.prop.moduleId = MIXER_MODULE_ID;
    propData.prop.propertyId = MixerProperty_MixerLineWeight;
    propData.weightsArray[0].address = MixerLineAddressFromChannel(outputChannel);
    propData.weightsArray[0].weight = gain;

    TSTATUS st = SetDspProperty(&propData, sizeof(propData));
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": SetDspProperty(MixerProperty_MixerLineWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::GetOutputGain(
    Channel outputChannel,
    Gain& gain
    )
{
    gain = Gain_Zero;

    if ( !IsOutputPin(outputChannel.pin) ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), outputChannel.pin));
        return TSTATUS_INVALID_PARAMETER;
    }

    MixerPropData_LineWeight propData;
    TbZeroObject(propData);
    propData.prop.moduleId = MIXER_MODULE_ID;
    propData.prop.propertyId = MixerProperty_MixerLineWeight;
    propData.weightsArray[0].address = MixerLineAddressFromChannel(outputChannel);

    TSTATUS st = GetDspProperty(&propData, sizeof(propData));
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetDspProperty(MixerProperty_MixerLineWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    gain = propData.weightsArray[0].weight;

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::SetOutputsGain(
    const std::vector<Channel>& outputChannels,
    Gain gain
    )
{
    size_t dataSize = (sizeof(MixerPropData_LineWeight) - sizeof(MixerLineWeight)) + (outputChannels.size() * sizeof(MixerLineWeight));
    std::vector<unsigned char> buffer(dataSize);
    MixerPropData_LineWeight* propData = reinterpret_cast<MixerPropData_LineWeight*>(buffer.data());

    propData->prop.moduleId = MIXER_MODULE_ID;
    propData->prop.propertyId = MixerProperty_MixerLineWeight;

    for ( size_t i = 0; i < outputChannels.size(); i++ ) {
        const Channel& channel = outputChannels[i];

        if ( !IsOutputPin(channel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), channel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }

        propData->weightsArray[i].address = MixerLineAddressFromChannel(channel);
        propData->weightsArray[i].weight = gain;
    }

    TSTATUS st = SetDspProperty(propData, dataSize);
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": SetDspProperty(MixerProperty_MixerLineWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::SetOutputGains(
    const std::vector<Channel>& outputChannels,
    const std::vector<Gain>& gains
    )
{
    if ( gains.size() < outputChannels.size() ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": gains.size %zu smaller than outputs.size %zu\n"), gains.size(), outputChannels.size() ));
        return TSTATUS_INVALID_PARAMETER;
    }

    size_t dataSize = (sizeof(MixerPropData_LineWeight) - sizeof(MixerLineWeight)) + (outputChannels.size() * sizeof(MixerLineWeight));
    std::vector<unsigned char> buffer(dataSize);
    MixerPropData_LineWeight* propData = reinterpret_cast<MixerPropData_LineWeight*>(buffer.data());

    propData->prop.moduleId = MIXER_MODULE_ID;
    propData->prop.propertyId = MixerProperty_MixerLineWeight;

    for ( size_t i = 0; i < outputChannels.size(); i++ ) {
        const Channel& channel = outputChannels[i];

        if ( !IsOutputPin(channel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), channel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }

        propData->weightsArray[i].address = MixerLineAddressFromChannel(channel);
        propData->weightsArray[i].weight = gains[i];
    }

    TSTATUS st = SetDspProperty(propData, dataSize);
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": SetDspProperty(MixerProperty_MixerLineWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::SetOutputGains(
    const std::vector<ChannelGain>& outputChannelGains
    )
{
    size_t dataSize = (sizeof(MixerPropData_LineWeight) - sizeof(MixerLineWeight)) + (outputChannelGains.size() * sizeof(MixerLineWeight));
    std::vector<unsigned char> buffer(dataSize);
    MixerPropData_LineWeight* propData = reinterpret_cast<MixerPropData_LineWeight*>(buffer.data());

    propData->prop.moduleId = MIXER_MODULE_ID;
    propData->prop.propertyId = MixerProperty_MixerLineWeight;

    for ( size_t i = 0; i < outputChannelGains.size(); i++ ) {
        const ChannelGain& cg = outputChannelGains[i];

        if ( !IsOutputPin(cg.channel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), cg.channel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }

        propData->weightsArray[i].address = MixerLineAddressFromChannel(cg.channel);
        propData->weightsArray[i].weight = cg.gain;
    }

    TSTATUS st = SetDspProperty(propData, dataSize);
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": SetDspProperty(MixerProperty_MixerLineWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::GetOutputGains(
    const std::vector<Channel>& outputChannels,
    std::vector<Gain>& gains
    )
{
    size_t dataSize = (sizeof(MixerPropData_LineWeight) - sizeof(MixerLineWeight)) + (outputChannels.size() * sizeof(MixerLineWeight));
    std::vector<unsigned char> buffer(dataSize);
    MixerPropData_LineWeight* propData = reinterpret_cast<MixerPropData_LineWeight*>(buffer.data());

    propData->prop.moduleId = MIXER_MODULE_ID;
    propData->prop.propertyId = MixerProperty_MixerLineWeight;

    for ( size_t i = 0; i < outputChannels.size(); i++ ) {
        const Channel& channel = outputChannels[i];

        if ( !IsOutputPin(channel.pin) ) {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), channel.pin));
            return TSTATUS_INVALID_PARAMETER;
        }

        propData->weightsArray[i].address = MixerLineAddressFromChannel(channel);
    }

    TSTATUS st = GetDspProperty(propData, dataSize);
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetDspProperty(MixerProperty_MixerLineWeight) failed with 0x%08X.\n"), st));
        return st;
    }

    gains.reserve(outputChannels.size());
    for ( size_t i = 0; i < outputChannels.size(); i++ ) {
        gains.push_back(propData->weightsArray[i].weight);
    }

    return TSTATUS_SUCCESS;
}


TSTATUS
TUsbAudioMixer::SetLevelMetersEnable(bool enable)
{
    // reset state
    mLevelMetersEnabled = false;

    MixerPropData_LevelMeterEnable propData;
    TbZeroObject(propData);
    propData.prop.moduleId = MIXER_MODULE_ID;
    propData.prop.propertyId = MixerProperty_LevelMeterEnable;
    propData.enable = enable ? 1 : 0;

    TSTATUS st = SetDspProperty(&propData, sizeof(propData));
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": SetDspProperty(MixerProperty_LevelMeterEnable) failed with 0x%08X.\n"), st));
        return st;
    }

    // new state
    mLevelMetersEnabled = enable;

    return TSTATUS_SUCCESS;
}



TSTATUS
TUsbAudioMixer::EnableLevelMeters()
{
    AllocLevelMetersBuffer();
    TSTATUS st = SetLevelMetersEnable(true);
    return st;
}

TSTATUS
TUsbAudioMixer::DisableLevelMeters()
{
    TSTATUS st = SetLevelMetersEnable(false);
    FreeLevelMetersBuffer();
    return st;
}


TSTATUS
TUsbAudioMixer::UpdateLevelMeterData()
{
    if ( !(mLevelMetersEnabled && mLevelMetersData.size() > 0) ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": level meters are not enabled\n") ));
        return TSTATUS_NOT_ENABLED;        
    }

    TSTATUS st = GetDspProperty(mLevelMetersData.data(), mLevelMetersData.size());
    if (st != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": GetDspProperty failed with 0x%08X.\n"), st));
        return st;
    }

    return TSTATUS_SUCCESS;
}


void
TUsbAudioMixer::FreeLevelMetersBuffer()
{
    mLevelMetersData.clear();
}


void
TUsbAudioMixer::AllocLevelMetersBuffer()
{
    size_t itemCount = mChannelCountAppPlayback +
                       mChannelCountDeviceInput +
                       mChannelCountAppPlaybackVirt +
                       mChannelCountDeviceOutput +
                       mChannelCountAppRecording +
                       mChannelCountAppRecordingVirt ;

    size_t dataSize = (sizeof(MixerPropData_LevelMeterValues) - sizeof(LevelMeterValue)) + (itemCount * sizeof(LevelMeterValue) );

    mLevelMetersData.clear();
    mLevelMetersData.resize(dataSize);
    
    MixerPropData_LevelMeterValues* lmv = reinterpret_cast<MixerPropData_LevelMeterValues*>(mLevelMetersData.data());
    
    lmv->prop.moduleId = MIXER_MODULE_ID;
    lmv->prop.propertyId = MixerProperty_LevelMeterValues;

    LevelMeterValue* val = lmv->valuesArray;

    // mixer inputs
    for ( unsigned int i = 0; i < mChannelCountAppPlayback; i++ ) {
        val->direction = 0;
        val->channelType = MIXER_CHANNEL_TYPE_APPLICATION;
        val->channelIndex = static_cast<unsigned char>(i);
        val++;
    }
    for ( unsigned int i = 0; i < mChannelCountDeviceInput; i++ ) {
        val->direction = 0;
        val->channelType = MIXER_CHANNEL_TYPE_DEVICE;
        val->channelIndex = static_cast<unsigned char>(i);
        val++;
    }
    for ( unsigned int i = 0; i < mChannelCountAppPlaybackVirt; i++ ) {
        val->direction = 0;
        val->channelType = MIXER_CHANNEL_TYPE_VIRTUAL;
        val->channelIndex = static_cast<unsigned char>(i);
        val++;
    }

    // mixer outputs
    for ( unsigned int i = 0; i < mChannelCountDeviceOutput; i++ ) {
        val->direction = 1;
        val->channelType = MIXER_CHANNEL_TYPE_DEVICE;
        val->channelIndex = static_cast<unsigned char>(i);
        val++;
    }
    for ( unsigned int i = 0; i < mChannelCountAppRecording; i++ ) {
        val->direction = 1;
        val->channelType = MIXER_CHANNEL_TYPE_APPLICATION;
        val->channelIndex = static_cast<unsigned char>(i);
        val++;
    }
    for ( unsigned int i = 0; i < mChannelCountAppRecordingVirt; i++ ) {
        val->direction = 1;
        val->channelType = MIXER_CHANNEL_TYPE_VIRTUAL;
        val->channelIndex = static_cast<unsigned char>(i);
        val++;
    }
}


inline
void
TUsbAudioMixer::GetLevelDataArray(
    LevelDataArray& lda, 
    Pin pin
    )
{
    lda.val = nullptr;
    lda.count = 0;

    if ( mLevelMetersEnabled && mLevelMetersData.size() > 0 ) {

        const MixerPropData_LevelMeterValues* lmv = reinterpret_cast<MixerPropData_LevelMeterValues*>(mLevelMetersData.data());
        unsigned int outputsOffset = mChannelCountAppPlayback + mChannelCountDeviceInput + mChannelCountAppPlaybackVirt;

        switch ( pin ) {

            case AppPlayback:
                lda.val = &lmv->valuesArray[0];
                lda.count = mChannelCountAppPlayback;
                break;
            case DeviceInput:
                lda.val = &lmv->valuesArray[mChannelCountAppPlayback];
                lda.count = mChannelCountDeviceInput;
                break;
            case AppPlaybackVirt:
                lda.val = &lmv->valuesArray[mChannelCountAppPlayback + mChannelCountDeviceInput];
                lda.count = mChannelCountAppPlaybackVirt;
                break;

            case DeviceOutput:
                lda.val = &lmv->valuesArray[outputsOffset];
                lda.count = mChannelCountDeviceOutput;
                break;
            case AppRecording:
                lda.val = &lmv->valuesArray[outputsOffset + mChannelCountDeviceOutput];
                lda.count = mChannelCountAppRecording;
                break;
            case AppRecordingVirt:
                lda.val = &lmv->valuesArray[outputsOffset + mChannelCountDeviceOutput + mChannelCountAppRecording];
                lda.count = mChannelCountAppRecordingVirt;
                break;

            default:
                WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid pin %d\n"), pin));
                break;
        }
    }
}


TUsbAudioMixer::Level
TUsbAudioMixer::QueryLevelMeter(
    Channel channel
    )
{
    LevelDataArray lda;
    GetLevelDataArray(lda, channel.pin);

    if ( lda.val != nullptr ) {
        if ( channel.index < lda.count ) {
            return lda.val[channel.index].value;
        } else {
            WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid index %d\n"), channel.index));
        }
    }

    return Level_Min;
}


std::vector<TUsbAudioMixer::Level>
TUsbAudioMixer::QueryLevelMeters(
    Pin pin
    )
{
    std::vector<Level> levels;

    LevelDataArray lda;
    GetLevelDataArray(lda, pin);

    if ( lda.val != nullptr ) {
        levels.reserve(lda.count);
        for ( unsigned int i = 0; i < lda.count; i++ ) {
            levels.push_back(lda.val[i].value);
        }
    }
    
    return levels;
}


unsigned int
TUsbAudioMixer::GetChannelCount(Pin pin) const
{
    switch ( pin ) {
    case AppPlayback:
        return mChannelCountAppPlayback;
    case DeviceInput:
        return mChannelCountDeviceInput;
    case AppPlaybackVirt:
        return mChannelCountAppPlaybackVirt;
    case AppRecording:
        return mChannelCountAppRecording;
    case DeviceOutput:
        return mChannelCountDeviceOutput;
    case AppRecordingVirt:
        return mChannelCountAppRecordingVirt;
    default:
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid pin=%d\n"), pin));
        break;
    }

    return 0;
}


std::vector<TUsbAudioMixer::Channel>
TUsbAudioMixer::GetChannels(Pin pin) const
{
    std::vector<Channel> channels;

    unsigned int count = GetChannelCount(pin);
    if ( count > 0 ) {
        channels.reserve(count);
        for ( unsigned int i = 0; i < count; i++ ) {
            channels.push_back( Channel{pin, i} );
        }
    }

    return channels;
}


std::vector<TUsbAudioMixer::Node>
TUsbAudioMixer::GetNodes(Pin inputPin, Pin outputPin) const
{
    std::vector<Node> nodes;

    if ( !IsInputPin(inputPin) ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid input pin=%d\n"), inputPin));
    } else if ( !IsOutputPin(outputPin) ) {
        WNTRACE(TRCERR,tprint(_T(__FUNCTION__) _T(": invalid output pin=%d\n"), outputPin));
    } else {
        unsigned int inputCount = GetChannelCount(inputPin);
        unsigned int outputCount = GetChannelCount(outputPin);
        if ( inputCount > 0 && outputCount > 0 ) {
            nodes.reserve(inputCount * outputCount);
            for ( unsigned int i = 0; i < outputCount; i++ ) {
                for ( unsigned int j = 0; j < inputCount; j++ ) {
                    nodes.push_back( Node{inputPin, j, outputPin, i} );
                }
            }
        }
    }

    return nodes;
}



//static
double
TUsbAudioMixer::GainToLog(
    Gain gain
    )
{
    double x = gain;
    return ( log10(x / Gain_OneDotZero) * 20.0 );
}


//static
TUsbAudioMixer::Gain
TUsbAudioMixer::LogToGain(
    double dB,
    double minusInf
    )
{
    if ( dB <= minusInf ) {
        return Gain_Zero;
    }

    constexpr double TenDotZero = 10.0;
    double g = pow(TenDotZero, dB/20.0) * Gain_OneDotZero;
    return static_cast<Gain>(g);
}



/***************************** EOF **************************************/

#ifndef VOLUMEMETERS1M1_H
#define VOLUMEMETERS1M1_H


#include <QFont>
#include <QRect>
#include <QTimer>
#include <QWidget>
#include <QPainter>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QResizeEvent>


class VolumeMeterS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit VolumeMeterS1M1(QWidget* parent=nullptr);
    ~VolumeMeterS1M1();
    void setFont(QFont font);
    void setColorBG(QColor color);
    void setValue(int value);
    void setMeterClear();
    void setMeterSlip();
    void setWidthRatio(int space1, int meter, int space2, int scale);
    void setHeightRatio(int text, int space1, int clip, int space2, int volume, int space3);
    void setScaleLineHidden(bool hidden=true);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
    void mouseDoubleClickEvent(QMouseEvent* e) override;
private:
    struct RectMeter
    {
        int volumeValue=-60;
        int volumeMax=-60;
        bool clipStatus=false;
        QRect text;
        QRect clip;
        QRect volume;
        QTimer timerText;
        QTimer timerClip;
    };
    QTimer mTimerMeter;
    RectMeter mRectMeter;
    QRect mRectScale;
    QFont mFont;
    QColor mColorBG=QColor(22, 22, 22);
    int mSpace1=10;
    int mMeter=34;
    int mSpace2=10;
    int mScale=46;
    int mHText=10;
    int mHSpace1=2;
    int mHClip=2;
    int mHSpace2=1;
    int mHVolume=83;
    int mHSpace3=2;
    bool mScaleLineHidden=false;
    void drawBG(QPainter* painter);
    void drawElement(QPainter* painter);
private slots:
    void in_timerText_timeout();
    void in_timerClip_timeout();
    void in_mTimerMeter_timeout();
};


#endif // VOLUMEMETERS1M1_H


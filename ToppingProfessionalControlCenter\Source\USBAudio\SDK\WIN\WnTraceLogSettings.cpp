/************************************************************************
 *
 *  Module:       WnTraceLogSettings.cpp
 *
 *  Description:  Trace and log settings
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    Udo <PERSON>hardt,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

// Module is empty if .h file was not included (category turned off).
#ifdef __WnTraceLogSettings_h__

#include "WnStringUtils_impl.h"

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

const TCHAR sTraceFilePathRegistryName[] = TEXT("TraceFilePath");
const TCHAR sTraceFileEnableRegistryName[] = TEXT("TraceFileEnable");
const TCHAR sTraceFileAppendRegistryName[] = TEXT("TraceFileAppend");

#ifndef UNDER_CE
const TCHAR sDefaultTraceFilePath[] = TEXT("%TEMP%");
#endif


// ctor
WnTraceLogSettings::WnTraceLogSettings(
    HMODULE hModule
    )
{
    mModuleHandle = hModule;
}


WNERR
WnTraceLogSettings::LoadSettingsFromRegistry(
    WnTraceLogContext& traceLogContext,
    HKEY registryRootKey,
    const TCHAR* registryPath
    )
{
    WNERR returnStatus = ERROR_SUCCESS;
    WNERR err;

    if ( !mModuleFileName.IsValid() ) {
        err = mModuleFileName.InitWithModuleHandle(mModuleHandle);
        if ( !SUCC(err) ) {
            // failed
            return err;
        }
    }

    // by default set prefix to module name
    traceLogContext.SetPrefix(mModuleFileName.GetBaseName());

    TCHAR traceFile[MAX_PATH];

#ifdef UNDER_CE
    // path is fixed
    TbStringNCopyToArray(traceFile, TEXT("\\"));
#else
    // default path
    TbStringNCopyToArray(traceFile, sDefaultTraceFilePath);
#endif

    bool enableTraceFile = traceLogContext.GetTraceFileEnable();
    bool appendTraceFile = traceLogContext.GetTraceFileAppend();

    TCHAR regKeyName[512];
    TCHAR keyName[256];

    GetDebugRegistryKey(traceLogContext, registryPath, mModuleFileName, regKeyName,TB_ARRAY_ELEMENTS(regKeyName));

    WnRegistryKey key;

    err = key.OpenKey(registryRootKey, regKeyName, KEY_READ);
    if ( SUCC(err) ) {
        // registry opened
        unsigned int mask = traceLogContext.GetMask();

        for (int i=0; i<32; i++) {
            const char* desc = traceLogContext.GetTraceBitDescription(i);
            if ( 0 != TbStringLen(desc) ) {
                WnStringPrintf_impl(keyName,TB_ARRAY_ELEMENTS(keyName), TEXT("%02u: %hs"), i, desc);
            } else {
                // no description
                WnStringPrintf_impl(keyName,TB_ARRAY_ELEMENTS(keyName), TEXT("%02u"), i);
            }

            DWORD val = 0;
            err = key.QueryDword(keyName,val);
            if ( SUCC(err) ) {
                // got value for this bit
                if (val) {
                    // set bit
                    mask |= TRCBITMASK(i);
                } else {
                    // clear bit
                    mask &= ~TRCBITMASK(i);
                }
            } else {
                // failed to query value
                if ( SUCC(returnStatus) ) returnStatus = err;
            }
        }

        traceLogContext.SetMask(mask);

#ifndef UNDER_CE
        // read path from registry
        err = key.QueryString(sTraceFilePathRegistryName, traceFile, TB_ARRAY_ELEMENTS(traceFile));
        if ( !SUCC(err) ) {
            if ( SUCC(returnStatus) ) returnStatus = err;
        }
#endif

        DWORD val;
        err = key.QueryDword(sTraceFileEnableRegistryName, val);
        if ( SUCC(err) ) {
            enableTraceFile = (val!=0);
        } else {
            if ( SUCC(returnStatus) ) returnStatus = err;
        }

        err = key.QueryDword(sTraceFileAppendRegistryName, val);
        if ( SUCC(err) ) {
            appendTraceFile = (val!=0);
        } else {
            if ( SUCC(returnStatus) ) returnStatus = err;
        }

    } else {
        // failed to open key
        returnStatus = err;
    }

    // append \ if not already present
    unsigned int len = TbStringLen(traceFile);
    if ( len > 0 && traceFile[len-1] != TEXT('\\') ) {
        TbStringNCatToArray(traceFile,TEXT("\\"));
    }
    // default trace file name = modulename.log
    TbStringNCatToArray(traceFile,mModuleFileName.GetBaseName());
    TbStringNCatToArray(traceFile,TEXT(".log"));

    // trace file settings
    traceLogContext.SetTraceFile(traceFile);
    traceLogContext.SetTraceFileAppend(appendTraceFile);
    traceLogContext.SetTraceFileEnable(enableTraceFile);

#ifdef UNDER_CE
    // enable timestamps
    traceLogContext.SetTimePrefixEnable(true);
#endif

    if ( SUCC(returnStatus) ) {
        // trace context is initialized now, print info
        WNTRACE(TRCINF,
            tprintf("\n");
            tprint(TEXT("*** %s%s - Trace Module init ***\n"),
                mModuleFileName.GetBaseName(), mModuleFileName.GetExtension()
                );
            tprint(TEXT("Trace settings have been read from %s\\%s\n"),
                (HKEY_LOCAL_MACHINE==registryRootKey) ? TEXT("HKEY_LOCAL_MACHINE") : TEXT("HKEY_CURRENT_USER"),
                regKeyName
                )
        );
    }

    return returnStatus;

} //LoadSettingsFromRegistry



WNERR
WnTraceLogSettings::SaveSettingsToRegistry(
    WnTraceLogContext& traceLogContext,
    HKEY registryRootKey,
    const TCHAR* registryPath
    )
{
    WNERR returnStatus = ERROR_SUCCESS;
    WNERR err;

    if ( !mModuleFileName.IsValid() ) {
        err = mModuleFileName.InitWithModuleHandle(mModuleHandle);
        if ( !SUCC(err) ) {
            // failed
            return err;
        }
    }

    TCHAR regKeyName[512];
    TCHAR keyName[256];

    GetDebugRegistryKey(traceLogContext, registryPath, mModuleFileName, regKeyName,TB_ARRAY_ELEMENTS(regKeyName));

    WnRegistryKey key;

    // create registry sub key if not exists
    for (;;) {
        err = key.CreateKey(registryRootKey, regKeyName, KEY_READ|KEY_WRITE);
        if ( !SUCC(err) ) {
            // failed
            return err;
        }

        // delete all values of the key
        err = key.EnumValue(0,keyName,TB_ARRAY_ELEMENTS(keyName));
        if ( !SUCC(err) ) {
            if (err == ERROR_NO_MORE_ITEMS) {
                // done
                break;
            }
            // failed
            return err;
        }
        err = key.DeleteValue(keyName);
        if ( !SUCC(err) ) {
            // failed
            return err;
        }

        key.Close();
    }

    unsigned int mask = traceLogContext.GetMask();

    // save trace bits
    for (int i=0; i<32; i++) {
        const char* desc = traceLogContext.GetTraceBitDescription(i);
        if ( 0 != TbStringLen(desc) ) {
            WnStringPrintf_impl(keyName,TB_ARRAY_ELEMENTS(keyName), TEXT("%02u: %hs"), i, desc);
        } else {
            // no description
            WnStringPrintf_impl(keyName,TB_ARRAY_ELEMENTS(keyName), TEXT("%02u"), i);
        }

        err = key.SetDword(keyName,(TRCBITMASK(i)&mask) ? 1 : 0);
        if ( !SUCC(err) ) {
            if ( SUCC(returnStatus) ) returnStatus = err;
            // abort on error
            break;
        }
    }

    // save trace file settings
#ifdef UNDER_CE
    // fixed values under Windows CE
#else
    // split filename in components
    TCHAR drive[_MAX_DRIVE] = {0};
    TCHAR path[_MAX_DIR] = {0};
    TCHAR fname[_MAX_FNAME] = {0};
    TCHAR ext[_MAX_EXT] = {0};
    _tsplitpath_s(
        traceLogContext.GetTraceFile(), // in
        drive,
        path,
        fname,
        ext
        );

    // make path only, ignore error
    TCHAR traceFilePath[MAX_PATH] = {0};
    _tmakepath_s(
        traceFilePath,
        drive,
        path,
        NULL,
        NULL
        );

    err = key.SetString(sTraceFilePathRegistryName, traceFilePath, REG_EXPAND_SZ);
    if ( !SUCC(err) ) {
        if ( SUCC(returnStatus) ) returnStatus = err;
    }

    err = key.SetDword(sTraceFileEnableRegistryName, traceLogContext.GetTraceFileEnable() ? 1 : 0);
    if ( !SUCC(err) ) {
        if ( SUCC(returnStatus) ) returnStatus = err;
    }

    err = key.SetDword(sTraceFileAppendRegistryName, traceLogContext.GetTraceFileAppend() ? 1 : 0);
    if ( !SUCC(err) ) {
        if ( SUCC(returnStatus) ) returnStatus = err;
    }

    if ( SUCC(returnStatus) ) {
        // print info
        WNTRACE(TRCINF,
            tprintf("\n");
            tprint(TEXT("*** %s%s - Trace Module init ***\n"),
                mModuleFileName.GetBaseName(), mModuleFileName.GetExtension()
                );
            tprint(TEXT("Trace settings have been written to %s\\%s\n"),
                (HKEY_LOCAL_MACHINE==registryRootKey) ? TEXT("HKEY_LOCAL_MACHINE") : TEXT("HKEY_CURRENT_USER"),
                regKeyName
                )
        );
    }
#endif

    return returnStatus;

} //SaveSettingsToRegistry



void
WnTraceLogSettings::GetDebugRegistryKey(
    WnTraceLogContext& traceLogContext,
    const TCHAR* registryPath,
    WnModuleFileName& moduleFileName,
    TCHAR* keyName,
    unsigned int maxChars
    )
{
    WnStringPrintf_impl(keyName, maxChars,
              TEXT("%s\\%s%s\\%s"),
              registryPath,
              moduleFileName.GetBaseName(),
              moduleFileName.GetExtension(),
              traceLogContext.GetContextName()
              );
}

#ifdef LIBWN_NAMESPACE
}
#endif

#endif //__WnTraceLogSettings_h__

/*************************** EOF **************************************/

#ifndef SOLO_H
#define SOLO_H


#include <QWidget>
#include <QString>


class Solo : public QWidget
{
    Q_OBJECT
public:
    Solo(QWidget* parent=nullptr) : QWidget(parent) { }
    virtual ~Solo() = default;
    void assignSoloState(int* state) { mSoloState = state; }
    void doGlobalSoloChanged(int state);
    bool doSolo();
    bool doSoloLeft();
    bool doSoloRight();
    bool doMute();
    bool doMuteLeft();
    bool doMuteRight();
    void setLinkState(bool state=true);
    bool getLinkState() { return mLinkState; }
    void loadSoloMuteState(bool stateLink, bool stateSolo, bool stateSoloLeft, bool stateSoloRight, bool stateMute, bool stateMuteLeft, bool stateMuteRight);
protected:
    virtual void setSoloState(bool state) = 0;
    virtual void setSoloStateLeft(bool state) = 0;
    virtual void setSoloStateRight(bool state) = 0;
    virtual void setMuteState(bool state) = 0;
    virtual void setMuteStateLeft(bool state) = 0;
    virtual void setMuteStateRight(bool state) = 0;
    virtual void setSoloClicked(bool state) = 0;
    virtual void setSoloClickedLeft(bool state) = 0;
    virtual void setSoloClickedRight(bool state) = 0;
    virtual void setMuteClicked(bool state) = 0;
    virtual void setMuteClickedLeft(bool state) = 0;
    virtual void setMuteClickedRight(bool state) = 0;
    virtual bool getSoloState() = 0;
    virtual bool getSoloStateLeft() = 0;
    virtual bool getSoloStateRight() = 0;
    virtual bool getMuteState() = 0;
    virtual bool getMuteStateLeft() = 0;
    virtual bool getMuteStateRight() = 0;
private:
    int* mSoloState=nullptr;
    bool mLinkState=false;
    bool mMuteState=false;
    bool mMuteStateLeft=false;
    bool mMuteStateRight=false;
signals:
    void soloStateChanged(QString objectName, bool state);
};


#endif // SOLO_H


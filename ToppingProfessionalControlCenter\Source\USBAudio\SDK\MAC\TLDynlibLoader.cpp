/************************************************************************

    Description:
        TLDFU API library
        Represents the dynamically loaded API library

    Author(s):
        <PERSON><PERSON><PERSON><PERSON> Eberhardt
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#include "tlusbdfusdk.h"

// If our header file was not included, this file is empty.
#ifdef __TLDynlibLoader_h__


#if TLRT_OS_ENV_WINDOWS
//
// OS specific implementation for Windows
//
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN 1
#endif
#include <windows.h>

// static
TLSTATUS
TLDynlibLoader::OSLoadDynLib(
    DynLibHandleType& handle,
    const T_UNICHAR* fileName
    )
{
    // check whether the DLL exists, this allows us to return a more specific error code
    if ( ::GetFileAttributes( fileName ) == INVALID_FILE_ATTRIBUTES ) {
        DWORD err = ::GetLastError();
        if ( ERROR_FILE_NOT_FOUND == err || ERROR_PATH_NOT_FOUND == err ) {
            return TLSTATUS_FILE_NOT_FOUND;
        }
    }

    HMODULE h = ::LoadLibrary(fileName);
    if ( nullptr == h ) {
        TLSTATUS st = ::GetLastError();
        return st;
    }
    
    handle = h;
    return TLSTATUS_SUCCESS;
}

// static
void
TLDynlibLoader::OSUnloadDynLib(
    DynLibHandleType handle
    )
{
    ::FreeLibrary(static_cast<HMODULE>(handle));
}

// static
void*
TLDynlibLoader::OSResolveSymbol(
    DynLibHandleType handle, 
    const char* symbol
    )
{
    return ::GetProcAddress(static_cast<HMODULE>(handle), symbol);
}


#elif (TLRT_OS_ENV_MACOS || TLRT_OS_ENV_LINUX)
//
// OS specific implementation for Linux/Mac
//
#include <dlfcn.h>

// static
TLSTATUS
TLDynlibLoader::OSLoadDynLib(
    DynLibHandleType& handle,
    const T_UNICHAR* fileName
    )
{
    void* h = ::dlopen(fileName, RTLD_LAZY);
    if ( nullptr == h ) {
        return TLSTATUS_FAILED;
    }
    
    handle = h;
    return TLSTATUS_SUCCESS;
}

// static
void
TLDynlibLoader::OSUnloadDynLib(
    DynLibHandleType handle
    )
{
    ::dlclose(handle);
}

// static
void*
TLDynlibLoader::OSResolveSymbol(
    DynLibHandleType handle,
    const char* symbol
    )
{
    return ::dlsym(handle, symbol);
}

#else
#error OS specific implementation required.
#endif



TLDynlibLoader::TLDynlibLoader()
{
    // empty
}


TLDynlibLoader::~TLDynlibLoader()
{
    // NOTE: No automatic unload here because execution order 
    // of destructors for global objects is not deterministic in call cases.
    //UnloadDynlib();
}


TLSTATUS
TLDynlibLoader::LoadDynlibByName(
    const T_UNICHAR* fileName
    )
{
    TLSTATUS st;

    // make sure the current handle is released
    UnloadDynlib();

    st = OSLoadDynLib(mDynLibHandle, fileName);
    if ( st != TLSTATUS_SUCCESS ) {
        return st;
    }

    return TLSTATUS_SUCCESS;
}



void
TLDynlibLoader::UnloadDynlib()
{
    if ( mDynLibHandle != InvalidHandleValue ) {
        OSUnloadDynLib(mDynLibHandle);
        mDynLibHandle = InvalidHandleValue;
    }
}


void*
TLDynlibLoader::ResolveDynlibSymbol(
    const char* name
    )
{
    if ( mDynLibHandle != InvalidHandleValue ) {
        return OSResolveSymbol(mDynLibHandle, name);
    }
    return nullptr;
}


#endif  //#ifdef __TLDynlibLoader_h__


/*** EOF ***/

/****************************************************************************
** Meta object code from reading C++ file 'effects1m2.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'effects1m2.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN10EffectS1M2E_t {};
} // unnamed namespace

template <> constexpr inline auto EffectS1M2::qt_create_metaobjectdata<qt_meta_tag_ZN10EffectS1M2E_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "EffectS1M2",
        "in_mTimer_timeout",
        "",
        "in_widgetPushButtonGroup1_buttonStateChanged",
        "PushButtonS1M5::ButtonID",
        "button",
        "state",
        "in_widgetDial_valueChanged",
        "value",
        "in_widgetDialRoom_valueChanged",
        "in_widgetDialDecay_valueChanged",
        "in_widgetPushButtonGroup2_buttonStateChanged",
        "PushButtonS1M7::ButtonID",
        "on_lineEdit_textChanged",
        "arg1",
        "on_lineEdit_editingFinished",
        "on_pushButtonClose_clicked",
        "on_pushButtonNavigation_clicked"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'in_mTimer_timeout'
        QtMocHelpers::SlotData<void()>(1, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'in_widgetPushButtonGroup1_buttonStateChanged'
        QtMocHelpers::SlotData<void(PushButtonS1M5::ButtonID, bool)>(3, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 4, 5 }, { QMetaType::Bool, 6 },
        }}),
        // Slot 'in_widgetDial_valueChanged'
        QtMocHelpers::SlotData<void(float)>(7, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Float, 8 },
        }}),
        // Slot 'in_widgetDialRoom_valueChanged'
        QtMocHelpers::SlotData<void(float)>(9, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Float, 8 },
        }}),
        // Slot 'in_widgetDialDecay_valueChanged'
        QtMocHelpers::SlotData<void(float)>(10, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Float, 8 },
        }}),
        // Slot 'in_widgetPushButtonGroup2_buttonStateChanged'
        QtMocHelpers::SlotData<void(PushButtonS1M7::ButtonID, bool)>(11, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 12, 5 }, { QMetaType::Bool, 6 },
        }}),
        // Slot 'on_lineEdit_textChanged'
        QtMocHelpers::SlotData<void(const QString &)>(13, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 14 },
        }}),
        // Slot 'on_lineEdit_editingFinished'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButtonClose_clicked'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pushButtonNavigation_clicked'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<EffectS1M2, qt_meta_tag_ZN10EffectS1M2E_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject EffectS1M2::staticMetaObject = { {
    QMetaObject::SuperData::link<EffectBase::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10EffectS1M2E_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10EffectS1M2E_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN10EffectS1M2E_t>.metaTypes,
    nullptr
} };

void EffectS1M2::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<EffectS1M2 *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->in_mTimer_timeout(); break;
        case 1: _t->in_widgetPushButtonGroup1_buttonStateChanged((*reinterpret_cast< std::add_pointer_t<PushButtonS1M5::ButtonID>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 2: _t->in_widgetDial_valueChanged((*reinterpret_cast< std::add_pointer_t<float>>(_a[1]))); break;
        case 3: _t->in_widgetDialRoom_valueChanged((*reinterpret_cast< std::add_pointer_t<float>>(_a[1]))); break;
        case 4: _t->in_widgetDialDecay_valueChanged((*reinterpret_cast< std::add_pointer_t<float>>(_a[1]))); break;
        case 5: _t->in_widgetPushButtonGroup2_buttonStateChanged((*reinterpret_cast< std::add_pointer_t<PushButtonS1M7::ButtonID>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 6: _t->on_lineEdit_textChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: _t->on_lineEdit_editingFinished(); break;
        case 8: _t->on_pushButtonClose_clicked(); break;
        case 9: _t->on_pushButtonNavigation_clicked(); break;
        default: ;
        }
    }
}

const QMetaObject *EffectS1M2::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *EffectS1M2::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10EffectS1M2E_t>.strings))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "WorkspaceObserver"))
        return static_cast< WorkspaceObserver*>(this);
    if (!strcmp(_clname, "AppSettingsObserver"))
        return static_cast< AppSettingsObserver*>(this);
    return EffectBase::qt_metacast(_clname);
}

int EffectS1M2::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = EffectBase::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 10;
    }
    return _id;
}
QT_WARNING_POP

#ifndef FIELDORIGINS1M1_H
#define FIELDORIGINS1M1_H


#include <QWidget>
#include <QVector>
#include <QString>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"
#include "fieldoriginbase1.h"


class FieldOriginS1M1 : public FieldOriginBase1, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldOriginS1M1(QWidget* parent=nullptr, QString name="");
    ~FieldOriginS1M1();
    FieldOriginS1M1& setName(QString name);
    FieldOriginS1M1& modifyWidgetList(QVector<OriginBase*> list);
    FieldOriginS1M1& setVisibleListDefault(QVector<OriginBase*> list);
protected:
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    QVector<OriginBase*> mWidgetList;
    QVariantList mVisibleListDefault;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
    void in_widgetList_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDORIGINS1M1_H


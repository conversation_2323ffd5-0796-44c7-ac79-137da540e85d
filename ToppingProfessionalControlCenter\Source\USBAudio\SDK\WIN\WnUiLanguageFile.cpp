/************************************************************************
 *  The module is used to access a file containing texts for an UI language.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

 // Module is empty if .h file was not included (category turned off).
#ifdef __WnUiLanguageFile_h__

 // optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif


WnUiLanguageFile::WnUiLanguageFile()
{
}


WnUiLanguageFile::~WnUiLanguageFile()
{
}


WNERR WnUiLanguageFile::Read( WnUiLanguageTextGroupVector& textGroups )
{
    //error checks
    if (mFilePathAndName.length() == 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": File path and name no yet set.\n")));
        return TSTATUS_REJECTED;
    }
    if (mCommentBeginToken.length() == 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No token defined to start a comment.\n")));
        return TSTATUS_REJECTED;
    }
    if (mIdToValueDelimiter.length() == 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No delimiter specified to separate language text identifier and value.\n")));
        return TSTATUS_REJECTED;
    }

    //reset all language texts
    for (const auto& group : textGroups) {
        for (auto uiText : group.GetUiLanguageTexts()) {
            uiText->ClearLanguageText();
        }        
    }

    //open the file
    //Note: We cannot use std::wifstream, since it doesn't examine the BOM (Byte Order mark) that may be present
    //at the beginning of the file. Instead the BOM is read as file content, which disturbs content parsing.
    //To avoid implementation of our own BOM parser we use fopen instead.
    FILE* fileHandle = nullptr;
    errno_t res = _wfopen_s( &fileHandle, mFilePathAndName.c_str(), L"rt,ccs=UNICODE");
    if (res != 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Failed to open file '%s'.\n"), mFilePathAndName.c_str()));
        return TSTATUS_OPEN_FILE_FAILED;
    }   

    //initialization
    const int tmpStrLen = (10 * 1024);
    wchar_t tmpStr[tmpStrLen];
    tmpStr[0] = L'\0';
    int pos = 0;
    wchar_t c = 0;    
    std::vector<WString> lines;

    //read the file line by line
    do { 
        //next line
        pos = 0;
        do {
            //next character in the line
            c = fgetwc(fileHandle);

            //end of file or error
            if (c == WEOF) {
                if (ferror(fileHandle) != 0) {
                    WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Failed to read file '%s'.\n"), mFilePathAndName.c_str()));
                    //cleanup
                    fclose(fileHandle);
                    return TSTATUS_READ_FILE_FAILED;
                }
                continue;
            }                       
           
            tmpStr[pos++] = c;
                                    
        } while ((c != WEOF) && (c != L'\n'));

        //add a null-termination
        tmpStr[pos] = L'\0';

        //trim leading and trailing white spaces 
        TbStringTrimBegin(tmpStr);
        TbStringTrimEnd(tmpStr);

        //add the line to the line container
        lines.push_back(tmpStr);
       
    } while (c != WEOF);


    if (fclose(fileHandle) != 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Failed to close file.\n")));
        //not critical, we continue ...
    }

    //now parse the read lines
    bool isCommentContinued = false;
    bool isValueContinued = false;
    UiLanguageTextHandle uiText;
    WString::size_type beginPos = WString::npos;
    WString::size_type endPos = WString::npos;       
    WString valueWithToken;
    bool isValueToProcess = false;
    for (std::vector<WString>::iterator itLine = lines.begin(); itLine != lines.end(); ++itLine) {

        //Is a comment continued by this line?
        if (isCommentContinued) {

            //YES: So the line is ignored. Is the token to finish the comment at the end of this line?
            if ((itLine->length() >= mCommentEndToken.length()) && (itLine->compare(itLine->length() - mCommentEndToken.length(), mCommentEndToken.length(), mCommentEndToken) == 0)) {
                //YES
                isCommentContinued = false;
            }
        }
        //Is a value continued by this line?
        else if (isValueContinued) {

            //Is it an empty line?
            if (itLine->length() == 0) {
                //YES: so it is ignored
                continue;
            }

            //Is the value in the line continued in the next line?
            if ((mLineContinuationToken.length() > 0)
                && ((itLine->length() - (beginPos + mIdToValueDelimiter.length())) >= mLineContinuationToken.length())
                && (itLine->compare(itLine->length() - mLineContinuationToken.length(), mLineContinuationToken.length(), mLineContinuationToken) == 0)) {
                //YES
                isValueContinued = true;
            }
            else {
                //NO
                isValueContinued = false;
            }

            //get the value
            if (isValueContinued) {
                valueWithToken += itLine->substr(0, (itLine->length() - mLineContinuationToken.length()));
                //mark the line continuation in the case the value is written again to a file
                valueWithToken += WnUiLanguageText::F_NL;
            }
            else {
                valueWithToken += *itLine;
            } 

            //Is the value complete or is it last line? The latter will result in a valid value if the last line
            //has a redundant continuation token and a value end token is not required.
            if (!isValueContinued || ((itLine + 1) == lines.end())) {

                isValueToProcess = true;
            }
        }
        //Does a comment start?
        else if (itLine->compare(0, mCommentBeginToken.length(), mCommentBeginToken) == 0)
        {
            //YES: So the line is ignored. Is there a token that finishes the comment?
            if (mCommentEndToken.length() > 0) {
                //YES
                isCommentContinued = true;

                //Is this token at the end of this line?
                if ((itLine->length() >= mCommentEndToken.length()) && (itLine->compare(itLine->length() - mCommentEndToken.length(), mCommentEndToken.length(), mCommentEndToken) == 0)) {
                    //YES
                    isCommentContinued = false;
                }
            }
        }
        else {
            //Is it an empty line?
            if (itLine->length() == 0) {
                //YES: so it is ignored
                continue;
            }

            //The line contains a Identifier-Value pair. Find the delimiter.
            beginPos = itLine->find(mIdToValueDelimiter);
            if (beginPos == WString::npos) {
                WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Line '%s' contains no delimiter between identifier and value. The line is ignored.\n"), itLine->c_str()));
                continue;
            }
            if (beginPos == 0) {
                WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Line '%s' contains no identifier. The line is ignored.\n"), itLine->c_str()));
                continue;
            }

            if ((beginPos + mIdToValueDelimiter.length()) == itLine->length() - 1) {
                WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Line '%s' contains no value. The line is ignored.\n"), itLine->c_str()));
                continue;
            }
            
            //get the identifier
            TbStringNCopy(tmpStr, itLine->substr(0, beginPos).c_str(), tmpStrLen);
            TbStringTrimEnd(tmpStr);

            //Is it the identifier of a requested language text?
            uiText = nullptr;
            for (const auto& group : textGroups) {
                uiText = group.FindUILanguageText(tmpStr);
                if (uiText) {
                    //YES
                    break;
                }
            }

            if (!uiText) {
                WNTRACE(TRCWRN, tprint(__FUNCTION__ _T(": Line '%s' contains unknown text identifier '%s'. The line is ignored.\n"), itLine->c_str(), tmpStr));
                continue;
            }                        

            //Is the value in the line continued in the next line?
            if (   (mLineContinuationToken.length() > 0)
                && ((itLine->length() - (beginPos + mIdToValueDelimiter.length())) >= mLineContinuationToken.length())
                && (itLine->compare(itLine->length() - mLineContinuationToken.length(), mLineContinuationToken.length(), mLineContinuationToken) == 0)) {
                //YES
                isValueContinued = true;
            }

            //get the value
            if (isValueContinued) {
                TbStringNCopy(tmpStr, itLine->substr((beginPos + mIdToValueDelimiter.length()), (itLine->length() - (beginPos + mIdToValueDelimiter.length()) - mLineContinuationToken.length())).c_str(), tmpStrLen);
                //mark the line continuation in the case the value is written again to a file
                TbStringNCat(tmpStr, WnUiLanguageText::F_NL.c_str(), tmpStrLen);
            }
            else {
                TbStringNCopy(tmpStr, itLine->substr(beginPos + mIdToValueDelimiter.length()).c_str(), tmpStrLen);
            }            
            TbStringTrimBegin(tmpStr);  
            valueWithToken = tmpStr;            

            //Is the value complete?
            if (!isValueContinued) {

                isValueToProcess = true;
            }
        }

        //Is there a found value to process?
        if (isValueToProcess) {

            isValueToProcess = false;

            //YES: Is there a beginning token for values specified?
            beginPos = 0;
            if (mValueBeginToken.length() > 0) {

                //YES: Is it present?
                if (valueWithToken.compare(0, mValueBeginToken.length(), mValueBeginToken) == 0) {
                    beginPos += mValueBeginToken.length();
                }
                else {
                    WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Beginning token of value '%s' not found. The value is ignored.\n"), valueWithToken.c_str()));
                    valueWithToken.clear();
                    uiText.reset();
                    continue;
                }
            }

            //Is there an end token for values specified?
            endPos = valueWithToken.length() - 1;
            if (mValueEndToken.length() > 0) {

                //YES: Is it present?
                if ((valueWithToken.length() >= mValueEndToken.length())
                    && (valueWithToken.compare(valueWithToken.length() - mValueEndToken.length(), mValueEndToken.length(), mValueEndToken) == 0)) {
                    endPos -= mValueEndToken.length();
                }
                else {
                    WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": End token of value '%s' not found. The value is ignored.\n"), valueWithToken.c_str()));
                    valueWithToken.clear();
                    uiText.reset();
                    continue;
                }
            }

            //save the value without beginning and end token
            uiText->SetLanguageText(valueWithToken.substr(beginPos, (endPos - beginPos + 1)));
            WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Value for identifier '%s' = '%s' .\n"), uiText->GetIdentifierStr(), uiText->GetLanguageText().c_str()));
            WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Display value for identifier '%s' = '%s'.\n"), uiText->GetIdentifierStr(), uiText->GetLanguageTextToDisplay().c_str()));
            valueWithToken.clear();
            uiText.reset();
        }
    }    

    //OK
    return TSTATUS_SUCCESS;
}

WNERR WnUiLanguageFile::Write(const WnUiLanguageTextGroupVector& textGroups, bool onlyDefault)
{
    //error check
    if (mFilePathAndName.length() == 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": File path and name no yet set.\n")));
        return TSTATUS_REJECTED;
    }    
    if (mCommentBeginToken.length() == 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No token defined to start a comment.\n")));
        return TSTATUS_REJECTED;
    }
    if (mIdToValueDelimiter.length() == 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No delimiter specified to separate language text identifier and value.\n")));
        return TSTATUS_REJECTED;
    }

    //prepare some strings
    size_t indentation = (mCommentEndToken.length() > 0 ? mCommentBeginToken.length() + 1 : 1);
    WString commentIndentation;
    for (size_t i = 0; i < indentation; ++i) {
        commentIndentation += L" ";
    }    

    WString formattedDelimiter;
    formattedDelimiter += L" ";
    formattedDelimiter += mIdToValueDelimiter;
    formattedDelimiter += L" ";     

    //calculate the indentation of the delimiter between text identifier and value to get all delimiter in the same column
    size_t delimiterIdentation = 0;
    size_t tmpLen = 0;
    for (const auto& group : textGroups) {
        for (auto uiText : group.GetUiLanguageTexts()) {
            tmpLen = TbStringLen(uiText->GetIdentifierStr());
            if (tmpLen > delimiterIdentation) {
                delimiterIdentation = tmpLen;
            }
        }
    }

    WString valueNewLineReplacement;
    valueNewLineReplacement = mLineContinuationToken;
    valueNewLineReplacement += L"\n";
    indentation = (delimiterIdentation + formattedDelimiter.length() + mValueBeginToken.length());
    for (size_t i = 0; i < indentation; ++i) {
        valueNewLineReplacement += L" ";
    }

    //create the content of the file to write.
    WString content;     
    std::vector<WString> commentLines;
    size_t startPos = 0;
    size_t foundPos = 0;
    const int commentLineLen = (10 * 1024);
    wchar_t commentLine[commentLineLen];
    commentLine[0] = L'\0';
    size_t commentLen = 0;
    for (const auto& group : textGroups) {

        //next group starts after an empty line
        content += L"\n";

        for (auto uiText : group.GetUiLanguageTexts()) {

            //Next text. Has it a comment?
            if (uiText->GetComment().length() > 0) {

                //YES: divide the comment into lines defined by the contained file-newline tokens
                commentLines.clear();
                startPos = 0;
                commentLen = uiText->GetComment().length();
                while (true) {

                    //is there more comment
                    if (startPos == commentLen) {
                        //NO
                        break;
                    }

                    //next line
                    foundPos = uiText->GetComment().find(WnUiLanguageText::F_NL, startPos);
                    if (foundPos != WString::npos) {
                        //add the line from the start position to the file-newline
                        TbStringNCopy(commentLine, uiText->GetComment().substr(startPos, foundPos - startPos).c_str(), commentLineLen);
                        startPos = foundPos + WnUiLanguageText::F_NL.length();
                    }
                    else {
                        //add the remaining comment as last line
                        TbStringNCopy(commentLine, uiText->GetComment().substr(startPos, commentLen - startPos).c_str(), commentLineLen);
                        startPos = commentLen;
                    }

                    //remove leading white spaces that occur, e.g., if the comment was defined in code with line continuation characters
                    TbStringTrimBegin(commentLine);

                    //add the line to the list
                    commentLines.push_back(commentLine);
                }
                
                //write the comment lines to the file
                for (std::vector<WString>::const_iterator it = commentLines.begin(); it != commentLines.end(); ++it) {

                    //If it is the first comment line or if there is no comment end token 
                    //we start the comment line with a comment beginning token.
                    if ((it == commentLines.begin()) || (mCommentEndToken.length() == 0)) {
                        content += mCommentBeginToken;                        
                    }
                    
                    //write the comment line indented to the content
                    if (it == commentLines.begin()) {
                        content += L" ";
                    }
                    else {
                        content += commentIndentation;
                    }                    
                    content += *it;

                    //write the comment end token to the content, if it is the last line
                    if (((it + 1) == commentLines.end()) && (mCommentEndToken.length() > 0)) {
                        content += L" ";
                        content += mCommentEndToken;
                    }

                    //the next content starts in the next line
                    content += L"\n";
                }              
            }

            //write the identifier to the content
            content += uiText->GetIdentifierStr();

            //indent the following delimiter
            tmpLen = TbStringLen(uiText->GetIdentifierStr());
            for (size_t i = tmpLen; i < delimiterIdentation; ++i) {
                content += L" ";
            }

            //write the delimiter to the value
            content += formattedDelimiter;

            //add the beginning token for a value, if specified
            content += mValueBeginToken;

            //get the value to write
            const WString& value = (!onlyDefault && (uiText->GetLanguageText().length() > 0) ? uiText->GetLanguageText() : uiText->GetDefaultText());

            //Is there a line continuation token specified?
            if (mLineContinuationToken.length() > 0) {

                //YES: So the token must be inserted before every file new line of the value.
                //Furthermore we indent the continued lines so that they start at the same position
                //like the first value line.
                //The resulting value is added to the content.                       
                content += TbStdReplaceAll(value, WnUiLanguageText::F_NL, valueNewLineReplacement);
            }
            else {

                //NO: check whether the value contains newlines
                if (value.find(WnUiLanguageText::F_NL) != WString::npos) {
                    WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No line continuation token specified, but value '%s' contains more than one file line.\n"), value.c_str()));
                    return TSTATUS_INVALID_PARAMETER;
                }

                //now add to the value to the content
                content += value;
            }

            //add the end token for a value, if specified
            content += mValueEndToken;

            //next content starts in the next line
            content += L"\n";
        }
    }
    
    //open/create the file
    //Note: We cannot use std::wofstream, since it doesn't prepend a BOM (Byte Order mark) to the content. 
    //A BOM is appropriate for apps like Notepad with which the user can process the file to create.  
    FILE* fileHandle = nullptr;
    errno_t res = _wfopen_s(&fileHandle, mFilePathAndName.c_str(), L"wt,ccs=UNICODE");
    if (res != 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Failed to open/create file '%s'.\n"), mFilePathAndName.c_str()));
        return TSTATUS_OPEN_FILE_FAILED;
    }

    //write the content to the file
    size_t writtenBytes = fwrite(content.c_str(), 1, (content.length() * sizeof(wchar_t)), fileHandle);
    if (writtenBytes != (content.length() * sizeof(wchar_t))) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Failed to write content to the file '%s'.\n"), mFilePathAndName.c_str()));
        //cleanup
        fclose(fileHandle);
        return TSTATUS_WRITE_FILE_FAILED;
    }    

    if (fclose(fileHandle) != 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Failed to close file.\n")));
        //not critical, we continue ...
    }

    //OK
    return TSTATUS_SUCCESS;
}


#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguageFile_h__

/*************************** EOF **************************************/

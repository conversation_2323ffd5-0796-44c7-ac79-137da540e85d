/********************************************************************************
** Form generated from reading UI file 'effects1m2.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_EFFECTS1M2_H
#define UI_EFFECTS1M2_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <dials1m3.h>
#include <pushbuttons1m5.h>
#include <pushbuttons1m7.h>
#include <volumemeters1m1.h>

QT_BEGIN_NAMESPACE

class Ui_EffectS1M2
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QFrame *frameLeft;
    QPushButton *pushButtonClose;
    QLineEdit *lineEdit;
    PushButtonS1M5 *widgetPushButtonGroup1;
    VolumeMeterS1M1 *widgetVolumeMeter;
    DialS1M3 *widgetDial;
    PushButtonS1M7 *widgetPushButtonGroup2;
    QLabel *labelWet;
    QLabel *labelDry;
    QFrame *frameRight;
    DialS1M3 *widgetDialRoom;
    DialS1M3 *widgetDialDecay;
    QPushButton *pushButtonNavigation;
    QLabel *labelMin;
    QLabel *labelMax;
    QLabel *labelRate;
    QLabel *labelDecay;
    QLabel *labelSize;
    QLabel *labelRoom;
    QLabel *labelSmall;
    QLabel *labelLarge;

    void setupUi(QWidget *EffectS1M2)
    {
        if (EffectS1M2->objectName().isEmpty())
            EffectS1M2->setObjectName("EffectS1M2");
        EffectS1M2->resize(280, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(EffectS1M2->sizePolicy().hasHeightForWidth());
        EffectS1M2->setSizePolicy(sizePolicy);
        EffectS1M2->setMinimumSize(QSize(160, 220));
        EffectS1M2->setMaximumSize(QSize(16777215, 16777215));
        gridLayout = new QGridLayout(EffectS1M2);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(EffectS1M2);
        frame->setObjectName("frame");
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        frameLeft = new QFrame(frame);
        frameLeft->setObjectName("frameLeft");
        frameLeft->setGeometry(QRect(0, 0, 131, 281));
        frameLeft->setMinimumSize(QSize(0, 0));
        frameLeft->setStyleSheet(QString::fromUtf8(""));
        frameLeft->setFrameShape(QFrame::Shape::StyledPanel);
        frameLeft->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frameLeft);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 20));
        lineEdit = new QLineEdit(frameLeft);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(9, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetPushButtonGroup1 = new PushButtonS1M5(frameLeft);
        widgetPushButtonGroup1->setObjectName("widgetPushButtonGroup1");
        widgetPushButtonGroup1->setGeometry(QRect(80, 40, 21, 131));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup1->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup1->setSizePolicy(sizePolicy);
        widgetVolumeMeter = new VolumeMeterS1M1(frameLeft);
        widgetVolumeMeter->setObjectName("widgetVolumeMeter");
        widgetVolumeMeter->setGeometry(QRect(10, 40, 21, 131));
        sizePolicy.setHeightForWidth(widgetVolumeMeter->sizePolicy().hasHeightForWidth());
        widgetVolumeMeter->setSizePolicy(sizePolicy);
        widgetDial = new DialS1M3(frameLeft);
        widgetDial->setObjectName("widgetDial");
        widgetDial->setGeometry(QRect(40, 170, 31, 31));
        sizePolicy.setHeightForWidth(widgetDial->sizePolicy().hasHeightForWidth());
        widgetDial->setSizePolicy(sizePolicy);
        widgetDial->setMinimumSize(QSize(0, 0));
        widgetPushButtonGroup2 = new PushButtonS1M7(frameLeft);
        widgetPushButtonGroup2->setObjectName("widgetPushButtonGroup2");
        widgetPushButtonGroup2->setGeometry(QRect(10, 230, 90, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup2->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup2->setSizePolicy(sizePolicy);
        widgetPushButtonGroup2->setMinimumSize(QSize(0, 0));
        labelWet = new QLabel(frameLeft);
        labelWet->setObjectName("labelWet");
        labelWet->setGeometry(QRect(60, 200, 53, 21));
        labelWet->setMinimumSize(QSize(0, 0));
        labelWet->setAlignment(Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter);
        labelDry = new QLabel(frameLeft);
        labelDry->setObjectName("labelDry");
        labelDry->setGeometry(QRect(0, 200, 53, 21));
        labelDry->setMinimumSize(QSize(0, 0));
        labelDry->setAlignment(Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter);
        frameRight = new QFrame(frame);
        frameRight->setObjectName("frameRight");
        frameRight->setGeometry(QRect(140, 10, 131, 261));
        frameRight->setFrameShape(QFrame::Shape::StyledPanel);
        frameRight->setFrameShadow(QFrame::Shadow::Raised);
        widgetDialRoom = new DialS1M3(frameRight);
        widgetDialRoom->setObjectName("widgetDialRoom");
        widgetDialRoom->setGeometry(QRect(50, 70, 31, 21));
        sizePolicy.setHeightForWidth(widgetDialRoom->sizePolicy().hasHeightForWidth());
        widgetDialRoom->setSizePolicy(sizePolicy);
        widgetDialRoom->setMinimumSize(QSize(0, 0));
        widgetDialDecay = new DialS1M3(frameRight);
        widgetDialDecay->setObjectName("widgetDialDecay");
        widgetDialDecay->setGeometry(QRect(50, 200, 31, 41));
        sizePolicy.setHeightForWidth(widgetDialDecay->sizePolicy().hasHeightForWidth());
        widgetDialDecay->setSizePolicy(sizePolicy);
        widgetDialDecay->setMinimumSize(QSize(0, 0));
        pushButtonNavigation = new QPushButton(frameRight);
        pushButtonNavigation->setObjectName("pushButtonNavigation");
        pushButtonNavigation->setGeometry(QRect(110, 40, 10, 45));
        pushButtonNavigation->setMinimumSize(QSize(8, 35));
        pushButtonNavigation->setMaximumSize(QSize(16777215, 16777215));
        pushButtonNavigation->setStyleSheet(QString::fromUtf8(""));
        labelMin = new QLabel(frameRight);
        labelMin->setObjectName("labelMin");
        labelMin->setGeometry(QRect(10, 240, 53, 21));
        labelMin->setMinimumSize(QSize(0, 0));
        labelMin->setAlignment(Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter);
        labelMax = new QLabel(frameRight);
        labelMax->setObjectName("labelMax");
        labelMax->setGeometry(QRect(70, 240, 53, 21));
        labelMax->setMinimumSize(QSize(0, 0));
        labelMax->setAlignment(Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter);
        labelRate = new QLabel(frameRight);
        labelRate->setObjectName("labelRate");
        labelRate->setGeometry(QRect(40, 180, 53, 21));
        labelRate->setMinimumSize(QSize(0, 0));
        labelRate->setAlignment(Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop);
        labelDecay = new QLabel(frameRight);
        labelDecay->setObjectName("labelDecay");
        labelDecay->setGeometry(QRect(40, 160, 53, 21));
        labelDecay->setMinimumSize(QSize(0, 0));
        labelDecay->setAlignment(Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignHCenter);
        labelSize = new QLabel(frameRight);
        labelSize->setObjectName("labelSize");
        labelSize->setGeometry(QRect(40, 40, 53, 21));
        labelSize->setMinimumSize(QSize(0, 0));
        labelSize->setAlignment(Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop);
        labelRoom = new QLabel(frameRight);
        labelRoom->setObjectName("labelRoom");
        labelRoom->setGeometry(QRect(40, 20, 53, 21));
        labelRoom->setMinimumSize(QSize(0, 0));
        labelRoom->setAlignment(Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignHCenter);
        labelSmall = new QLabel(frameRight);
        labelSmall->setObjectName("labelSmall");
        labelSmall->setGeometry(QRect(10, 100, 53, 21));
        labelSmall->setMinimumSize(QSize(0, 0));
        labelSmall->setStyleSheet(QString::fromUtf8(""));
        labelSmall->setAlignment(Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter);
        labelLarge = new QLabel(frameRight);
        labelLarge->setObjectName("labelLarge");
        labelLarge->setGeometry(QRect(70, 100, 53, 21));
        labelLarge->setMinimumSize(QSize(0, 0));
        labelLarge->setAlignment(Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter);

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(EffectS1M2);

        QMetaObject::connectSlotsByName(EffectS1M2);
    } // setupUi

    void retranslateUi(QWidget *EffectS1M2)
    {
        EffectS1M2->setWindowTitle(QCoreApplication::translate("EffectS1M2", "Form", nullptr));
        pushButtonClose->setText(QString());
        labelWet->setText(QCoreApplication::translate("EffectS1M2", "Wet", nullptr));
        labelDry->setText(QCoreApplication::translate("EffectS1M2", "Dry", nullptr));
        pushButtonNavigation->setText(QString());
        labelMin->setText(QCoreApplication::translate("EffectS1M2", "Min", nullptr));
        labelMax->setText(QCoreApplication::translate("EffectS1M2", "Max", nullptr));
        labelRate->setText(QCoreApplication::translate("EffectS1M2", "Rate", nullptr));
        labelDecay->setText(QCoreApplication::translate("EffectS1M2", "Decay", nullptr));
        labelSize->setText(QCoreApplication::translate("EffectS1M2", "Size", nullptr));
        labelRoom->setText(QCoreApplication::translate("EffectS1M2", "Room", nullptr));
        labelSmall->setText(QCoreApplication::translate("EffectS1M2", "Small", nullptr));
        labelLarge->setText(QCoreApplication::translate("EffectS1M2", "Large", nullptr));
    } // retranslateUi

};

namespace Ui {
    class EffectS1M2: public Ui_EffectS1M2 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_EFFECTS1M2_H

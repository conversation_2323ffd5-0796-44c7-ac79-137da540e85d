#include "globalfont.h"
#include "pushbuttons1m6.h"


PushButtonS1M6::PushButtonS1M6(QWidget* parent)
    : QWidget(parent)
{
    mPushButtonSOLOLeft.setParent(this);
    mPushButtonSOLORight.setParent(this);
    mPushButtonMUTELeft.setParent(this);
    mPushButtonMUTERight.setParent(this);
    QString style;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonSOLOLeft.setStyleSheet(style);
    mPushButtonSOLORight.setStyleSheet(style);
    mPushButtonMUTELeft.setStyleSheet(style);
    mPushButtonMUTERight.setStyleSheet(style);
    mPushButtonSOLOLeft.setText("SOLO");
    mPushButtonSOLORight.setText("SOLO");
    mPushButtonMUTELeft.setText("MUTE");
    mPushButtonMUTERight.setText("MUTE");
    connect(&mPushButtonSOLOLeft, SIGNAL(clicked()), this, SLOT(in_mPushButtonSOLOLeft_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonSOLORight, SIGNAL(clicked()), this, SLOT(in_mPushButtonSOLORight_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMUTELeft, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTELeft_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMUTERight, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTERight_clicked()), Qt::UniqueConnection);
}
PushButtonS1M6::~PushButtonS1M6()
{

}


// override
void PushButtonS1M6::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wPushButton=wPixelPerRatio * mWeightWidth;
    int xPushButtonLeft=(size().width() - wPushButton * 2) / 4;
    int xPushButtonRight=size().width() / 2 + xPushButtonLeft;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hSpace1=hPixelPerRatio * 10;
    int hSpace2=hPixelPerRatio * 15;
    int hSpace3=hPixelPerRatio * 10;
    int hPushButton=(size().height() - hSpace1 - hSpace2 - hSpace3) / 2;
    mPushButtonSOLOLeft.setGeometry(xPushButtonLeft, hSpace1, wPushButton, hPushButton);
    mPushButtonSOLORight.setGeometry(xPushButtonRight, hSpace1, wPushButton, hPushButton);
    mPushButtonMUTELeft.setGeometry(xPushButtonLeft, hSpace1 + hPushButton + hSpace2, wPushButton, hPushButton);
    mPushButtonMUTERight.setGeometry(xPushButtonRight, hSpace1 + hPushButton + hSpace2, wPushButton, hPushButton);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonSOLOLeft.text(), mPushButtonSOLOLeft.rect()));
    mPushButtonSOLOLeft.setFont(mFont);
    mPushButtonSOLORight.setFont(mFont);
    mPushButtonMUTELeft.setFont(mFont);
    mPushButtonMUTERight.setFont(mFont);
}


// slot
void PushButtonS1M6::in_mPushButtonSOLOLeft_clicked()
{
    QString style;
    mPushButtonStateSOLOLeft = !mPushButtonStateSOLOLeft;
    if(mPushButtonStateSOLOLeft)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(190, 175, 48);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonSOLOLeft.setStyleSheet(style);
    emit buttonStateChanged(buttonSOLOLeft, mPushButtonStateSOLOLeft);
}
void PushButtonS1M6::in_mPushButtonSOLORight_clicked()
{
    QString style;
    mPushButtonStateSOLORight = !mPushButtonStateSOLORight;
    if(mPushButtonStateSOLORight)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(190, 175, 48);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonSOLORight.setStyleSheet(style);
    emit buttonStateChanged(buttonSOLORight, mPushButtonStateSOLORight);
}
void PushButtonS1M6::in_mPushButtonMUTELeft_clicked()
{
    QString style;
    mPushButtonStateMUTELeft = !mPushButtonStateMUTELeft;
    if(mPushButtonStateMUTELeft)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(107, 5, 5);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonMUTELeft.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTELeft, mPushButtonStateMUTELeft);
}
void PushButtonS1M6::in_mPushButtonMUTERight_clicked()
{
    QString style;
    mPushButtonStateMUTERight = !mPushButtonStateMUTERight;
    if(mPushButtonStateMUTERight)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(107, 5, 5);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonMUTERight.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTERight, mPushButtonStateMUTERight);
}


// setter & getter
PushButtonS1M6& PushButtonS1M6::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M6& PushButtonS1M6::setPushButtonWeightWidth(int weight)
{
    mWeightWidth = weight;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M6& PushButtonS1M6::setPushButtonStateSOLOLeft(bool state)
{
    QString style;
    mPushButtonStateSOLOLeft = state;
    if(mPushButtonStateSOLOLeft)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(190, 175, 48);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonSOLOLeft.setStyleSheet(style);
    return *this;
}
PushButtonS1M6& PushButtonS1M6::setPushButtonStateSOLORight(bool state)
{
    QString style;
    mPushButtonStateSOLORight = state;
    if(mPushButtonStateSOLORight)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(190, 175, 48);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonSOLORight.setStyleSheet(style);
    return *this;
}
PushButtonS1M6& PushButtonS1M6::setPushButtonStateMUTELeft(bool state)
{
    QString style;
    mPushButtonStateMUTELeft = state;
    if(mPushButtonStateMUTELeft)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(107, 5, 5);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonMUTELeft.setStyleSheet(style);
    return *this;
}
PushButtonS1M6& PushButtonS1M6::setPushButtonStateMUTERight(bool state)
{
    QString style;
    mPushButtonStateMUTERight = state;
    if(mPushButtonStateMUTERight)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(107, 5, 5);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonMUTERight.setStyleSheet(style);
    return *this;
}
PushButtonS1M6& PushButtonS1M6::setPushButtonClickedSOLOLeft(bool state)
{
    mPushButtonStateSOLOLeft = !state;
    in_mPushButtonSOLOLeft_clicked();
    return *this;
}
PushButtonS1M6& PushButtonS1M6::setPushButtonClickedSOLORight(bool state)
{
    mPushButtonStateSOLORight = !state;
    in_mPushButtonSOLORight_clicked();
    return *this;
}
PushButtonS1M6& PushButtonS1M6::setPushButtonClickedMUTELeft(bool state)
{
    mPushButtonStateMUTELeft = !state;
    in_mPushButtonMUTELeft_clicked();
    return *this;
}
PushButtonS1M6& PushButtonS1M6::setPushButtonClickedMUTERight(bool state)
{
    mPushButtonStateMUTERight = !state;
    in_mPushButtonMUTERight_clicked();
    return *this;
}
bool PushButtonS1M6::getPushButtonStateSOLOLeft()
{
    return mPushButtonStateSOLOLeft;
}
bool PushButtonS1M6::getPushButtonStateSOLORight()
{
    return mPushButtonStateSOLORight;
}
bool PushButtonS1M6::getPushButtonStateMUTELeft()
{
    return mPushButtonStateMUTELeft;
}
bool PushButtonS1M6::getPushButtonStateMUTERight()
{
    return mPushButtonStateMUTERight;
}


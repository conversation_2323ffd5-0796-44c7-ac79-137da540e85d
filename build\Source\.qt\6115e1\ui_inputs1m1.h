/********************************************************************************
** Form generated from reading UI file 'inputs1m1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_INPUTS1M1_H
#define UI_INPUTS1M1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <dials1m5.h>
#include <hsliders1m1.h>
#include <pushbuttongroups1m1.h>
#include <pushbuttongroups1m2.h>
#include <pushbuttongroups1m9.h>
#include <volumemeters1m1.h>

QT_BEGIN_NAMESPACE

class Ui_InputS1M1
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    QLineEdit *lineEdit;
    PushButtonGroupS1M9 *widgetPushButtonGroup1;
    VolumeMeterS1M1 *widgetVolumeMeter;
    DialS1M5 *widgetDial;
    PushButtonGroupS1M2 *widgetPushButtonGroup2;
    HSliderS1M1 *widgetHSlider;
    QWidget *widgetOverlay;
    PushButtonGroupS1M1 *widgetPushButtonGroup3;

    void setupUi(QWidget *InputS1M1)
    {
        if (InputS1M1->objectName().isEmpty())
            InputS1M1->setObjectName("InputS1M1");
        InputS1M1->resize(140, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(InputS1M1->sizePolicy().hasHeightForWidth());
        InputS1M1->setSizePolicy(sizePolicy);
        InputS1M1->setMinimumSize(QSize(80, 220));
        InputS1M1->setStyleSheet(QString::fromUtf8(""));
        gridLayout = new QGridLayout(InputS1M1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(InputS1M1);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 20));
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(9, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetPushButtonGroup1 = new PushButtonGroupS1M9(frame);
        widgetPushButtonGroup1->setObjectName("widgetPushButtonGroup1");
        widgetPushButtonGroup1->setGeometry(QRect(80, 40, 21, 31));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup1->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup1->setSizePolicy(sizePolicy);
        widgetPushButtonGroup1->setMinimumSize(QSize(1, 1));
        widgetVolumeMeter = new VolumeMeterS1M1(frame);
        widgetVolumeMeter->setObjectName("widgetVolumeMeter");
        widgetVolumeMeter->setGeometry(QRect(10, 40, 21, 131));
        sizePolicy.setHeightForWidth(widgetVolumeMeter->sizePolicy().hasHeightForWidth());
        widgetVolumeMeter->setSizePolicy(sizePolicy);
        widgetDial = new DialS1M5(frame);
        widgetDial->setObjectName("widgetDial");
        widgetDial->setGeometry(QRect(10, 180, 90, 41));
        sizePolicy.setHeightForWidth(widgetDial->sizePolicy().hasHeightForWidth());
        widgetDial->setSizePolicy(sizePolicy);
        widgetDial->setMinimumSize(QSize(0, 0));
        widgetPushButtonGroup2 = new PushButtonGroupS1M2(frame);
        widgetPushButtonGroup2->setObjectName("widgetPushButtonGroup2");
        widgetPushButtonGroup2->setGeometry(QRect(10, 230, 90, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup2->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup2->setSizePolicy(sizePolicy);
        widgetPushButtonGroup2->setMinimumSize(QSize(6, 5));
        widgetHSlider = new HSliderS1M1(frame);
        widgetHSlider->setObjectName("widgetHSlider");
        widgetHSlider->setGeometry(QRect(80, 130, 21, 41));
        widgetOverlay = new QWidget(frame);
        widgetOverlay->setObjectName("widgetOverlay");
        widgetOverlay->setGeometry(QRect(110, 40, 21, 21));
        widgetPushButtonGroup3 = new PushButtonGroupS1M1(frame);
        widgetPushButtonGroup3->setObjectName("widgetPushButtonGroup3");
        widgetPushButtonGroup3->setGeometry(QRect(80, 90, 21, 31));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup3->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup3->setSizePolicy(sizePolicy);
        widgetPushButtonGroup3->setMinimumSize(QSize(6, 7));
        widgetPushButtonGroup3->raise();
        pushButtonClose->raise();
        lineEdit->raise();
        widgetPushButtonGroup1->raise();
        widgetVolumeMeter->raise();
        widgetDial->raise();
        widgetPushButtonGroup2->raise();
        widgetHSlider->raise();
        widgetOverlay->raise();

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(InputS1M1);

        QMetaObject::connectSlotsByName(InputS1M1);
    } // setupUi

    void retranslateUi(QWidget *InputS1M1)
    {
        InputS1M1->setWindowTitle(QCoreApplication::translate("InputS1M1", "Form", nullptr));
        pushButtonClose->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class InputS1M1: public Ui_InputS1M1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_INPUTS1M1_H

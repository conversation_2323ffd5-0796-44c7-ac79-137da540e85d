#include "eqwidget.h"
#include <qnamespace.h>
#include <QApplication>
#include <QScrollBar>
#include <QSizePolicy>
#include <QTimer>

EqWidgetIem::EqWidgetIem(int index, QWidget* parent)
    : QFrame(parent)
    , mIndex(index)
    , mLayoutSpacing(0)
    , mLayoutMargins(0, 0, 0, 0)
    , mMinimumItemWidth(50)
    , mLabelStretch(3)
    , mComboStretch(3)
    , mGainStretch(9)
    , mFreqStretch(9)
    , mQStretch(9)
    , mCheckStretch(3)
    , mMainLayout(nullptr)
    , mItemLabel(nullptr)
    , mTypeComboBox(nullptr)
    , mGainDial(nullptr)
    , mFrequencyDial(nullptr)
    , mQValueDial(nullptr)
    , mEnabledCheckBox(nullptr)
{
    mData.type = "high pass";
    mData.gain = 0.0f;
    mData.frequency = 1000.0f;
    mData.qValue = 0.7f;
    mData.enabled = true;

    setupUI();
    setupConnections();
    setStyleSheet(
        "EqWidgetIem {"
        "    background-color: #2a2a2a;"
        "}"
        "QLabel {"
        "    color: #404040;"
        "    font-size: 12px;"
        "}"
        "QCheckBox {"
        "    color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: transparent;"
        "}"
    );
}

EqWidgetIem::~EqWidgetIem()
{
}

void EqWidgetIem::setupUI()
{
    setMinimumWidth(mMinimumItemWidth);

    mMainLayout = new QVBoxLayout(this);
    applyLayoutSettings();

    mItemLabel = new QLabel(QString::number(mIndex + 1), this);
    mItemLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mItemLabel->setAlignment(Qt::AlignCenter);
    mMainLayout->addWidget(mItemLabel, mLabelStretch);

    mTypeComboBox = new ComboBoxS1M3(nullptr);
    mTypeComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    updateTypeOptions();
    mMainLayout->addWidget(mTypeComboBox, mComboStretch);

    mGainDial = new DialS1M1(this);
    mGainDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mGainDial->setRange(-20.0f, 20.0f)
              .setValue(0.0f)
              .setStep(0.1f)
              .setPrecision(1)
              .showSign(true);
    mMainLayout->addWidget(mGainDial, mGainStretch);

    mFrequencyDial = new DialS1M1(this);
    mFrequencyDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mFrequencyDial->setRange(20.0f, 20000.0f)
                  .setValue(1000.0f)
                  .setStep(1.0f)
                  .setPrecision(0);
    mMainLayout->addWidget(mFrequencyDial, mFreqStretch);

    mQValueDial = new DialS1M1(this);
    mQValueDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mQValueDial->setRange(0.1f, 10.0f)
                .setValue(0.7f)
                .setStep(0.1f)
                .setPrecision(1);
    mMainLayout->addWidget(mQValueDial, mQStretch);

    mEnabledCheckBox = new QCheckBox(this);
    mEnabledCheckBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mEnabledCheckBox->setChecked(true);
    mMainLayout->addWidget(mEnabledCheckBox, mCheckStretch, Qt::AlignCenter);
}

void EqWidgetIem::setupConnections()
{
    connect(mTypeComboBox, &ComboBoxS1M3::currentTextChanged,
            this, &EqWidgetIem::onTypeChanged);
    connect(mGainDial, &DialS1M1::valueChanged,
            this, &EqWidgetIem::onGainChanged);
    connect(mFrequencyDial, &DialS1M1::valueChanged,
            this, &EqWidgetIem::onFrequencyChanged);
    connect(mQValueDial, &DialS1M1::valueChanged,
            this, &EqWidgetIem::onQValueChanged);
    connect(mEnabledCheckBox, &QCheckBox::toggled,
            this, &EqWidgetIem::onEnabledChanged);
}

void EqWidgetIem::updateTypeOptions()
{
    QVector<QString> types = {
        "high pass",
        "Low pass",
        "High Shelf",
        "High Shelf"
    };
    
    mTypeComboBox->setCurrentText("high pass");
}

void EqWidgetIem::setItemData(const EqWidgetItemData& data)
{
    mData = data;
    mTypeComboBox->setCurrentText(data.type);
    mGainDial->setValue(data.gain);
    mFrequencyDial->setValue(data.frequency);
    mQValueDial->setValue(data.qValue);
    mEnabledCheckBox->setChecked(data.enabled);
}

EqWidgetItemData EqWidgetIem::getItemData() const
{
    return mData;
}

void EqWidgetIem::setItemIndex(int index)
{
    mIndex = index;
    mItemLabel->setText(QString::number(index + 1));
}

void EqWidgetIem::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqWidgetIem::onTypeChanged(const QString& text)
{
    mData.type = text;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onGainChanged(float value)
{
    mData.gain = value;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onFrequencyChanged(float value)
{
    mData.frequency = value;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onQValueChanged(float value)
{
    mData.qValue = value;
    emit dataChanged(mIndex, mData);
}

void EqWidgetIem::onEnabledChanged(bool enabled)
{
    mData.enabled = enabled;
    emit dataChanged(mIndex, mData);
}

// 间隔和间隙设置方法
void EqWidgetIem::setLayoutSpacing(int spacing)
{
    mLayoutSpacing = spacing;
    if (mMainLayout) {
        mMainLayout->setSpacing(spacing);
    }
}

void EqWidgetIem::setLayoutMargins(int left, int top, int right, int bottom)
{
    mLayoutMargins = QMargins(left, top, right, bottom);
    if (mMainLayout) {
        mMainLayout->setContentsMargins(mLayoutMargins);
    }
}

void EqWidgetIem::setLayoutMargins(int margin)
{
    setLayoutMargins(margin, margin, margin, margin);
}

void EqWidgetIem::setMinimumItemWidth(int width)
{
    mMinimumItemWidth = width;
    setMinimumWidth(width);
}

QMargins EqWidgetIem::getLayoutMargins() const
{
    return mLayoutMargins;
}

void EqWidgetIem::applyLayoutSettings()
{
    if (mMainLayout) {
        mMainLayout->setSpacing(mLayoutSpacing);
        mMainLayout->setContentsMargins(mLayoutMargins);
    }
}

void EqWidgetIem::setWidgetStretchFactors(int labelStretch, int comboStretch, int gainStretch,
                                          int freqStretch, int qStretch, int checkStretch)
{
    mLabelStretch = labelStretch;
    mComboStretch = comboStretch;
    mGainStretch = gainStretch;
    mFreqStretch = freqStretch;
    mQStretch = qStretch;
    mCheckStretch = checkStretch;

    if (mMainLayout) {
        if (mItemLabel) mMainLayout->setStretchFactor(mItemLabel, mLabelStretch);
        if (mTypeComboBox) mMainLayout->setStretchFactor(mTypeComboBox, mComboStretch);
        if (mGainDial) mMainLayout->setStretchFactor(mGainDial, mGainStretch);
        if (mFrequencyDial) mMainLayout->setStretchFactor(mFrequencyDial, mFreqStretch);
        if (mQValueDial) mMainLayout->setStretchFactor(mQValueDial, mQStretch);
        if (mEnabledCheckBox) mMainLayout->setStretchFactor(mEnabledCheckBox, mCheckStretch);
    }
}

void EqWidgetIem::getWidgetStretchFactors(int& labelStretch, int& comboStretch, int& gainStretch,
                                         int& freqStretch, int& qStretch, int& checkStretch) const
{
    labelStretch = mLabelStretch;
    comboStretch = mComboStretch;
    gainStretch = mGainStretch;
    freqStretch = mFreqStretch;
    qStretch = mQStretch;
    checkStretch = mCheckStretch;
}

EqWidget::EqWidget(QWidget* parent)
    : QWidget(parent)
    , mMainLayoutSpacing(0)
    , mItemsLayoutSpacing(0)
    , mMainLayoutMargins(0, 0, 0, 0)
    , mItemsLayoutMargins(0, 0, 0, 0)
    , mScrollAreaMargins(0, 0, 0, 0)
    , mMainLayout(nullptr)
    , mScrollArea(nullptr)
    , mScrollWidget(nullptr)
    , mItemsLayout(nullptr)
    , mAddItemButton(nullptr)
    , mRemoveItemButton(nullptr)
    , mTitleLabel(nullptr)
    , mItemStretchFactor(2.0)
    , mMainLeftStretch(1)
    , mMainCenterStretch(20)
    , mMainRightStretch(1)
    , mLabelAreaStretch(1)
    , mScrollAreaStretch(10)
{
    setupUI();
    setupConnections();
    createDefaultItems();
    setStyleSheet(
        "QWidget {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #404040;"
        "    font-size: 12px;"
        "}"
        "QPushButton {"
        "    background-color: #43cf7c;"
        "    color: #ffffff;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #52d689;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3bb56e;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #666666;"
        "    color: #999999;"
        "}"
        "QScrollArea {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: #404040;"
        "    height: 12px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background-color: #43cf7c;"
        "    min-width: 20px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #52d689;"
        "}"
    );
}

EqWidget::~EqWidget()
{

}

void EqWidget::setupUI()
{
    mTitleLayout = new QVBoxLayout();
    mTitleLayout->setContentsMargins(0, 0, 0, 0);
    mTitleLayout->setSpacing(0);

    mScrollArea = new QScrollArea(this);
    mScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    mScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea->setWidgetResizable(true);
    mScrollArea->setContentsMargins(mScrollAreaMargins);

    mMainLayout = new QHBoxLayout(this);
    mMainLayout->setContentsMargins(mMainLayoutMargins);
    mMainLayout->setSpacing(mMainLayoutSpacing);
    mMainLayout->addStretch(mMainLeftStretch);
    mMainLayout->addLayout(mTitleLayout, mLabelAreaStretch);
    mMainLayout->addWidget(mScrollArea, mScrollAreaStretch);
    mMainLayout->addStretch(mMainRightStretch);

    mAddItemButton = new QPushButton("add", this);
    mAddItemButton->setGeometry(0,0, 80, 30);

    mRemoveItemButton = new QPushButton("remove", this);
    mRemoveItemButton->setGeometry(85,0, 80, 30);

    mScrollWidget = new QWidget();
    mItemsLayout = new QHBoxLayout(mScrollWidget);
    mItemsLayout->setSpacing(mItemsLayoutSpacing);
    mItemsLayout->setContentsMargins(mItemsLayoutMargins);
    mItemsLayout->addStretch();
    mScrollArea->setWidget(mScrollWidget);

    auto labelType = new QLabel("Type", this);
    labelType->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    auto labelGain = new QLabel("Gain", this);
    labelGain->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    auto labelFrequency = new QLabel("Freq", this);
    labelFrequency->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    auto labelQ = new QLabel("Q", this);
    labelQ->setAlignment(Qt::AlignLeft|Qt::AlignVCenter);
    mTitleLayout->addStretch(1);
    mTitleLayout->addWidget(labelType, 1);
    mTitleLayout->addWidget(labelGain, 1);
    mTitleLayout->addWidget(labelFrequency, 1);
    mTitleLayout->addWidget(labelQ, 1);
    mTitleLayout->addStretch(1);
}

void EqWidget::setupConnections()
{
    connect(mAddItemButton, &QPushButton::clicked,
            this, &EqWidget::onAddItemClicked);
    connect(mRemoveItemButton, &QPushButton::clicked,
            this, &EqWidget::onRemoveItemClicked);
}

void EqWidget::createDefaultItems()
{
    setItemCount(4);
}

void EqWidget::setItemStretchFactor(double stretchFactor)
{
    mItemStretchFactor = stretchFactor;
    for (auto item : mItems) {
        updateItemWidth(item);
    }
}

void EqWidget::setItemCount(int count)
{
    if (count < 0) count = 0;
    if (count > 32) count = 32;

    int currentCount = mItems.size();

    if (count > currentCount) {
        for (int i = currentCount; i < count; ++i) {
            addItem();
        }
    } else if (count < currentCount) {
        for (int i = currentCount - 1; i >= count; --i) {
            removeItem(i);
        }
    }

    emit ItemCountChanged(count);
}

void EqWidget::addItem()
{
    setUpdatesEnabled(false);

    int index = mItems.size();
    EqWidgetIem* ItemWidget = new EqWidgetIem(index, this);
    updateItemWidth(ItemWidget);

    connect(ItemWidget, &EqWidgetIem::dataChanged,
            this, &EqWidget::itemDataChanged);

    mItems.append(ItemWidget);

    ItemWidget->hide();

    mItemsLayout->insertWidget(mItemsLayout->count() - 1, ItemWidget);

    ItemWidget->show();
    setUpdatesEnabled(true);

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeItem(int index)
{
    if (mItems.isEmpty()) return;

    if (index < 0 || index >= mItems.size()) {
        index = mItems.size() - 1;
    }

    EqWidgetIem* ItemWidget = mItems.takeAt(index);
    mItemsLayout->removeWidget(ItemWidget);
    ItemWidget->deleteLater();

    updateItemIndices();

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeAllItems()
{
    while (!mItems.isEmpty()) {
        removeItem(-1);
    }
}

void EqWidget::updateItemIndices()
{
    for (int i = 0; i < mItems.size(); ++i) {
        mItems[i]->setItemIndex(i);
    }
}

void EqWidget::setEqData(const QVector<EqWidgetItemData>& data)
{
    setItemCount(data.size());

    for (int i = 0; i < data.size() && i < mItems.size(); ++i) {
        mItems[i]->setItemData(data[i]);
    }
}

QVector<EqWidgetItemData> EqWidget::getEqData() const
{
    QVector<EqWidgetItemData> data;
    for (const auto& Item : mItems) {
        data.append(Item->getItemData());
    }
    return data;
}

void EqWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqWidget::onAddItemClicked()
{
    //addItem();
    setItemStretchFactor(++mItemStretchFactor);
}

void EqWidget::onRemoveItemClicked()
{
    //removeItem(-1);
    mItems[3]->hide();
}

void EqWidget::setMainLayoutSpacing(int spacing)
{
    mMainLayoutSpacing = spacing;
    if (mMainLayout) {
        mMainLayout->setSpacing(spacing);
    }
}

void EqWidget::setMainLayoutMargins(int left, int top, int right, int bottom)
{
    mMainLayoutMargins = QMargins(left, top, right, bottom);
    if (mMainLayout) {
        mMainLayout->setContentsMargins(left, top, right, bottom);
    }
}

void EqWidget::setMainLayoutMargins(int margin)
{
    setMainLayoutMargins(margin, margin, margin, margin);
}

void EqWidget::setTitleLayoutSpacing(int spacing)
{
    mTitleLayoutSpacing = spacing;
    if (mTitleLayout) {
        mTitleLayout->setSpacing(spacing);
    }
}

void EqWidget::setTitleLayoutMargins(int left, int top, int right, int bottom)
{
    mTitleLayoutMargins = QMargins(left, top, right, bottom);
    if (mTitleLayout) {
        mTitleLayout->setContentsMargins(left, top, right, bottom);
    }
}

void EqWidget::setTitleLayoutMargins(int margin)
{
    setTitleLayoutMargins(margin, margin, margin, margin);
}

void EqWidget::setItemsLayoutSpacing(int spacing)
{
    mItemsLayoutSpacing = spacing;
    if (mItemsLayout) {
        mItemsLayout->setSpacing(spacing);
    }
}

void EqWidget::setItemsLayoutMargins(int left, int top, int right, int bottom)
{
    mItemsLayoutMargins = QMargins(left, top, right, bottom);
    if (mItemsLayout) {
        mItemsLayout->setContentsMargins(left, top, right, bottom);
    }
}

void EqWidget::setItemsLayoutMargins(int margin)
{
    setItemsLayoutMargins(margin, margin, margin, margin);
}

void EqWidget::setScrollAreaMargins(int left, int top, int right, int bottom)
{
    mScrollAreaMargins = QMargins(left, top, right, bottom);
    if (mScrollArea) {
        mScrollArea->setContentsMargins(left, top, right, bottom);
    }
}

void EqWidget::setScrollAreaMargins(int margin)
{
    setScrollAreaMargins(margin, margin, margin, margin);
}

void EqWidget::setAllItemsLayoutSpacing(int spacing)
{
    for (auto* item : mItems) {
        if (item) {
            item->setLayoutSpacing(spacing);
        }
    }
}

void EqWidget::setAllItemsLayoutMargins(int left, int top, int right, int bottom)
{
    for (auto* item : mItems) {
        if (item) {
            item->setLayoutMargins(left, top, right, bottom);
        }
    }
}

void EqWidget::setAllItemsLayoutMargins(int margin)
{
    setAllItemsLayoutMargins(margin, margin, margin, margin);
}

void EqWidget::setAllItemsMinimumWidth(int width)
{
    for (auto* item : mItems) {
        if (item) {
            item->setMinimumItemWidth(width);
        }
    }
}

QMargins EqWidget::getMainLayoutMargins() const
{
    return mMainLayoutMargins;
}

QMargins EqWidget::getItemsLayoutMargins() const
{
    return mItemsLayoutMargins;
}

QMargins EqWidget::getScrollAreaMargins() const
{
    return mScrollAreaMargins;
}

void EqWidget::setAllItemsStretchFactors(int labelStretch, int comboStretch, int gainStretch,
                                         int freqStretch, int qStretch, int checkStretch)
{
    for (auto* item : mItems) {
        if (item) {
            item->setWidgetStretchFactors(labelStretch, comboStretch, gainStretch,
                                         freqStretch, qStretch, checkStretch);
        }
    }
}

void EqWidget::setMainLayoutStretchFactors(int leftStretch, int centerStretch, int rightStretch)
{
    mMainLeftStretch = leftStretch;
    mMainCenterStretch = centerStretch;
    mMainRightStretch = rightStretch;

    if (mMainLayout && mMainLayout->count() >= 3) {
        mMainLayout->setStretch(0, mMainLeftStretch);
        mMainLayout->setStretch(1, mMainCenterStretch);
        mMainLayout->setStretch(2, mMainRightStretch);
    }
}

void EqWidget::getMainLayoutStretchFactors(int& leftStretch, int& centerStretch, int& rightStretch) const
{
    leftStretch = mMainLeftStretch;
    centerStretch = mMainCenterStretch;
    rightStretch = mMainRightStretch;
}

void EqWidget::setContentLayoutStretchFactors(int labelAreaStretch, int scrollAreaStretch)
{
    mLabelAreaStretch = labelAreaStretch;
    mScrollAreaStretch = scrollAreaStretch;
}

void EqWidget::getContentLayoutStretchFactors(int& labelAreaStretch, int& scrollAreaStretch) const
{
    labelAreaStretch = mLabelAreaStretch;
    scrollAreaStretch = mScrollAreaStretch;
}

void EqWidget::applyLayoutSettings()
{
    if (mMainLayout) {
        mMainLayout->setSpacing(mMainLayoutSpacing);
        mMainLayout->setContentsMargins(mMainLayoutMargins);
    }

    if (mItemsLayout) {
        mItemsLayout->setSpacing(mItemsLayoutSpacing);
        mItemsLayout->setContentsMargins(mItemsLayoutMargins);
    }

    if (mScrollArea) {
        mScrollArea->setContentsMargins(mScrollAreaMargins);
    }
}

void EqWidget::updateItemWidth(EqWidgetIem* item)
{
    item->setFixedWidth(mItemStretchFactor * item->minimumWidth());
}

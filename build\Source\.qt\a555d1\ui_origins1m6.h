/********************************************************************************
** Form generated from reading UI file 'origins1m6.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ORIGINS1M6_H
#define UI_ORIGINS1M6_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <pushbuttons1m7.h>
#include <volumemeters1m4.h>
#include <volumemeters1m5.h>
#include <vsliders1m2.h>

QT_BEGIN_NAMESPACE

class Ui_OriginS1M6
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QPushButton *pushButtonClose;
    QLineEdit *lineEdit;
    PushButtonS1M7 *widgetPushButtonGroup1;
    VolumeMeterS1M4 *widgetLinkedMeterLeft;
    VolumeMeterS1M5 *widgetLinkedMeterRight;
    VSliderS1M2 *widgetLinkedVSlider;

    void setupUi(QWidget *OriginS1M6)
    {
        if (OriginS1M6->objectName().isEmpty())
            OriginS1M6->setObjectName("OriginS1M6");
        OriginS1M6->resize(140, 280);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(OriginS1M6->sizePolicy().hasHeightForWidth());
        OriginS1M6->setSizePolicy(sizePolicy);
        OriginS1M6->setMinimumSize(QSize(60, 220));
        gridLayout = new QGridLayout(OriginS1M6);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(OriginS1M6);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        pushButtonClose = new QPushButton(frame);
        pushButtonClose->setObjectName("pushButtonClose");
        pushButtonClose->setGeometry(QRect(110, 10, 21, 21));
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(10, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        widgetPushButtonGroup1 = new PushButtonS1M7(frame);
        widgetPushButtonGroup1->setObjectName("widgetPushButtonGroup1");
        widgetPushButtonGroup1->setGeometry(QRect(10, 230, 91, 40));
        sizePolicy.setHeightForWidth(widgetPushButtonGroup1->sizePolicy().hasHeightForWidth());
        widgetPushButtonGroup1->setSizePolicy(sizePolicy);
        widgetPushButtonGroup1->setMinimumSize(QSize(0, 0));
        widgetLinkedMeterLeft = new VolumeMeterS1M4(frame);
        widgetLinkedMeterLeft->setObjectName("widgetLinkedMeterLeft");
        widgetLinkedMeterLeft->setGeometry(QRect(10, 110, 21, 111));
        sizePolicy.setHeightForWidth(widgetLinkedMeterLeft->sizePolicy().hasHeightForWidth());
        widgetLinkedMeterLeft->setSizePolicy(sizePolicy);
        widgetLinkedMeterRight = new VolumeMeterS1M5(frame);
        widgetLinkedMeterRight->setObjectName("widgetLinkedMeterRight");
        widgetLinkedMeterRight->setGeometry(QRect(80, 110, 21, 111));
        sizePolicy.setHeightForWidth(widgetLinkedMeterRight->sizePolicy().hasHeightForWidth());
        widgetLinkedMeterRight->setSizePolicy(sizePolicy);
        widgetLinkedVSlider = new VSliderS1M2(frame);
        widgetLinkedVSlider->setObjectName("widgetLinkedVSlider");
        widgetLinkedVSlider->setGeometry(QRect(40, 110, 31, 111));

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(OriginS1M6);

        QMetaObject::connectSlotsByName(OriginS1M6);
    } // setupUi

    void retranslateUi(QWidget *OriginS1M6)
    {
        OriginS1M6->setWindowTitle(QCoreApplication::translate("OriginS1M6", "Form", nullptr));
        pushButtonClose->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class OriginS1M6: public Ui_OriginS1M6 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ORIGINS1M6_H

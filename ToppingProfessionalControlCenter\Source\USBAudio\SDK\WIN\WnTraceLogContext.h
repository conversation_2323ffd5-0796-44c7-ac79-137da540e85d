/************************************************************************
 *
 *  Module:       WnTraceLogContext.h
 *
 *  Description:  Trace and log context
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    Udo <PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnTraceLogContext_h__
#define __WnTraceLogContext_h__

#include <evntprov.h>

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

// standard trace bit allocations
// further bits are to be defined by the local project
#define TRCERR      0   // fatal errors, failed API calls, etc.
#define TRCWRN      1   // warnings, e.g. invalid input parameters that can be tolerated
#define TRCINF      2   // info messages, low volume, e.g. general state transitions, load, unload, PnP add/remove, etc.
#define TRCEXTINF   3   // info messages, high volume



// calculate bit mask from bit number
#define TRCBITMASK(bitnb)   ( (0x1U)<<(bitnb) )


// default context name
#define WNTRACELOG_DEFAULT_CONTEXT_NAME   TEXT("default")


// one entry per trace bit
struct WnTraceInfoEntry {
    int traceBitNb;                 // 0..31
    const char* traceBitTag;          // used in trace message prefix
    const char* traceBitDescription;  // used for registry key name
};



//
// This class represents a context (channel) used
// to output trace and log messages.
//
class WnTraceLogContext
{
public:
    // ctor
    WnTraceLogContext(
        const TCHAR* contextName = WNTRACELOG_DEFAULT_CONTEXT_NAME
        );

    // dtor
    virtual
    ~WnTraceLogContext();


/////////////////////////////////////////
// Methods
/////////////////////////////////////////
public:


    // context name

    void
    SetContextName(
        const TCHAR* contextName
        );

    const TCHAR*
    GetContextName()
        { return mContextName; }



    // Set module name prefix string to prepend to each message.
    // The provided string is stored internally.
    // To disable the prefix call the function with NULL or an empty string
    void
    SetPrefix(
        const TCHAR* prefix // points to null-terminated string
        );


    void
    SetTraceBitInfo(
        const WnTraceInfoEntry* infoTable
        );


    // trace mask

    void
    SetMask(
        unsigned int newMask
        )
            { mMask = newMask; }

    unsigned int
    GetMask()
            { return mMask; }



    // enable/disable output to OutputDebugString (debugger)
    void
    SetOutputDebugStringEnable(
        bool enable
        )
            { mOutputDebugStringEnabled = enable; }



    // Trace file name and path.
    // traceFilePathAndName can be:
    // - a file name only
    // - a relative path and file name
    // - an absolute path and file name (fully qualified)
    // traceFilePathAndName will be passed to fopen unmodified
    void
    SetTraceFile(
        const TCHAR* traceFilePathAndName
        );

    const TCHAR*
    GetTraceFile()
        { return mTraceFilePathAndName; }



    // insert PID into file name: on/off
    void
    SetTraceFileInsertPid(bool insertPid)
        { mTraceFileInsertPid = insertPid; }

    bool
    GetTraceFileInsertPid()
        { return mTraceFileInsertPid; }


    // append mode on/off
    void
    SetTraceFileAppend(bool append)
        { mTraceFileAppend = append; }

    bool
    GetTraceFileAppend()
        { return mTraceFileAppend; }


    // Set max size of log files, in bytes
    // default is cDefaultLogFileSizeLimit
    void
    SetLogFileSizeLimit(
        unsigned int maxSizeInBytes
        );

    // Set flush mode
    void
    SetFlushAfterEachWrite(
        bool enable
        );


    // Enable/Disable trace file.
    // Before enable, trace filename and path must be set with SetTraceFile().
    // Append mode and size limit can be adjusted before.
    // If fifoBufferSizeKB != 0 then an intermediate buffer and a worker thread will be created.
    WNERR
    SetTraceFileEnable(bool enable);


    // returns true if trace file is currently enabled
    bool
    GetTraceFileEnable()
        { return mTraceFileEnable; }


#if LIBWN_CAT_TraceLogFileBuffered

    // This creates an intermediate buffer for the trace file
    // and starts an internal worker thread.
    bool
    StartThread(unsigned int bufferSizeInKB = 64);

    // Stops the internal worker thread and free the buffer.
    void
    StopThread();

#endif


    // prefix features
    void
    SetDatePrefixEnable(bool enable)
        { mDatePrefixEnable = enable; }

    void
    SetTimePrefixEnable(bool enable)
        { mTimePrefixEnable = enable; }

    void
    SetProcessIdEnable(bool enable)
        { mProcessIdPrefixEnable = enable; }

    void
    SetThreadIdEnable(bool enable)
        { mThreadIdPrefixEnable = enable; }

    void
    SetBitTagPrefixEnable(bool enable)
        { mBitTagPrefixEnable = enable; }


    // print message, format string conforms to printf conventions
    void Print(const char* format, ...);
    void Print(const WCHAR* format, ...);
    void PrintEx(int bitnb, const char* format, ...);
    void PrintEx(int bitnb, const WCHAR* format, ...);

    // print message, format string conforms to printf conventions
    void VPrintf(const char* format, va_list args)  { VPrintfEx(-1, format, args); }
    void VPrintf(const WCHAR* format, va_list args) { VPrintfEx(-1, format, args); }
    void VPrintfEx(int bitnb, const char* format, va_list args);
    void VPrintfEx(int bitnb, const WCHAR* format, va_list args);


    // print a hex dump of bytes for the given buffer
    void
    DumpBytes(
        const void* ptr,
        size_t byteCount
        );

    // print a hex dump of DWORDs for the given buffer
    void
    DumpDwords(
        const unsigned long* ptr,
        unsigned int dwordCount
        );


    // print GUID in standard registry format, e.g. {c200e360-38c5-11ce-ae62-08002b2b79ef}
    void
    PrintGuid(
        const GUID* guid
        );


    // get tag for trace bit
    const char*
    GetTraceBitTag(int bitnb)
        { return ( (bitnb>=0 && bitnb<TB_ARRAY_ELEMENTS(mTraceBitTable)) ? mTraceBitTable[bitnb].traceBitTag : "" ); }

    // get description for trace bit
    const char*
    GetTraceBitDescription(int bitnb)
        { return ( (bitnb>=0 && bitnb<TB_ARRAY_ELEMENTS(mTraceBitTable)) ? mTraceBitTable[bitnb].traceBitDescription: "" ); }

    // ETW

    WNERR
    RegisterEtwTracing(const GUID& etwTraceProviderGuid);

    void
    UnregisterEtwTracing();


/////////////////////////////////////////
// Implementation
/////////////////////////////////////////
protected:

    void
    InitTraceBitInfo();

    // output the given string to the active targets
    // called with no lock held
    template<typename T>
    void
    OutputString(
        const T* str,   // points to null-terminated string
        int bitnb = -1
        );

    // called with lock held
    virtual
    void
    WriteString(
        const char* str   // points to null-terminated string
        );

    // called with lock held
    virtual
    unsigned int
    GetLinePrefix(
        int bitnb,
        char* buffer,
        unsigned int numberOfChars
        );


    struct TraceBitEntry
    {
        const char* traceBitTag;          // used in trace message prefix
        const char* traceBitDescription;  // used for registry key name
    };

/////////////////////////////////////////
// Data Members
/////////////////////////////////////////
protected:

    // magic value that marks this instance valid, see constructor
    unsigned int mMagic;

    // current trace mask
    unsigned int mMask;

    // current log file
#if LIBWN_CAT_TraceLogFileBuffered
    WnTraceLogFileBuffered mTraceFile;
#else
    WnTraceLogFile mTraceFile;
#endif

    // current trace file path and name
    TCHAR mTraceFilePathAndName[MAX_PATH];

    // true if trace file is enabled
    bool mTraceFileEnable;

    // true if append trace file
    bool mTraceFileAppend;

    // true if PID is to be inserted into trace file name
    bool mTraceFileInsertPid;

    // true if OutputDebugString is enabled
    bool mOutputDebugStringEnabled;

    // prefix string
    char mPrefix[44];

    // name of the trace context
    // used for saving/loading settings from registry
    TCHAR mContextName[40];

    // prefix features
    bool mDatePrefixEnable;
    bool mTimePrefixEnable;
    bool mProcessIdPrefixEnable;
    bool mThreadIdPrefixEnable;
    bool mBitTagPrefixEnable;


    // lock that protects state
    WnCriticalSection mLock;
    // current state
    unsigned int mCol;

    // table with one entry for each trace bit, 0..31
    TraceBitEntry mTraceBitTable[32];

    // ETW tracing
    // provider handle
    REGHANDLE mTraceProviderHandle {0};

    // event descriptor is fixed
    EVENT_DESCRIPTOR mEventDescripor;

    // trace provider GUID
    GUID mTraceProviderGuid {};

// make copy constructor and assignment operator unavailable
PRIVATE_COPY_CONSTRUCTOR(WnTraceLogContext)
PRIVATE_ASSIGNMENT_OPERATOR(WnTraceLogContext)

};//class WnTraceLogContext

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnTraceLogContext_h__

/*************************** EOF **************************************/

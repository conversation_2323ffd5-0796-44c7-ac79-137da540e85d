/********************************************************************************
** Form generated from reading UI file 'messageboxwidget3.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MESSAGEBOXWIDGET3_H
#define UI_MESSAGEBOXWIDGET3_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MessageBoxWidget3
{
public:
    QGridLayout *gridLayout_4;
    QSpacerItem *verticalSpacer_3;
    QGridLayout *gridLayout_2;
    QSpacerItem *horizontalSpacer;
    QGridLayout *gridLayout;
    QCheckBox *CheckBox1;
    QSpacerItem *verticalSpacer_2;
    QCheckBox *CheckBox2;
    QSpacerItem *verticalSpacer;
    QCheckBox *CheckBox3;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer_4;
    QGridLayout *gridLayout_3;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *PushButton1;
    QSpacerItem *horizontalSpacer_5;
    QPushButton *PushButton2;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *verticalSpacer_5;

    void setupUi(QWidget *MessageBoxWidget3)
    {
        if (MessageBoxWidget3->objectName().isEmpty())
            MessageBoxWidget3->setObjectName("MessageBoxWidget3");
        MessageBoxWidget3->resize(300, 200);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(MessageBoxWidget3->sizePolicy().hasHeightForWidth());
        MessageBoxWidget3->setSizePolicy(sizePolicy);
        gridLayout_4 = new QGridLayout(MessageBoxWidget3);
        gridLayout_4->setSpacing(0);
        gridLayout_4->setObjectName("gridLayout_4");
        gridLayout_4->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_3 = new QSpacerItem(20, 29, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_4->addItem(verticalSpacer_3, 0, 0, 1, 1);

        gridLayout_2 = new QGridLayout();
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName("gridLayout_2");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_2->addItem(horizontalSpacer, 0, 0, 1, 1);

        gridLayout = new QGridLayout();
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        CheckBox1 = new QCheckBox(MessageBoxWidget3);
        CheckBox1->setObjectName("CheckBox1");
        sizePolicy.setHeightForWidth(CheckBox1->sizePolicy().hasHeightForWidth());
        CheckBox1->setSizePolicy(sizePolicy);
        CheckBox1->setMinimumSize(QSize(1, 1));

        gridLayout->addWidget(CheckBox1, 0, 0, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 17, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_2, 1, 0, 1, 1);

        CheckBox2 = new QCheckBox(MessageBoxWidget3);
        CheckBox2->setObjectName("CheckBox2");
        sizePolicy.setHeightForWidth(CheckBox2->sizePolicy().hasHeightForWidth());
        CheckBox2->setSizePolicy(sizePolicy);
        CheckBox2->setMinimumSize(QSize(1, 1));

        gridLayout->addWidget(CheckBox2, 2, 0, 1, 1);

        verticalSpacer = new QSpacerItem(20, 17, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer, 3, 0, 1, 1);

        CheckBox3 = new QCheckBox(MessageBoxWidget3);
        CheckBox3->setObjectName("CheckBox3");
        sizePolicy.setHeightForWidth(CheckBox3->sizePolicy().hasHeightForWidth());
        CheckBox3->setSizePolicy(sizePolicy);
        CheckBox3->setMinimumSize(QSize(1, 1));

        gridLayout->addWidget(CheckBox3, 4, 0, 1, 1);

        gridLayout->setRowStretch(0, 100);
        gridLayout->setRowStretch(1, 60);
        gridLayout->setRowStretch(2, 100);
        gridLayout->setRowStretch(3, 60);
        gridLayout->setRowStretch(4, 100);

        gridLayout_2->addLayout(gridLayout, 0, 1, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_2->addItem(horizontalSpacer_2, 0, 2, 1, 1);

        gridLayout_2->setColumnStretch(0, 8);
        gridLayout_2->setColumnStretch(1, 100);
        gridLayout_2->setColumnStretch(2, 8);

        gridLayout_4->addLayout(gridLayout_2, 1, 0, 1, 1);

        verticalSpacer_4 = new QSpacerItem(20, 24, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_4->addItem(verticalSpacer_4, 2, 0, 1, 1);

        gridLayout_3 = new QGridLayout();
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName("gridLayout_3");
        horizontalSpacer_3 = new QSpacerItem(40, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_3, 0, 0, 1, 1);

        PushButton1 = new QPushButton(MessageBoxWidget3);
        PushButton1->setObjectName("PushButton1");
        sizePolicy.setHeightForWidth(PushButton1->sizePolicy().hasHeightForWidth());
        PushButton1->setSizePolicy(sizePolicy);
        PushButton1->setMinimumSize(QSize(1, 1));

        gridLayout_3->addWidget(PushButton1, 0, 1, 1, 1);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_5, 0, 2, 1, 1);

        PushButton2 = new QPushButton(MessageBoxWidget3);
        PushButton2->setObjectName("PushButton2");
        sizePolicy.setHeightForWidth(PushButton2->sizePolicy().hasHeightForWidth());
        PushButton2->setSizePolicy(sizePolicy);
        PushButton2->setMinimumSize(QSize(1, 1));

        gridLayout_3->addWidget(PushButton2, 0, 3, 1, 1);

        horizontalSpacer_4 = new QSpacerItem(40, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_4, 0, 4, 1, 1);

        gridLayout_3->setColumnStretch(0, 100);
        gridLayout_3->setColumnStretch(1, 80);
        gridLayout_3->setColumnStretch(2, 10);
        gridLayout_3->setColumnStretch(3, 80);
        gridLayout_3->setColumnStretch(4, 100);

        gridLayout_4->addLayout(gridLayout_3, 3, 0, 1, 1);

        verticalSpacer_5 = new QSpacerItem(20, 38, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_4->addItem(verticalSpacer_5, 4, 0, 1, 1);

        gridLayout_4->setRowStretch(0, 70);
        gridLayout_4->setRowStretch(1, 160);
        gridLayout_4->setRowStretch(2, 60);
        gridLayout_4->setRowStretch(3, 60);
        gridLayout_4->setRowStretch(4, 90);

        retranslateUi(MessageBoxWidget3);

        QMetaObject::connectSlotsByName(MessageBoxWidget3);
    } // setupUi

    void retranslateUi(QWidget *MessageBoxWidget3)
    {
        MessageBoxWidget3->setWindowTitle(QCoreApplication::translate("MessageBoxWidget3", "Form", nullptr));
        CheckBox1->setText(QString());
        CheckBox2->setText(QString());
        CheckBox3->setText(QString());
        PushButton1->setText(QString());
        PushButton2->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class MessageBoxWidget3: public Ui_MessageBoxWidget3 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MESSAGEBOXWIDGET3_H

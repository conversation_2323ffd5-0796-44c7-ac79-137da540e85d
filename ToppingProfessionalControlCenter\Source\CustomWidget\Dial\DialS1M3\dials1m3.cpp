#include "dials1m3.h"
#include "globalfont.h"
#include <qnamespace.h>
#include <qtypes.h>

DialS1M3::DialS1M3(QWidget* parent)
    : QWidget(parent)
{
    setFocusPolicy(Qt::StrongFocus);
}
DialS1M3::~DialS1M3()
{

}

// override
void DialS1M3::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int diameter = qMin(rect().width(), rect().height());
    mPenWidth=diameter / 20 + 1;
    mRectDial.setX(rect().x() + mPenWidth);
    mRectDial.setY(rect().y() + mPenWidth);
    if(diameter == rect().width())
    {
        mRectDial.setY(rect().y() + (rect().height() - diameter) / 2 + mPenWidth);
    }
    if(diameter == rect().height())
    {
        mRectDial.setX(rect().x() + (rect().width() - diameter) / 2 + mPenWidth);
    }
    mRectDial.setWidth(diameter - 2 * mPenWidth);
    mRectDial.setHeight(diameter - 2 * mPenWidth);
}
void DialS1M3::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void DialS1M3::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(mRectDial.contains(e->pos()))
    {
        if(mValue != mValueDefault)
        {
            mValue = mValueDefault;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M3::mousePressEvent(QMouseEvent* e)
{
    if(mMouseEnabled && mRectDial.contains(e->pos()))
    {
        mPressed = true;
        mPressedValue = mValue;
        mPressedPoint = e->pos();
    }
}
void DialS1M3::mouseMoveEvent(QMouseEvent* e)
{
    float value=0;
    if(mPressed)
    {
        value = mPressedValue;
        value += ((int) (mPressedPoint.y() - e->pos().y()) / mSensitivity) * mValueStep;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M3::mouseReleaseEvent(QMouseEvent* e)
{
    Q_UNUSED(e);
    mPressed = false;
}
void DialS1M3::wheelEvent(QWheelEvent* e)
{
    float value=mValue;
    value += e->angleDelta().y() / 120;
    value = qMax(value, mValueMin);
    value = qMin(value, mValueMax);
    if(mValue != value)
    {
        mValue = value;
        emit valueChanged(mValue);
    }
    update();
}
void DialS1M3::keyPressEvent(QKeyEvent* e)
{
    float value=mValue;
    if(e->key() == Qt::Key_Up || e->key() == Qt::Key_Right)
    {
        value += 1;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
    else if(e->key() == Qt::Key_Down || e->key() == Qt::Key_Left)
    {
        value -= 1;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
}
void DialS1M3::drawBG(QPainter* painter)
{
    painter->fillRect(rect(), Qt::transparent);
}
void DialS1M3::drawElement(QPainter* painter)
{
    painter->save();
    QPen pen=painter->pen();
    qreal arcPenWidth = mPenWidth * 0.9;
    qreal innerPenWidth = mPenWidth * 0.6;
    qreal innerSpace = mPenWidth * 1.5;
    qreal mCurValueAngle = 270 * (mValue - mValueMin) / (mValueMax - mValueMin);

    pen.setWidth(arcPenWidth);
    pen.setColor(mColorCircleBG);
    painter->setPen(pen);
    painter->drawArc(mRectDial, -45 * 16, 270 * 16);

    pen.setColor(mColorDial);
    pen.setWidth(innerPenWidth);
    painter->setPen(pen);
    painter->setBrush(mColorCircleBG);
    QRectF innerRect = mRectDial.adjusted(innerSpace, innerSpace, -innerSpace, -innerSpace);
    painter->drawEllipse(innerRect);

    pen.setWidth(arcPenWidth);
    pen.setColor(mColorCircleValue);
    painter->setPen(pen);
    qreal curAngle = (225 - mCurValueAngle) * 16;
    painter->drawArc(mRectDial, mDoublePercent ? 90 * 16 : curAngle, mDoublePercent ? (135 - mCurValueAngle) * 16 : mCurValueAngle * 16);

    if(mValueShowArrow)
    {
        painter->save();
        qreal dialRadius = mRectDial.width() / 2.0;
        qreal innerCircleRadius = dialRadius - mPenWidth * 2.0;
        qreal indicatorWidth = innerCircleRadius * 0.05;
        qreal indicatorHeight = innerCircleRadius *0.23;
        painter->translate(mRectDial.center());
        qreal degRotate = -135 + mCurValueAngle;
        painter->rotate(degRotate);
        QRectF indicatorRect(-indicatorWidth/2.0, -dialRadius + mPenWidth * 1.5, indicatorWidth, indicatorHeight);
        qreal cornerRadius = indicatorWidth/4.0;
        painter->setBrush(mColorHandle);
        pen.setColor(mColorHandle);
        pen.setWidth(mPenWidth * 0.4);
        painter->setPen(pen);
        painter->drawRoundedRect(indicatorRect, cornerRadius, cornerRadius);
        painter->restore();
    }

    if(mValueShowText)
    {
        painter->resetTransform();
        int widthText=mRectDial.width() * 0.6;
        int heightText=mRectDial.height() * 0.3;
        QRect rectText(mRectDial.x() + (mRectDial.width() - widthText) / 2, mRectDial.y() + (mRectDial.height() - heightText) / 2, widthText, heightText);
            // get suitable font
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "+88", rectText));
        painter->setFont(mFont);
        painter->setPen(mColorText);
            // Text
        QString str;
        if(mValueShowSign)
        {
            if(mValue > 0)
            {
                str = QString("+%1").arg((double) mValue, 0, 'f', mPrecision);
            }
            else
            {
                str = QString("%1").arg((double) mValue, 0, 'f', mPrecision);
            }
        }
        else
        {
            str = QString("%1").arg((double) mValue, 0, 'f', mPrecision);
        }
        if(str.toFloat() == mValueMin)
        {
            str = mShowInfinitesimal ? ("-∞") : (str);
        }
        else if(str.toFloat() == mValueMax)
        {
            str = mShowInfinity ? ("+∞") : (str);
        }
        painter->drawText(rectText, Qt::AlignCenter, str);
    }
    painter->restore();
}

// setter & getter
DialS1M3& DialS1M3::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
DialS1M3& DialS1M3::setValue(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValue = value;
    update();
    return *this;
}
DialS1M3& DialS1M3::setDefault(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValueDefault = value;
    return *this;
}
DialS1M3& DialS1M3::setRange(float min, float max)
{
    if(min > max)
    {
        return *this;
    }
    mValueMin = min;
    mValueMax = max;
    if(mValueMax < mValueDefault || mValueDefault < mValueMin)
    {
        mValueDefault = mValueMin;
    }
    update();
    return *this;
}
DialS1M3& DialS1M3::setStep(float step)
{
    if(step > mValueMax - mValueMin || step <= 0)
    {
        return *this;
    }
    mValueStep = step;
    return *this;
}
DialS1M3& DialS1M3::setPrecision(int precision)
{
    if(precision > 3 || precision < 0)
    {
        return *this;
    }
    mPrecision = precision;
    update();
    return *this;
}
DialS1M3& DialS1M3::setSensitivity(int sensitivity)
{
    if(sensitivity > 100 || sensitivity <= 0)
    {
        return *this;
    }
    mSensitivity = sensitivity;
    return *this;
}
DialS1M3& DialS1M3::setMovable(bool status)
{
    mMouseEnabled = status;
    return *this;
}
DialS1M3& DialS1M3::setDoublePercent(bool status)
{
    mDoublePercent = status;
    update();
    return *this;
}
DialS1M3& DialS1M3::setColorBG(QColor color)
{
    mColorBG = color;
    update();
    return *this;
}
DialS1M3& DialS1M3::setColorDial(QColor color)
{
    mColorDial = color;
    update();
    return *this;
}
DialS1M3& DialS1M3::setColorCircleBG(QColor color)
{
    mColorCircleBG = color;
    update();
    return *this;
}
DialS1M3& DialS1M3::setColorCircleValue(QColor color)
{
    mColorCircleValue = color;
    update();
    return *this;
}
DialS1M3& DialS1M3::setColorHandle(QColor color)
{
    mColorHandle = color;
    update();
    return *this;
}
DialS1M3& DialS1M3::setColorText(QColor color)
{
    mColorText = color;
    update();
    return *this;
}
DialS1M3& DialS1M3::showArrow(bool status)
{
    mValueShowArrow = status;
    update();
    return *this;
}
DialS1M3& DialS1M3::showCircle(bool status)
{
    mValueShowCircle = status;
    update();
    return *this;
}
DialS1M3& DialS1M3::showText(bool status)
{
    mValueShowText = status;
    update();
    return *this;
}
DialS1M3& DialS1M3::showSign(bool status)
{
    mValueShowSign = status;
    update();
    return *this;
}
DialS1M3& DialS1M3::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
DialS1M3& DialS1M3::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}

/********************************************************************************
** Form generated from reading UI file 'pushbuttongroups1m2.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PUSHBUTTONGROUPS1M2_H
#define UI_PUSHBUTTONGROUPS1M2_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_PushButtonGroupS1M2
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QGridLayout *gridLayout_3;
    QSpacerItem *verticalSpacer;
    QSpacerItem *horizontalSpacer_2;
    QGridLayout *gridLayout_2;
    QPushButton *PushButtonMUTE;
    QSpacerItem *verticalSpacer_2;
    QPushButton *PushButtonSOLO;
    QSpacerItem *horizontalSpacer;
    QSpacerItem *verticalSpacer_4;

    void setupUi(QWidget *PushButtonGroupS1M2)
    {
        if (PushButtonGroupS1M2->objectName().isEmpty())
            PushButtonGroupS1M2->setObjectName("PushButtonGroupS1M2");
        PushButtonGroupS1M2->resize(120, 100);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(PushButtonGroupS1M2->sizePolicy().hasHeightForWidth());
        PushButtonGroupS1M2->setSizePolicy(sizePolicy);
        PushButtonGroupS1M2->setMinimumSize(QSize(6, 5));
        gridLayout = new QGridLayout(PushButtonGroupS1M2);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(PushButtonGroupS1M2);
        frame->setObjectName("frame");
        sizePolicy.setHeightForWidth(frame->sizePolicy().hasHeightForWidth());
        frame->setSizePolicy(sizePolicy);
        frame->setFrameShape(QFrame::Shape::NoFrame);
        frame->setFrameShadow(QFrame::Shadow::Plain);
        frame->setLineWidth(0);
        gridLayout_3 = new QGridLayout(frame);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName("gridLayout_3");
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        verticalSpacer = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer, 0, 1, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_2, 1, 0, 1, 1);

        gridLayout_2 = new QGridLayout();
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName("gridLayout_2");
        PushButtonMUTE = new QPushButton(frame);
        PushButtonMUTE->setObjectName("PushButtonMUTE");
        sizePolicy.setHeightForWidth(PushButtonMUTE->sizePolicy().hasHeightForWidth());
        PushButtonMUTE->setSizePolicy(sizePolicy);
        PushButtonMUTE->setMinimumSize(QSize(1, 1));

        gridLayout_2->addWidget(PushButtonMUTE, 2, 0, 1, 1);

        verticalSpacer_2 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_2->addItem(verticalSpacer_2, 1, 0, 1, 1);

        PushButtonSOLO = new QPushButton(frame);
        PushButtonSOLO->setObjectName("PushButtonSOLO");
        sizePolicy.setHeightForWidth(PushButtonSOLO->sizePolicy().hasHeightForWidth());
        PushButtonSOLO->setSizePolicy(sizePolicy);
        PushButtonSOLO->setMinimumSize(QSize(1, 1));

        gridLayout_2->addWidget(PushButtonSOLO, 0, 0, 1, 1);

        gridLayout_2->setRowStretch(0, 14);
        gridLayout_2->setRowStretch(1, 6);
        gridLayout_2->setRowStretch(2, 14);

        gridLayout_3->addLayout(gridLayout_2, 1, 1, 1, 1);

        horizontalSpacer = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer, 1, 2, 1, 1);

        verticalSpacer_4 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer_4, 2, 1, 1, 1);

        gridLayout_3->setRowStretch(0, 8);
        gridLayout_3->setRowStretch(1, 34);
        gridLayout_3->setRowStretch(2, 8);
        gridLayout_3->setColumnStretch(0, 11);
        gridLayout_3->setColumnStretch(1, 38);
        gridLayout_3->setColumnStretch(2, 11);

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(PushButtonGroupS1M2);

        QMetaObject::connectSlotsByName(PushButtonGroupS1M2);
    } // setupUi

    void retranslateUi(QWidget *PushButtonGroupS1M2)
    {
        PushButtonGroupS1M2->setWindowTitle(QCoreApplication::translate("PushButtonGroupS1M2", "Form", nullptr));
        PushButtonMUTE->setText(QString());
        PushButtonSOLO->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class PushButtonGroupS1M2: public Ui_PushButtonGroupS1M2 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PUSHBUTTONGROUPS1M2_H

{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Qt/Tools/CMake_64/bin/cmake.exe", "cpack": "D:/Qt/Tools/CMake_64/bin/cpack.exe", "ctest": "D:/Qt/Tools/CMake_64/bin/ctest.exe", "root": "D:/Qt/Tools/CMake_64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-54ae52dfaa7e92ea1194.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-7e2b09fc31ce479dad88.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-bb63f7838dbd65958239.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-98513bd530553bc3cd1b.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-7e2b09fc31ce479dad88.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-54ae52dfaa7e92ea1194.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-98513bd530553bc3cd1b.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-bb63f7838dbd65958239.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}
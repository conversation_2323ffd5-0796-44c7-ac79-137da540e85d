/*******************************************************************************
 *
 *  Module:         libtb_OSEnv_impl.cpp
 *  Description:    implementation of libtb environment
 *
 *  Runtime Env.:   any
 *  Author(s):      Udo <PERSON>hardt
 *  Company:        Thesycon GmbH, Ilmenau
 *  Copyright:      (c) 2010 Thesycon Systemsoftware and Consulting GmbH
 *
 ******************************************************************************/

#include "libwn_min_global.h"

void* LIBTB_NEW_DELETE_DECL operator new   (size_t size, TB_MEM_TYPE /*memType*/) throw()
{
    // call standard new operator at global scope, provided by C++ runtime
    return ::operator new(size, std::nothrow);
}

void* LIBTB_NEW_DELETE_DECL operator new[] (size_t size, TB_MEM_TYPE /*memType*/) throw()
{
    // call standard new operator at global scope, provided by C++ runtime
    return ::operator new[](size, std::nothrow);
}


void LIBTB_NEW_DELETE_DECL operator delete   (void* p, TB_MEM_TYPE /*memType*/) throw()
{
    // call standard delete operator at global scope
    ::operator delete(p);
}

void LIBTB_NEW_DELETE_DECL operator delete[] (void* p, TB_MEM_TYPE /*memType*/) throw()
{
    // call standard delete operator at global scope
    ::operator delete[](p);
}


// placement new
void* LIBTB_NEW_DELETE_DECL operator new   (size_t /*size*/, void* p, TB_MEM_TYPE /*memTypeDummy*/) throw()
{
    return p;
}

void* LIBTB_NEW_DELETE_DECL operator new[] (size_t /*size*/, void* p, TB_MEM_TYPE /*memTypeDummy*/) throw()
{
    return p;
}

// matching delete
void LIBTB_NEW_DELETE_DECL operator delete   (void* /*p*/, void* /*dummy*/, TB_MEM_TYPE /*memTypeDummy*/) throw()
{
}

void LIBTB_NEW_DELETE_DECL operator delete[] (void* /*p*/, void* /*dummy*/, TB_MEM_TYPE /*memTypeDummy*/) throw()
{
}


// note: delete maps to C++ runtime



// libbase AL TbAtomicXxx functions

TB_COMPILE_TIME_ASSERT(sizeof(T_INT32)==sizeof(LONG));
TB_COMPILE_TIME_ASSERT(sizeof(T_UINT32)==sizeof(LONG));

#ifdef UNDER_CE
#define TB_INTERLOCKED_PTR_TYPE LPLONG
#else
#define TB_INTERLOCKED_PTR_TYPE volatile LONG*
#endif

T_INT32
TBASE_AL_CALL
TbAtomicIncrementInt32(volatile T_INT32* target)
{
    return InterlockedIncrement((TB_INTERLOCKED_PTR_TYPE)target);
}

T_INT32
TBASE_AL_CALL
TbAtomicDecrementInt32(volatile T_INT32* target)
{
    return InterlockedDecrement((TB_INTERLOCKED_PTR_TYPE)target);
}

T_UINT32
TBASE_AL_CALL
TbAtomicExchangeUInt32(volatile T_UINT32* target, T_UINT32 val)
{
    return InterlockedExchange((TB_INTERLOCKED_PTR_TYPE)target, val);
}

//### InterlockedOr or is contained in Microsoft docs but not in .h files...
// T_UINT32
// TBASE_AL_CALL
// TbAtomicOrUInt32(volatile T_UINT32* target, T_UINT32 val)
// {
//  return InterlockedOr((volatile LONG*)target, val);
// }

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

#if TB_DEBUG

// map trace channel
inline
int
MapTraceChannel(
    TbDebug::TraceChannel chan
    )
{
    return (
        (TbDebug::TrcError == chan) ? TRCERR :
        (TbDebug::TrcInfo == chan) ?  TRCINF :
        (TbDebug::TrcVerbose == chan) ? TRCEXTINF :
        TRCEXTINF
        );
}


//static
void
TbDebug::DebugVPrintf(
    TraceChannel chan,
    const char* format,
    va_list args
    )
{
    WNTRACE(MapTraceChannel(chan),
        gWnTrace->VPrintf(format, args);
        );
}


//static
void
TbDebug::DebugDumpBytes(
    TraceChannel chan,
    const void* ptr,
    unsigned int byteCount
    )
{
    WNTRACE(MapTraceChannel(chan),
        gWnTrace->DumpBytes(ptr, byteCount);
        );
}


//static
void
TbDebug::AssertFailed(
    const char* conditionstr,
    const char* filename,
    unsigned int linenb
    )
{
    WNTRACE(TRCERR,
        tprint("\nASSERTION FAILED: (%s)\n" "  %s(%u)\n", conditionstr, filename, linenb );
        );
}

#endif  //TB_DEBUG

#ifdef LIBWN_NAMESPACE
}
#endif

/******************************** EOF ***********************************/

#ifndef MAINWINDOW_BASE_H
#define MAINWINDOW_BASE_H


#include <QTimer>
#include <QMainWindow>

#include "devicebase.h"


class MainWindow_Base : public QMainWindow
{
    Q_OBJECT
public:
    explicit MainWindow_Base(QWidget* parent=nullptr);
    virtual ~MainWindow_Base();
    void startDeviceAuthentication();
protected:
    void showFrameRate();
    void showSendingFrame(bool state=true) { mDevice->showSendingFrame(state); }
    void increaseFrameTick();
    void assignDevice(DeviceBase* device, QString deviceName);
    void setAuthResult(int result=0);
    virtual void sendAuthInfoToDevice() = 0;
    virtual void onAuthResult(int result=0) = 0;
    virtual void onUSBAudioAttributeChanged(QString attribute, QString value) = 0;
private:
    DeviceBase* mDevice;
    QString mDeviceName;
    QTimer mTimerFrameRate;
    QTimer mTimerAuth;
    QTimer mTimerOnline;
    int mFrameRate=0;
    int mAuthCounter=0;
private slots:
    void in_mTimerFrameRate_timeout();
    void in_mTimerAuth_timeout();
    void in_mTimerOnline_timeout();
    void in_USBAudio_attributeChanged(QString attribute, QString value);
    void in_mDevice_deviceDisconnected();
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // MAINWINDOW_BASE_H


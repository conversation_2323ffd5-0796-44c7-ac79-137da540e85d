/********************************************************************************
** Form generated from reading UI file 'inputs2m2.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_INPUTS2M2_H
#define UI_INPUTS2M2_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>
#include <hsliders2m1.h>
#include <volumemeters2m1.h>

QT_BEGIN_NAMESPACE

class Ui_InputS2M2
{
public:
    QGridLayout *gridLayout;
    QFrame *frame;
    QLineEdit *lineEdit;
    QPushButton *buttonMute;
    HSliderS2M1 *slider;
    VolumeMeterS2M1 *volumeBottom;
    VolumeMeterS2M1 *volumeTop;

    void setupUi(QWidget *InputS2M2)
    {
        if (InputS2M2->objectName().isEmpty())
            InputS2M2->setObjectName("InputS2M2");
        InputS2M2->resize(226, 60);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(InputS2M2->sizePolicy().hasHeightForWidth());
        InputS2M2->setSizePolicy(sizePolicy);
        InputS2M2->setMinimumSize(QSize(226, 40));
        gridLayout = new QGridLayout(InputS2M2);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(InputS2M2);
        frame->setObjectName("frame");
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::Shape::StyledPanel);
        frame->setFrameShadow(QFrame::Shadow::Raised);
        lineEdit = new QLineEdit(frame);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setGeometry(QRect(0, 10, 41, 41));
        sizePolicy.setHeightForWidth(lineEdit->sizePolicy().hasHeightForWidth());
        lineEdit->setSizePolicy(sizePolicy);
        lineEdit->setStyleSheet(QString::fromUtf8(""));
        lineEdit->setAlignment(Qt::AlignmentFlag::AlignCenter);
        buttonMute = new QPushButton(frame);
        buttonMute->setObjectName("buttonMute");
        buttonMute->setGeometry(QRect(40, 20, 41, 21));
        buttonMute->setCheckable(true);
        slider = new HSliderS2M1(frame);
        slider->setObjectName("slider");
        slider->setGeometry(QRect(110, 17, 101, 16));
        volumeBottom = new VolumeMeterS2M1(frame);
        volumeBottom->setObjectName("volumeBottom");
        volumeBottom->setGeometry(QRect(110, 37, 101, 16));
        volumeTop = new VolumeMeterS2M1(frame);
        volumeTop->setObjectName("volumeTop");
        volumeTop->setGeometry(QRect(110, 0, 101, 16));

        gridLayout->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(InputS2M2);

        QMetaObject::connectSlotsByName(InputS2M2);
    } // setupUi

    void retranslateUi(QWidget *InputS2M2)
    {
        InputS2M2->setWindowTitle(QCoreApplication::translate("InputS2M2", "Form", nullptr));
        lineEdit->setText(QCoreApplication::translate("InputS2M2", "AUX", nullptr));
        buttonMute->setText(QCoreApplication::translate("InputS2M2", "MUTE", nullptr));
    } // retranslateUi

};

namespace Ui {
    class InputS2M2: public Ui_InputS2M2 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_INPUTS2M2_H

#include <QDebug>

#include "usbhidapi.h"
#include "devicetype1.h"


// override
int DeviceType1::readFrame()
{
    unsigned char buf[16]={0x00};
    int ret=USBHHandle.read(buf, 16);
    if(ret == -1)
    {
        return ret;
    }
    FrameInfo frame;
    if(ret == 16 && buf[0] == 0x22 && buf[1] == 0x33 && buf[13] == 0x66 && buf[14] == 0x77)
    {
        frame.cmd = (frame.cmd << 8) | buf[5];
        frame.cmd = (frame.cmd << 8) | buf[6];
        frame.data = (frame.data << 8) | buf[7];
        frame.data = (frame.data << 8) | buf[8];
        frame.data = (frame.data << 8) | buf[9];
        frame.data = (frame.data << 8) | buf[10];
        frame.total = buf[3];
        frame.current = buf[4];
        frame.protocol = (FrameProtocol) buf[2];
        if(frame.cmd != 0)
        {
            emit newFrameReceived(frame);
            ret = 1;
        }
        else
        {
            ret = 0;
        }
    }
    else
    {
        ret = 0;
    }
    return ret;
}


// setter & getter
void DeviceType1::sendFrame(FrameInfo frame)
{
    unsigned char buf[16]={0x00};
    buf[0] = 0x00;
    buf[1] = 0x22;
    buf[2] = 0x33;
    buf[3] = frame.protocol;
    buf[4] = frame.total;
    buf[5] = frame.current;
    buf[6] = (frame.cmd >> 8) & 0xff;
    buf[7] = (frame.cmd >> 0) & 0xff;
    buf[8] = (frame.data >> 24) & 0xff;
    buf[9] = (frame.data >> 16) & 0xff;
    buf[10] = (frame.data >> 8) & 0xff;
    buf[11] = (frame.data >> 0) & 0xff;
    buf[12] = 0;
    buf[13] = 0;
    buf[14] = 0x66;
    buf[15] = 0x77;
    sendPacket(QByteArray(reinterpret_cast<const char*>(buf), sizeof(buf)));
    if(showSendingFrameState())
    {
        qDebug() << QByteArray(reinterpret_cast<const char*>(buf), 16).toHex(' ').toUpper().replace(" ", "  ") << "\t" << frame.data;
    }
}


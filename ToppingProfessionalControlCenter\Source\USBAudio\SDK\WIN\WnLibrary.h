/************************************************************************
 *
 *  Module:       WnLibrary.h
 *
 *  Description:
 *    Wrapper for a Win32 HMODULE object
 *
 *  Runtime Env.:
 *    Win32
 *
 *  Author(s):
 *    <PERSON>, <PERSON>@thesycon.de
 *    <PERSON><PERSON>,  Udo.E<PERSON><EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnLibrary_h__
#define __WnLibrary_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

// helper macro
#ifdef  UNICODE
#define WN_DLL_PROC_NAME(x) x "W"
#else
#define WN_DLL_PROC_NAME(x) x "A"
#endif



//
// WnLibrary
//
// Wrapper for a Win32 HMODULE object
//
class WnLibrary
{
public:
    // constructor
    WnLibrary()
            {
                mHandle = NULL;
            }

    // construct from an existing handle
    WnLibrary(
        HMODULE handle
        )
            {
                mHandle = handle;
            }

    // destructor
    virtual
    ~WnLibrary()
            {
                Free();
            }


    // ------------
    // Interface
public:

    // returns true if a valid handle is attached
    bool
    IsValid()
            { return (NULL != mHandle); }


    //
    // get the attached handle,
    // returns NULL if no valid handle is attached
    //
    HMODULE
    GetHandle() const
            { return mHandle; }


    //
    // attach a handle
    // closes existing handle, if any
    //
    void
    AttachHandle(
        HMODULE handle
        )
            {
                // make sure the current handle is closed
                Free();
                // save new handle
                mHandle = handle;
            }

    //
    // detach handle
    // returns current handle
    //
    HMODULE
    DetachHandle()
        {
            HMODULE h = mHandle;
            mHandle = NULL;
            return h;
        }


    //
    // load
    //
    WNERR
    Load(
        LPCTSTR lpFileName
        )
            {
                WNERR err = ERROR_SUCCESS;

            Free();

            HMODULE h = ::LoadLibrary(lpFileName);
            if (NULL == h) {
                // failed
                err = ::GetLastError();
                WNTRACE(TRCERR,tprint(__FUNCTION__": LoadLibrary failed, err=0x%X\n", err));
            } else {
                mHandle = h;
            }
            return err;
        }

    //
    // free
    //
    WNERR
    Free()
        {
            WNERR err = ERROR_SUCCESS;

          if (NULL != mHandle) {
              BOOL succ = ::FreeLibrary(mHandle);
              mHandle = NULL;
              if (!succ) {
                  // failed
                  err = ::GetLastError();
                  WNTRACE(TRCERR,tprint(__FUNCTION__": FreeLibrary failed, err=0x%X\n", err));
              }
          }

          return err;
      }
      // return value of this function is likely to be ignored
      //lint -esym(534, WnLibrary::Free)


    //
    // GetProcAddress
    //
#ifdef UNDER_CE
    FARPROC
    GetProcAddress(
        LPCTSTR lpProcName
        )
            {
                WNASSERT(IsValid());
                WNERR err = ERROR_SUCCESS;

            FARPROC p = ::GetProcAddress(mHandle, lpProcName);
            if (NULL == p) {
                // failed
                err = ::GetLastError();
                WNTRACE(TRCERR,tprint(__FUNCTION__": GetProcAddress failed, err=0x%X\n", err));
            }

            return p;
        }
#else
    FARPROC
    GetProcAddress(
        LPCSTR lpProcName
        )
            {
                WNASSERT(IsValid());
                WNERR err = ERROR_SUCCESS;

            FARPROC p = ::GetProcAddress(mHandle, lpProcName);
            if (NULL == p) {
                // failed
                err = ::GetLastError();
                WNTRACE(TRCERR,tprint(__FUNCTION__": GetProcAddress failed, err=0x%X\n", err));
            }

            return p;
        }
#endif



    // ------------
    // Data Members

protected:

    // handle value, NULL if invalid
    HMODULE mHandle;


// make copy constructor and assignment operator unavailable
PRIVATE_COPY_CONSTRUCTOR(WnLibrary)
PRIVATE_ASSIGNMENT_OPERATOR(WnLibrary)

}; // class WnLibrary

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnLibrary_h__

/***************************** EOF **************************************/

#include "outputbase.h"


// setter & getter
OutputBase& OutputBase::setChannel<PERSON>ame(QString name)
{
    mChannelName = name;
    return *this;
}
OutputBase& OutputBase::setWidgetEnable(bool state)
{
    mEnable = state;
    return *this;
}
OutputBase& OutputBase::setWidgetEnableWithUpdate(bool state)
{
    mEnable = state;
    if(mEmitAction)
    {
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(mEnable));
    }
    updateAttribute();
    return *this;
}
OutputBase& OutputBase::setWidgetMovable(bool state)
{
    mMovable = state;
    return *this;
}
OutputBase& OutputBase::setWidgetReady(bool state)
{
    mReady = state;
    return *this;
}
OutputBase& OutputBase::setWidgetEmitAction(bool state)
{
    mEmitAction = state;
    return *this;
}


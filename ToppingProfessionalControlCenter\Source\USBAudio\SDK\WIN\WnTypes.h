/************************************************************************
 *
 *  Module:       WnTypes.h
 *
 *  Description:  Types used by the library
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    U<PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnTypes_h__
#define __WnTypes_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// WNERR is used in libwn for Windows error codes.
// This is an implicit documenting of return values.
// MS uses DWORD for everything which is not self-documenting.
//
typedef DWORD WNERR;


#ifdef LIBWN_NAMESPACE
}
#endif

#endif //__WnTypes_h__

/********************************* EOF *********************************/

/************************************************************************

    Description:
        basic integer types of known size

    Author(s):
        <PERSON>
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __tlrt_types_h__
#define __tlrt_types_h__

// pull in header file for compiler detection
#include "tlrt_platform.h"


#if TLRT_STDINT_AVAILABLE
/*
    stdint.h specifies a set of fixed-size integer types which we use.
    They should be defined in stdint.h and stdbool.h which comes with the compiler.
*/
#include <stdint.h>
#include <stdbool.h>

#else
/*
    If stdint.h is not available, we repeat some of the stdint types here.
    This is necessary to use some external libraries, e.g. SHA library.
*/
typedef signed char        int8_t;
typedef short              int16_t;
typedef int                int32_t;
typedef long long          int64_t;
typedef unsigned char      uint8_t;
typedef unsigned short     uint16_t;
typedef unsigned int       uint32_t;
typedef unsigned long long uint64_t;

typedef signed char        int_least8_t;
typedef short              int_least16_t;
typedef int                int_least32_t;
typedef long long          int_least64_t;
typedef unsigned char      uint_least8_t;
typedef unsigned short     uint_least16_t;
typedef unsigned int       uint_least32_t;
typedef unsigned long long uint_least64_t;

#endif


#if defined(__cplusplus)
// C++
#if defined(TLRT_COMPILER_APPLE_KERNEL_MODE)
#include <limits.h>
#else

// Note: In Windows kernel mode we use the MSVC standard C++ library instead 
// of the restricted subset from the EWDK (Windows Kits\10\Include\10.0.22621.0\km\crt). 
// See also CRT_IncludePath_Override property in EWDK_Build_vs2022.props.

#include <limits>
#define TLRT_STD_NUMERIC_LIMITS_AVAILABLE
#endif

#else
// C
#include <limits.h>
#endif


/*
    The following typedef aliases are used for better documentation in
    struct definitions to indicate a fixed byte order,
    e.g. USB structs use little endian and Ethernet uses big endian.
*/
typedef uint16_t be_uint16_t;
typedef uint16_t le_uint16_t;

typedef uint32_t be_uint32_t;
typedef uint32_t le_uint32_t;

typedef uint64_t be_uint64_t;
typedef uint64_t le_uint64_t;

typedef int16_t be_int16_t;
typedef int16_t le_int16_t;

typedef int32_t be_int32_t;
typedef int32_t le_int32_t;

typedef int64_t be_int64_t;
typedef int64_t le_int64_t;



/*
    size checks
*/
TL_CHECK_SIZEOF(bool, 1);

TL_CHECK_SIZEOF(uint8_t,1);
TL_CHECK_SIZEOF(uint16_t,2);
TL_CHECK_SIZEOF(uint32_t,4);
TL_CHECK_SIZEOF(uint64_t,8);

TL_CHECK_SIZEOF(int8_t,1);
TL_CHECK_SIZEOF(int16_t,2);
TL_CHECK_SIZEOF(int32_t,4);
TL_CHECK_SIZEOF(int64_t,8);



//
// T_UNICHAR - UNICODE character type
// size of this type varies depending on platform
//
#if TLRT_OS_ENV_WINDOWS || TLRT_OS_ENV_WINDOWS_KERNEL

// Microsoft uses 16-bit UNICODE characters
typedef wchar_t T_UNICHAR;
// Macro for defining string literals.
#define UNICHAR_TEXT(x)   L ## x
// formatter for printf, format string is ASCII
#define UNICHAR_FMT   "%C"
#define UNISTR_FMT    "%S"
// new line
#define NEWLINE_STR   "\r\n"
#define NEWLINE_WSTR L"\r\n"

#elif TLRT_OS_ENV_LINUX || TLRT_OS_ENV_MACOS || TLRT_OS_ENV_MACOS_KERNEL || TLRT_OS_ENV_EMBEDDED

// UNICODE is encoded as UTF-8
typedef char T_UNICHAR;
// Macro for defining string literals.
#define UNICHAR_TEXT(x)   x
// formatter for printf, format string is ASCII
#define UNICHAR_FMT   "%c"
#define UNISTR_FMT    "%s"
// new line
#define NEWLINE_STR   "\n"
#define NEWLINE_WSTR L"\n"

#else
#error OS enviroment undefined.
#endif

//
// Usage example:
//
// const char* charString = "Hallo";
// const T_UNICHAR* uniString = UNICHAR_TEXT("Beef");
//



// For the sake of implicit documentation:
// integer type alias for a time interval expressed in time units
typedef int duration_ns;    // nanoseconds
typedef int duration_us;    // microseconds
typedef int duration_ms;    // milliseconds

// All negative durations are interpreted as infinite.
#define TL_INFINITE_DURATION (-1)

#if defined(__cplusplus)
constexpr bool TLIsInfiniteDuration(int duration) { return ( duration < 0 ); }
#endif


// Type for timeout interval in milliseconds. A negative value specifies an infinite timeout.
typedef duration_ms TLTimeoutInterval;

// Constant for an infinite timeout. In fact all negative numbers are regarded as infinite.
#define TL_INFINITE_TIMEOUT_INTERVAL      TL_INFINITE_DURATION

#if defined(__cplusplus)
constexpr bool TLIsInfiniteTimeout(int timeout) { return ( timeout < 0 ); }
#if defined(TLRT_STD_NUMERIC_LIMITS_AVAILABLE)
constexpr TLTimeoutInterval TLTimeoutInterval_max = (std::numeric_limits<TLTimeoutInterval>::max)();
#endif
#endif


// sometimes unsigned types are useful
typedef unsigned int uduration_ns;
typedef unsigned int uduration_us;
typedef unsigned int uduration_ms;


// For the sake of implicit documentation:
// integer type alias for a frequency expressed in Hz
typedef uint32_t freq_hz;
typedef uint64_t freq64_hz;

#if defined(__cplusplus)
// factor to express kHz, MHz, etc.
constexpr freq_hz kHzScale =    1000u;
constexpr freq_hz MHzScale = 1000000u;
#endif


// For the sake of implicit documentation:
// integer type aliases for voltage levels
typedef int voltage_V;
typedef int voltage_mV;
typedef int voltage_uV;




#endif

/*** EOF ***/

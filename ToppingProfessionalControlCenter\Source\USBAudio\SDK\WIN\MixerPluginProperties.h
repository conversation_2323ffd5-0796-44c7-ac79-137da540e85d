/************************************************************************
 *
 *  Module:       MixerPluginProperties.h
 *  Description:  properties supported by the Mixer plugin
 *
 *  Runtime Env.: any
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo<PERSON>@thesycon.de
 *    <PERSON>, <PERSON>@thesycon.de
 *  Company:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __MixerPluginProperties_h__
#define __MixerPluginProperties_h__

#include "CommonPluginProperties.h"


// struct alignment = 1 byte
#include <pshpack1.h>


// module ID to be set in CommonPropertyHeader.moduleId
#define MIXER_MODULE_ID     'M'

// Major version of this interface.
// Will be incremented if incompatible changes are made.
#define MIXER_MODULE_INTERFACE_VERSION_MJ    2
// Minor version of this interface.
// Will be incremented if compatible changes are made.
#define MIXER_MODULE_INTERFACE_VERSION_MN    0


//
// Mixer property identifiers
//
// properties that can be get/set on the mixer
//
typedef enum tagMixerProperty
{
    MixerProperty_reserved = 0, // for internal use, not a real property

    MixerProperty_InterfaceVersion = 42,    // GET only, returns MIXER_MODULE_INTERFACE_VERSION

    // device stream OUT/IN
    MixerProperty_DeviceChannelCountOut = 100,    // GET only
    MixerProperty_DeviceChannelCountIn,           // GET only
    // app playback/record
    MixerProperty_PlaybackChannelCount,         // GET only
    MixerProperty_RecordChannelCount,           // GET only
    // app virtual playback/record
    MixerProperty_VirtualPlaybackChannelCount,  // GET only
    MixerProperty_VirtualRecordChannelCount,    // GET only

    // mixer nodes
    MixerProperty_MixerWeight = 200,        // GET/SET
    // mixer output line
    MixerProperty_MixerLineWeight,          // GET/SET

    // level meters
    MixerProperty_LevelMeterEnable = 300,   // GET/SET
    MixerProperty_LevelMeterValues,         // GET only

} MixerProperty;



//
// data structure to be used with the MixerProperty_InterfaceVersion property
//
typedef struct tagMixerPropData_InterfaceVersion
{
    // header
    CommonPropertyHeader prop;

    // out parameter: will be set to MIXER_MODULE_INTERFACE_VERSION_MJ
    unsigned int majorVersion;

    // out parameter: will be set to MIXER_MODULE_INTERFACE_VERSION_MN
    unsigned int minorVersion;

} MixerPropData_InterfaceVersion;



//
// the mixer weight is a Q7.24 value
// range is [0,4]
//
#define MIXER_WEIGHT_MAX  (4*DSP_Q724_ONE)    // signal gets multiplied with 4.0 (+12 dB)
#define MIXER_WEIGHT_MIN  DSP_Q724_ZERO       // signal gets multiplied with 0.0 (-inf dB)



//
// type of the mixer pins
//

// application playback or recording pins
#define MIXER_CHANNEL_TYPE_APPLICATION  0
// device in or out pins
#define MIXER_CHANNEL_TYPE_DEVICE       1
// virtual playback or recording pins
#define MIXER_CHANNEL_TYPE_VIRTUAL      2


//
// mixer inputs are assigned as follows:
//
//   type          channel count                                purpose
//   ------------------------------------------------------------------------------------------------------
//   APPLICATION   MixerProperty_PlaybackChannelCount           Application playback through WDM/ASIO - physical channels
//   DEVICE        MixerProperty_DeviceChannelCountIn           Device IN from USB device
//   VIRTUAL       MixerProperty_VirtualPlaybackChannelCount    Application playback through WDM/ASIO - virtual channels
//

//
// mixer outputs are assigned as follows:
//
//   type          channel count                                purpose
//   ------------------------------------------------------------------------------------------------------
//   DEVICE        MixerProperty_DeviceChannelCountOut          Device OUT to USB device
//   APPLICATION   MixerProperty_RecordChannelCount             Application recording through WDM/ASIO - physical channels
//   VIRTUAL       MixerProperty_VirtualRecordChannelCount      Application recording through WDM/ASIO - virtual channels
//



//
// address information required to access a mixer node (weight)
//
typedef struct tagMixerAddress
{
    //
    // mixer input address
    //

    // input type, set to
    // MIXER_CHANNEL_TYPE_APPLICATION, MIXER_CHANNEL_TYPE_DEVICE, or MIXER_CHANNEL_TYPE_VIRTUAL
    unsigned char inChannelType;
    // zero based index (count is defined by the respective property)
    unsigned char inChannelIndex;


    //
    // mixer output address
    //

    // output type, set to
    // MIXER_CHANNEL_TYPE_APPLICATION, MIXER_CHANNEL_TYPE_DEVICE, or MIXER_CHANNEL_TYPE_VIRTUAL
    unsigned char outChannelType;
    // zero based index (count is defined by the respective property)
    unsigned char outChannelIndex;

} MixerAddress;


//
// mixer weight data
//
typedef struct tagMixerWeight
{
    // mixer address
    MixerAddress address;

    // weight for this node
    DSP_Q724 weight;

} MixerWeight;



//
// data structure to be used with the following properties:
//   MixerProperty_MixerWeight
//
typedef struct tagMixerPropData_Weight
{
    // header
    CommonPropertyHeader prop;

    // variable-sized array of weights
    MixerWeight weightsArray[1];

} MixerPropData_Weight;


//
// address information required to access a mixer line (weight)
//
typedef struct tagMixerLineAddress
{
    //
    // mixer line address
    //

    // type, set to
    // MIXER_CHANNEL_TYPE_APPLICATION, MIXER_CHANNEL_TYPE_DEVICE, or MIXER_CHANNEL_TYPE_VIRTUAL
    unsigned char channelType;
    // zero based index (count is defined by the respective property)
    unsigned char channelIndex;

} MixerLineAddress;


//
// mixer weight data
//
typedef struct tagMixerLineWeight
{
    // mixer line address
    MixerLineAddress address;

    // master output for this line
    DSP_Q724 weight;

} MixerLineWeight;



//
// data structure to be used with the following properties:
//   MixerProperty_MixerWeight
//
typedef struct tagMixerPropData_LineWeight
{
    // header
    CommonPropertyHeader prop;

    // variable-sized array of weights
    MixerLineWeight weightsArray[1];

} MixerPropData_LineWeight;



//
// data structure to be used with the following properties:
//   MixerProperty_DeviceChannelCountOut
//   MixerProperty_DeviceChannelCountIn
//   MixerProperty_VirtualPlaybackChannelCount
//   MixerProperty_VirtualRecordChannelCount
//
typedef struct tagMixerPropData_ChannelCount
{
    // header
    CommonPropertyHeader prop;

    // out parameter reported by the mixer plugin
    unsigned int channelCount;

} MixerPropData_ChannelCount;



//
// data structure to be used with the following properties:
//   MixerProperty_LevelMeterEnable
//
typedef struct tagMixerPropData_LevelMeterEnable
{
    // header
    CommonPropertyHeader prop;

    // in/out parameter reported by the mixer plugin
    unsigned int enable;

} MixerPropData_LevelMeterEnable;


//
// level meter data
//
typedef struct tagLevelMeterValue
{
    //
    // level meter address
    //

    // 0 for mixer input, 1 for mixer output
    unsigned int direction;

    // type, set to
    // MIXER_CHANNEL_TYPE_APPLICATION, MIXER_CHANNEL_TYPE_DEVICE, or MIXER_CHANNEL_TYPE_VIRTUAL
    unsigned char channelType;
    // zero based index (count is defined by the respective property)
    unsigned char channelIndex;

    //
    // level meter value
    //
    //  Input signal Level measured in 1/100 dB relatively full scale sine wave.
    //  Value is in range [-12000 0] that corresponds to levels -120dB...0dB
    //  There is a correspondence between absolute values and dB for SIN wave with no DC offset
    //     Absolute maximum sin value (amplitude)        Returned level in 1/100 dB
    //         2^31                                        0     (0dB)
    //         2^30                                       -600   (-6dB)
    //         2^29                                       -1200  (-12dB)
    //                            .................
    //         2^11                                       -12000 (-120dB)
    //
    short value;

#define LEVEL_METER_MINIMUM_VALUE (-12000)

} LevelMeterValue;


//
// data structure to be used with the following properties:
//   MixerProperty_LevelMeterValues
//
typedef struct tagMixerPropData_LevelMeterValues
{
    // header
    CommonPropertyHeader prop;

    // variable-sized array of values
    LevelMeterValue valuesArray[1];

} MixerPropData_LevelMeterValues;



// restore previous alignment
#include <poppack.h>


#endif // __MixerPluginProperties_h__

/******************************** EOF ***********************************/

/********************************************************************************
** Form generated from reading UI file 'm62_privatewidget7.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_M62_PRIVATEWIDGET7_H
#define UI_M62_PRIVATEWIDGET7_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include <comboboxs1m3.h>

QT_BEGIN_NAMESPACE

class Ui_M62_PrivateWidget7
{
public:
    QGridLayout *gridLayout;
    QSpacerItem *verticalSpacer_7;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer;
    QVBoxLayout *verticalLayout;
    QWidget *widget1;
    QLabel *icon1;
    QLabel *label1;
    QPushButton *button1;
    QSpacerItem *verticalSpacer_3;
    QWidget *widget2;
    QLabel *icon2;
    QLabel *label2;
    ComboBoxS1M3 *comboBox2;
    QSpacerItem *verticalSpacer_2;
    QWidget *widget3;
    QLabel *icon3;
    QLabel *label3;
    QPushButton *button3;
    QSpacerItem *verticalSpacer_4;
    QWidget *widget4;
    QLabel *icon4;
    QLabel *label4;
    ComboBoxS1M3 *comboBox4;
    QSpacerItem *verticalSpacer_5;
    QWidget *widget5;
    QLabel *icon5;
    QLabel *label5;
    ComboBoxS1M3 *comboBox5;
    QSpacerItem *verticalSpacer_6;
    QWidget *widget6;
    QLabel *icon6;
    QLabel *label6;
    QPushButton *button6;
    QSpacerItem *verticalSpacer_8;
    QWidget *widget7;
    QLabel *icon7;
    QLabel *label7;
    QPushButton *button7;
    QSpacerItem *verticalSpacer_9;
    QWidget *widget8;
    QLabel *icon8;
    QLabel *label8;
    ComboBoxS1M3 *comboBox8;
    QSpacerItem *verticalSpacer_10;
    QWidget *widget9;
    QLabel *icon9;
    QLabel *label9;
    ComboBoxS1M3 *comboBox9;
    QSpacerItem *horizontalSpacer;

    void setupUi(QWidget *M62_PrivateWidget7)
    {
        if (M62_PrivateWidget7->objectName().isEmpty())
            M62_PrivateWidget7->setObjectName("M62_PrivateWidget7");
        M62_PrivateWidget7->resize(833, 890);
        gridLayout = new QGridLayout(M62_PrivateWidget7);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_7 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_7, 0, 0, 1, 3);

        horizontalSpacer_2 = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer_2, 1, 2, 1, 1);

        verticalSpacer = new QSpacerItem(20, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer, 2, 0, 1, 3);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(-1, -1, -1, 0);
        widget1 = new QWidget(M62_PrivateWidget7);
        widget1->setObjectName("widget1");
        icon1 = new QLabel(widget1);
        icon1->setObjectName("icon1");
        icon1->setGeometry(QRect(1, 1, 16, 16));
        icon1->setStyleSheet(QString::fromUtf8("image: url(:/Icon/blueTooth.svg);"));
        icon1->setScaledContents(false);
        icon1->setAlignment(Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter);
        label1 = new QLabel(widget1);
        label1->setObjectName("label1");
        label1->setGeometry(QRect(30, 1, 57, 16));
        button1 = new QPushButton(widget1);
        button1->setObjectName("button1");
        button1->setGeometry(QRect(510, 0, 32, 16));
        QSizePolicy sizePolicy(QSizePolicy::Policy::Preferred, QSizePolicy::Policy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(button1->sizePolicy().hasHeightForWidth());
        button1->setSizePolicy(sizePolicy);
        button1->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/Icon/switchOff.svg);\n"
"}\n"
"QPushButton:checked{\n"
"	image: url(:/Icon/switchOn.svg);\n"
"}"));
        button1->setCheckable(true);

        verticalLayout->addWidget(widget1);

        verticalSpacer_3 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_3);

        widget2 = new QWidget(M62_PrivateWidget7);
        widget2->setObjectName("widget2");
        icon2 = new QLabel(widget2);
        icon2->setObjectName("icon2");
        icon2->setGeometry(QRect(1, 1, 16, 16));
        icon2->setStyleSheet(QString::fromUtf8("image: url(:/Icon/luminance.svg);"));
        label2 = new QLabel(widget2);
        label2->setObjectName("label2");
        label2->setGeometry(QRect(30, 1, 47, 16));
        comboBox2 = new ComboBoxS1M3(widget2);
        comboBox2->setObjectName("comboBox2");
        comboBox2->setGeometry(QRect(470, 1, 72, 23));

        verticalLayout->addWidget(widget2);

        verticalSpacer_2 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_2);

        widget3 = new QWidget(M62_PrivateWidget7);
        widget3->setObjectName("widget3");
        icon3 = new QLabel(widget3);
        icon3->setObjectName("icon3");
        icon3->setGeometry(QRect(1, 1, 16, 16));
        icon3->setStyleSheet(QString::fromUtf8("image: url(:/Icon/otg.svg);"));
        label3 = new QLabel(widget3);
        label3->setObjectName("label3");
        label3->setGeometry(QRect(30, 1, 201, 16));
        button3 = new QPushButton(widget3);
        button3->setObjectName("button3");
        button3->setGeometry(QRect(495, 1, 32, 16));
        sizePolicy.setHeightForWidth(button3->sizePolicy().hasHeightForWidth());
        button3->setSizePolicy(sizePolicy);
        button3->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/Icon/switchOff.svg);\n"
"}\n"
"QPushButton:checked{\n"
"	image: url(:/Icon/switchOn.svg);\n"
"}"));
        button3->setCheckable(true);

        verticalLayout->addWidget(widget3);

        verticalSpacer_4 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_4);

        widget4 = new QWidget(M62_PrivateWidget7);
        widget4->setObjectName("widget4");
        icon4 = new QLabel(widget4);
        icon4->setObjectName("icon4");
        icon4->setGeometry(QRect(1, 1, 16, 16));
        icon4->setStyleSheet(QString::fromUtf8("image: url(:/Icon/usb.svg);"));
        label4 = new QLabel(widget4);
        label4->setObjectName("label4");
        label4->setGeometry(QRect(30, 1, 124, 16));
        comboBox4 = new ComboBoxS1M3(widget4);
        comboBox4->setObjectName("comboBox4");
        comboBox4->setGeometry(QRect(460, 0, 72, 23));

        verticalLayout->addWidget(widget4);

        verticalSpacer_5 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_5);

        widget5 = new QWidget(M62_PrivateWidget7);
        widget5->setObjectName("widget5");
        icon5 = new QLabel(widget5);
        icon5->setObjectName("icon5");
        icon5->setGeometry(QRect(1, 1, 16, 16));
        icon5->setStyleSheet(QString::fromUtf8("image: url(:/Icon/mode.svg);"));
        label5 = new QLabel(widget5);
        label5->setObjectName("label5");
        label5->setGeometry(QRect(30, 1, 46, 16));
        comboBox5 = new ComboBoxS1M3(widget5);
        comboBox5->setObjectName("comboBox5");
        comboBox5->setGeometry(QRect(470, 0, 72, 23));

        verticalLayout->addWidget(widget5);

        verticalSpacer_6 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_6);

        widget6 = new QWidget(M62_PrivateWidget7);
        widget6->setObjectName("widget6");
        icon6 = new QLabel(widget6);
        icon6->setObjectName("icon6");
        icon6->setGeometry(QRect(1, 1, 16, 16));
        icon6->setStyleSheet(QString::fromUtf8("image: url(:/Icon/autoPoweroff.svg);"));
        label6 = new QLabel(widget6);
        label6->setObjectName("label6");
        label6->setGeometry(QRect(30, 1, 136, 16));
        button6 = new QPushButton(widget6);
        button6->setObjectName("button6");
        button6->setGeometry(QRect(495, 1, 32, 16));
        sizePolicy.setHeightForWidth(button6->sizePolicy().hasHeightForWidth());
        button6->setSizePolicy(sizePolicy);
        button6->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/Icon/switchOff.svg);\n"
"}\n"
"QPushButton:checked{\n"
"	image: url(:/Icon/switchOn.svg);\n"
"}"));
        button6->setCheckable(true);

        verticalLayout->addWidget(widget6);

        verticalSpacer_8 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_8);

        widget7 = new QWidget(M62_PrivateWidget7);
        widget7->setObjectName("widget7");
        icon7 = new QLabel(widget7);
        icon7->setObjectName("icon7");
        icon7->setGeometry(QRect(1, 1, 16, 16));
        icon7->setStyleSheet(QString::fromUtf8("image: url(:/Icon/reset.svg);"));
        label7 = new QLabel(widget7);
        label7->setObjectName("label7");
        label7->setGeometry(QRect(30, 1, 136, 16));
        button7 = new QPushButton(widget7);
        button7->setObjectName("button7");
        button7->setGeometry(QRect(495, 1, 32, 16));
        sizePolicy.setHeightForWidth(button7->sizePolicy().hasHeightForWidth());
        button7->setSizePolicy(sizePolicy);
        button7->setStyleSheet(QString::fromUtf8(""));
        button7->setCheckable(true);

        verticalLayout->addWidget(widget7);

        verticalSpacer_9 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_9);

        widget8 = new QWidget(M62_PrivateWidget7);
        widget8->setObjectName("widget8");
        icon8 = new QLabel(widget8);
        icon8->setObjectName("icon8");
        icon8->setGeometry(QRect(1, 1, 16, 16));
        icon8->setStyleSheet(QString::fromUtf8("image: url(:/Icon/MainButtonSingle.svg);"));
        label8 = new QLabel(widget8);
        label8->setObjectName("label8");
        label8->setGeometry(QRect(30, 1, 46, 16));
        comboBox8 = new ComboBoxS1M3(widget8);
        comboBox8->setObjectName("comboBox8");
        comboBox8->setGeometry(QRect(470, 0, 72, 23));

        verticalLayout->addWidget(widget8);

        verticalSpacer_10 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_10);

        widget9 = new QWidget(M62_PrivateWidget7);
        widget9->setObjectName("widget9");
        icon9 = new QLabel(widget9);
        icon9->setObjectName("icon9");
        icon9->setGeometry(QRect(1, 1, 16, 16));
        icon9->setStyleSheet(QString::fromUtf8("image: url(:/Icon/MainButtonDouble.svg);"));
        label9 = new QLabel(widget9);
        label9->setObjectName("label9");
        label9->setGeometry(QRect(30, 1, 46, 16));
        comboBox9 = new ComboBoxS1M3(widget9);
        comboBox9->setObjectName("comboBox9");
        comboBox9->setGeometry(QRect(470, 0, 72, 23));

        verticalLayout->addWidget(widget9);

        verticalLayout->setStretch(0, 26);
        verticalLayout->setStretch(1, 22);
        verticalLayout->setStretch(2, 26);
        verticalLayout->setStretch(3, 22);
        verticalLayout->setStretch(4, 26);
        verticalLayout->setStretch(5, 22);
        verticalLayout->setStretch(6, 26);
        verticalLayout->setStretch(7, 22);
        verticalLayout->setStretch(8, 26);
        verticalLayout->setStretch(9, 22);
        verticalLayout->setStretch(10, 26);
        verticalLayout->setStretch(11, 22);
        verticalLayout->setStretch(12, 26);
        verticalLayout->setStretch(13, 22);
        verticalLayout->setStretch(14, 26);
        verticalLayout->setStretch(15, 22);
        verticalLayout->setStretch(16, 26);

        gridLayout->addLayout(verticalLayout, 1, 1, 1, 1);

        horizontalSpacer = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer, 1, 0, 1, 1);

        gridLayout->setRowStretch(0, 19);
        gridLayout->setRowStretch(1, 330);
        gridLayout->setRowStretch(2, 24);
        gridLayout->setColumnStretch(0, 60);
        gridLayout->setColumnStretch(1, 480);
        gridLayout->setColumnStretch(2, 60);

        retranslateUi(M62_PrivateWidget7);

        QMetaObject::connectSlotsByName(M62_PrivateWidget7);
    } // setupUi

    void retranslateUi(QWidget *M62_PrivateWidget7)
    {
        M62_PrivateWidget7->setWindowTitle(QCoreApplication::translate("M62_PrivateWidget7", "WidgetSytem1", nullptr));
        icon1->setText(QString());
        label1->setText(QCoreApplication::translate("M62_PrivateWidget7", "Bluetooth", nullptr));
        button1->setText(QString());
        icon2->setText(QString());
        label2->setText(QCoreApplication::translate("M62_PrivateWidget7", "Brightness", nullptr));
        icon3->setText(QString());
        label3->setText(QCoreApplication::translate("M62_PrivateWidget7", "Reverse charging via OTG port", nullptr));
        button3->setText(QString());
        icon4->setText(QString());
        label4->setText(QCoreApplication::translate("M62_PrivateWidget7", "USB-C interface charging settings", nullptr));
        icon5->setText(QString());
        label5->setText(QCoreApplication::translate("M62_PrivateWidget7", "Mode select", nullptr));
        icon6->setText(QString());
        label6->setText(QCoreApplication::translate("M62_PrivateWidget7", "Auto power-off", nullptr));
        button6->setText(QString());
        icon7->setText(QString());
        label7->setText(QCoreApplication::translate("M62_PrivateWidget7", "Reset to factory default", nullptr));
        button7->setText(QCoreApplication::translate("M62_PrivateWidget7", "Reset", nullptr));
        icon8->setText(QString());
        label8->setText(QCoreApplication::translate("M62_PrivateWidget7", "Main button single-click customization", nullptr));
        icon9->setText(QString());
        label9->setText(QCoreApplication::translate("M62_PrivateWidget7", "Main button double-click customization", nullptr));
    } // retranslateUi

};

namespace Ui {
    class M62_PrivateWidget7: public Ui_M62_PrivateWidget7 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_M62_PRIVATEWIDGET7_H

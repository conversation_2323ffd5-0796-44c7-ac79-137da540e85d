#include "globalfont.h"
#include "pushbuttons1m4.h"


PushButtonS1M4::PushButtonS1M4(QWidget* parent)
    : QWidget(parent)
{
    mPushButtonIN1.setParent(this);
    mPushButtonIN2.setParent(this);
    mPushButtonIN12.setParent(this);
    mPushButtonOFF.setParent(this);
    mPushButtonNC1.setParent(this);
    mPushButtonNC2.setParent(this);
    mPushButtonNC3.setParent(this);
    mLabelIN1.setParent(this);
    mLabelIN2.setParent(this);
    mLabelIN12.setParent(this);
    QString style;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelIN1.setStyleSheet(style);
    mLabelIN2.setStyleSheet(style);
    mLabelIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN1.setStyleSheet(style);
    mPushButtonIN2.setStyleSheet(style);
    mPushButtonIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    mPushButtonNC1.setStyleSheet(style);
    mPushButtonNC2.setStyleSheet(style);
    mPushButtonNC3.setStyleSheet(style);
    mPushButtonIN1.setText("IN 1");
    mPushButtonIN2.setText("IN 2");
    mPushButtonIN12.setText("IN 1+2");
    mPushButtonOFF.setText("OFF");
    mPushButtonNC1.setText("NC1");
    mPushButtonNC2.setText("NC2");
    mPushButtonNC3.setText("NC3");
    connect(&mPushButtonIN1, SIGNAL(clicked()), this, SLOT(in_mPushButtonIN1_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonIN2, SIGNAL(clicked()), this, SLOT(in_mPushButtonIN2_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonIN12, SIGNAL(clicked()), this, SLOT(in_mPushButtonIN12_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonOFF, SIGNAL(clicked()), this, SLOT(in_mPushButtonOFF_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonNC1, SIGNAL(clicked()), this, SLOT(in_mPushButtonNC1_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonNC2, SIGNAL(clicked()), this, SLOT(in_mPushButtonNC2_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonNC3, SIGNAL(clicked()), this, SLOT(in_mPushButtonNC3_clicked()), Qt::UniqueConnection);
}
PushButtonS1M4::~PushButtonS1M4()
{

}


// override
void PushButtonS1M4::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wLabel=wPixelPerRatio * 10;
    int wSpace1=wPixelPerRatio * 10;
    int wPushButton=size().width() - wLabel - wSpace1;
    int xLabel=0;
    int xPushButton=xLabel + wLabel + wSpace1;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hSpace1=hPixelPerRatio * 12;
    int hSpace2=hPixelPerRatio * 2;
    int hSpace3=hPixelPerRatio * 18;
    int hSpace4=hPixelPerRatio * 3;
    int hPushButton=(size().height() - hSpace1 - hSpace2 * 6 - hSpace3 - hSpace4) / 7;
    mLabelIN1.setGeometry(xLabel, hSpace1, wLabel, hPushButton);
    mPushButtonIN1.setGeometry(xPushButton, hSpace1, wPushButton, hPushButton);
    mLabelIN2.setGeometry(xLabel, hSpace1 + hSpace2 + hPushButton, wLabel, hPushButton);
    mPushButtonIN2.setGeometry(xPushButton, hSpace1 + hPushButton + hSpace2, wPushButton, hPushButton);
    mLabelIN12.setGeometry(xLabel, hSpace1 + hSpace2 * 2 + hPushButton * 2, wLabel, hPushButton);
    mPushButtonIN12.setGeometry(xPushButton, hSpace1 + hSpace2 * 2 + hPushButton * 2, wPushButton, hPushButton);
    mPushButtonOFF.setGeometry(xLabel + (wLabel + wSpace1) / 3, hSpace1 + hSpace2 * 2 + hSpace3 + hPushButton * 3, wPushButton * 1.1, hPushButton);
    mPushButtonNC1.setGeometry(xLabel + (wLabel + wSpace1) / 3, hSpace1 + hSpace2 * 3 + hSpace3 + hPushButton * 4, wPushButton * 1.1, hPushButton);
    mPushButtonNC2.setGeometry(xLabel + (wLabel + wSpace1) / 3, hSpace1 + hSpace2 * 4 + hSpace3 + hPushButton * 5, wPushButton * 1.1, hPushButton);
    mPushButtonNC3.setGeometry(xLabel + (wLabel + wSpace1) / 3, hSpace1 + hSpace2 * 5 + hSpace3 + hPushButton * 6, wPushButton * 1.1, hPushButton);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonIN2.height()));
    mPushButtonIN1.setFont(mFont);
    mPushButtonIN2.setFont(mFont);
    mPushButtonIN12.setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonNC1.text(), mPushButtonNC1.rect()));
    mPushButtonOFF.setFont(mFont);
    mPushButtonNC1.setFont(mFont);
    mPushButtonNC2.setFont(mFont);
    mPushButtonNC3.setFont(mFont);
}


// slot
void PushButtonS1M4::in_mPushButtonIN1_clicked()
{
    QString style;
    if(!mPushButtonStateIN1)
    {
        mPushButtonStateIN1 = true;
        mPushButtonStateIN2 = false;
        mPushButtonStateIN12 = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelIN1.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelIN2.setStyleSheet(style);
        mLabelIN12.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN1.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN2.setStyleSheet(style);
        mPushButtonIN12.setStyleSheet(style);
        emit buttonStateChanged(buttonIN1, true);
    }
}
void PushButtonS1M4::in_mPushButtonIN2_clicked()
{
    QString style;
    if(!mPushButtonStateIN2)
    {
        mPushButtonStateIN2 = true;
        mPushButtonStateIN1 = false;
        mPushButtonStateIN12 = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelIN2.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelIN1.setStyleSheet(style);
        mLabelIN12.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN2.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN1.setStyleSheet(style);
        mPushButtonIN12.setStyleSheet(style);
        emit buttonStateChanged(buttonIN2, true);
    }
}
void PushButtonS1M4::in_mPushButtonIN12_clicked()
{
    QString style;
    if(!mPushButtonStateIN12)
    {
        mPushButtonStateIN12 = true;
        mPushButtonStateIN1 = false;
        mPushButtonStateIN2 = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelIN12.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelIN1.setStyleSheet(style);
        mLabelIN2.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN12.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN1.setStyleSheet(style);
        mPushButtonIN2.setStyleSheet(style);
        emit buttonStateChanged(buttonIN12, true);
    }
}
void PushButtonS1M4::in_mPushButtonOFF_clicked()
{
    QString style;
    if(!mPushButtonStateOFF)
    {
        mPushButtonStateOFF = true;
        mPushButtonStateNC1 = false;
        mPushButtonStateNC2 = false;
        mPushButtonStateNC3 = false;
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(0, 126, 168);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonOFF.setStyleSheet(style);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonNC1.setStyleSheet(style);
        mPushButtonNC2.setStyleSheet(style);
        mPushButtonNC3.setStyleSheet(style);
        emit buttonStateChanged(buttonOFF, true);
    }
}
void PushButtonS1M4::in_mPushButtonNC1_clicked()
{
    QString style;
    if(!mPushButtonStateNC1)
    {
        mPushButtonStateNC1 = true;
        mPushButtonStateOFF = false;
        mPushButtonStateNC2 = false;
        mPushButtonStateNC3 = false;
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(0, 126, 168);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonNC1.setStyleSheet(style);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonOFF.setStyleSheet(style);
        mPushButtonNC2.setStyleSheet(style);
        mPushButtonNC3.setStyleSheet(style);
        emit buttonStateChanged(buttonNC1, true);
    }
}
void PushButtonS1M4::in_mPushButtonNC2_clicked()
{
    QString style;
    if(!mPushButtonStateNC2)
    {
        mPushButtonStateNC2 = true;
        mPushButtonStateOFF = false;
        mPushButtonStateNC1 = false;
        mPushButtonStateNC3 = false;
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(0, 126, 168);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonNC2.setStyleSheet(style);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonOFF.setStyleSheet(style);
        mPushButtonNC1.setStyleSheet(style);
        mPushButtonNC3.setStyleSheet(style);
        emit buttonStateChanged(buttonNC2, true);
    }
}
void PushButtonS1M4::in_mPushButtonNC3_clicked()
{
    QString style;
    if(!mPushButtonStateNC3)
    {
        mPushButtonStateNC3 = true;
        mPushButtonStateOFF = false;
        mPushButtonStateNC1 = false;
        mPushButtonStateNC2 = false;
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(0, 126, 168);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonNC3.setStyleSheet(style);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonOFF.setStyleSheet(style);
        mPushButtonNC1.setStyleSheet(style);
        mPushButtonNC2.setStyleSheet(style);
        emit buttonStateChanged(buttonNC3, true);
    }
}


// setter & getter
PushButtonS1M4& PushButtonS1M4::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M4& PushButtonS1M4::setPushButtonStateIN1(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateIN1 = true;
    mPushButtonStateIN2 = false;
    mPushButtonStateIN12 = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelIN1.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelIN2.setStyleSheet(style);
    mLabelIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN1.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN2.setStyleSheet(style);
    mPushButtonIN12.setStyleSheet(style);
    return *this;
}
PushButtonS1M4& PushButtonS1M4::setPushButtonStateIN2(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateIN2 = true;
    mPushButtonStateIN1 = false;
    mPushButtonStateIN12 = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelIN2.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelIN1.setStyleSheet(style);
    mLabelIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN2.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN1.setStyleSheet(style);
    mPushButtonIN12.setStyleSheet(style);
    return *this;
}
PushButtonS1M4& PushButtonS1M4::setPushButtonStateIN12(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateIN12 = true;
    mPushButtonStateIN1 = false;
    mPushButtonStateIN2 = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelIN12.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelIN1.setStyleSheet(style);
    mLabelIN2.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN1.setStyleSheet(style);
    mPushButtonIN2.setStyleSheet(style);
    return *this;
}
PushButtonS1M4& PushButtonS1M4::setPushButtonStateOFF(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateOFF = true;
    mPushButtonStateNC1 = false;
    mPushButtonStateNC2 = false;
    mPushButtonStateNC3 = false;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(229, 229, 229);"
            "   background-color: rgb(0, 126, 168);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonNC1.setStyleSheet(style);
    mPushButtonNC2.setStyleSheet(style);
    mPushButtonNC3.setStyleSheet(style);
    return *this;
}
PushButtonS1M4& PushButtonS1M4::setPushButtonStateNC1(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateNC1 = true;
    mPushButtonStateOFF = false;
    mPushButtonStateNC2 = false;
    mPushButtonStateNC3 = false;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(229, 229, 229);"
            "   background-color: rgb(0, 126, 168);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonNC1.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    mPushButtonNC2.setStyleSheet(style);
    mPushButtonNC3.setStyleSheet(style);
    return *this;
}
PushButtonS1M4& PushButtonS1M4::setPushButtonStateNC2(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateNC2 = true;
    mPushButtonStateOFF = false;
    mPushButtonStateNC1 = false;
    mPushButtonStateNC3 = false;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(229, 229, 229);"
            "   background-color: rgb(0, 126, 168);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonNC2.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    mPushButtonNC1.setStyleSheet(style);
    mPushButtonNC3.setStyleSheet(style);
    return *this;
}
PushButtonS1M4& PushButtonS1M4::setPushButtonStateNC3(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateNC3 = true;
    mPushButtonStateOFF = false;
    mPushButtonStateNC1 = false;
    mPushButtonStateNC2 = false;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(229, 229, 229);"
            "   background-color: rgb(0, 126, 168);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonNC3.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    mPushButtonNC1.setStyleSheet(style);
    mPushButtonNC2.setStyleSheet(style);
    return *this;
}
bool PushButtonS1M4::getPushButtonStateIN1()
{
    return mPushButtonStateIN1;
}
bool PushButtonS1M4::getPushButtonStateIN2()
{
    return mPushButtonStateIN2;
}
bool PushButtonS1M4::getPushButtonStateIN12()
{
    return mPushButtonStateIN12;
}
bool PushButtonS1M4::getPushButtonStateOFF()
{
    return mPushButtonStateOFF;
}
bool PushButtonS1M4::getPushButtonStateNC1()
{
    return mPushButtonStateNC1;
}
bool PushButtonS1M4::getPushButtonStateNC2()
{
    return mPushButtonStateNC2;
}
bool PushButtonS1M4::getPushButtonStateNC3()
{
    return mPushButtonStateNC3;
}


/****************************************************************************
** Meta object code from reading C++ file 'mainwindow_m62.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mainwindow_m62.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN14MainWindow_M62E_t {};
} // unnamed namespace

template <> constexpr inline auto MainWindow_M62::qt_create_metaobjectdata<qt_meta_tag_ZN14MainWindow_M62E_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "MainWindow_M62",
        "in_mWidgetAll_attributeChanged",
        "",
        "objectName",
        "attribute",
        "value",
        "in_mDevice_newFrameReceived",
        "DeviceType1::FrameInfo",
        "frame",
        "on_PageFctyPushButton1_clicked",
        "on_PageFctyPushButton2_clicked",
        "on_PageMblePushButton_clicked"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'in_mWidgetAll_attributeChanged'
        QtMocHelpers::SlotData<void(QString, QString, QString)>(1, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::QString, 4 }, { QMetaType::QString, 5 },
        }}),
        // Slot 'in_mDevice_newFrameReceived'
        QtMocHelpers::SlotData<void(DeviceType1::FrameInfo)>(6, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 7, 8 },
        }}),
        // Slot 'on_PageFctyPushButton1_clicked'
        QtMocHelpers::SlotData<void()>(9, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_PageFctyPushButton2_clicked'
        QtMocHelpers::SlotData<void()>(10, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_PageMblePushButton_clicked'
        QtMocHelpers::SlotData<void()>(11, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<MainWindow_M62, qt_meta_tag_ZN14MainWindow_M62E_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject MainWindow_M62::staticMetaObject = { {
    QMetaObject::SuperData::link<MainWindow_Base::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14MainWindow_M62E_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14MainWindow_M62E_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN14MainWindow_M62E_t>.metaTypes,
    nullptr
} };

void MainWindow_M62::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<MainWindow_M62 *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->in_mWidgetAll_attributeChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 1: _t->in_mDevice_newFrameReceived((*reinterpret_cast< std::add_pointer_t<DeviceType1::FrameInfo>>(_a[1]))); break;
        case 2: _t->on_PageFctyPushButton1_clicked(); break;
        case 3: _t->on_PageFctyPushButton2_clicked(); break;
        case 4: _t->on_PageMblePushButton_clicked(); break;
        default: ;
        }
    }
}

const QMetaObject *MainWindow_M62::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow_M62::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14MainWindow_M62E_t>.strings))
        return static_cast<void*>(this);
    return MainWindow_Base::qt_metacast(_clname);
}

int MainWindow_M62::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = MainWindow_Base::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    return _id;
}
QT_WARNING_POP

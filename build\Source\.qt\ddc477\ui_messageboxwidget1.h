/********************************************************************************
** Form generated from reading UI file 'messageboxwidget1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MESSAGEBOXWIDGET1_H
#define UI_MESSAGEBOXWIDGET1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MessageBoxWidget1
{
public:
    QGridLayout *gridLayout;
    QSpacerItem *verticalSpacer;
    QGridLayout *gridLayout_2;
    QSpacerItem *horizontalSpacer_2;
    QLabel *Label1;
    QSpacerItem *horizontalSpacer;
    QSpacerItem *verticalSpacer_7;
    QLabel *Label2;
    QSpacerItem *verticalSpacer_8;
    QLabel *Label3;
    QSpacerItem *verticalSpacer_9;
    QLabel *Label4;
    QSpacerItem *verticalSpacer_10;
    QGridLayout *gridLayout_3;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *PushButton1;
    QSpacerItem *horizontalSpacer_5;
    QPushButton *PushButton2;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *verticalSpacer_2;

    void setupUi(QWidget *MessageBoxWidget1)
    {
        if (MessageBoxWidget1->objectName().isEmpty())
            MessageBoxWidget1->setObjectName("MessageBoxWidget1");
        MessageBoxWidget1->resize(300, 200);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(MessageBoxWidget1->sizePolicy().hasHeightForWidth());
        MessageBoxWidget1->setSizePolicy(sizePolicy);
        gridLayout = new QGridLayout(MessageBoxWidget1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer = new QSpacerItem(20, 4, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer, 0, 0, 1, 1);

        gridLayout_2 = new QGridLayout();
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName("gridLayout_2");
        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_2->addItem(horizontalSpacer_2, 0, 0, 1, 1);

        Label1 = new QLabel(MessageBoxWidget1);
        Label1->setObjectName("Label1");
        sizePolicy.setHeightForWidth(Label1->sizePolicy().hasHeightForWidth());
        Label1->setSizePolicy(sizePolicy);
        Label1->setMinimumSize(QSize(1, 1));
        Label1->setLineWidth(0);
        Label1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_2->addWidget(Label1, 0, 1, 1, 1);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_2->addItem(horizontalSpacer, 0, 2, 1, 1);

        gridLayout_2->setColumnStretch(0, 10);
        gridLayout_2->setColumnStretch(1, 20);
        gridLayout_2->setColumnStretch(2, 10);

        gridLayout->addLayout(gridLayout_2, 1, 0, 1, 1);

        verticalSpacer_7 = new QSpacerItem(20, 5, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_7, 2, 0, 1, 1);

        Label2 = new QLabel(MessageBoxWidget1);
        Label2->setObjectName("Label2");
        sizePolicy.setHeightForWidth(Label2->sizePolicy().hasHeightForWidth());
        Label2->setSizePolicy(sizePolicy);
        Label2->setMinimumSize(QSize(1, 1));
        Label2->setLineWidth(0);
        Label2->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout->addWidget(Label2, 3, 0, 1, 1);

        verticalSpacer_8 = new QSpacerItem(20, 4, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_8, 4, 0, 1, 1);

        Label3 = new QLabel(MessageBoxWidget1);
        Label3->setObjectName("Label3");
        sizePolicy.setHeightForWidth(Label3->sizePolicy().hasHeightForWidth());
        Label3->setSizePolicy(sizePolicy);
        Label3->setMinimumSize(QSize(1, 1));
        Label3->setLineWidth(0);
        Label3->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout->addWidget(Label3, 5, 0, 1, 1);

        verticalSpacer_9 = new QSpacerItem(20, 4, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_9, 6, 0, 1, 1);

        Label4 = new QLabel(MessageBoxWidget1);
        Label4->setObjectName("Label4");
        sizePolicy.setHeightForWidth(Label4->sizePolicy().hasHeightForWidth());
        Label4->setSizePolicy(sizePolicy);
        Label4->setMinimumSize(QSize(1, 1));
        Label4->setLineWidth(0);
        Label4->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout->addWidget(Label4, 7, 0, 1, 1);

        verticalSpacer_10 = new QSpacerItem(20, 5, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_10, 8, 0, 1, 1);

        gridLayout_3 = new QGridLayout();
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName("gridLayout_3");
        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_3, 0, 0, 1, 1);

        PushButton1 = new QPushButton(MessageBoxWidget1);
        PushButton1->setObjectName("PushButton1");
        sizePolicy.setHeightForWidth(PushButton1->sizePolicy().hasHeightForWidth());
        PushButton1->setSizePolicy(sizePolicy);
        PushButton1->setMinimumSize(QSize(1, 1));

        gridLayout_3->addWidget(PushButton1, 0, 1, 1, 1);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_5, 0, 2, 1, 1);

        PushButton2 = new QPushButton(MessageBoxWidget1);
        PushButton2->setObjectName("PushButton2");
        sizePolicy.setHeightForWidth(PushButton2->sizePolicy().hasHeightForWidth());
        PushButton2->setSizePolicy(sizePolicy);
        PushButton2->setMinimumSize(QSize(1, 1));

        gridLayout_3->addWidget(PushButton2, 0, 3, 1, 1);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_4, 0, 4, 1, 1);

        gridLayout_3->setColumnStretch(0, 100);
        gridLayout_3->setColumnStretch(1, 80);
        gridLayout_3->setColumnStretch(2, 10);
        gridLayout_3->setColumnStretch(3, 80);
        gridLayout_3->setColumnStretch(4, 100);

        gridLayout->addLayout(gridLayout_3, 9, 0, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 19, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_2, 10, 0, 1, 1);

        gridLayout->setRowStretch(0, 2);
        gridLayout->setRowStretch(1, 60);
        gridLayout->setRowStretch(2, 3);
        gridLayout->setRowStretch(3, 12);
        gridLayout->setRowStretch(4, 3);
        gridLayout->setRowStretch(5, 12);
        gridLayout->setRowStretch(6, 6);
        gridLayout->setRowStretch(7, 12);
        gridLayout->setRowStretch(8, 12);
        gridLayout->setRowStretch(9, 19);
        gridLayout->setRowStretch(10, 22);

        retranslateUi(MessageBoxWidget1);

        QMetaObject::connectSlotsByName(MessageBoxWidget1);
    } // setupUi

    void retranslateUi(QWidget *MessageBoxWidget1)
    {
        MessageBoxWidget1->setWindowTitle(QCoreApplication::translate("MessageBoxWidget1", "Form", nullptr));
        Label1->setText(QString());
        Label2->setText(QString());
        Label3->setText(QString());
        Label4->setText(QString());
        PushButton1->setText(QString());
        PushButton2->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class MessageBoxWidget1: public Ui_MessageBoxWidget1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MESSAGEBOXWIDGET1_H

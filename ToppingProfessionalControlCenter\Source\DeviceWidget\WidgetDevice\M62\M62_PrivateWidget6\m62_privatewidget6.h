#ifndef M62_PRIVATEWIDGET6_H
#define M62_PRIVATEWIDGET6_H

#include <QWidget>

class M62_PrivateWidget6 : public QWidget {
    Q_OBJECT

public:
    explicit M62_PrivateWidget6(QWidget *parent = nullptr);

protected:
    void resizeEvent(QResizeEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void timerEvent(QTimerEvent *event) override;

private:
    struct SvgCircle {
        qreal x, y, width, height;
    };
    QList<SvgCircle> parseSvgJson();
    QList<SvgCircle> scaleElements(const QList<SvgCircle> &elements);

private:
    qreal originalWidth = 205;
    qreal originalHeight = 427;
    QList<SvgCircle> originalElements;
    QList<SvgCircle> ScaleElements;
    QPixmap pix;
    int startIndex = 0;
    int randomNum = 0; 

    const QByteArray jsonData =
    R"({
        "groups": [
            {
                "y": 111.46,
                "elements": [
                    {"x": 74.28, "y": 111.46, "width": 3.4, "height": 3.4},
                    {"x": 88.45, "y": 111.46, "width": 3.4, "height": 3.4},
                    {"x": 116.8, "y": 111.46, "width": 3.4, "height": 3.4},
                    {"x": 130.97, "y": 111.46, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 122.79,
                "elements": [
                    {"x": 74.28, "y": 122.79, "width": 3.4, "height": 3.4},
                    {"x": 88.45, "y": 122.79, "width": 3.4, "height": 3.4},
                    {"x": 116.8, "y": 122.79, "width": 3.4, "height": 3.4},
                    {"x": 130.97, "y": 122.79, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 134.13,
                "elements": [
                    {"x": 74.28, "y": 134.13, "width": 3.4, "height": 3.4},
                    {"x": 88.45, "y": 134.13, "width": 3.4, "height": 3.4},
                    {"x": 116.8, "y": 134.13, "width": 3.4, "height": 3.4},
                    {"x": 130.97, "y": 134.13, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 145.47,
                "elements": [
                    {"x": 74.28, "y": 145.47, "width": 3.4, "height": 3.4},
                    {"x": 88.45, "y": 145.47, "width": 3.4, "height": 3.4},
                    {"x": 116.8, "y": 145.47, "width": 3.4, "height": 3.4},
                    {"x": 130.97, "y": 145.47, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 156.81,
                "elements": [
                    {"x": 74.28, "y": 156.81, "width": 3.4, "height": 3.4},
                    {"x": 88.45, "y": 156.81, "width": 3.4, "height": 3.4},
                    {"x": 116.8, "y": 156.81, "width": 3.4, "height": 3.4},
                    {"x": 130.97, "y": 156.81, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 168.15,
                "elements": [
                    {"x": 74.28, "y": 168.15, "width": 3.4, "height": 3.4},
                    {"x": 88.45, "y": 168.15, "width": 3.4, "height": 3.4},
                    {"x": 116.8, "y": 168.15, "width": 3.4, "height": 3.4},
                    {"x": 130.97, "y": 168.15, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 179.49,
                "elements": [
                    {"x": 74.28, "y": 179.49, "width": 3.4, "height": 3.4},
                    {"x": 88.45, "y": 179.49, "width": 3.4, "height": 3.4},
                    {"x": 116.8, "y": 179.49, "width": 3.4, "height": 3.4},
                    {"x": 130.97, "y": 179.49, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 190.83,
                "elements": [
                    {"x": 74.28, "y": 190.83, "width": 3.4, "height": 3.4},
                    {"x": 88.45, "y": 190.83, "width": 3.4, "height": 3.4},
                    {"x": 116.8, "y": 190.83, "width": 3.4, "height": 3.4},
                    {"x": 130.97, "y": 190.83, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 202.16,
                "elements": [
                    {"x": 74.28, "y": 202.16, "width": 3.4, "height": 3.4},
                    {"x": 88.45, "y": 202.16, "width": 3.4, "height": 3.4},
                    {"x": 116.8, "y": 202.16, "width": 3.4, "height": 3.4},
                    {"x": 130.97, "y": 202.16, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 219.17,
                "elements": [
                    {"x": 43.1, "y": 219.17, "width": 3.4, "height": 3.4},
                    {"x": 82.78, "y": 219.17, "width": 3.4, "height": 3.4},
                    {"x": 122.47, "y": 219.17, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 227.68,
                "elements": [
                    {"x": 43.1, "y": 227.68, "width": 3.4, "height": 3.4},
                    {"x": 82.78, "y": 227.68, "width": 3.4, "height": 3.4},
                    {"x": 122.47, "y": 227.68, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 236.18,
                "elements": [
                    {"x": 43.1, "y": 236.18, "width": 3.4, "height": 3.4},
                    {"x": 82.78, "y": 236.18, "width": 3.4, "height": 3.4},
                    {"x": 122.47, "y": 236.18, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 251.77,
                "elements": [
                    {"x": 43.1, "y": 251.77, "width": 3.4, "height": 3.4},
                    {"x": 82.78, "y": 251.77, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y": 260.27,
                "elements": [
                    {"x": 43.1, "y": 260.27, "width": 3.4, "height": 3.4},
                    {"x": 82.78, "y": 260.27, "width": 3.4, "height": 3.4},
                    {"x": 122.47, "y": 260.27, "width": 3.4, "height": 3.4},
                    {"x": 162.15, "y": 260.27, "width": 3.4, "height": 3.4}
                ]
            },
            {
                "y":402.7,
                "elements": [
                    {"x": 153.7, "y": 402.7, "width": 3.4, "height": 3.4},
                    {"x": 102.7, "y": 402.7, "width": 3.4, "height": 3.4},
                    {"x": 52.7, "y": 402.7, "width": 3.4, "height": 3.4}
                ]
            }
        ]
    })";
};

#endif // M62_PRIVATEWIDGET6_H


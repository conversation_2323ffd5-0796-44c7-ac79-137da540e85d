/************************************************************************
 *  The module is a wrapper for an UI language text.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

 // Module is empty if .h file was not included (category turned off).
#ifdef __WnUiLanguageText_h__

 // optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//static
WString WnUiLanguageText::UI_NL(UILT_UI_NEWLINE);
WString WnUiLanguageText::F_NL(UILT_FILE_NEWLINE);

WnUiLanguageText::WnUiLanguageText()
{
}

WnUiLanguageText::WnUiLanguageText(const wchar_t* idStr, const wchar_t* defaultText, const WString& comment /*= L""*/)
    : mIdentifierStr(idStr), mDefaultText(defaultText), mComment(comment)
{
}


WnUiLanguageText::~WnUiLanguageText()
{
}


WnUiLanguageText::WnUiLanguageText( const WnUiLanguageText& src)
{
    *this = src;
}


WnUiLanguageText& WnUiLanguageText::operator = (const WnUiLanguageText& src)
{
    // Self assignment? 
    if (&src == this) return *this;

    mIdentifierStr = src.mIdentifierStr;
    mLanguageText = src.mLanguageText;
    mIsLanguageTextValid = src.mIsLanguageTextValid;
    mDefaultText = src.mDefaultText;
    mComment = src.mComment;    

    return *this;
}


void WnUiLanguageText::Clear()
{
    mIdentifierStr = nullptr;
    mLanguageText.clear();
    mIsLanguageTextValid = false;
    mDefaultText = nullptr;
    mComment.clear();    
}


WString WnUiLanguageText::GetLanguageTextToDisplay() const
{
    //get the text to display
    const WString& text = (mIsLanguageTextValid ? mLanguageText : ((nullptr == mDefaultText) ? L"" : mDefaultText));
    
    //remove all file newlines
    WString displayText = TbStdReplaceAll(text, WnUiLanguageText::F_NL, L"");

    //replace all UI newlines by a displayable newline
    displayText = TbStdReplaceAll(displayText, WnUiLanguageText::UI_NL, L"\n");

    return displayText;
}


#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguageText_h__

/*************************** EOF **************************************/

#include "origins1m3.h"
#include "menus1m1.h"
#include <QStack>
#include <QToolButton>
#include <QPainter>
#include <QStyleOption>
#include "globalfont.h"
#include <float.h>

OriginS1M3::OriginS1M3(QWidget *parent, const QString& name)
    : OriginBase(parent), mWidget(nullptr), mMenuDuck(nullptr), mMenuConfig(nullptr), mButtonRouteSpecification(nullptr),
    WorkspaceObserver(name), AppSettingsObserver(name), mOFF(true)
{
    setStyleSheet("background:transparent");
    setMinimumSize(80, 220);
    mWidget = new QWidget(this);
    mWidget->setMinimumSize(80, 220);
    QString style = QString("background: rgba(31, 31, 31, 1);border-radius: 8px;");
    mWidget->setStyleSheet(style);
    mMenuDuck = new MenuS1M1(mWidget, "Duck");
    mMenuDuck->setMenuPosition(MenuS1M1::MenuPosition::Right);
    mMenuDuck->addButton({"OFF", "DuckingSwitch"});
    mMenuDuck->setStretchFactor(40, 60);
    mMenuDuck->setButtonFontSizeRatio(0.5);
    mMenuConfig = new MenuS1M1(mWidget, "Config");
    mMenuConfig->setMenuPosition(MenuS1M1::MenuPosition::Right);
    mMenuConfig->addButton({"Save", "Save"}, {{"User1", "User1", true}, {"User2", "User2", true}});
    mMenuConfig->addButton({"Load", "Load"}, {{"User1", "User1", true}, {"User2", "User2", true}});
    //mMenuConfig->addButton({"Default", "Default"}, {{"Previous changes cannot be undone.", "Previous changes cannot be undone.", false}, {"yes", "yes", true}});
    mMenuConfig->addButton({"Professional", "Professional"});
    mMenuConfig->setStretchFactor(14, 86);
    mMenuConfig->setButtonFontSizeRatio(0.5);
    mButtonRouteSpecification = new QToolButton(mWidget);
    mButtonRouteSpecification->setMinimumHeight(25);
    mButtonRouteSpecification->setCheckable(true);
    mButtonRouteSpecification->setText("Router Manuals");
    style = QString("QToolButton{color: rgba(161, 161, 161, 1);"
                            "border-radius: 5px;"
                            "background:rgba(22, 22, 22, 1);}"
                            "QToolButton:checked{color: rgba(255, 255, 255, 1);}");
    mButtonRouteSpecification->setStyleSheet(style);
    
    reset();
    initSigConnect();
}

OriginS1M3& OriginS1M3::setName(const QString &name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}

OriginS1M3& OriginS1M3::setFont(const QFont &font)
{
    QStack<QWidget*> stack;
    stack.push(this);

    while (!stack.isEmpty())
    {
        QWidget* widget = stack.pop();
        widget->setFont(font);

        for (auto child : widget->children())
        {
            if (QWidget* childWidget = qobject_cast<QWidget*>(child))
            {
                stack.push(childWidget);
            }
        }
    }
    return *this;
}

OriginS1M3& OriginS1M3::setValueDuckingSwitch(bool state)
{
    mOFF = state;
    mMenuDuck->setButtonText("DuckingSwitch", state == true?"ON":"OFF");
    WorkspaceObserver::setValue("DuckingSwitch", state == true?"true":"false");
    saveAttribute("DuckingSwitch");
    return *this;
}

void OriginS1M3::loadSettings()
{
    reset();
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("DuckingSwitch", true);
    }
    mMenuDuck->setButtonChecked("DuckingSwitch", WorkspaceObserver::value("DuckingSwitch").toBool());
    mMenuDuck->setButtonText("DuckingSwitch", WorkspaceObserver::value("DuckingSwitch").toBool()?"ON":"OFF");
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_DuckingSwitch", QString::number(WorkspaceObserver::value("DuckingSwitch").toBool()));
    }
    updateAttribute();
}

void OriginS1M3::AppSettingsChanged(QString objectName, QString attribute, QString value)
{

}

void OriginS1M3::updateAttribute()
{
    if(mOFF != static_cast<int>(WorkspaceObserver::value("DuckingSwitch").toBool()))
    {
        mOFF = WorkspaceObserver::value("DuckingSwitch").toBool();
        emit attributeChanged(this->objectName(), "DuckingSwitch", QString::number(mOFF));
    }
}

void OriginS1M3::reset()
{
    mOFF = INT_MAX;
}

void OriginS1M3::resizeEvent(QResizeEvent *event)
{
    auto getWHHoldRatio = [](QWidget* parent, QWidget* child, double wRatio=1.0f, double hRatio=1.0f){
        double aspectRatio = static_cast<double>(child->minimumWidth()) / child->minimumHeight();
        double widthScale = static_cast<double>(parent->width()) *wRatio / child->minimumWidth();
        double heightScale = static_cast<double>(parent->height()) *hRatio / child->minimumHeight();
        double scale = std::min(widthScale, heightScale);
        double newWidth = child->minimumWidth() * scale;
        int newHeight = static_cast<int>(newWidth / aspectRatio);
        return std::make_tuple(newWidth, newHeight);
    };
    auto childCenterToParentAndHoldRatio = [=](QWidget* parent, QWidget* child){
        auto wh =  getWHHoldRatio(parent, child);
        int w =std::get<0>(wh);
        int h =std::get<1>(wh);
        child->setGeometry((parent->width()-w)/2,(parent->height()-h)/2,w, h);
    };
    childCenterToParentAndHoldRatio(this, mWidget);

    double hPixelPerRatio = mWidget->height() / 100.0;
    double wPixelPerRatio = mWidget->width() / 100.0;
    double spacing = 3 * hPixelPerRatio;

    mMenuDuck->setGeometry(0, 0, 100 * wPixelPerRatio, 20 * hPixelPerRatio);
    mMenuConfig->setGeometry(0, (20 + 5) * hPixelPerRatio, 100 * wPixelPerRatio, 60 * hPixelPerRatio);
    mButtonRouteSpecification->setGeometry(0, (20 + 5 + 60 + 5) * hPixelPerRatio, 100 * wPixelPerRatio, 10 * hPixelPerRatio);

    QFont font = mButtonRouteSpecification->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, mButtonRouteSpecification->height())*55/100);
    mButtonRouteSpecification->setFont(font);
}

void OriginS1M3::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    QPainter painter(this);
    QStyleOption opt;
    opt.initFrom(this);
    style()->drawPrimitive(QStyle::PE_Widget, &opt, &painter, this);
}

void OriginS1M3::saveAttribute(QString attribute)
{
    auto saveValue = [this](const QString& attribute, const QString& value){
        if(isWidgetEmitAction())
        {
            emit attributeChanged(this->objectName(), QString("Save_")+attribute, value);
        }
    };

    if(attribute == "DuckingSwitch")
    {
        mOFF = WorkspaceObserver::value("DuckingSwitch").toBool();
        saveValue(attribute, QString::number(mOFF));
    }
}

void OriginS1M3::in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value)
{
    WorkspaceObserver::setValue(attribute, value);
    updateAttribute();
    saveAttribute(attribute);
}

void OriginS1M3::initSigConnect()
{
    connect(mMenuDuck, &MenuS1M1::attributeChanged, [this](QString objectName, QString attribute, QString value){
        if(attribute == "DuckingSwitch")
        {
            mMenuDuck->setButtonText("DuckingSwitch", value == "true"?"ON":"OFF");
            in_mWidgetListAll_attributeChanged(this->objectName(), "DuckingSwitch", value);
        }
    });
    connect(mMenuConfig, &MenuS1M1::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        emit attributeChanged(this->objectName(), attribute, value);
    });
    connect(mButtonRouteSpecification, &QToolButton::clicked, this, [=](bool checked){
        emit attributeChanged(this->objectName(), "RouterManuals", checked ? "ON" : "OFF");
    });
}

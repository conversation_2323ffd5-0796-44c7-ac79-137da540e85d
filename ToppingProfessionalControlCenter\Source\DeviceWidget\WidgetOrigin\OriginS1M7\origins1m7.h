#ifndef ORIGINS1M7_H
#define ORIGINS1M7_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"
#include "pushbuttons1m7.h"


namespace Ui {
class OriginS1M7;
}


class OriginS1M7 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M7(QWidget* parent=nullptr, QString name="");
    ~OriginS1M7();
    OriginS1M7& setName(QString name);
    OriginS1M7& setFont(QFont font);
    OriginS1M7& setVolumeMeterLeft(int value);
    OriginS1M7& setVolumeMeterLeftClear();
    OriginS1M7& setVolumeMeterLeftSlip();
    OriginS1M7& setVolumeMeterRight(int value);
    OriginS1M7& setVolumeMeterRightClear();
    OriginS1M7& setVolumeMeterRightSlip();
    OriginS1M7& setGain(float value);
    OriginS1M7& setGainLock(bool state=true);
    OriginS1M7& setMuteAffectGain(bool state=true);
    OriginS1M7& setGainAffectMute(bool state=true);
    OriginS1M7& setGainRange(float min, float max);
    OriginS1M7& setGainDefault(float value);
    OriginS1M7& setGainWidgetDisable(float value);
    OriginS1M7& setChannelNameEditable(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OriginS1M7* ui;
    QTimer mTimer;
    QFont mFont;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(int value);
    void in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M7::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // ORIGINS1M7_H


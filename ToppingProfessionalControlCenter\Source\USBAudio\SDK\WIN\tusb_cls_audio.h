/*******************************************************************************
 *
 *  Module:         tusb_cls_audio.h
 *  Description:    USB Device Class Specification for Audio devices.
 *
 *  Runtime Env.:   any
 *  Author(s):      Rein<PERSON>
 *  Company:        Thesycon GmbH, Ilmenau
 *  Copyright:      (c) 2010 Thesycon Systemsoftware and Consulting GmbH
 *
 ******************************************************************************/

#ifndef __tusb_cls_audio_h__
#define __tusb_cls_audio_h__

#include "tusb_spec.h"


// pack the following structures
#include "tbase_pack1.h"

//////////////////////////////////////////////////////////////////////////
//
// version numbers of the audio specs. used for bcdAudio
//
// NOTE: these defines assume the little endian byte order
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_VER_10   0x0100
#define TUSB_CLS_AUDIO_VER_18   0x0180
#define TUSB_CLS_AUDIO_VER_20   0x0200


//////////////////////////////////////////////////////////////////////////
//
// audio specific sub class codes
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_SUB_UNDEFINED        0x00
#define TUSB_CLS_AUDIO_SUB_AUDIOCONTROL     0x01
#define TUSB_CLS_AUDIO_SUB_AUDIOSTREAMING   0x02
#define TUSB_CLS_AUDIO_SUB_MIDISTREAMING    0x03


//////////////////////////////////////////////////////////////////////////
//
// audio specific protocol codes
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_PROT_UNDEFINED           0x00
/* USB Audio 2.0 only */
#define TUSB_CLS_AUDIO_PROT_IP_VERSION_02_00    0x20


//////////////////////////////////////////////////////////////////////////
//
// audio specific descriptor types
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_UNDEFINED_DESCRIPTOR       0x20
#define TUSB_CLS_AUDIO_DEVICE_DESCRIPTOR          0x21
#define TUSB_CLS_AUDIO_CONFIGURATION_DESCRIPTOR   0x22
#define TUSB_CLS_AUDIO_STRING_DESCRIPTOR          0x23
#define TUSB_CLS_AUDIO_INTERFACE_DESCRIPTOR       0x24
#define TUSB_CLS_AUDIO_ENDPOINT_DESCRIPTOR        0x25


//////////////////////////////////////////////////////////////////////////
//
// Audio Control (AC) interface descriptor subtypes
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_AC_DESCRIPTOR_UNDEFINED      0x00
#define TUSB_CLS_AUDIO_AC_HEADER_DESCRIPTOR         0x01
#define TUSB_CLS_AUDIO_AC_INPUT_TERMINAL            0x02
#define TUSB_CLS_AUDIO_AC_OUTPUT_TERMINAL           0x03
#define TUSB_CLS_AUDIO_AC_MIXER_UNIT                0x04
#define TUSB_CLS_AUDIO_AC_SELECTOR_UNIT             0x05
#define TUSB_CLS_AUDIO_AC_FEATURE_UNIT              0x06
/* USB Audio 1.0 specific */
#define TUSB_CLS_AUDIO10_AC_PROCESSING_UNIT         0x07
#define TUSB_CLS_AUDIO10_AC_EXTENSION_UNIT          0x08
/* USB Audio 2.0 specific */
#define TUSB_CLS_AUDIO20_AC_EFFECT_UNIT             0x07
#define TUSB_CLS_AUDIO20_AC_PROCESSING_UNIT         0x08
#define TUSB_CLS_AUDIO20_AC_EXTENSION_UNIT          0x09
#define TUSB_CLS_AUDIO20_AC_CLOCK_SOURCE            0x0A
#define TUSB_CLS_AUDIO20_AC_CLOCK_SELECTOR          0x0B
#define TUSB_CLS_AUDIO20_AC_CLOCK_MULTIPLIER        0x0C
#define TUSB_CLS_AUDIO20_AC_SAMPLE_RATE_CONVERTER   0x0D


//////////////////////////////////////////////////////////////////////////
//
// Audio Streaming (AS) interface descriptor subtypes
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_AS_DESCRIPTOR_UNDEFINED    0x00
#define TUSB_CLS_AUDIO_AS_GENERAL                 0x01
#define TUSB_CLS_AUDIO_AS_FORMAT_TYPE             0x02
/* USB Audio 1.0 only */
#define TUSB_CLS_AUDIO10_AS_FORMAT_SPECIFIC       0x03
/* USB Audio 2.0 only */
#define TUSB_CLS_AUDIO20_AS_ENCODER               0x03
#define TUSB_CLS_AUDIO20_AS_DECODER               0x04


//////////////////////////////////////////////////////////////////////////
//
// endpoint descriptor subtypes
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_EP_DESCRIPTOR_UNDEFINED   0x00
#define TUSB_CLS_AUDIO_EP_GENERAL                0x01


//////////////////////////////////////////////////////////////////////////
//
// Processing Unit Process Types
//
// NOTE: Processing Unit Process Types which only occurs in one
//       USB Audio spec. (1.0; 1.8 or 2.0) are marked with the Version
//       of the spec. where the Processing Unit Process Types occur.
//       E.g.: TUSB_CLS_AUDIO10_3D_STEREO_EXTENDER_PROCESS
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_PROCESS_UNDEFINED                0x00
#define TUSB_CLS_AUDIO_UPDOWNMIX_PROCESS                0x01
#define TUSB_CLS_AUDIO_DOLBY_PROLOGIC_PROCESS           0x02
/* USB Audio 1.0 specific */
#define TUSB_CLS_AUDIO10_3D_STEREO_EXTENDER_PROCESS     0x03
#define TUSB_CLS_AUDIO10_REVERBERATION_PROCESS          0x04
#define TUSB_CLS_AUDIO10_CHORUS_PROCESS                 0x05
#define TUSB_CLS_AUDIO10_DYN_RANGE_COMP_PROCESS         0x06
/* USB Audio 2.0 specific */
#define TUSB_CLS_AUDIO20_STEREO_EXTENDER_PROCESS        0x03


//////////////////////////////////////////////////////////////////////////
//
// audio terminal types
//
//////////////////////////////////////////////////////////////////////////

/* USB Terminal Types */
#define TUSB_CLS_AUDIO_TT_USB_UNDEFINED                   0x0100
#define TUSB_CLS_AUDIO_TT_USB_STREAMING                   0x0101
#define TUSB_CLS_AUDIO_TT_USB_VENDOR_SPECIFIC             0x01FF

/* Input Terminal Types */
#define TUSB_CLS_AUDIO_TT_INPUT_UNDEFINED                 0x0200
#define TUSB_CLS_AUDIO_TT_MICROPHONE                      0x0201
#define TUSB_CLS_AUDIO_TT_DESKTOP_MICROPHONE              0x0202
#define TUSB_CLS_AUDIO_TT_PERSONAL_MICROPHONE             0x0203
#define TUSB_CLS_AUDIO_TT_OMNI_DIRECTIONAL_MICROPHONE     0x0204
#define TUSB_CLS_AUDIO_TT_MICROPHONE_ARRAY                0x0205
#define TUSB_CLS_AUDIO_TT_PROCESSING_MICROPHONE_ARRAY     0x0206

/* Output Terminal Types */
#define TUSB_CLS_AUDIO_TT_OUTPUT_UNDEFINED                0x0300
#define TUSB_CLS_AUDIO_TT_SPEAKER                         0x0301
#define TUSB_CLS_AUDIO_TT_HEADPHONES                      0x0302
#define TUSB_CLS_AUDIO_TT_HEAD_MOUNTED_DISPLAY_AUDIO      0x0303
#define TUSB_CLS_AUDIO_TT_DESKTOP_SPEAKER                 0x0304
#define TUSB_CLS_AUDIO_TT_ROOM_SPEAKER                    0x0305
#define TUSB_CLS_AUDIO_TT_COMMUNICATION_SPEAKER           0x0306
#define TUSB_CLS_AUDIO_TT_LOW_FREQUENCY_EFFECTS_SPEAKER   0x0307

/* Bi-directional Terminal Types */
#define TUSB_CLS_AUDIO_TT_BI_DIRECTIONAL_UNDEFINED        0x0400
#define TUSB_CLS_AUDIO_TT_HANDSET                         0x0401
#define TUSB_CLS_AUDIO_TT_HEADSET                         0x0402
#define TUSB_CLS_AUDIO_TT_SPEAKERPHONE_NO_ECHO_REDUCTION  0x0403
#define TUSB_CLS_AUDIO_TT_ECHO_SUPPRESSING_SPEAKERPHONE   0x0404
#define TUSB_CLS_AUDIO_TT_ECHO_CANCELING_SPEAKERPHONE     0x0405

/* Telephony Terminal Types */
#define TUSB_CLS_AUDIO_TT_TELEPHONY_UNDEFINED             0x0500
#define TUSB_CLS_AUDIO_TT_PHONE_LINE                      0x0501
#define TUSB_CLS_AUDIO_TT_TELEPHONE                       0x0502
#define TUSB_CLS_AUDIO_TT_DOWN_LINE_PHONE                 0x0503

/* External Terminal Types */
#define TUSB_CLS_AUDIO_TT_EXTERNAL_UNDEFINED              0x0600
#define TUSB_CLS_AUDIO_TT_ANALOG_CONNECTOR                0x0601
#define TUSB_CLS_AUDIO_TT_DIGITAL_AUDIO_INTERFACE         0x0602
#define TUSB_CLS_AUDIO_TT_LINE_CONNECTOR                  0x0603
#define TUSB_CLS_AUDIO_TT_LEGACY_AUDIO_CONNECTOR          0x0604
#define TUSB_CLS_AUDIO_TT_SPDIF_INTERFACE                 0x0605
#define TUSB_CLS_AUDIO_TT_1394_DA_STREAM                  0x0606
#define TUSB_CLS_AUDIO_TT_1394_DV_STREAM_SOUNDTRACK       0x0607
// added by Audio Class 2.0
#define TUSB_CLS_AUDIO_TT_ADAT_LIGHTPIPE                  0x0608
#define TUSB_CLS_AUDIO_TT_TDIF                            0x0609
#define TUSB_CLS_AUDIO_TT_MADI                            0x060A

/* Embedded Function Terminal Types */
#define TUSB_CLS_AUDIO_TT_EMBEDDED_UNDEFINED              0x0700
#define TUSB_CLS_AUDIO_TT_LEVEL_CALIBRATION_NOISE_SOURCE  0x0701
#define TUSB_CLS_AUDIO_TT_EQUALIZATION_NOISE              0x0702
#define TUSB_CLS_AUDIO_TT_CD_PLAYER                       0x0703
#define TUSB_CLS_AUDIO_TT_DAT                             0x0704
#define TUSB_CLS_AUDIO_TT_DCC                             0x0705
#define TUSB_CLS_AUDIO_TT_MINIDISK                        0x0706
#define TUSB_CLS_AUDIO_TT_ANALOG_TAPE                     0x0707
#define TUSB_CLS_AUDIO_TT_PHONOGRAPH                      0x0708
#define TUSB_CLS_AUDIO_TT_VCR_AUDIO                       0x0709
#define TUSB_CLS_AUDIO_TT_VIDEO_DISC_AUDIO                0x070A
#define TUSB_CLS_AUDIO_TT_DVD_AUDIO                       0x070B
#define TUSB_CLS_AUDIO_TT_TV_TUNER_AUDIO                  0x070C
#define TUSB_CLS_AUDIO_TT_SATELLITE_RECEIVER_AUDIO        0x070D
#define TUSB_CLS_AUDIO_TT_CABLE_TUNER_AUDIO               0x070E
#define TUSB_CLS_AUDIO_TT_DSS_AUDIO                       0x070F
#define TUSB_CLS_AUDIO_TT_RADIO_RECEIVER                  0x0710
#define TUSB_CLS_AUDIO_TT_RADIO_TRANSMITTER               0x0711
#define TUSB_CLS_AUDIO_TT_MULTITRACK_RECORDER             0x0712
#define TUSB_CLS_AUDIO_TT_SYNTHESIZER                     0x0713
// added by Audio Class 2.0
#define TUSB_CLS_AUDIO_TT_PIANO                           0x0714
#define TUSB_CLS_AUDIO_TT_GUITAR                          0x0715
#define TUSB_CLS_AUDIO_TT_DRUMS_RHYTHM                    0x0716
#define TUSB_CLS_AUDIO_TT_OTHER_MUSICAL_INSTRUMENT        0x0717


//////////////////////////////////////////////////////////////////////////
//
// spatial locations
// Audio 1.0: wChannelConfig bitmask
// Audio 2.0: bmChannelConfig bitmask
//
//////////////////////////////////////////////////////////////////////////

// NOTE: The encoding below corresponds to the constants defined by Microsoft.
// See SPEAKER_FRONT_LEFT, SPEAKER_FRONT_RIGHT, etc. defined in ksmedia.h.
#define TUSB_CLS_AUDIO_LOC_L      (1u<<0)   // Left Front
#define TUSB_CLS_AUDIO_LOC_R      (1u<<1)   // Right Front
#define TUSB_CLS_AUDIO_LOC_C      (1u<<2)   // Center Front
#define TUSB_CLS_AUDIO_LOC_LFE    (1u<<3)   // Low Frequency Enhancement
#define TUSB_CLS_AUDIO_LOC_LS     (1u<<4)   // Left Surround
#define TUSB_CLS_AUDIO_LOC_RS     (1u<<5)   // Right Surround
#define TUSB_CLS_AUDIO_LOC_LC     (1u<<6)   // Left of Center
#define TUSB_CLS_AUDIO_LOC_RC     (1u<<7)   // Right of Center
#define TUSB_CLS_AUDIO_LOC_S      (1u<<8)   // Surround
#define TUSB_CLS_AUDIO_LOC_SL     (1u<<9)   // Side Left
#define TUSB_CLS_AUDIO_LOC_SR     (1u<<10)  // Side Right
#define TUSB_CLS_AUDIO_LOC_T      (1u<<11)  // Top



//////////////////////////////////////////////////////////////////////////
//
// format type codes
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_FORMAT_TYPE_UNDEFINED     0x00
#define TUSB_CLS_AUDIO_FORMAT_TYPE_1             0x01
#define TUSB_CLS_AUDIO_FORMAT_TYPE_2             0x02
#define TUSB_CLS_AUDIO_FORMAT_TYPE_3             0x03
/* only USB Audio 2.0 */
#define TUSB_CLS_AUDIO_FORMAT_TYPE_4             0x04
#define TUSB_CLS_AUDIO_EXT_FORMAT_TYPE_1         0x81
#define TUSB_CLS_AUDIO_EXT_FORMAT_TYPE_2         0x82
#define TUSB_CLS_AUDIO_EXT_FORMAT_TYPE_3         0x83


//////////////////////////////////////////////////////////////////////////
//
// audio data format type (FT) codes
//
//////////////////////////////////////////////////////////////////////////

/* USB Audio 1.0 */

/* Format Type 1 Codes - for use with TUSB_CLS_AUDIO_FORMAT_TYPE_1 */
#define TUSB_CLS_AUDIO10_FT1_UNDEFINED                  0x0000
#define TUSB_CLS_AUDIO10_FT1_PCM                        0x0001
#define TUSB_CLS_AUDIO10_FT1_PCM8                       0x0002
#define TUSB_CLS_AUDIO10_FT1_IEEE_FLOAT                 0x0003
#define TUSB_CLS_AUDIO10_FT1_ALAW                       0x0004
#define TUSB_CLS_AUDIO10_FT1_MULAW                      0x0005

/* Format Type 2 Codes - for use with TUSB_CLS_AUDIO_FORMAT_TYPE_2 */
#define TUSB_CLS_AUDIO10_FT2_UNDEFINED                  0x1000
#define TUSB_CLS_AUDIO10_FT2_MPEG                       0x1001
#define TUSB_CLS_AUDIO10_FT2_AC_3                       0x1002

/* Format Type 3 Codes - for use with TUSB_CLS_AUDIO_FORMAT_TYPE_3 */
#define TUSB_CLS_AUDIO10_FT3_UNDEFINED                  0x2000
#define TUSB_CLS_AUDIO10_FT3_IEC1937_AC_3               0x2001
#define TUSB_CLS_AUDIO10_FT3_IEC1937_MPEG_1_LAYER1      0x2002
#define TUSB_CLS_AUDIO10_FT3_IEC1937_MPEG_1_LAYER2_3    0x2003
#define TUSB_CLS_AUDIO10_FT3_IEC1937_MPEG_2_NOEXT       0x2003
#define TUSB_CLS_AUDIO10_FT3_IEC1937_MPEG_2_EXT         0x2004
#define TUSB_CLS_AUDIO10_FT3_IEC1937_MPEG_2_LAYER1_LS   0x2005
#define TUSB_CLS_AUDIO10_FT3_IEC1937_MPEG_2_LAYER2_3_LS 0x2006


/* USB Audio 2.0 */

/* Format Type 1 Bit Allocations - for TUSB_CLS_AUDIO_FORMAT_TYPE_1 */
#define TUSB_CLS_AUDIO20_FT1_PCM                    0x00000001  /* bit masks ! */
#define TUSB_CLS_AUDIO20_FT1_PCM8                   0x00000002
#define TUSB_CLS_AUDIO20_FT1_IEEE_FLOAT             0x00000004
#define TUSB_CLS_AUDIO20_FT1_ALAW                   0x00000008
#define TUSB_CLS_AUDIO20_FT1_MULAW                  0x00000010
/* bits 5..30 are reserved */
#define TUSB_CLS_AUDIO20_FT1_RAW_DATA               0x80000000

/* Format Type 2 Bit Allocations - for TUSB_CLS_AUDIO_FORMAT_TYPE_2 */

/* Format Type 3 Bit Allocations - for TUSB_CLS_AUDIO_FORMAT_TYPE_3 */
#define TUSB_CLS_AUDIO20_FT3_IEC1937_AC_3                0x00000001
#define TUSB_CLS_AUDIO20_FT3_IEC1937_MPEG_1_LAYER1       0x00000002
#define TUSB_CLS_AUDIO20_FT3_IEC1937_MPEG_1_LAYER2_3     0x00000004
#define TUSB_CLS_AUDIO20_FT3_IEC1937_MPEG_2_EXT          0x00000008
#define TUSB_CLS_AUDIO20_FT3_IEC1937_MPEG_2_AAC_ADTS     0x00000010
#define TUSB_CLS_AUDIO20_FT3_IEC1937_MPEG_2_LAYER1_LS    0x00000020
#define TUSB_CLS_AUDIO20_FT3_IEC1937_MPEG_2_LAYER2_3_LS  0x00000040
#define TUSB_CLS_AUDIO20_FT3_IEC1937_DTS_I               0x00000080
#define TUSB_CLS_AUDIO20_FT3_IEC1937_DTS_II              0x00000100
#define TUSB_CLS_AUDIO20_FT3_IEC1937_DTS_III             0x00000200
#define TUSB_CLS_AUDIO20_FT3_IEC1937_ATRAC               0x00000400
#define TUSB_CLS_AUDIO20_FT3_IEC1937_ATRAC2_3            0x00000800
#define TUSB_CLS_AUDIO20_FT3_WMA                         0x00001000

/* ### implement this */


//////////////////////////////////////////////////////////////////////////
//
// Audio request codes
//
//////////////////////////////////////////////////////////////////////////

#define TUSB_CLS_AUDIO_REQ_CODE_UNDEFINED     0x00
/* Audio class 1.0 */
#define TUSB_CLS_AUDIO10_REQ_SET_CUR          0x01
#define TUSB_CLS_AUDIO10_REQ_GET_CUR          0x81
#define TUSB_CLS_AUDIO10_REQ_SET_MIN          0x02
#define TUSB_CLS_AUDIO10_REQ_GET_MIN          0x82
#define TUSB_CLS_AUDIO10_REQ_SET_MAX          0x03
#define TUSB_CLS_AUDIO10_REQ_GET_MAX          0x83
#define TUSB_CLS_AUDIO10_REQ_SET_RES          0x04
#define TUSB_CLS_AUDIO10_REQ_GET_RES          0x84
#define TUSB_CLS_AUDIO10_REQ_SET_MEM          0x05
#define TUSB_CLS_AUDIO10_REQ_GET_MEM          0x85
#define TUSB_CLS_AUDIO10_REQ_GET_STAT         0xFF
/* Audio class 2.0 */
#define TUSB_CLS_AUDIO20_REQ_CURRENT          0x01
#define TUSB_CLS_AUDIO20_REQ_RANGE            0x02
#define TUSB_CLS_AUDIO20_REQ_MEMORY           0x03



//////////////////////////////////////////////////////////////////////////
//
// Audio Control Selector Codes used for requests
//
//////////////////////////////////////////////////////////////////////////

/* clock source */
#define TUSB_CLS_AUDIO_CS_CONTROL_UNDEFINED         0x00
#define TUSB_CLS_AUDIO_CS_SAM_FREQ_CONTROL          0x01
#define TUSB_CLS_AUDIO_CS_CLOCK_VALID_CONTROL       0x02

/* clock selector */
#define TUSB_CLS_AUDIO_CX_CONTROL_UNDEFINED         0x00
#define TUSB_CLS_AUDIO_CX_CLOCK_SELECTOR_CONTROL    0x01

/* clock multiplier */
#define TUSB_CLS_AUDIO_CM_CONTROL_UNDEFINED         0x00
#define TUSB_CLS_AUDIO_CM_NUMERATOR_CONTROL         0x01
#define TUSB_CLS_AUDIO_CM_DENOMINATOR_CONTROL       0x02

/* terminal */
#define TUSB_CLS_AUDIO_TE_CONTROL_UNDEFINED         0x00
#define TUSB_CLS_AUDIO_TE_COPY_PROTECT_CONTROL      0x01
#define TUSB_CLS_AUDIO_TE_CONNECTOR_CONTROL         0x02
#define TUSB_CLS_AUDIO_TE_OVERLOAD_CONTROL          0x03
#define TUSB_CLS_AUDIO_TE_CLUSTER_CONTROL           0x04
#define TUSB_CLS_AUDIO_TE_UNDERFLOW_CONTROL         0x05
#define TUSB_CLS_AUDIO_TE_OVERFLOW_CONTROL          0x06
#define TUSB_CLS_AUDIO_TE_LATENCY_CONTROL           0x07

/* mixer */
#define TUSB_CLS_AUDIO_MU_CONTROL_UNDEFINED         0x00
#define TUSB_CLS_AUDIO_MU_MIXER_CONTROL             0x01
#define TUSB_CLS_AUDIO_MU_CLUSTER_CONTROL           0x02
#define TUSB_CLS_AUDIO_MU_UNDERFLOW_CONTROL         0x03
#define TUSB_CLS_AUDIO_MU_OVERFLOW_CONTROL          0x04
#define TUSB_CLS_AUDIO_MU_LATENCY_CONTROL           0x05

/* selector */
#define TUSB_CLS_AUDIO_SU_CONTROL_UNDEFINED         0x00
#define TUSB_CLS_AUDIO_SU_SELECTOR_CONTROL          0x01
#define TUSB_CLS_AUDIO_SU_LATENCY_CONTROL           0x02

/* feature unit */
#define TUSB_CLS_AUDIO_FU_CONTROL_UNDEFINED         0x00
#define TUSB_CLS_AUDIO_FU_MUTE_CONTROL              0x01
#define TUSB_CLS_AUDIO_FU_VOLUME_CONTROL            0x02
#define TUSB_CLS_AUDIO_FU_BASS_CONTROL              0x03
#define TUSB_CLS_AUDIO_FU_MID_CONTROL               0x04
#define TUSB_CLS_AUDIO_FU_TREBLE_CONTROL            0x05
#define TUSB_CLS_AUDIO_FU_GRAPHIC_EQUALIZER_CONTROL 0x06
#define TUSB_CLS_AUDIO_FU_AUTOMATIC_GAIN_CONTROL    0x07
#define TUSB_CLS_AUDIO_FU_DELAY_CONTROL             0x08
#define TUSB_CLS_AUDIO_FU_BASS_BOOST_CONTROL        0x09
#define TUSB_CLS_AUDIO_FU_LOUDNESS_CONTROL          0x0A
#define TUSB_CLS_AUDIO_FU_INPUT_GAIN_CONTROL        0x0B
#define TUSB_CLS_AUDIO_FU_INPUT_GAIN_PAD_CONTROL    0x0C
#define TUSB_CLS_AUDIO_FU_PHASE_INVERTER_CONTROL    0x0D
#define TUSB_CLS_AUDIO_FU_UNDERFLOW_CONTROL         0x0E
#define TUSB_CLS_AUDIO_FU_OVERFLOW_CONTROL          0x0F
#define TUSB_CLS_AUDIO_FU_LATENCY_CONTROL           0x10

/* effect units*/
/* parametric equalizer */
/* reverberation */
/* modulation delay */
/* dynamic range compressor */

/* processing */
/* up/down-mix */
/* dolby prologic */
/* stereo extender */

/* extension */
#define TUSB_CLS_AUDIO_XU_CONTROL_UNDEFINED         0x00
#define TUSB_CLS_AUDIO_XU_ENABLE_CONTROL            0x01
#define TUSB_CLS_AUDIO_XU_CLUSTER_CONTROL           0x02
#define TUSB_CLS_AUDIO_XU_UNDERFLOW_CONTROL         0x03
#define TUSB_CLS_AUDIO_XU_OVERFLOW_CONTROL          0x04
#define TUSB_CLS_AUDIO_XU_LATENCY_CONTROL           0x05

/* audio streaming (audio class 2.0 only) */
#define TUSB_CLS_AUDIO_AS_CONTROL_UNDEFINED          0x00
#define TUSB_CLS_AUDIO_AS_ACT_ALT_SETTING_CONTROL    0x01
#define TUSB_CLS_AUDIO_AS_VAL_ALT_SETTING_CONTROL    0x02
#define TUSB_CLS_AUDIO_AS_AUDIO_DATA_FORMAT_CONTROL  0x03

/* encoder */

/* decoder */
/* mpeg */
/* ac-3 */
/* wma */
/* dts */

/* endpoint */
/* Audio class 1.0 */
#define TUSB_CLS_AUDIO10_EP_CONTROL_UNDEFINED       0x00
#define TUSB_CLS_AUDIO10_EP_SAMPLING_FREQ_CONTROL   0x01
#define TUSB_CLS_AUDIO10_EP_PITCH_CONTROL           0x02

/* Audio class 2.0 */
#define TUSB_CLS_AUDIO20_EP_CONTROL_UNDEFINED       0x00
#define TUSB_CLS_AUDIO20_EP_PITCH_CONTROL           0x01
#define TUSB_CLS_AUDIO20_EP_DATA_OVERRUN_CONTROL    0x02
#define TUSB_CLS_AUDIO20_EP_DATA_UNDERRUN_CONTROL   0x03


//////////////////////////////////////////////////////////////////////////
//
// Volume control
//
//////////////////////////////////////////////////////////////////////////

// USB volume = -INF
#define TUSB_CLS_AUDIO_VOLUME_SILENCE   (-32768)



//////////////////////////////////////////////////////////////////////////
//
// Common audio specific descriptors
// These descriptors are equal in all audio specs. Audio Version specific
// descriptors are defined in the header file for the specific audio version.
// Throughout the definition of the descriptors the following naming convention is used:
//
// naming convention:
//    T_UsbClsAudio[VER]_[AC/AS/MIDI]_[DescriptorName]
//
//    [VER]             version of the USB Audio spec. where the descriptor
//                      is defined.
//                      10 == USB Audio 1.0 Spec.
//                      20 == USB Audio 2.0 Spec.
//                NOTE: if [VER] is not included in name the descriptor is the same for all
//                      USB Audio Specs.
//
//      [AC/AS/MS]      define the interface type of the descriptor.
//
//            [AC]      audio control interface descriptor
//            [AS]      audio streaming interface descriptor
//            [MS]      MIDI streaming interface descriptor
//
//    [DescriptorName]  name of the descriptor as defined in the
//                      USB Audio Spec.
//
// NOTE: all structs assume the little endian byte order
//
//////////////////////////////////////////////////////////////////////////

/* audio descriptor header */
typedef struct tagT_UsbClsAudio_DescriptorHeader
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;
    T_UINT8   bDescriptorSubtype;
} T_UsbClsAudio_DescriptorHeader;

TB_CHECK_SIZEOF(T_UsbClsAudio_DescriptorHeader, 3);

/* audio control descriptor header */
typedef struct tagT_UsbClsAudio_AC_DescriptorHeader
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;
    T_UINT8   bDescriptorSubtype;
    T_UINT8   bID;                  // terminal, clock, or unit ID
} T_UsbClsAudio_AC_DescriptorHeader;

TB_CHECK_SIZEOF(T_UsbClsAudio_AC_DescriptorHeader, 4);

/* audio control version independent */

/* common part of the Interface Header Descriptors in the
     USB Audio Spec. 1.0; 1.8 and 2.0 */
typedef struct tagT_UsbClsAudio_AC_InterfaceHeaderCommonDescriptor
{
    T_UINT8      bLength;
    T_UINT8      bDescriptorType;       /* TUSB_CLS_AUDIO_INTERFACE_DESCRIPTOR */
    T_UINT8      bDescriptorSubtype;    /* TUSB_CLS_AUDIO_AC_HEADER_DESCRIPTOR */
    T_LE_UINT16  bcdADC;  /* unaligned */
} T_UsbClsAudio_AC_InterfaceHeaderCommonDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio_AC_InterfaceHeaderCommonDescriptor, 5);

/* audio streaming version independent */

/* common part of the AS Interface Descriptor */
typedef struct tagT_UsbClsAudio_AS_InterfaceDescriptorHeader
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;
    T_UINT8   bDescriptorSubtype;
    T_UINT8   bTerminalLink;
} T_UsbClsAudio_AS_InterfaceDescriptorHeader;

TB_CHECK_SIZEOF(T_UsbClsAudio_AS_InterfaceDescriptorHeader, 4);

/* common part of the format type descriptors */
typedef struct tagT_UsbClsAudio_AS_FormatTypeCommonDescriptor
{
    T_UINT8   bLength;
    T_UINT8   bDescriptorType;      /* TUSB_CLS_AUDIO_INTERFACE_DESCRIPTOR */
    T_UINT8   bDescriptorSubtype;   /* TUSB_CLS_AUDIO_AS_FORMAT_TYPE */
    T_UINT8   bFormatType;          /* TUSB_CLS_AUDIO_FORMAT_TYPE_1, ... */
} T_UsbClsAudio_AS_FormatTypeCommonDescriptor;

TB_CHECK_SIZEOF(T_UsbClsAudio_AS_FormatTypeCommonDescriptor, 4);

// restore packing
#include "tbase_packrestore.h"

#endif // __tusb_cls_audio_h__

/*************************** EOF **************************************/

#include "equalizerwidget.h"
#include <qnamespace.h>
#include <QApplication>
#include <QScrollBar>
#include <QSizePolicy>
#include <QTimer>

// EqualizerBandWidget 实现
EqualizerBandWidget::EqualizerBandWidget(int bandIndex, QWidget* parent)
    : QFrame(parent)
    , mBandIndex(bandIndex)
    , mMainLayout(nullptr)
    , mBandLabel(nullptr)
    , mTypeComboBox(nullptr)
    , mGainDial(nullptr)
    , mFrequencyDial(nullptr)
    , mQValueDial(nullptr)
    , mEnabledCheckBox(nullptr)
{
    // 初始化默认数据
    mBandData.type = "高通滤波";
    mBandData.gain = 0.0f;
    mBandData.frequency = 1000.0f;
    mBandData.qValue = 0.7f;
    mBandData.enabled = true;
    
    setupUI();
    setupConnections();
    setDarkTheme();
}

EqualizerBandWidget::~EqualizerBandWidget()
{
}

void EqualizerBandWidget::setupUI()
{
    setMinimumWidth(50);
    
    mMainLayout = new QVBoxLayout(this);
    mMainLayout->setSpacing(0);
    mMainLayout->setContentsMargins(0, 0, 0, 0);
    
    mBandLabel = new QLabel(QString::number(mBandIndex + 1), this);
    mBandLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mBandLabel->setAlignment(Qt::AlignCenter);
    mMainLayout->addWidget(mBandLabel, 4);
    
    mTypeComboBox = new ComboBoxS1M3(nullptr);
    mTypeComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    updateTypeOptions();
    mMainLayout->addWidget(mTypeComboBox, 6);
    
    mGainDial = new DialS1M1(this);
    mGainDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mGainDial->setRange(-20.0f, 20.0f)
              .setValue(0.0f)
              .setStep(0.1f)
              .setPrecision(1)
              .showSign(true);
    mMainLayout->addWidget(mGainDial, 9);

    mFrequencyDial = new DialS1M1(this);
    mFrequencyDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mFrequencyDial->setRange(20.0f, 20000.0f)
                  .setValue(1000.0f)
                  .setStep(1.0f)
                  .setPrecision(0);
    mMainLayout->addWidget(mFrequencyDial, 9);
    
    mQValueDial = new DialS1M1(this);
    mQValueDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mQValueDial->setRange(0.1f, 10.0f)
                .setValue(0.7f)
                .setStep(0.1f)
                .setPrecision(1);
    mMainLayout->addWidget(mQValueDial, 9);
    
    mEnabledCheckBox = new QCheckBox(this);
    mEnabledCheckBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    mEnabledCheckBox->setChecked(true);
    mMainLayout->addWidget(mEnabledCheckBox, 3, Qt::AlignCenter);
}

void EqualizerBandWidget::setupConnections()
{
    connect(mTypeComboBox, &ComboBoxS1M3::currentTextChanged,
            this, &EqualizerBandWidget::onTypeChanged);
    connect(mGainDial, &DialS1M1::valueChanged,
            this, &EqualizerBandWidget::onGainChanged);
    connect(mFrequencyDial, &DialS1M1::valueChanged,
            this, &EqualizerBandWidget::onFrequencyChanged);
    connect(mQValueDial, &DialS1M1::valueChanged,
            this, &EqualizerBandWidget::onQValueChanged);
    connect(mEnabledCheckBox, &QCheckBox::toggled,
            this, &EqualizerBandWidget::onEnabledChanged);
}

void EqualizerBandWidget::updateTypeOptions()
{
    QVector<QString> types = {
        "高通滤波",
        "低通滤波",
        "High Shelf",
        "High Shelf"
    };
    
    mTypeComboBox->setCurrentText("高通滤波");
}

void EqualizerBandWidget::setBandData(const EqualizerBand& data)
{
    mBandData = data;
    
    // 更新UI
    mTypeComboBox->setCurrentText(data.type);
    mGainDial->setValue(data.gain);
    mFrequencyDial->setValue(data.frequency);
    mQValueDial->setValue(data.qValue);
    mEnabledCheckBox->setChecked(data.enabled);
}

EqualizerBand EqualizerBandWidget::getBandData() const
{
    return mBandData;
}

void EqualizerBandWidget::setBandIndex(int index)
{
    mBandIndex = index;
    mBandLabel->setText(QString::number(index + 1));
}

void EqualizerBandWidget::setDarkTheme()
{
    setStyleSheet(
        "EqualizerBandWidget {"
        "    background-color: #2a2a2a;"
        "    border: 1px solid #404040;"
        "    border-radius: 8px;"
        "}"
        "QLabel {"
        "    color: #404040;"
        "    font-size: 12px;"
        "}"
        "QCheckBox {"
        "    color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "    border: 2px solid #43cf7c;"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: transparent;"
        "    border: 2px solid #666666;"
        "}"
    );
}

void EqualizerBandWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqualizerBandWidget::onTypeChanged(const QString& text)
{
    mBandData.type = text;
    emit bandDataChanged(mBandIndex, mBandData);
}

void EqualizerBandWidget::onGainChanged(float value)
{
    mBandData.gain = value;
    emit bandDataChanged(mBandIndex, mBandData);
}

void EqualizerBandWidget::onFrequencyChanged(float value)
{
    mBandData.frequency = value;
    emit bandDataChanged(mBandIndex, mBandData);
}

void EqualizerBandWidget::onQValueChanged(float value)
{
    mBandData.qValue = value;
    emit bandDataChanged(mBandIndex, mBandData);
}

void EqualizerBandWidget::onEnabledChanged(bool enabled)
{
    mBandData.enabled = enabled;
    emit bandDataChanged(mBandIndex, mBandData);
}

// EqualizerWidget 实现
EqualizerWidget::EqualizerWidget(QWidget* parent)
    : QWidget(parent)
    , mMainLayout(nullptr)
    , mControlLayout(nullptr)
    , mScrollArea(nullptr)
    , mScrollWidget(nullptr)
    , mBandsLayout(nullptr)
    , mAddBandButton(nullptr)
    , mRemoveBandButton(nullptr)
    , mTitleLabel(nullptr)
    , mStretchFactor(3.0)
{
    setupUI();
    setupConnections();
    createDefaultBands();
    setDarkTheme();
}

EqualizerWidget::~EqualizerWidget()
{
    removeAllBands();
}

void EqualizerWidget::setupUI()
{
    mMainLayout = new QVBoxLayout(this);
    mMainLayout->setSpacing(0);
    mMainLayout->setContentsMargins(0, 0, 0, 0);

    // 标题和控制按钮
    mControlLayout = new QHBoxLayout();

    mTitleLabel = new QLabel("均衡器", this);
    mControlLayout->addWidget(mTitleLabel);

    mControlLayout->addStretch();

    mAddBandButton = new QPushButton("添加段", this);
    mAddBandButton->setFixedSize(80, 30);
    mControlLayout->addWidget(mAddBandButton);

    mRemoveBandButton = new QPushButton("删除段", this);
    mRemoveBandButton->setFixedSize(80, 30);
    mControlLayout->addWidget(mRemoveBandButton);

    mMainLayout->addLayout(mControlLayout);

    // 滚动区域
    mScrollArea = new QScrollArea(this);
    mScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    mScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea->setWidgetResizable(true);

    mScrollWidget = new QWidget();
    mBandsLayout = new QHBoxLayout(mScrollWidget);
    mBandsLayout->setSpacing(0);
    mBandsLayout->setContentsMargins(0, 0, 0, 0);
    mBandsLayout->addStretch();
    mScrollArea->setWidget(mScrollWidget);

    auto vBoxLayout = new QVBoxLayout();
    vBoxLayout->setContentsMargins(0, 0, 0, 0);
    vBoxLayout->setSpacing(0);
    vBoxLayout->addStretch(1);
    auto labelType = new QLabel("Type", this);
    labelType->setAlignment(Qt::AlignCenter);
    auto labelGain = new QLabel("Gain", this);
    labelGain->setAlignment(Qt::AlignCenter);
    auto labelFrequency = new QLabel("Freq", this);
    labelFrequency->setAlignment(Qt::AlignCenter);
    auto labelQ = new QLabel("Q", this);
    labelQ->setAlignment(Qt::AlignCenter);
    vBoxLayout->addWidget(labelType, 1);
    vBoxLayout->addWidget(labelGain, 1);
    vBoxLayout->addWidget(labelFrequency, 1);
    vBoxLayout->addWidget(labelQ, 1);
    vBoxLayout->addStretch(1);

    auto hBoxLayout = new QHBoxLayout();
    hBoxLayout->addLayout(vBoxLayout);
    hBoxLayout->addWidget(mScrollArea);

    mMainLayout->addLayout(hBoxLayout);
}

void EqualizerWidget::setupConnections()
{
    connect(mAddBandButton, &QPushButton::clicked,
            this, &EqualizerWidget::onAddBandClicked);
    connect(mRemoveBandButton, &QPushButton::clicked,
            this, &EqualizerWidget::onRemoveBandClicked);
}

void EqualizerWidget::createDefaultBands()
{
    // 创建默认的4个均衡器段
    setBandCount(4);
}

void EqualizerWidget::setBandCount(int count)
{
    if (count < 0) count = 0;
    if (count > 32) count = 32; // 限制最大段数

    int currentCount = mBands.size();

    if (count > currentCount) {
        // 添加段
        for (int i = currentCount; i < count; ++i) {
            addBand();
        }
    } else if (count < currentCount) {
        // 删除段
        for (int i = currentCount - 1; i >= count; --i) {
            removeBand(i);
        }
    }

    emit bandCountChanged(count);
}

void EqualizerWidget::addBand()
{
    int index = mBands.size();
    EqualizerBandWidget* bandWidget = new EqualizerBandWidget(index, this);
    bandWidget->setFixedWidth(mStretchFactor * bandWidget->minimumWidth());

    connect(bandWidget, &EqualizerBandWidget::bandDataChanged,
            this, &EqualizerWidget::onBandDataChanged);

    mBands.append(bandWidget);

    // 在stretch之前插入
    mBandsLayout->insertWidget(mBandsLayout->count() - 1, bandWidget);

    // 更新删除按钮状态
    mRemoveBandButton->setEnabled(mBands.size() > 1);

    emit bandCountChanged(mBands.size());
}

void EqualizerWidget::removeBand(int index)
{
    if (mBands.isEmpty()) return;

    if (index < 0 || index >= mBands.size()) {
        index = mBands.size() - 1; // 删除最后一个
    }

    EqualizerBandWidget* bandWidget = mBands.takeAt(index);
    mBandsLayout->removeWidget(bandWidget);
    bandWidget->deleteLater();

    // 更新剩余段的索引
    updateBandIndices();

    // 更新删除按钮状态
    mRemoveBandButton->setEnabled(mBands.size() > 1);

    emit bandCountChanged(mBands.size());
    emit equalizerDataChanged(getEqualizerData());
}

void EqualizerWidget::removeAllBands()
{
    while (!mBands.isEmpty()) {
        removeBand(-1);
    }
}

void EqualizerWidget::updateBandIndices()
{
    for (int i = 0; i < mBands.size(); ++i) {
        mBands[i]->setBandIndex(i);
    }
}

void EqualizerWidget::setEqualizerData(const QVector<EqualizerBand>& data)
{
    setBandCount(data.size());

    for (int i = 0; i < data.size() && i < mBands.size(); ++i) {
        mBands[i]->setBandData(data[i]);
    }

    emit equalizerDataChanged(getEqualizerData());
}

QVector<EqualizerBand> EqualizerWidget::getEqualizerData() const
{
    QVector<EqualizerBand> data;
    for (const auto& band : mBands) {
        data.append(band->getBandData());
    }
    return data;
}

void EqualizerWidget::setDarkTheme()
{
    setStyleSheet(
        "QWidget {"
        "    background-color: rgb(22,22,22);"
        "    border: 1px solid #404040;"
        "    border-radius: 10px;"
        "}"
        "QLabel {"
        "    color: #404040;"
        "    font-size: 12px;"
        "}"
        "QPushButton {"
        "    background-color: #43cf7c;"
        "    color: #ffffff;"
        "    border: none;"
        "    border-radius: 4px;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #52d689;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3bb56e;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #666666;"
        "    color: #999999;"
        "}"
        "QScrollArea {"
        "    background-color: rgb(22,22,22);"
        "    border: 1px solid #404040;"
        "    border-radius: 8px;"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: #404040;"
        "    height: 12px;"
        "    border-radius: 6px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background-color: #43cf7c;"
        "    border-radius: 6px;"
        "    min-width: 20px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #52d689;"
        "}"
    );
}

void EqualizerWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqualizerWidget::onBandDataChanged(int bandIndex, const EqualizerBand& data)
{
    if (bandIndex >= 0 && bandIndex < mBands.size()) {
        emit equalizerDataChanged(getEqualizerData());
    }
}

void EqualizerWidget::onAddBandClicked()
{
    addBand();
}

void EqualizerWidget::onRemoveBandClicked()
{
    removeBand(-1);
}

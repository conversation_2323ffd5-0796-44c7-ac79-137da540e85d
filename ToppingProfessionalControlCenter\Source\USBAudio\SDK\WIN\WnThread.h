/************************************************************************
 *
 *  Module:       WnThread.h
 *
 *  Description:  Thread object wrapper
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    U<PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnThread_h__
#define __WnThread_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// Interface called by Wn<PERSON><PERSON><PERSON> to execute the thread routine.
//
//lint -esym(1510, WnThreadCallbackInterface)  base class has no destructor
class WnThreadCallbackInterface
{
public:
    virtual
    DWORD
    ThreadRoutine() = 0;
};


//
// Template to create a helper class that forwards the
// ThreadRoutine() callback to a member function of the specified class T.
// The signature of the member function has to match with WnThreadCallbackInterface::ThreadRoutine.
//
template <class T>
class WnThreadCallbackForwarderT : public WnThreadCallbackInterface
{
public:
    // signature of member function to be called
    typedef DWORD (T::*MemberFnPtr)();

    // ctor
    WnThreadCallbackForwarderT()
            {
                mObjectPtr = NULL;
                mFnPtr = NULL;
            }

    // Initialize, store a pointer to an object that implements
    // a member function with a signature as defined by MemberFnPtr
    void
    Init(
        T* objectPtr,               // pointer to an instance of T
        MemberFnPtr memberFnPtr     // pointer to a member function of T
        )
            {
                mObjectPtr = objectPtr;
                mFnPtr = memberFnPtr;
            }

// data members
private:
    T* mObjectPtr;
    MemberFnPtr mFnPtr;

// implement WnThreadCallbackInterface
private:
    virtual
    DWORD
    ThreadRoutine()
            { // call member function
                return ( (mObjectPtr->*mFnPtr)() );
            }

};//WnThreadCallbackForwarderT




//
// Wrapper class for a Win32 thread object.
//
class WnThread : public WnHandle
{
public:
    // ctor
    WnThread();

    // dtor
    // WaitForTerminationAndClose MUST BE called before this instance is destroyed!
     virtual
     ~WnThread();


/////////////////////////////////////////
// Defs
/////////////////////////////////////////
public:



/////////////////////////////////////////
// Methods
/////////////////////////////////////////
public:

    //
    // Override Close()
    //
    virtual
    void
    Close();


    // Returns true if the thread is still executing.
    // Returns false if no thread has been created, or the thread
    // has already terminated itself.
    bool
    IsRunning() const;


    //
    // Create a new thread that executes callback->ThreadRoutine().
    // The thread is created by means of the CRT function _beginthreadex().
    // So the thread routine is allowed to call CRT functions.
    //
    // Note: Under Windows CE this is mapped to CreateNativeThread.
    //
    WNERR
    CreateCrtAwareThread(
        WnThreadCallbackInterface* callback,
        unsigned int stackSize = 0, // 0 for default
        unsigned int flags = 0      // 0 or CREATE_SUSPENDED
        );

    //
    // Create a new thread that executes callback->ThreadRoutine().
    // The thread is created by means of the Win32 function CreateThread().
    // The thread routine MUST NOT call any CRT functions.
    //
    WNERR
    CreateNativeThread(
        WnThreadCallbackInterface* callback,
        DWORD stackSize = 0,  // 0 for default
        DWORD flags = 0       // 0 or CREATE_SUSPENDED
        );


    // Wait until the thread terminated.
    // When the thread is terminated, close the thread handle.
    // This function MUST BE called before this instance is destroyed!
    // Formerly this function was named WaitForThreadTermination!
    DWORD
    WaitForTerminationAndClose(
        DWORD timeoutMilliseconds = INFINITE
        );


    // Wait until the thread terminated.
    DWORD
    WaitForTermination(
        DWORD timeoutMilliseconds = INFINITE
        );


    // return the thread handle
    HANDLE
    GetThreadHandle()
            { return GetHandle(); }

    // return the thread ID
    DWORD
    GetThreadId()
            { return mThreadId; }


    //
    // Return the thread ID of the current thread.
    //
    static
    DWORD
    GetCurrentThreadId()
            {
                return ::GetCurrentThreadId();
            }


    //
    // Returns true if the calling thread is the thread represented by the object.
    //
    bool
    IsCurrentThread() const
            {
                return (mThreadId == GetCurrentThreadId());
            }


    // Decrements a thread's suspend count. When the suspend count is decremented to zero,
    // the execution of the thread is resumed.
    DWORD
    Resume()
            { return ::ResumeThread(mHandle); }


    // Suspends the specified thread.
    DWORD
    Suspend()
            { return ::SuspendThread(mHandle); }

    // sets the priority of this thread instance
    WNERR
    SetThreadPriority(int nPriority);

    // sets the priority of the current/calling thread
    static
    WNERR
    SetCurrentThreadPriority(int nPriority);

    // returns the priority of this thread instance in the nPriority variable
    // If successful this function returns ERROR_SUCCESS. In case of an error this function returns the GetLastError value.
    WNERR
    GetThreadPriority(int& nPriority);

    // returns the priority of the current/calling thread in the nPriority variable
    // If successful this function returns ERROR_SUCCESS. In case of an error this function returns the GetLastError value.
    static
    WNERR
    GetCurrentThreadPriority(int& nPriority);


/////////////////////////////////////////
// Implementation
/////////////////////////////////////////
private:

    // thread routine called by CRT
    static
    unsigned int
    __stdcall
    beginthread_routine(
        void* context
        );

    // thread routine called by the system
    static
    DWORD
    WINAPI
    thread_routine(
        void* context
        );


/////////////////////////////////////////
// Data Members
/////////////////////////////////////////
protected:

    // thread ID
    DWORD mThreadId;



// make copy constructor and assignment operator unavailable
PRIVATE_COPY_CONSTRUCTOR(WnThread)
PRIVATE_ASSIGNMENT_OPERATOR(WnThread)

};//class WnThread

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnThread_h__

/*************************** EOF **************************************/

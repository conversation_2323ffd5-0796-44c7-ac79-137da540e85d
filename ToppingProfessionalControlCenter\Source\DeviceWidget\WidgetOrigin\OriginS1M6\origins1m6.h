#ifndef ORIGINS1M6_H
#define ORIGINS1M6_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"
#include "pushbuttons1m7.h"


namespace Ui {
class OriginS1M6;
}


class OriginS1M6 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M6(QWidget* parent=nullptr, QString name="");
    ~OriginS1M6();
    OriginS1M6& setName(QString name);
    OriginS1M6& setFont(QFont font);
    OriginS1M6& setVolumeMeterLeft(int value);
    OriginS1M6& setVolumeMeterLeftClear();
    OriginS1M6& setVolumeMeterLeftSlip();
    OriginS1M6& setVolumeMeterRight(int value);
    OriginS1M6& setVolumeMeterRightClear();
    OriginS1M6& setVolumeMeterRightSlip();
    OriginS1M6& setGain(float value);
    OriginS1M6& setGainLock(bool state=true);
    OriginS1M6& setMuteAffectGain(bool state=true);
    OriginS1M6& setGainAffectMute(bool state=true);
    OriginS1M6& setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    OriginS1M6& setGainDefault(float value);
    OriginS1M6& setGainWidgetDisable(float value);
    OriginS1M6& setChannelNameEditable(bool state=true);
    OriginS1M6& setValueGAIN(float value);
    OriginS1M6& setValueMUTE(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OriginS1M6* ui;
    QTimer mTimer;
    QFont mFont;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(float value);
    void in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M7::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // ORIGINS1M6_H


#include "dials1m2.h"
#include "globalfont.h"


DialS1M2::DialS1M2(QWidget* parent)
    : QWidget(parent)
{
    setFocusPolicy(Qt::StrongFocus);
}
DialS1M2::~DialS1M2()
{

}


// override
void DialS1M2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int diameter = qMin(rect().width(), rect().height());
    mPenWidth=diameter / 20 + 1;
    mRectDial.setX(rect().x() + mPenWidth);
    mRectDial.setY(rect().y() + mPenWidth);
    if(diameter == rect().width())
    {
        mRectDial.setY(rect().y() + (rect().height() - diameter) / 2 + mPenWidth);
    }
    if(diameter == rect().height())
    {
        mRectDial.setX(rect().x() + (rect().width() - diameter) / 2 + mPenWidth);
    }
    mRectDial.setWidth(diameter - 2 * mPenWidth);
    mRectDial.setHeight(diameter - 2 * mPenWidth);
}
void DialS1M2::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void DialS1M2::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(mRectDial.contains(e->pos()))
    {
        if(mValue != mValueDefault)
        {
            mValue = mValueDefault;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M2::mousePressEvent(QMouseEvent* e)
{
    if(mMouseEnabled && mRectDial.contains(e->pos()))
    {
        mPressed = true;
        mPressedValue = mValue;
        mPressedPoint = e->pos();
    }
}
void DialS1M2::mouseMoveEvent(QMouseEvent* e)
{
    float value=0;
    if(mPressed)
    {
        value = mPressedValue;
        value += ((int) (mPressedPoint.y() - e->pos().y()) / mSensitivity) * 0.5;
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            float newValueMod=std::abs(std::fmod(value, 1.0));
            if(value >= mValueEnd05)
            {
            }
            else if(value >= mValueEnd10)
            {
                if(newValueMod != 0)
                {
                    return;
                }
            }
            else if(value >= mValueEnd20)
            {
                if(newValueMod != 0 || ((int) value) % 2)
                {
                    return;
                }
            }
            mValue = value;
            emit valueChanged(mValue);
            update();
        }
    }
}
void DialS1M2::mouseReleaseEvent(QMouseEvent* e)
{
    Q_UNUSED(e);
    mPressed = false;
}
void DialS1M2::wheelEvent(QWheelEvent* e)
{
    float value=mValue;
    int numSteps=e->angleDelta().y() / 120;
    int numStepsAbs=qAbs(numSteps);
    for(int i=0;i<numStepsAbs;i++)
    {
        if(numSteps > 0)
        {
            if(value >= mValueEnd05)
            {
                value += 0.5;
            }
            else if(value >= mValueEnd10)
            {
                value += 1;
            }
            else if(value >= mValueEnd20)
            {
                value += 2;
            }
        }
        else
        {
            if(value > mValueEnd05)
            {
                value -= 0.5;
            }
            else if(value > mValueEnd10)
            {
                value -= 1;
            }
            else if(value > mValueEnd20)
            {
                value -= 2;
            }
        }
    }
    value = qMax(value, mValueMin);
    value = qMin(value, mValueMax);
    if(mValue != value)
    {
        mValue = value;
        emit valueChanged(mValue);
    }
    update();
}
void DialS1M2::keyPressEvent(QKeyEvent* e)
{
    float value=mValue;
    if(e->key() == Qt::Key_Up || e->key() == Qt::Key_Right)
    {
        if(value >= mValueEnd05)
        {
            value += 0.5;
        }
        else if(value >= mValueEnd10)
        {
            value += 1;
        }
        else if(value >= mValueEnd20)
        {
            value += 2;
        }
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
    else if(e->key() == Qt::Key_Down || e->key() == Qt::Key_Left)
    {
        if(value > mValueEnd05)
        {
            value -= 0.5;
        }
        else if(value > mValueEnd10)
        {
            value -= 1;
        }
        else if(value > mValueEnd20)
        {
            value -= 2;
        }
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
}
void DialS1M2::drawBG(QPainter* painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(mColorBG));
    painter->drawRect(rect());
    painter->restore();
}
void DialS1M2::drawElement(QPainter* painter)
{
    painter->save();
    // draw BG
    QPen pen=painter->pen();
    pen.setWidth(mPenWidth);
    pen.setColor(mColorCircleBG);
    painter->setPen(pen);
    painter->setBrush(QBrush(mColorDial));
    painter->drawEllipse(mRectDial);
    // draw Indicator
    // arrow
    QPolygon pts;
    pts.setPoints(3, -mRectDial.width() / 13, mRectDial.height() / 7 * 2, mRectDial.width() / 13, mRectDial.height() / 7 * 2, 0, mRectDial.height() / 5 * 2);
    painter->translate(mRectDial.x() + mRectDial.width() / 2, mRectDial.y() + mRectDial.height() / 2);
    painter->rotate(45);
    qreal degRotate = 270.0 / (mValueMax - mValueMin) * (mValue - mValueMin);
    painter->rotate(degRotate);
    if(mValueShowArrow)
    {
        pen.setWidth(1);
        pen.setColor(mColorHandle);
        painter->setPen(pen);
        painter->setBrush(mColorHandle);
        painter->drawConvexPolygon(pts);
    }
    // circle
    if(mValueShowCircle)
    {
        painter->resetTransform();
        pen.setWidth(mPenWidth);
        pen.setColor(mColorCircleValue);
        painter->setPen(pen);
        painter->setBrush(Qt::NoBrush);
        painter->drawArc(mRectDial, 225 * 16, -degRotate * 16);
    }
    // draw Text
    if(mValueShowText)
    {
        painter->resetTransform();
        QRect rectText(mRectDial.x() + mRectDial.width() / 4, mRectDial.y() + mRectDial.height() / 4, mRectDial.width() / 2, mRectDial.height() / 2);
            // get suitable font
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "-88", rectText.width()) - 3);
        if(mFont.pointSize() < 4)
        {
            mFont.setPointSize(mFont.pointSize() + 2);
        }
        painter->setFont(mFont);
        painter->setPen(mColorText);
            // Text
        QString str;
        if(mValue > mValueEnd05)
        {
            str = QString("%1").arg((double) mValue, 0, 'f', 1);
            if(mValue > 0)
            {
                str = QString("+%1").arg((double) mValue, 0, 'f', 1);
            }
        }
        else
        {
            str = QString("%1").arg((double) mValue, 0, 'f', 0);
        }
        if(str.toFloat() == mValueMin)
        {
            str = mShowInfinitesimal ? ("-∞") : (str);
        }
        else if(str.toFloat() == mValueMax)
        {
            str = mShowInfinity ? ("+∞") : (str);
        }
        painter->drawText(rectText, Qt::AlignCenter, str);
    }
    painter->restore();
}


// setter & getter
DialS1M2& DialS1M2::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
DialS1M2& DialS1M2::setValue(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValue = value;
    update();
    return *this;
}
DialS1M2& DialS1M2::setDefault(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    mValueDefault = value;
    return *this;
}
DialS1M2& DialS1M2::setRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20)
{
    mValueStart = valueStart;
    mValueEnd05 = valueEnd05;
    mValueEnd10 = valueEnd10;
    mValueEnd20 = valueEnd20;
    mValueMin = mValueEnd20;
    mValueMax = mValueStart;
    if(mValueMax < mValueDefault || mValueDefault < mValueMin)
    {
        mValueDefault = mValueMin;
    }
    update();
    return *this;
}
DialS1M2& DialS1M2::setSensitivity(int sensitivity)
{
    if(sensitivity > 100 || sensitivity <= 0)
    {
        return *this;
    }
    mSensitivity = sensitivity;
    return *this;
}
DialS1M2& DialS1M2::setMovable(bool status)
{
    mMouseEnabled = status;
    return *this;
}
DialS1M2& DialS1M2::setColorBG(QColor color)
{
    mColorBG = color;
    update();
    return *this;
}
DialS1M2& DialS1M2::setColorDial(QColor color)
{
    mColorDial = color;
    update();
    return *this;
}
DialS1M2& DialS1M2::setColorCircleBG(QColor color)
{
    mColorCircleBG = color;
    update();
    return *this;
}
DialS1M2& DialS1M2::setColorCircleValue(QColor color)
{
    mColorCircleValue = color;
    update();
    return *this;
}
DialS1M2& DialS1M2::setColorHandle(QColor color)
{
    mColorHandle = color;
    update();
    return *this;
}
DialS1M2& DialS1M2::setColorText(QColor color)
{
    mColorText = color;
    update();
    return *this;
}
DialS1M2& DialS1M2::showArrow(bool status)
{
    mValueShowArrow = status;
    update();
    return *this;
}
DialS1M2& DialS1M2::showCircle(bool status)
{
    mValueShowCircle = status;
    update();
    return *this;
}
DialS1M2& DialS1M2::showText(bool status)
{
    mValueShowText = status;
    update();
    return *this;
}
DialS1M2& DialS1M2::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
DialS1M2& DialS1M2::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}


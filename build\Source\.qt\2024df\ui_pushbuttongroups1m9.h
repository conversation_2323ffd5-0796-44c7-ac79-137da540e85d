/********************************************************************************
** Form generated from reading UI file 'pushbuttongroups1m9.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PUSHBUTTONGROUPS1M9_H
#define UI_PUSHBUTTONGROUPS1M9_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_PushButtonGroupS1M9
{
public:
    QGridLayout *gridLayout_3;
    QFrame *frame;
    QGridLayout *gridLayout;
    QGridLayout *gridLayout_2;
    QPushButton *PushButtonMic1;
    QSpacerItem *verticalSpacer_2;
    QPushButton *PushButtonMic35;
    QSpacerItem *verticalSpacer_3;
    QPushButton *PushButtonMicHP;

    void setupUi(QWidget *PushButtonGroupS1M9)
    {
        if (PushButtonGroupS1M9->objectName().isEmpty())
            PushButtonGroupS1M9->setObjectName("PushButtonGroupS1M9");
        PushButtonGroupS1M9->resize(60, 70);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(PushButtonGroupS1M9->sizePolicy().hasHeightForWidth());
        PushButtonGroupS1M9->setSizePolicy(sizePolicy);
        PushButtonGroupS1M9->setMinimumSize(QSize(6, 7));
        gridLayout_3 = new QGridLayout(PushButtonGroupS1M9);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName("gridLayout_3");
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        frame = new QFrame(PushButtonGroupS1M9);
        frame->setObjectName("frame");
        sizePolicy.setHeightForWidth(frame->sizePolicy().hasHeightForWidth());
        frame->setSizePolicy(sizePolicy);
        frame->setFrameShape(QFrame::Shape::NoFrame);
        frame->setFrameShadow(QFrame::Shadow::Plain);
        frame->setLineWidth(0);
        gridLayout = new QGridLayout(frame);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        gridLayout_2 = new QGridLayout();
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName("gridLayout_2");
        PushButtonMic1 = new QPushButton(frame);
        PushButtonMic1->setObjectName("PushButtonMic1");
        sizePolicy.setHeightForWidth(PushButtonMic1->sizePolicy().hasHeightForWidth());
        PushButtonMic1->setSizePolicy(sizePolicy);
        PushButtonMic1->setMinimumSize(QSize(1, 1));

        gridLayout_2->addWidget(PushButtonMic1, 0, 0, 1, 1);

        verticalSpacer_2 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_2->addItem(verticalSpacer_2, 1, 0, 1, 1);

        PushButtonMic35 = new QPushButton(frame);
        PushButtonMic35->setObjectName("PushButtonMic35");
        sizePolicy.setHeightForWidth(PushButtonMic35->sizePolicy().hasHeightForWidth());
        PushButtonMic35->setSizePolicy(sizePolicy);
        PushButtonMic35->setMinimumSize(QSize(1, 1));

        gridLayout_2->addWidget(PushButtonMic35, 2, 0, 1, 1);

        verticalSpacer_3 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_2->addItem(verticalSpacer_3, 3, 0, 1, 1);

        PushButtonMicHP = new QPushButton(frame);
        PushButtonMicHP->setObjectName("PushButtonMicHP");
        sizePolicy.setHeightForWidth(PushButtonMicHP->sizePolicy().hasHeightForWidth());
        PushButtonMicHP->setSizePolicy(sizePolicy);
        PushButtonMicHP->setMinimumSize(QSize(1, 1));

        gridLayout_2->addWidget(PushButtonMicHP, 4, 0, 1, 1);

        gridLayout_2->setRowStretch(0, 7);
        gridLayout_2->setRowStretch(1, 7);
        gridLayout_2->setRowStretch(2, 7);
        gridLayout_2->setRowStretch(3, 7);
        gridLayout_2->setRowStretch(4, 7);

        gridLayout->addLayout(gridLayout_2, 0, 0, 1, 1);


        gridLayout_3->addWidget(frame, 0, 0, 1, 1);


        retranslateUi(PushButtonGroupS1M9);

        QMetaObject::connectSlotsByName(PushButtonGroupS1M9);
    } // setupUi

    void retranslateUi(QWidget *PushButtonGroupS1M9)
    {
        PushButtonGroupS1M9->setWindowTitle(QCoreApplication::translate("PushButtonGroupS1M9", "Form", nullptr));
        PushButtonMic1->setText(QString());
        PushButtonMic35->setText(QString());
        PushButtonMicHP->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class PushButtonGroupS1M9: public Ui_PushButtonGroupS1M9 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PUSHBUTTONGROUPS1M9_H

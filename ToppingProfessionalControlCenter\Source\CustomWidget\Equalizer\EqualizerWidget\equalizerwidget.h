#ifndef EQUALIZERWIDGET_H
#define EQUALIZERWIDGET_H

#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QCheckBox>
#include <QVector>
#include <QScrollArea>
#include <QFrame>
#include "comboboxs1m3.h"
#include "dials1m1.h"

// 单个均衡器段的数据结构
struct EqualizerBand {
    QString type;           // 滤波器类型
    float gain;            // 增益值
    float frequency;       // 频率值
    float qValue;          // Q值
    bool enabled;          // 是否启用
};

// 单个均衡器段控件
class EqualizerBandWidget : public QFrame
{
    Q_OBJECT

public:
    explicit EqualizerBandWidget(int bandIndex, QWidget* parent = nullptr);
    ~EqualizerBandWidget();

    // 设置和获取参数
    void setBandData(const EqualizerBand& data);
    EqualizerBand getBandData() const;
    
    // 设置段号
    void setBandIndex(int index);
    int getBandIndex() const { return mBandIndex; }
    
    // 设置样式
    void setDarkTheme();

protected:
    void resizeEvent(QResizeEvent* event) override;

private slots:
    void onTypeChanged(const QString& text);
    void onGainChanged(float value);
    void onFrequencyChanged(float value);
    void onQValueChanged(float value);
    void onEnabledChanged(bool enabled);

private:
    void setupUI();
    void setupConnections();
    void updateTypeOptions();

    int mBandIndex;
    
    // UI 组件
    QVBoxLayout* mMainLayout;
    QLabel* mBandLabel;
    
    // 类型选择
    ComboBoxS1M3* mTypeComboBox;
    
    // 旋钮控件
    DialS1M1* mGainDial;
    DialS1M1* mFrequencyDial;
    DialS1M1* mQValueDial;
    
    // 启用开关
    QCheckBox* mEnabledCheckBox;
    
    // 当前数据
    EqualizerBand mBandData;

signals:
    void bandDataChanged(int bandIndex, const EqualizerBand& data);
};

// 主均衡器控件
class EqualizerWidget : public QWidget
{
    Q_OBJECT

public:
    explicit EqualizerWidget(QWidget* parent = nullptr);
    ~EqualizerWidget();
    void setStretchFactor(double stretchFactor);

    // 段数管理
    void setBandCount(int count);
    int getBandCount() const { return mBands.size(); }
    
    // 添加和删除段
    void addBand();
    void removeBand(int index = -1); // -1表示删除最后一个
    void removeAllBands();
    
    // 数据管理
    void setEqualizerData(const QVector<EqualizerBand>& data);
    QVector<EqualizerBand> getEqualizerData() const;
    
    // 样式设置
    void setDarkTheme();

protected:
    void resizeEvent(QResizeEvent* event) override;

private slots:
    void onBandDataChanged(int bandIndex, const EqualizerBand& data);
    void onAddBandClicked();
    void onRemoveBandClicked();

private:
    void setupUI();
    void setupConnections();
    void updateBandIndices();
    void createDefaultBands();

    // UI 组件
    QVBoxLayout* mMainLayout;
    QHBoxLayout* mControlLayout;
    QScrollArea* mScrollArea;
    QWidget* mScrollWidget;
    QHBoxLayout* mBandsLayout;
    
    // 控制按钮
    QPushButton* mAddBandButton;
    QPushButton* mRemoveBandButton;
    QLabel* mTitleLabel;
    
    QVector<EqualizerBandWidget*> mBands;

    double mStretchFactor;

signals:
    void equalizerDataChanged(const QVector<EqualizerBand>& data);
    void bandCountChanged(int count);
};

#endif // EQUALIZERWIDGET_H

/********************************************************************************
** Form generated from reading UI file 'm62_privatewidget3.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_M62_PRIVATEWIDGET3_H
#define UI_M62_PRIVATEWIDGET3_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSlider>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include <dials1m5.h>
#include <volumemeters1m7.h>
#include "dials1m1.h"

QT_BEGIN_NAMESPACE

class Ui_M62_PrivateWidget3
{
public:
    QVBoxLayout *verticalLayout;
    QFrame *frameMain;
    QHBoxLayout *horizontalLayout_2;
    QWidget *widget1;
    QSlider *sliderInput1;
    QLabel *labelInput1;
    QLabel *labelInputLevel1;
    QLabel *labelInput2Level;
    QSlider *sliderInput2;
    QLabel *labelInput2;
    QLabel *labelAuxLevel;
    QLabel *labelAux;
    QSlider *sliderAux;
    QLabel *labelBluetooth;
    QSlider *sliderBluetooth;
    QLabel *labelBluetoothLevel;
    QLabel *labelOtg;
    QSlider *sliderOtg;
    QLabel *labelOtgLevel;
    QWidget *widget2;
    QLabel *labelNoiseClass;
    QPushButton *pushButtonNC3;
    QLineEdit *lineEditNoiseReduction;
    DialS1M5 *dialNoiseReduction;
    QWidget *ncWidget;
    QGridLayout *gridLayout;
    QSpacerItem *verticalSpacer_2;
    QPushButton *pushButtonNC1;
    QSpacerItem *horizontalSpacer;
    QSpacerItem *verticalSpacer;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *pushButtonNC2;
    QSpacerItem *verticalSpacer_3;
    QWidget *ncBypassWidget;
    QGridLayout *gridLayout_2;
    QSpacerItem *verticalSpacer_4;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *pushButtonBypassNoise;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *verticalSpacer_5;
    QWidget *widget3;
    QLabel *labelDry;
    QLabel *labelRoomSize;
    QLabel *labelReverbRoomSmall;
    QLabel *labelReverbRoomLarge;
    QLabel *labelReverbDecayMin;
    QLabel *labelDecayRate;
    QLabel *labelReverbDecayMax;
    QLabel *labelWet;
    QLineEdit *lineEditReverb;
    DialS1M5 *dialDryWet;
    DialS1M1 *dialRoom;
    DialS1M1 *dialDecay;
    QWidget *rbWidget;
    QGridLayout *gridLayout_3;
    QSpacerItem *verticalSpacer_6;
    QPushButton *pushButtonReverbStudio;
    QSpacerItem *verticalSpacer_7;
    QPushButton *pushButtonReverbLive;
    QSpacerItem *horizontalSpacer_5;
    QSpacerItem *verticalSpacer_8;
    QSpacerItem *horizontalSpacer_6;
    QPushButton *pushButtonReverbHall;
    QSpacerItem *verticalSpacer_9;
    QPushButton *pushButtonBypassReverb;
    QSpacerItem *verticalSpacer_10;
    QWidget *widget4;
    QLabel *labelFxIn;
    QLabel *labelFxOut;
    QLabel *labelFxMeterIn;
    QLabel *labelFxMeterOut;
    VolumeMeterS1M7 *widgetVolumeMeter;
    QWidget *mutefxWidget;
    QGridLayout *gridLayout_4;
    QSpacerItem *verticalSpacer_11;
    QSpacerItem *horizontalSpacer_7;
    QSpacerItem *horizontalSpacer_8;
    QSpacerItem *verticalSpacer_12;
    QPushButton *pushButtonMuteFx;

    void setupUi(QWidget *M62_PrivateWidget3)
    {
        if (M62_PrivateWidget3->objectName().isEmpty())
            M62_PrivateWidget3->setObjectName("M62_PrivateWidget3");
        M62_PrivateWidget3->resize(1089, 649);
        M62_PrivateWidget3->setMinimumSize(QSize(313, 220));
        verticalLayout = new QVBoxLayout(M62_PrivateWidget3);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        frameMain = new QFrame(M62_PrivateWidget3);
        frameMain->setObjectName("frameMain");
        frameMain->setFrameShape(QFrame::Shape::NoFrame);
        frameMain->setFrameShadow(QFrame::Shadow::Raised);
        horizontalLayout_2 = new QHBoxLayout(frameMain);
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName("horizontalLayout_2");
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        widget1 = new QWidget(frameMain);
        widget1->setObjectName("widget1");
        sliderInput1 = new QSlider(widget1);
        sliderInput1->setObjectName("sliderInput1");
        sliderInput1->setGeometry(QRect(20, 260, 22, 160));
        labelInput1 = new QLabel(widget1);
        labelInput1->setObjectName("labelInput1");
        labelInput1->setGeometry(QRect(10, 220, 53, 15));
        labelInput1->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelInputLevel1 = new QLabel(widget1);
        labelInputLevel1->setObjectName("labelInputLevel1");
        labelInputLevel1->setGeometry(QRect(10, 450, 53, 15));
        labelInputLevel1->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelInput2Level = new QLabel(widget1);
        labelInput2Level->setObjectName("labelInput2Level");
        labelInput2Level->setGeometry(QRect(70, 450, 53, 15));
        labelInput2Level->setAlignment(Qt::AlignmentFlag::AlignCenter);
        sliderInput2 = new QSlider(widget1);
        sliderInput2->setObjectName("sliderInput2");
        sliderInput2->setGeometry(QRect(80, 260, 22, 160));
        labelInput2 = new QLabel(widget1);
        labelInput2->setObjectName("labelInput2");
        labelInput2->setGeometry(QRect(70, 220, 53, 15));
        labelInput2->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelAuxLevel = new QLabel(widget1);
        labelAuxLevel->setObjectName("labelAuxLevel");
        labelAuxLevel->setGeometry(QRect(130, 450, 53, 15));
        labelAuxLevel->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelAux = new QLabel(widget1);
        labelAux->setObjectName("labelAux");
        labelAux->setGeometry(QRect(130, 220, 53, 15));
        labelAux->setAlignment(Qt::AlignmentFlag::AlignCenter);
        sliderAux = new QSlider(widget1);
        sliderAux->setObjectName("sliderAux");
        sliderAux->setGeometry(QRect(140, 260, 22, 160));
        labelBluetooth = new QLabel(widget1);
        labelBluetooth->setObjectName("labelBluetooth");
        labelBluetooth->setGeometry(QRect(190, 220, 53, 15));
        labelBluetooth->setAlignment(Qt::AlignmentFlag::AlignCenter);
        sliderBluetooth = new QSlider(widget1);
        sliderBluetooth->setObjectName("sliderBluetooth");
        sliderBluetooth->setGeometry(QRect(200, 260, 22, 160));
        labelBluetoothLevel = new QLabel(widget1);
        labelBluetoothLevel->setObjectName("labelBluetoothLevel");
        labelBluetoothLevel->setGeometry(QRect(190, 450, 53, 15));
        labelBluetoothLevel->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelOtg = new QLabel(widget1);
        labelOtg->setObjectName("labelOtg");
        labelOtg->setGeometry(QRect(240, 220, 53, 15));
        labelOtg->setAlignment(Qt::AlignmentFlag::AlignCenter);
        sliderOtg = new QSlider(widget1);
        sliderOtg->setObjectName("sliderOtg");
        sliderOtg->setGeometry(QRect(250, 260, 22, 160));
        labelOtgLevel = new QLabel(widget1);
        labelOtgLevel->setObjectName("labelOtgLevel");
        labelOtgLevel->setGeometry(QRect(240, 450, 53, 15));
        labelOtgLevel->setAlignment(Qt::AlignmentFlag::AlignCenter);

        horizontalLayout_2->addWidget(widget1);

        widget2 = new QWidget(frameMain);
        widget2->setObjectName("widget2");
        labelNoiseClass = new QLabel(widget2);
        labelNoiseClass->setObjectName("labelNoiseClass");
        labelNoiseClass->setGeometry(QRect(90, 120, 91, 41));
        labelNoiseClass->setAlignment(Qt::AlignmentFlag::AlignCenter);
        pushButtonNC3 = new QPushButton(widget2);
        pushButtonNC3->setObjectName("pushButtonNC3");
        pushButtonNC3->setGeometry(QRect(110, 410, 75, 23));
        pushButtonNC3->setCheckable(false);
        lineEditNoiseReduction = new QLineEdit(widget2);
        lineEditNoiseReduction->setObjectName("lineEditNoiseReduction");
        lineEditNoiseReduction->setGeometry(QRect(70, 10, 121, 21));
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(lineEditNoiseReduction->sizePolicy().hasHeightForWidth());
        lineEditNoiseReduction->setSizePolicy(sizePolicy);
        lineEditNoiseReduction->setStyleSheet(QString::fromUtf8(""));
        lineEditNoiseReduction->setAlignment(Qt::AlignmentFlag::AlignCenter);
        dialNoiseReduction = new DialS1M5(widget2);
        dialNoiseReduction->setObjectName("dialNoiseReduction");
        dialNoiseReduction->setGeometry(QRect(100, 60, 90, 41));
        sizePolicy.setHeightForWidth(dialNoiseReduction->sizePolicy().hasHeightForWidth());
        dialNoiseReduction->setSizePolicy(sizePolicy);
        dialNoiseReduction->setMinimumSize(QSize(0, 0));
        ncWidget = new QWidget(widget2);
        ncWidget->setObjectName("ncWidget");
        ncWidget->setGeometry(QRect(50, 170, 169, 192));
        ncWidget->setStyleSheet(QString::fromUtf8(""));
        gridLayout = new QGridLayout(ncWidget);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_2 = new QSpacerItem(0, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_2, 0, 1, 1, 1);

        pushButtonNC1 = new QPushButton(ncWidget);
        pushButtonNC1->setObjectName("pushButtonNC1");
        QSizePolicy sizePolicy1(QSizePolicy::Policy::Preferred, QSizePolicy::Policy::Preferred);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(pushButtonNC1->sizePolicy().hasHeightForWidth());
        pushButtonNC1->setSizePolicy(sizePolicy1);
        pushButtonNC1->setMinimumSize(QSize(0, 0));
        pushButtonNC1->setCheckable(false);

        gridLayout->addWidget(pushButtonNC1, 1, 1, 1, 1);

        horizontalSpacer = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer, 2, 0, 1, 1);

        verticalSpacer = new QSpacerItem(0, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer, 2, 1, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer_2, 2, 2, 1, 1);

        pushButtonNC2 = new QPushButton(ncWidget);
        pushButtonNC2->setObjectName("pushButtonNC2");
        sizePolicy1.setHeightForWidth(pushButtonNC2->sizePolicy().hasHeightForWidth());
        pushButtonNC2->setSizePolicy(sizePolicy1);
        pushButtonNC2->setMinimumSize(QSize(0, 0));
        pushButtonNC2->setCheckable(false);

        gridLayout->addWidget(pushButtonNC2, 3, 1, 1, 1);

        verticalSpacer_3 = new QSpacerItem(0, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_3, 4, 1, 1, 1);

        gridLayout->setRowStretch(0, 8);
        gridLayout->setRowStretch(1, 14);
        gridLayout->setRowStretch(2, 6);
        gridLayout->setRowStretch(3, 14);
        gridLayout->setRowStretch(4, 8);
        gridLayout->setColumnStretch(0, 11);
        gridLayout->setColumnStretch(1, 38);
        gridLayout->setColumnStretch(2, 11);
        ncBypassWidget = new QWidget(widget2);
        ncBypassWidget->setObjectName("ncBypassWidget");
        ncBypassWidget->setGeometry(QRect(100, 520, 101, 61));
        gridLayout_2 = new QGridLayout(ncBypassWidget);
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName("gridLayout_2");
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_4 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_2->addItem(verticalSpacer_4, 0, 1, 1, 1);

        horizontalSpacer_3 = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_2->addItem(horizontalSpacer_3, 1, 0, 1, 1);

        pushButtonBypassNoise = new QPushButton(ncBypassWidget);
        pushButtonBypassNoise->setObjectName("pushButtonBypassNoise");
        sizePolicy1.setHeightForWidth(pushButtonBypassNoise->sizePolicy().hasHeightForWidth());
        pushButtonBypassNoise->setSizePolicy(sizePolicy1);
        pushButtonBypassNoise->setCheckable(true);

        gridLayout_2->addWidget(pushButtonBypassNoise, 1, 1, 1, 1);

        horizontalSpacer_4 = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_2->addItem(horizontalSpacer_4, 1, 2, 1, 1);

        verticalSpacer_5 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_2->addItem(verticalSpacer_5, 2, 1, 1, 1);

        gridLayout_2->setRowStretch(0, 11);
        gridLayout_2->setRowStretch(1, 14);
        gridLayout_2->setRowStretch(2, 10);
        gridLayout_2->setColumnStretch(0, 11);
        gridLayout_2->setColumnStretch(1, 38);
        gridLayout_2->setColumnStretch(2, 11);

        horizontalLayout_2->addWidget(widget2);

        widget3 = new QWidget(frameMain);
        widget3->setObjectName("widget3");
        labelDry = new QLabel(widget3);
        labelDry->setObjectName("labelDry");
        labelDry->setGeometry(QRect(20, 70, 54, 16));
        labelDry->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelRoomSize = new QLabel(widget3);
        labelRoomSize->setObjectName("labelRoomSize");
        labelRoomSize->setGeometry(QRect(100, 130, 62, 16));
        labelRoomSize->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelReverbRoomSmall = new QLabel(widget3);
        labelReverbRoomSmall->setObjectName("labelReverbRoomSmall");
        labelReverbRoomSmall->setGeometry(QRect(40, 200, 41, 51));
        labelReverbRoomSmall->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelReverbRoomLarge = new QLabel(widget3);
        labelReverbRoomLarge->setObjectName("labelReverbRoomLarge");
        labelReverbRoomLarge->setGeometry(QRect(170, 200, 81, 31));
        labelReverbRoomLarge->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelReverbDecayMin = new QLabel(widget3);
        labelReverbDecayMin->setObjectName("labelReverbDecayMin");
        labelReverbDecayMin->setGeometry(QRect(60, 410, 22, 15));
        labelReverbDecayMin->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelDecayRate = new QLabel(widget3);
        labelDecayRate->setObjectName("labelDecayRate");
        labelDecayRate->setGeometry(QRect(90, 300, 65, 15));
        labelDecayRate->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelReverbDecayMax = new QLabel(widget3);
        labelReverbDecayMax->setObjectName("labelReverbDecayMax");
        labelReverbDecayMax->setGeometry(QRect(180, 410, 101, 20));
        labelReverbDecayMax->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelWet = new QLabel(widget3);
        labelWet->setObjectName("labelWet");
        labelWet->setGeometry(QRect(210, 70, 54, 16));
        labelWet->setAlignment(Qt::AlignmentFlag::AlignCenter);
        lineEditReverb = new QLineEdit(widget3);
        lineEditReverb->setObjectName("lineEditReverb");
        lineEditReverb->setGeometry(QRect(90, 10, 91, 21));
        sizePolicy.setHeightForWidth(lineEditReverb->sizePolicy().hasHeightForWidth());
        lineEditReverb->setSizePolicy(sizePolicy);
        lineEditReverb->setStyleSheet(QString::fromUtf8(""));
        lineEditReverb->setAlignment(Qt::AlignmentFlag::AlignCenter);
        dialDryWet = new DialS1M5(widget3);
        dialDryWet->setObjectName("dialDryWet");
        dialDryWet->setGeometry(QRect(100, 60, 90, 41));
        sizePolicy.setHeightForWidth(dialDryWet->sizePolicy().hasHeightForWidth());
        dialDryWet->setSizePolicy(sizePolicy);
        dialDryWet->setMinimumSize(QSize(0, 0));
        dialRoom = new DialS1M1(widget3);
        dialRoom->setObjectName("dialRoom");
        dialRoom->setGeometry(QRect(90, 190, 61, 71));
        dialDecay = new DialS1M1(widget3);
        dialDecay->setObjectName("dialDecay");
        dialDecay->setGeometry(QRect(110, 330, 61, 71));
        rbWidget = new QWidget(widget3);
        rbWidget->setObjectName("rbWidget");
        rbWidget->setGeometry(QRect(50, 460, 171, 161));
        gridLayout_3 = new QGridLayout(rbWidget);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName("gridLayout_3");
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_6 = new QSpacerItem(0, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer_6, 0, 1, 1, 1);

        pushButtonReverbStudio = new QPushButton(rbWidget);
        pushButtonReverbStudio->setObjectName("pushButtonReverbStudio");
        sizePolicy1.setHeightForWidth(pushButtonReverbStudio->sizePolicy().hasHeightForWidth());
        pushButtonReverbStudio->setSizePolicy(sizePolicy1);
        pushButtonReverbStudio->setCheckable(false);

        gridLayout_3->addWidget(pushButtonReverbStudio, 1, 1, 1, 1);

        verticalSpacer_7 = new QSpacerItem(0, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer_7, 2, 1, 1, 1);

        pushButtonReverbLive = new QPushButton(rbWidget);
        pushButtonReverbLive->setObjectName("pushButtonReverbLive");
        sizePolicy1.setHeightForWidth(pushButtonReverbLive->sizePolicy().hasHeightForWidth());
        pushButtonReverbLive->setSizePolicy(sizePolicy1);
        pushButtonReverbLive->setCheckable(false);

        gridLayout_3->addWidget(pushButtonReverbLive, 3, 1, 1, 1);

        horizontalSpacer_5 = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_5, 4, 0, 1, 1);

        verticalSpacer_8 = new QSpacerItem(0, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer_8, 4, 1, 1, 1);

        horizontalSpacer_6 = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_6, 4, 2, 1, 1);

        pushButtonReverbHall = new QPushButton(rbWidget);
        pushButtonReverbHall->setObjectName("pushButtonReverbHall");
        sizePolicy1.setHeightForWidth(pushButtonReverbHall->sizePolicy().hasHeightForWidth());
        pushButtonReverbHall->setSizePolicy(sizePolicy1);
        pushButtonReverbHall->setCheckable(false);

        gridLayout_3->addWidget(pushButtonReverbHall, 5, 1, 1, 1);

        verticalSpacer_9 = new QSpacerItem(0, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer_9, 6, 1, 1, 1);

        pushButtonBypassReverb = new QPushButton(rbWidget);
        pushButtonBypassReverb->setObjectName("pushButtonBypassReverb");
        sizePolicy1.setHeightForWidth(pushButtonBypassReverb->sizePolicy().hasHeightForWidth());
        pushButtonBypassReverb->setSizePolicy(sizePolicy1);
        pushButtonBypassReverb->setCheckable(true);

        gridLayout_3->addWidget(pushButtonBypassReverb, 7, 1, 1, 1);

        verticalSpacer_10 = new QSpacerItem(0, 0, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_3->addItem(verticalSpacer_10, 8, 1, 1, 1);

        gridLayout_3->setRowStretch(0, 8);
        gridLayout_3->setRowStretch(1, 14);
        gridLayout_3->setRowStretch(2, 6);
        gridLayout_3->setRowStretch(3, 14);
        gridLayout_3->setRowStretch(4, 6);
        gridLayout_3->setRowStretch(5, 14);
        gridLayout_3->setRowStretch(6, 6);
        gridLayout_3->setRowStretch(7, 14);
        gridLayout_3->setRowStretch(8, 8);
        gridLayout_3->setColumnStretch(0, 11);
        gridLayout_3->setColumnStretch(1, 38);
        gridLayout_3->setColumnStretch(2, 11);

        horizontalLayout_2->addWidget(widget3);

        widget4 = new QWidget(frameMain);
        widget4->setObjectName("widget4");
        labelFxIn = new QLabel(widget4);
        labelFxIn->setObjectName("labelFxIn");
        labelFxIn->setGeometry(QRect(10, 55, 53, 20));
        labelFxIn->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelFxOut = new QLabel(widget4);
        labelFxOut->setObjectName("labelFxOut");
        labelFxOut->setGeometry(QRect(90, 60, 53, 20));
        labelFxOut->setAlignment(Qt::AlignmentFlag::AlignCenter);
        labelFxMeterIn = new QLabel(widget4);
        labelFxMeterIn->setObjectName("labelFxMeterIn");
        labelFxMeterIn->setGeometry(QRect(10, 90, 53, 20));
        labelFxMeterIn->setAlignment(Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop);
        labelFxMeterOut = new QLabel(widget4);
        labelFxMeterOut->setObjectName("labelFxMeterOut");
        labelFxMeterOut->setGeometry(QRect(90, 95, 53, 20));
        labelFxMeterOut->setAlignment(Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop);
        widgetVolumeMeter = new VolumeMeterS1M7(widget4);
        widgetVolumeMeter->setObjectName("widgetVolumeMeter");
        widgetVolumeMeter->setGeometry(QRect(20, 140, 141, 361));
        sizePolicy.setHeightForWidth(widgetVolumeMeter->sizePolicy().hasHeightForWidth());
        widgetVolumeMeter->setSizePolicy(sizePolicy);
        mutefxWidget = new QWidget(widget4);
        mutefxWidget->setObjectName("mutefxWidget");
        mutefxWidget->setGeometry(QRect(30, 560, 101, 61));
        gridLayout_4 = new QGridLayout(mutefxWidget);
        gridLayout_4->setSpacing(0);
        gridLayout_4->setObjectName("gridLayout_4");
        gridLayout_4->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_11 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_4->addItem(verticalSpacer_11, 0, 1, 1, 1);

        horizontalSpacer_7 = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_4->addItem(horizontalSpacer_7, 1, 0, 1, 1);

        horizontalSpacer_8 = new QSpacerItem(1, 1, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_4->addItem(horizontalSpacer_8, 1, 2, 1, 1);

        verticalSpacer_12 = new QSpacerItem(1, 1, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_4->addItem(verticalSpacer_12, 2, 1, 1, 1);

        pushButtonMuteFx = new QPushButton(mutefxWidget);
        pushButtonMuteFx->setObjectName("pushButtonMuteFx");
        sizePolicy1.setHeightForWidth(pushButtonMuteFx->sizePolicy().hasHeightForWidth());
        pushButtonMuteFx->setSizePolicy(sizePolicy1);
        pushButtonMuteFx->setCheckable(true);

        gridLayout_4->addWidget(pushButtonMuteFx, 1, 1, 1, 1);

        gridLayout_4->setRowStretch(0, 11);
        gridLayout_4->setRowStretch(1, 14);
        gridLayout_4->setRowStretch(2, 10);
        gridLayout_4->setColumnStretch(0, 11);
        gridLayout_4->setColumnStretch(1, 38);
        gridLayout_4->setColumnStretch(2, 11);

        horizontalLayout_2->addWidget(widget4);

        horizontalLayout_2->setStretch(0, 101);
        horizontalLayout_2->setStretch(1, 80);
        horizontalLayout_2->setStretch(2, 80);
        horizontalLayout_2->setStretch(3, 52);

        verticalLayout->addWidget(frameMain);


        retranslateUi(M62_PrivateWidget3);

        QMetaObject::connectSlotsByName(M62_PrivateWidget3);
    } // setupUi

    void retranslateUi(QWidget *M62_PrivateWidget3)
    {
        M62_PrivateWidget3->setWindowTitle(QCoreApplication::translate("M62_PrivateWidget3", "Form", nullptr));
        labelInput1->setText(QCoreApplication::translate("M62_PrivateWidget3", "IN1", nullptr));
        labelInputLevel1->setText(QCoreApplication::translate("M62_PrivateWidget3", "+12", nullptr));
        labelInput2Level->setText(QCoreApplication::translate("M62_PrivateWidget3", "-\342\210\236", nullptr));
        labelInput2->setText(QCoreApplication::translate("M62_PrivateWidget3", "IN2", nullptr));
        labelAuxLevel->setText(QCoreApplication::translate("M62_PrivateWidget3", "-99", nullptr));
        labelAux->setText(QCoreApplication::translate("M62_PrivateWidget3", "AUX", nullptr));
        labelBluetooth->setText(QCoreApplication::translate("M62_PrivateWidget3", "BT", nullptr));
        labelBluetoothLevel->setText(QCoreApplication::translate("M62_PrivateWidget3", "-99", nullptr));
        labelOtg->setText(QCoreApplication::translate("M62_PrivateWidget3", "OTG", nullptr));
        labelOtgLevel->setText(QCoreApplication::translate("M62_PrivateWidget3", "-99", nullptr));
        labelNoiseClass->setText(QCoreApplication::translate("M62_PrivateWidget3", "Level", nullptr));
        pushButtonNC3->setText(QCoreApplication::translate("M62_PrivateWidget3", "NC3", nullptr));
        lineEditNoiseReduction->setText(QCoreApplication::translate("M62_PrivateWidget3", "Noise Reduction", nullptr));
        pushButtonNC1->setText(QString());
        pushButtonNC2->setText(QString());
        pushButtonBypassNoise->setText(QString());
        labelDry->setText(QCoreApplication::translate("M62_PrivateWidget3", "Dry ", nullptr));
        labelRoomSize->setText(QCoreApplication::translate("M62_PrivateWidget3", "Room Size", nullptr));
        labelReverbRoomSmall->setText(QCoreApplication::translate("M62_PrivateWidget3", "Small", nullptr));
        labelReverbRoomLarge->setText(QCoreApplication::translate("M62_PrivateWidget3", "Large", nullptr));
        labelReverbDecayMin->setText(QCoreApplication::translate("M62_PrivateWidget3", "Max", nullptr));
        labelDecayRate->setText(QCoreApplication::translate("M62_PrivateWidget3", "Decay Rate", nullptr));
        labelReverbDecayMax->setText(QCoreApplication::translate("M62_PrivateWidget3", "Min", nullptr));
        labelWet->setText(QCoreApplication::translate("M62_PrivateWidget3", "Wet", nullptr));
        lineEditReverb->setText(QCoreApplication::translate("M62_PrivateWidget3", "Reverb", nullptr));
        pushButtonReverbStudio->setText(QString());
        pushButtonReverbLive->setText(QString());
        pushButtonReverbHall->setText(QString());
        pushButtonBypassReverb->setText(QString());
        labelFxIn->setText(QCoreApplication::translate("M62_PrivateWidget3", "FX", nullptr));
        labelFxOut->setText(QCoreApplication::translate("M62_PrivateWidget3", "FX", nullptr));
        labelFxMeterIn->setText(QCoreApplication::translate("M62_PrivateWidget3", "IN", nullptr));
        labelFxMeterOut->setText(QCoreApplication::translate("M62_PrivateWidget3", "OUT", nullptr));
        pushButtonMuteFx->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class M62_PrivateWidget3: public Ui_M62_PrivateWidget3 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_M62_PRIVATEWIDGET3_H

#ifndef TRIALMANAGER_H
#define TRIALMANAGER_H

#include <QObject>
#include <QDateTime>
#include "singleton.h"

class QSettings;
class TrialManager : public QObject
{
    Q_OBJECT
    BE_SINGLETON(TrialManager)
public:
    enum TrialMode {
        Registry,
        BuildTime
    };
    Q_ENUM(TrialMode)
    void check(TrialMode mode, int trialDays);

private:
    explicit TrialManager(QObject *parent = nullptr);
    explicit TrialManager(TrialMode mode, int trialDays, QObject *parent = nullptr);
    ~TrialManager();
    QDateTime getBuildTime() const;
    void checkTrialExpired();
    QDateTime getNetworkTime() const;
    bool isFirstRun() const;
    QString encryptData(const QString &data) const;
    QString decryptData(const QString &data) const;
    void saveTrialData();
    void saveTrialFirstData();
    void loadTrialData();
    QString getRegistryPath() const;
    QString getEncryptedKeyName(const QString& originalKey) const;

    void setValue(const QString& key, const QVariant& value);
    QString getValue(const QString& key) const;

    TrialMode m_trialMode;
    bool m_enabled;
    int m_trialDays;
    QDateTime m_trialStartTime;
    QDateTime m_buildTime;
    const QString encryptionKey;
    QSettings *m_settings;
};

#define TRLMHandle TrialManager::instance()

#endif // TRIALMANAGER_H

#ifndef UPDATERBASE_H
#define UPDATERBASE_H

#include <QObject>
#include <QThread>
#include <QJsonObject>
#include <QElapsedTimer>
class QNetworkAccessManager;

class UpdaterBase : public QObject
{
    Q_OBJECT
public:    
    void update(const QString& url);
    virtual void doUpdate(const QString& filePath) = 0;
    void fetchUpdateDataAsync();
    bool fetchUpdateData();
    const QJsonObject& getUpdateData() const { return mUpdateData; }
    void setCurVersion(const QString& version) { m_curVersion = version; }
    const QString& getCurVersion() const {return m_curVersion; }
    const QString getUpdateUrl() const{
        QString version = QStringLiteral(APP_VERSION);
        if(version.section('.', -1) == QChar('0')){
            return "https://download.topping.pro/Jsons/UpdaterSeriesM.json";
        }else{
            return "https://download.topping.pro/Jsons/UpdaterSeriesM.json";
        }
    }

signals:
    void sigFetchUpdateDataFinish(bool success);
    void sigProgress(QString key, QString value);

protected:
    explicit UpdaterBase(QObject* parent=nullptr);
    virtual ~UpdaterBase();
    void download(const QString& url);

protected:
    static QJsonObject mUpdateData;
    static QThread mThread;
    QNetworkAccessManager* manager;
    QByteArray downloadedData;
    QElapsedTimer speedTimer;
    qint64 previousSecondBytesReceived=0;
    QString m_curVersion;
};

#endif // UPDATERBASE_H


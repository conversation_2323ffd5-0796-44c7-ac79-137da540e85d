#include "batterydrawstrategy.h"
#include <QTimer>

ContinuousDrawStrategy::ContinuousDrawStrategy(QObject* parent)
    : BatteryDrawStrategy(parent)
{
    mAnimationTimer = new QTimer(this);
    mAnimationInterval = 30;
    mAnimationTimer->setInterval(mAnimationInterval);
    connect(mAnimationTimer, &QTimer::timeout, this, &ContinuousDrawStrategy::onAnimationTimer);
}

ContinuousDrawStrategy::~ContinuousDrawStrategy()
{
    if (mAnimationTimer && mAnimationTimer->isActive()) {
        mAnimationTimer->stop();
    }
}

void ContinuousDrawStrategy::drawPower(QPainter* painter, const QRectF& powerRect,
                                      int value, int lowValue, bool charging, const BatteryColors& colors)
{
    mCurrentValue = value;

    qreal displayValue = charging ? mAnimationValue : value;
    qreal width = powerRect.width() * (displayValue / 100.0);
    QRectF filledRect(powerRect.x(), powerRect.y(), width, powerRect.height());

    painter->setBrush(charging ? colors.electricQuantityCharging : colors.electricQuantityNormal);
    painter->drawRect(filledRect);
}

void ContinuousDrawStrategy::startChargingAnimation()
{
    mAnimationValue = mCurrentValue;
    if (!mAnimationTimer->isActive()) {
        mAnimationTimer->start();
    }
}

void ContinuousDrawStrategy::stopChargingAnimation()
{
    if (mAnimationTimer->isActive()) {
        mAnimationTimer->stop();
    }
}

void ContinuousDrawStrategy::onAnimationTimer()
{
    mAnimationValue += 1;
    if (mAnimationValue > 100) {
        mAnimationValue = 0;
    }
    
    if (mUpdateCallback) {
        mUpdateCallback();
    }
}

SegmentedBlinkStrategy::SegmentedBlinkStrategy(QObject* parent, int segmentCount, 
                                             double segmentSpacingPercent, int blinkInterval)
    : BatteryDrawStrategy(parent)
    , mSegmentCount(segmentCount)
    , mSegmentSpacingPercent(segmentSpacingPercent)
{
    mAnimationInterval = blinkInterval;
    mAnimationTimer = new QTimer(this);
    mAnimationTimer->setInterval(mAnimationInterval);
    connect(mAnimationTimer, &QTimer::timeout, this, &SegmentedBlinkStrategy::onBlinkTimer);
}

SegmentedBlinkStrategy::~SegmentedBlinkStrategy()
{
    if (mAnimationTimer && mAnimationTimer->isActive()) {
        mAnimationTimer->stop();
    }
}

void SegmentedBlinkStrategy::drawPower(QPainter* painter, const QRectF& powerRect,
                                      int value, int lowValue, bool charging, const BatteryColors& colors)
{
    mCurrentValue = value;

    int currentSegment = getCurrentSegment(value, lowValue, charging);

    for (int i = 0; i < mSegmentCount; ++i) {
        QRectF segmentRect = getSegmentRect(i, powerRect);

        if (shouldSegmentShow(i, currentSegment, charging)) {
            painter->setPen(Qt::NoPen);
            painter->setBrush(charging ? colors.electricQuantityCharging : colors.electricQuantityNormal);
            painter->drawRect(segmentRect);
        }
    }
}

void SegmentedBlinkStrategy::startChargingAnimation()
{
    mAnimationValue = true;
    if (!mAnimationTimer->isActive()) {
        mAnimationTimer->start();
    }
}

void SegmentedBlinkStrategy::stopChargingAnimation()
{
    if (mAnimationTimer->isActive()) {
        mAnimationTimer->stop();
    }
}

void SegmentedBlinkStrategy::setSegmentCount(int count)
{
    if (count > 0) {
        mSegmentCount = count;
    }
}

void SegmentedBlinkStrategy::setSegmentSpacingPercent(double spacingPercent)
{
    if (spacingPercent >= 0) {
        mSegmentSpacingPercent = spacingPercent;
    }
}

void SegmentedBlinkStrategy::setBlinkInterval(int intervalMs)
{
    if (intervalMs > 0) {
        mAnimationInterval = intervalMs;
        if (mAnimationTimer) {
            mAnimationTimer->setInterval(mAnimationInterval);
        }
    }
}

void SegmentedBlinkStrategy::onBlinkTimer()
{
    mAnimationValue = !mAnimationValue;
    
    if (mUpdateCallback) {
        mUpdateCallback();
    }
}

int SegmentedBlinkStrategy::getCurrentSegment(int value, int lowValue,  bool charging) const
{
    if (value < 0 ) return -1;
    else if(value > 100) return mSegmentCount-1;
    
    int segment = (value * mSegmentCount) / 100;
    if(segment == 0){
        if(value <= lowValue){
            segment = -1;
        }else{
            segment = 0;
        }
        if(charging){
            segment += 1;
        }
    }
    return segment;
}

QRectF SegmentedBlinkStrategy::getSegmentRect(int segment, const QRectF& powerRect) const
{
    if (segment < 0 || segment >= mSegmentCount) {
        return QRectF();
    }
    
    qreal totalWidth = powerRect.width();
    qreal segmentSpacing = mSegmentSpacingPercent * totalWidth;
    qreal totalSpacing = (mSegmentCount - 1) * segmentSpacing;
    qreal segmentWidth = (totalWidth - totalSpacing) / mSegmentCount;
    qreal x = powerRect.x() + segment * (segmentWidth + segmentSpacing);
    return QRectF(x, powerRect.y(), segmentWidth, powerRect.height());
}

bool SegmentedBlinkStrategy::shouldSegmentShow(int segment, int currentSegment, bool charging) const
{    
    if (segment < currentSegment) {
        return true;
    } else if (segment == currentSegment) {
        if (charging) {
            return mCurrentValue==100?true:mAnimationValue;
        } else {
            return true;
        }
    } else {
        return false;
    }
}

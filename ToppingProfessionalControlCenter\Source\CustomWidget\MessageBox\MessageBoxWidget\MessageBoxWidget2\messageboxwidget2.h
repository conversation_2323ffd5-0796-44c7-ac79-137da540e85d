#ifndef MESSAGEBOXWIDGET2_H
#define MESSAGEBOXWIDGET2_H


#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class MessageBoxWidget2;
}


class MessageBoxWidget2 : public QWidget
{
    Q_OBJECT
public:
    explicit MessageBoxWidget2(QWidget* parent=nullptr);
    ~MessageBoxWidget2();
    MessageBoxWidget2& setFont(QFont font);
    MessageBoxWidget2& setLanguage(QString language);
    MessageBoxWidget2& showItemText(QString text);
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    Ui::MessageBoxWidget2* ui;
    QFont mFont;
private slots:
    void on_PushButton1_clicked();
    void on_PushButton2_clicked();
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // MESSAGEBOXWIDGET2_H


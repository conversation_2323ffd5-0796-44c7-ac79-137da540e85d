/********************************************************************************
** Form generated from reading UI file 'mainwindow_m62.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_M62_H
#define UI_MAINWINDOW_M62_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QWidget>
#include <fieldeffects1m1.h>
#include <fieldheads1m1.h>
#include <fieldheads1m2.h>
#include <fieldinputs1m1.h>
#include <fieldloopbacks1m1.h>
#include <fieldmixers1m1.h>
#include <fieldorigins1m1.h>
#include <fieldoutputs1m1.h>
#include <m62_privatewidget2.h>
#include <m62_privatewidget5.h>

QT_BEGIN_NAMESPACE

class Ui_MainWindow_M62
{
public:
    QWidget *centralWidget;
    QGridLayout *gridLayout_5;
    QStackedWidget *stackedWidget;
    QWidget *PageUpgd;
    QGridLayout *gridLayout_12;
    QSpacerItem *verticalSpacer_2;
    QLabel *PageUpgdLabel1;
    QSpacerItem *verticalSpacer;
    QGridLayout *gridLayout_14;
    QSpacerItem *horizontalSpacer_17;
    QProgressBar *PageUpgdProgressBar;
    QSpacerItem *horizontalSpacer_16;
    QSpacerItem *verticalSpacer_3;
    QLabel *PageUpgdLabel2;
    QSpacerItem *verticalSpacer_4;
    QWidget *PageFcty;
    QGridLayout *gridLayout_23;
    FieldHeadS1M2 *PageFctyFieldHead;
    QFrame *PageFctyFrameBody;
    QGridLayout *gridLayout_19;
    QSpacerItem *verticalSpacer_8;
    QGridLayout *gridLayout_20;
    QGridLayout *gridLayout_21;
    QSpacerItem *horizontalSpacer_5;
    QLabel *PageFctyLabel;
    QSpacerItem *horizontalSpacer_6;
    QSpacerItem *verticalSpacer_9;
    QGridLayout *gridLayout_22;
    QPushButton *PageFctyPushButton2;
    QSpacerItem *horizontalSpacer_8;
    QPushButton *PageFctyPushButton1;
    QSpacerItem *horizontalSpacer_7;
    QSpacerItem *horizontalSpacer_9;
    QSpacerItem *verticalSpacer_10;
    QWidget *PageMble;
    QGridLayout *gridLayout_13;
    FieldHeadS1M2 *PageMbleFieldHead;
    QFrame *PageMbleFrameBody;
    QGridLayout *gridLayout_18;
    QSpacerItem *verticalSpacer_6;
    QGridLayout *gridLayout_17;
    QGridLayout *gridLayout_15;
    QSpacerItem *horizontalSpacer;
    QLabel *PageMbleLabel;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer_5;
    QGridLayout *gridLayout_16;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *PageMblePushButton;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *verticalSpacer_7;
    QWidget *PageLive;
    QGridLayout *gridLayout;
    FieldHeadS1M1 *PageLiveFieldHead;
    QSplitter *PageLiveSplitterIE_MLO;
    QFrame *PageLiveFrameIE;
    QGridLayout *gridLayout_2;
    FieldInputS1M1 *PageLiveFieldInput;
    FieldEffectS1M1 *PageLiveFieldEffect;
    QFrame *PageLiveFrameMLO;
    QGridLayout *gridLayout_3;
    QFrame *PageLiveFrameLO;
    QGridLayout *gridLayout_4;
    QSplitter *PageLiveSplitterL_O;
    FieldLoopbackS1M1 *PageLiveFieldLoopback;
    FieldOutputS1M1 *PageLiveFieldOutput;
    M62_PrivateWidget2 *PageLiveWidgetConfigMenu;
    FieldMixerS1M1 *PageLiveFieldMixer;
    QWidget *PageTyro;
    QGridLayout *gridLayout_6;
    FieldHeadS1M2 *PageTyroFieldHead;
    QFrame *PageTyroFrameBody;
    QGridLayout *gridLayout_7;
    FieldOriginS1M1 *PageTyroFieldTop;
    FieldOriginS1M1 *PageTyroFieldBottom;
    QWidget *PageProf;
    QGridLayout *gridLayout_11;
    FieldHeadS1M1 *PageProfFieldHead;
    QSplitter *PageProfSplitterIL_MO;
    QFrame *PageProfFrameIL;
    QGridLayout *gridLayout_8;
    FieldInputS1M1 *PageProfFieldInput;
    FieldLoopbackS1M1 *PageProfFieldLoopback;
    QFrame *PageProfFrameMO;
    QGridLayout *gridLayout_9;
    QFrame *PageProfFrameO;
    QGridLayout *gridLayout_10;
    FieldOutputS1M1 *PageProfFieldOutput;
    M62_PrivateWidget5 *PageProfWidgetConfigMenu;
    FieldMixerS1M1 *PageProfFieldMixer;
    QStatusBar *statusBar;

    void setupUi(QMainWindow *MainWindow_M62)
    {
        if (MainWindow_M62->objectName().isEmpty())
            MainWindow_M62->setObjectName("MainWindow_M62");
        MainWindow_M62->resize(1100, 645);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(MainWindow_M62->sizePolicy().hasHeightForWidth());
        MainWindow_M62->setSizePolicy(sizePolicy);
        MainWindow_M62->setMinimumSize(QSize(1100, 645));
        MainWindow_M62->setStyleSheet(QString::fromUtf8(""));
        centralWidget = new QWidget(MainWindow_M62);
        centralWidget->setObjectName("centralWidget");
        sizePolicy.setHeightForWidth(centralWidget->sizePolicy().hasHeightForWidth());
        centralWidget->setSizePolicy(sizePolicy);
        gridLayout_5 = new QGridLayout(centralWidget);
        gridLayout_5->setSpacing(0);
        gridLayout_5->setObjectName("gridLayout_5");
        gridLayout_5->setContentsMargins(0, 0, 0, 0);
        stackedWidget = new QStackedWidget(centralWidget);
        stackedWidget->setObjectName("stackedWidget");
        sizePolicy.setHeightForWidth(stackedWidget->sizePolicy().hasHeightForWidth());
        stackedWidget->setSizePolicy(sizePolicy);
        stackedWidget->setLineWidth(0);
        PageUpgd = new QWidget();
        PageUpgd->setObjectName("PageUpgd");
        sizePolicy.setHeightForWidth(PageUpgd->sizePolicy().hasHeightForWidth());
        PageUpgd->setSizePolicy(sizePolicy);
        gridLayout_12 = new QGridLayout(PageUpgd);
        gridLayout_12->setSpacing(0);
        gridLayout_12->setObjectName("gridLayout_12");
        gridLayout_12->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_2 = new QSpacerItem(20, 78, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_12->addItem(verticalSpacer_2, 0, 0, 1, 1);

        PageUpgdLabel1 = new QLabel(PageUpgd);
        PageUpgdLabel1->setObjectName("PageUpgdLabel1");
        sizePolicy.setHeightForWidth(PageUpgdLabel1->sizePolicy().hasHeightForWidth());
        PageUpgdLabel1->setSizePolicy(sizePolicy);
        PageUpgdLabel1->setLineWidth(0);
        PageUpgdLabel1->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_12->addWidget(PageUpgdLabel1, 1, 0, 1, 1);

        verticalSpacer = new QSpacerItem(20, 78, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_12->addItem(verticalSpacer, 2, 0, 1, 1);

        gridLayout_14 = new QGridLayout();
        gridLayout_14->setSpacing(0);
        gridLayout_14->setObjectName("gridLayout_14");
        horizontalSpacer_17 = new QSpacerItem(17, 5, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_14->addItem(horizontalSpacer_17, 0, 0, 1, 1);

        PageUpgdProgressBar = new QProgressBar(PageUpgd);
        PageUpgdProgressBar->setObjectName("PageUpgdProgressBar");
        sizePolicy.setHeightForWidth(PageUpgdProgressBar->sizePolicy().hasHeightForWidth());
        PageUpgdProgressBar->setSizePolicy(sizePolicy);
        PageUpgdProgressBar->setMinimumSize(QSize(1, 1));
        PageUpgdProgressBar->setValue(0);
        PageUpgdProgressBar->setAlignment(Qt::AlignmentFlag::AlignCenter);
        PageUpgdProgressBar->setTextVisible(false);

        gridLayout_14->addWidget(PageUpgdProgressBar, 0, 1, 1, 1);

        horizontalSpacer_16 = new QSpacerItem(17, 5, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_14->addItem(horizontalSpacer_16, 0, 2, 1, 1);

        gridLayout_14->setColumnStretch(0, 10);
        gridLayout_14->setColumnStretch(1, 100);
        gridLayout_14->setColumnStretch(2, 10);

        gridLayout_12->addLayout(gridLayout_14, 3, 0, 1, 1);

        verticalSpacer_3 = new QSpacerItem(20, 78, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_12->addItem(verticalSpacer_3, 4, 0, 1, 1);

        PageUpgdLabel2 = new QLabel(PageUpgd);
        PageUpgdLabel2->setObjectName("PageUpgdLabel2");
        sizePolicy.setHeightForWidth(PageUpgdLabel2->sizePolicy().hasHeightForWidth());
        PageUpgdLabel2->setSizePolicy(sizePolicy);
        PageUpgdLabel2->setLineWidth(0);
        PageUpgdLabel2->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_12->addWidget(PageUpgdLabel2, 5, 0, 1, 1);

        verticalSpacer_4 = new QSpacerItem(20, 78, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_12->addItem(verticalSpacer_4, 6, 0, 1, 1);

        gridLayout_12->setRowStretch(0, 100);
        gridLayout_12->setRowStretch(1, 100);
        gridLayout_12->setRowStretch(2, 100);
        gridLayout_12->setRowStretch(3, 100);
        gridLayout_12->setRowStretch(4, 100);
        gridLayout_12->setRowStretch(5, 100);
        gridLayout_12->setRowStretch(6, 100);
        stackedWidget->addWidget(PageUpgd);
        PageFcty = new QWidget();
        PageFcty->setObjectName("PageFcty");
        sizePolicy.setHeightForWidth(PageFcty->sizePolicy().hasHeightForWidth());
        PageFcty->setSizePolicy(sizePolicy);
        gridLayout_23 = new QGridLayout(PageFcty);
        gridLayout_23->setSpacing(0);
        gridLayout_23->setObjectName("gridLayout_23");
        gridLayout_23->setContentsMargins(0, 0, 0, 0);
        PageFctyFieldHead = new FieldHeadS1M2(PageFcty);
        PageFctyFieldHead->setObjectName("PageFctyFieldHead");
        sizePolicy.setHeightForWidth(PageFctyFieldHead->sizePolicy().hasHeightForWidth());
        PageFctyFieldHead->setSizePolicy(sizePolicy);

        gridLayout_23->addWidget(PageFctyFieldHead, 0, 0, 1, 1);

        PageFctyFrameBody = new QFrame(PageFcty);
        PageFctyFrameBody->setObjectName("PageFctyFrameBody");
        sizePolicy.setHeightForWidth(PageFctyFrameBody->sizePolicy().hasHeightForWidth());
        PageFctyFrameBody->setSizePolicy(sizePolicy);
        PageFctyFrameBody->setFrameShape(QFrame::Shape::NoFrame);
        PageFctyFrameBody->setFrameShadow(QFrame::Shadow::Plain);
        PageFctyFrameBody->setLineWidth(0);
        gridLayout_19 = new QGridLayout(PageFctyFrameBody);
        gridLayout_19->setSpacing(0);
        gridLayout_19->setObjectName("gridLayout_19");
        gridLayout_19->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_8 = new QSpacerItem(20, 148, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_19->addItem(verticalSpacer_8, 0, 0, 1, 1);

        gridLayout_20 = new QGridLayout();
        gridLayout_20->setSpacing(0);
        gridLayout_20->setObjectName("gridLayout_20");
        gridLayout_21 = new QGridLayout();
        gridLayout_21->setSpacing(0);
        gridLayout_21->setObjectName("gridLayout_21");
        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_21->addItem(horizontalSpacer_5, 0, 0, 1, 1);

        PageFctyLabel = new QLabel(PageFctyFrameBody);
        PageFctyLabel->setObjectName("PageFctyLabel");
        sizePolicy.setHeightForWidth(PageFctyLabel->sizePolicy().hasHeightForWidth());
        PageFctyLabel->setSizePolicy(sizePolicy);
        PageFctyLabel->setLineWidth(0);
        PageFctyLabel->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_21->addWidget(PageFctyLabel, 0, 1, 1, 1);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_21->addItem(horizontalSpacer_6, 0, 2, 1, 1);

        gridLayout_21->setColumnStretch(0, 40);
        gridLayout_21->setColumnStretch(1, 100);
        gridLayout_21->setColumnStretch(2, 40);

        gridLayout_20->addLayout(gridLayout_21, 0, 0, 1, 1);

        verticalSpacer_9 = new QSpacerItem(20, 13, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_20->addItem(verticalSpacer_9, 1, 0, 1, 1);

        gridLayout_22 = new QGridLayout();
        gridLayout_22->setSpacing(0);
        gridLayout_22->setObjectName("gridLayout_22");
        PageFctyPushButton2 = new QPushButton(PageFctyFrameBody);
        PageFctyPushButton2->setObjectName("PageFctyPushButton2");
        sizePolicy.setHeightForWidth(PageFctyPushButton2->sizePolicy().hasHeightForWidth());
        PageFctyPushButton2->setSizePolicy(sizePolicy);

        gridLayout_22->addWidget(PageFctyPushButton2, 0, 3, 1, 1);

        horizontalSpacer_8 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_22->addItem(horizontalSpacer_8, 0, 4, 1, 1);

        PageFctyPushButton1 = new QPushButton(PageFctyFrameBody);
        PageFctyPushButton1->setObjectName("PageFctyPushButton1");
        sizePolicy.setHeightForWidth(PageFctyPushButton1->sizePolicy().hasHeightForWidth());
        PageFctyPushButton1->setSizePolicy(sizePolicy);
        PageFctyPushButton1->setMinimumSize(QSize(0, 0));

        gridLayout_22->addWidget(PageFctyPushButton1, 0, 1, 1, 1);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_22->addItem(horizontalSpacer_7, 0, 0, 1, 1);

        horizontalSpacer_9 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_22->addItem(horizontalSpacer_9, 0, 2, 1, 1);

        gridLayout_22->setColumnStretch(0, 100);
        gridLayout_22->setColumnStretch(1, 30);
        gridLayout_22->setColumnStretch(2, 10);
        gridLayout_22->setColumnStretch(3, 30);
        gridLayout_22->setColumnStretch(4, 100);

        gridLayout_20->addLayout(gridLayout_22, 2, 0, 1, 1);

        gridLayout_20->setRowStretch(0, 100);
        gridLayout_20->setRowStretch(1, 5);
        gridLayout_20->setRowStretch(2, 20);

        gridLayout_19->addLayout(gridLayout_20, 1, 0, 1, 1);

        verticalSpacer_10 = new QSpacerItem(20, 148, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_19->addItem(verticalSpacer_10, 2, 0, 1, 1);

        gridLayout_19->setRowStretch(0, 50);
        gridLayout_19->setRowStretch(1, 100);
        gridLayout_19->setRowStretch(2, 50);

        gridLayout_23->addWidget(PageFctyFrameBody, 1, 0, 1, 1);

        gridLayout_23->setRowStretch(0, 20);
        gridLayout_23->setRowStretch(1, 625);
        stackedWidget->addWidget(PageFcty);
        PageMble = new QWidget();
        PageMble->setObjectName("PageMble");
        sizePolicy.setHeightForWidth(PageMble->sizePolicy().hasHeightForWidth());
        PageMble->setSizePolicy(sizePolicy);
        gridLayout_13 = new QGridLayout(PageMble);
        gridLayout_13->setSpacing(0);
        gridLayout_13->setObjectName("gridLayout_13");
        gridLayout_13->setContentsMargins(0, 0, 0, 0);
        PageMbleFieldHead = new FieldHeadS1M2(PageMble);
        PageMbleFieldHead->setObjectName("PageMbleFieldHead");
        sizePolicy.setHeightForWidth(PageMbleFieldHead->sizePolicy().hasHeightForWidth());
        PageMbleFieldHead->setSizePolicy(sizePolicy);

        gridLayout_13->addWidget(PageMbleFieldHead, 0, 0, 1, 1);

        PageMbleFrameBody = new QFrame(PageMble);
        PageMbleFrameBody->setObjectName("PageMbleFrameBody");
        sizePolicy.setHeightForWidth(PageMbleFrameBody->sizePolicy().hasHeightForWidth());
        PageMbleFrameBody->setSizePolicy(sizePolicy);
        PageMbleFrameBody->setFrameShape(QFrame::Shape::NoFrame);
        PageMbleFrameBody->setFrameShadow(QFrame::Shadow::Plain);
        PageMbleFrameBody->setLineWidth(0);
        gridLayout_18 = new QGridLayout(PageMbleFrameBody);
        gridLayout_18->setSpacing(0);
        gridLayout_18->setObjectName("gridLayout_18");
        gridLayout_18->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_6 = new QSpacerItem(20, 148, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_18->addItem(verticalSpacer_6, 0, 0, 1, 1);

        gridLayout_17 = new QGridLayout();
        gridLayout_17->setSpacing(0);
        gridLayout_17->setObjectName("gridLayout_17");
        gridLayout_15 = new QGridLayout();
        gridLayout_15->setSpacing(0);
        gridLayout_15->setObjectName("gridLayout_15");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_15->addItem(horizontalSpacer, 0, 0, 1, 1);

        PageMbleLabel = new QLabel(PageMbleFrameBody);
        PageMbleLabel->setObjectName("PageMbleLabel");
        sizePolicy.setHeightForWidth(PageMbleLabel->sizePolicy().hasHeightForWidth());
        PageMbleLabel->setSizePolicy(sizePolicy);
        PageMbleLabel->setLineWidth(0);
        PageMbleLabel->setAlignment(Qt::AlignmentFlag::AlignCenter);

        gridLayout_15->addWidget(PageMbleLabel, 0, 1, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_15->addItem(horizontalSpacer_2, 0, 2, 1, 1);

        gridLayout_15->setColumnStretch(0, 40);
        gridLayout_15->setColumnStretch(1, 100);
        gridLayout_15->setColumnStretch(2, 40);

        gridLayout_17->addLayout(gridLayout_15, 0, 0, 1, 1);

        verticalSpacer_5 = new QSpacerItem(20, 13, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_17->addItem(verticalSpacer_5, 1, 0, 1, 1);

        gridLayout_16 = new QGridLayout();
        gridLayout_16->setSpacing(0);
        gridLayout_16->setObjectName("gridLayout_16");
        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_16->addItem(horizontalSpacer_3, 0, 0, 1, 1);

        PageMblePushButton = new QPushButton(PageMbleFrameBody);
        PageMblePushButton->setObjectName("PageMblePushButton");
        sizePolicy.setHeightForWidth(PageMblePushButton->sizePolicy().hasHeightForWidth());
        PageMblePushButton->setSizePolicy(sizePolicy);
        PageMblePushButton->setMinimumSize(QSize(0, 0));

        gridLayout_16->addWidget(PageMblePushButton, 0, 1, 1, 1);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout_16->addItem(horizontalSpacer_4, 0, 2, 1, 1);

        gridLayout_16->setColumnStretch(0, 100);
        gridLayout_16->setColumnStretch(1, 20);
        gridLayout_16->setColumnStretch(2, 100);

        gridLayout_17->addLayout(gridLayout_16, 2, 0, 1, 1);

        gridLayout_17->setRowStretch(0, 100);
        gridLayout_17->setRowStretch(1, 5);
        gridLayout_17->setRowStretch(2, 20);

        gridLayout_18->addLayout(gridLayout_17, 1, 0, 1, 1);

        verticalSpacer_7 = new QSpacerItem(20, 148, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout_18->addItem(verticalSpacer_7, 2, 0, 1, 1);

        gridLayout_18->setRowStretch(0, 50);
        gridLayout_18->setRowStretch(1, 100);
        gridLayout_18->setRowStretch(2, 50);

        gridLayout_13->addWidget(PageMbleFrameBody, 1, 0, 1, 1);

        gridLayout_13->setRowStretch(0, 20);
        gridLayout_13->setRowStretch(1, 625);
        stackedWidget->addWidget(PageMble);
        PageLive = new QWidget();
        PageLive->setObjectName("PageLive");
        sizePolicy.setHeightForWidth(PageLive->sizePolicy().hasHeightForWidth());
        PageLive->setSizePolicy(sizePolicy);
        gridLayout = new QGridLayout(PageLive);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        PageLiveFieldHead = new FieldHeadS1M1(PageLive);
        PageLiveFieldHead->setObjectName("PageLiveFieldHead");
        sizePolicy.setHeightForWidth(PageLiveFieldHead->sizePolicy().hasHeightForWidth());
        PageLiveFieldHead->setSizePolicy(sizePolicy);

        gridLayout->addWidget(PageLiveFieldHead, 0, 0, 1, 1);

        PageLiveSplitterIE_MLO = new QSplitter(PageLive);
        PageLiveSplitterIE_MLO->setObjectName("PageLiveSplitterIE_MLO");
        sizePolicy.setHeightForWidth(PageLiveSplitterIE_MLO->sizePolicy().hasHeightForWidth());
        PageLiveSplitterIE_MLO->setSizePolicy(sizePolicy);
        PageLiveSplitterIE_MLO->setLineWidth(0);
        PageLiveSplitterIE_MLO->setOrientation(Qt::Orientation::Horizontal);
        PageLiveSplitterIE_MLO->setHandleWidth(6);
        PageLiveFrameIE = new QFrame(PageLiveSplitterIE_MLO);
        PageLiveFrameIE->setObjectName("PageLiveFrameIE");
        sizePolicy.setHeightForWidth(PageLiveFrameIE->sizePolicy().hasHeightForWidth());
        PageLiveFrameIE->setSizePolicy(sizePolicy);
        PageLiveFrameIE->setFrameShape(QFrame::Shape::NoFrame);
        PageLiveFrameIE->setFrameShadow(QFrame::Shadow::Plain);
        PageLiveFrameIE->setLineWidth(0);
        gridLayout_2 = new QGridLayout(PageLiveFrameIE);
        gridLayout_2->setObjectName("gridLayout_2");
        gridLayout_2->setHorizontalSpacing(0);
        gridLayout_2->setVerticalSpacing(6);
        gridLayout_2->setContentsMargins(10, 0, 0, 0);
        PageLiveFieldInput = new FieldInputS1M1(PageLiveFrameIE);
        PageLiveFieldInput->setObjectName("PageLiveFieldInput");
        sizePolicy.setHeightForWidth(PageLiveFieldInput->sizePolicy().hasHeightForWidth());
        PageLiveFieldInput->setSizePolicy(sizePolicy);

        gridLayout_2->addWidget(PageLiveFieldInput, 0, 0, 1, 1);

        PageLiveFieldEffect = new FieldEffectS1M1(PageLiveFrameIE);
        PageLiveFieldEffect->setObjectName("PageLiveFieldEffect");
        sizePolicy.setHeightForWidth(PageLiveFieldEffect->sizePolicy().hasHeightForWidth());
        PageLiveFieldEffect->setSizePolicy(sizePolicy);

        gridLayout_2->addWidget(PageLiveFieldEffect, 1, 0, 1, 1);

        PageLiveSplitterIE_MLO->addWidget(PageLiveFrameIE);
        PageLiveFrameMLO = new QFrame(PageLiveSplitterIE_MLO);
        PageLiveFrameMLO->setObjectName("PageLiveFrameMLO");
        sizePolicy.setHeightForWidth(PageLiveFrameMLO->sizePolicy().hasHeightForWidth());
        PageLiveFrameMLO->setSizePolicy(sizePolicy);
        PageLiveFrameMLO->setFrameShape(QFrame::Shape::NoFrame);
        PageLiveFrameMLO->setFrameShadow(QFrame::Shadow::Plain);
        PageLiveFrameMLO->setLineWidth(0);
        gridLayout_3 = new QGridLayout(PageLiveFrameMLO);
        gridLayout_3->setObjectName("gridLayout_3");
        gridLayout_3->setHorizontalSpacing(0);
        gridLayout_3->setVerticalSpacing(6);
        gridLayout_3->setContentsMargins(0, 0, 10, 0);
        PageLiveFrameLO = new QFrame(PageLiveFrameMLO);
        PageLiveFrameLO->setObjectName("PageLiveFrameLO");
        sizePolicy.setHeightForWidth(PageLiveFrameLO->sizePolicy().hasHeightForWidth());
        PageLiveFrameLO->setSizePolicy(sizePolicy);
        PageLiveFrameLO->setFrameShape(QFrame::Shape::NoFrame);
        PageLiveFrameLO->setFrameShadow(QFrame::Shadow::Plain);
        PageLiveFrameLO->setLineWidth(0);
        gridLayout_4 = new QGridLayout(PageLiveFrameLO);
        gridLayout_4->setObjectName("gridLayout_4");
        gridLayout_4->setHorizontalSpacing(6);
        gridLayout_4->setVerticalSpacing(0);
        gridLayout_4->setContentsMargins(0, 0, 0, 0);
        PageLiveSplitterL_O = new QSplitter(PageLiveFrameLO);
        PageLiveSplitterL_O->setObjectName("PageLiveSplitterL_O");
        sizePolicy.setHeightForWidth(PageLiveSplitterL_O->sizePolicy().hasHeightForWidth());
        PageLiveSplitterL_O->setSizePolicy(sizePolicy);
        PageLiveSplitterL_O->setLineWidth(0);
        PageLiveSplitterL_O->setOrientation(Qt::Orientation::Horizontal);
        PageLiveSplitterL_O->setHandleWidth(6);
        PageLiveFieldLoopback = new FieldLoopbackS1M1(PageLiveSplitterL_O);
        PageLiveFieldLoopback->setObjectName("PageLiveFieldLoopback");
        sizePolicy.setHeightForWidth(PageLiveFieldLoopback->sizePolicy().hasHeightForWidth());
        PageLiveFieldLoopback->setSizePolicy(sizePolicy);
        PageLiveSplitterL_O->addWidget(PageLiveFieldLoopback);
        PageLiveFieldOutput = new FieldOutputS1M1(PageLiveSplitterL_O);
        PageLiveFieldOutput->setObjectName("PageLiveFieldOutput");
        sizePolicy.setHeightForWidth(PageLiveFieldOutput->sizePolicy().hasHeightForWidth());
        PageLiveFieldOutput->setSizePolicy(sizePolicy);
        PageLiveSplitterL_O->addWidget(PageLiveFieldOutput);

        gridLayout_4->addWidget(PageLiveSplitterL_O, 0, 0, 1, 1);

        PageLiveWidgetConfigMenu = new M62_PrivateWidget2(PageLiveFrameLO);
        PageLiveWidgetConfigMenu->setObjectName("PageLiveWidgetConfigMenu");
        sizePolicy.setHeightForWidth(PageLiveWidgetConfigMenu->sizePolicy().hasHeightForWidth());
        PageLiveWidgetConfigMenu->setSizePolicy(sizePolicy);

        gridLayout_4->addWidget(PageLiveWidgetConfigMenu, 0, 1, 1, 1);

        gridLayout_4->setColumnStretch(0, 85);
        gridLayout_4->setColumnStretch(1, 15);

        gridLayout_3->addWidget(PageLiveFrameLO, 1, 0, 1, 1);

        PageLiveFieldMixer = new FieldMixerS1M1(PageLiveFrameMLO);
        PageLiveFieldMixer->setObjectName("PageLiveFieldMixer");
        sizePolicy.setHeightForWidth(PageLiveFieldMixer->sizePolicy().hasHeightForWidth());
        PageLiveFieldMixer->setSizePolicy(sizePolicy);

        gridLayout_3->addWidget(PageLiveFieldMixer, 0, 0, 1, 1);

        PageLiveSplitterIE_MLO->addWidget(PageLiveFrameMLO);

        gridLayout->addWidget(PageLiveSplitterIE_MLO, 1, 0, 1, 1);

        gridLayout->setRowStretch(0, 20);
        gridLayout->setRowStretch(1, 625);
        stackedWidget->addWidget(PageLive);
        PageTyro = new QWidget();
        PageTyro->setObjectName("PageTyro");
        sizePolicy.setHeightForWidth(PageTyro->sizePolicy().hasHeightForWidth());
        PageTyro->setSizePolicy(sizePolicy);
        gridLayout_6 = new QGridLayout(PageTyro);
        gridLayout_6->setSpacing(0);
        gridLayout_6->setObjectName("gridLayout_6");
        gridLayout_6->setContentsMargins(0, 0, 0, 0);
        PageTyroFieldHead = new FieldHeadS1M2(PageTyro);
        PageTyroFieldHead->setObjectName("PageTyroFieldHead");
        sizePolicy.setHeightForWidth(PageTyroFieldHead->sizePolicy().hasHeightForWidth());
        PageTyroFieldHead->setSizePolicy(sizePolicy);

        gridLayout_6->addWidget(PageTyroFieldHead, 0, 0, 1, 1);

        PageTyroFrameBody = new QFrame(PageTyro);
        PageTyroFrameBody->setObjectName("PageTyroFrameBody");
        sizePolicy.setHeightForWidth(PageTyroFrameBody->sizePolicy().hasHeightForWidth());
        PageTyroFrameBody->setSizePolicy(sizePolicy);
        PageTyroFrameBody->setFrameShape(QFrame::Shape::NoFrame);
        PageTyroFrameBody->setFrameShadow(QFrame::Shadow::Plain);
        PageTyroFrameBody->setLineWidth(0);
        gridLayout_7 = new QGridLayout(PageTyroFrameBody);
        gridLayout_7->setObjectName("gridLayout_7");
        gridLayout_7->setHorizontalSpacing(0);
        gridLayout_7->setVerticalSpacing(10);
        gridLayout_7->setContentsMargins(10, 0, 10, 0);
        PageTyroFieldTop = new FieldOriginS1M1(PageTyroFrameBody);
        PageTyroFieldTop->setObjectName("PageTyroFieldTop");
        sizePolicy.setHeightForWidth(PageTyroFieldTop->sizePolicy().hasHeightForWidth());
        PageTyroFieldTop->setSizePolicy(sizePolicy);

        gridLayout_7->addWidget(PageTyroFieldTop, 0, 0, 1, 1);

        PageTyroFieldBottom = new FieldOriginS1M1(PageTyroFrameBody);
        PageTyroFieldBottom->setObjectName("PageTyroFieldBottom");
        sizePolicy.setHeightForWidth(PageTyroFieldBottom->sizePolicy().hasHeightForWidth());
        PageTyroFieldBottom->setSizePolicy(sizePolicy);

        gridLayout_7->addWidget(PageTyroFieldBottom, 1, 0, 1, 1);


        gridLayout_6->addWidget(PageTyroFrameBody, 1, 0, 1, 1);

        gridLayout_6->setRowStretch(0, 20);
        gridLayout_6->setRowStretch(1, 625);
        stackedWidget->addWidget(PageTyro);
        PageProf = new QWidget();
        PageProf->setObjectName("PageProf");
        sizePolicy.setHeightForWidth(PageProf->sizePolicy().hasHeightForWidth());
        PageProf->setSizePolicy(sizePolicy);
        gridLayout_11 = new QGridLayout(PageProf);
        gridLayout_11->setSpacing(0);
        gridLayout_11->setObjectName("gridLayout_11");
        gridLayout_11->setContentsMargins(0, 0, 0, 0);
        PageProfFieldHead = new FieldHeadS1M1(PageProf);
        PageProfFieldHead->setObjectName("PageProfFieldHead");
        sizePolicy.setHeightForWidth(PageProfFieldHead->sizePolicy().hasHeightForWidth());
        PageProfFieldHead->setSizePolicy(sizePolicy);

        gridLayout_11->addWidget(PageProfFieldHead, 0, 0, 1, 1);

        PageProfSplitterIL_MO = new QSplitter(PageProf);
        PageProfSplitterIL_MO->setObjectName("PageProfSplitterIL_MO");
        sizePolicy.setHeightForWidth(PageProfSplitterIL_MO->sizePolicy().hasHeightForWidth());
        PageProfSplitterIL_MO->setSizePolicy(sizePolicy);
        PageProfSplitterIL_MO->setLineWidth(0);
        PageProfSplitterIL_MO->setOrientation(Qt::Orientation::Horizontal);
        PageProfSplitterIL_MO->setHandleWidth(6);
        PageProfFrameIL = new QFrame(PageProfSplitterIL_MO);
        PageProfFrameIL->setObjectName("PageProfFrameIL");
        sizePolicy.setHeightForWidth(PageProfFrameIL->sizePolicy().hasHeightForWidth());
        PageProfFrameIL->setSizePolicy(sizePolicy);
        PageProfFrameIL->setFrameShape(QFrame::Shape::NoFrame);
        PageProfFrameIL->setFrameShadow(QFrame::Shadow::Plain);
        PageProfFrameIL->setLineWidth(0);
        gridLayout_8 = new QGridLayout(PageProfFrameIL);
        gridLayout_8->setObjectName("gridLayout_8");
        gridLayout_8->setHorizontalSpacing(0);
        gridLayout_8->setVerticalSpacing(6);
        gridLayout_8->setContentsMargins(10, 0, 0, 0);
        PageProfFieldInput = new FieldInputS1M1(PageProfFrameIL);
        PageProfFieldInput->setObjectName("PageProfFieldInput");
        sizePolicy.setHeightForWidth(PageProfFieldInput->sizePolicy().hasHeightForWidth());
        PageProfFieldInput->setSizePolicy(sizePolicy);

        gridLayout_8->addWidget(PageProfFieldInput, 0, 0, 1, 1);

        PageProfFieldLoopback = new FieldLoopbackS1M1(PageProfFrameIL);
        PageProfFieldLoopback->setObjectName("PageProfFieldLoopback");
        sizePolicy.setHeightForWidth(PageProfFieldLoopback->sizePolicy().hasHeightForWidth());
        PageProfFieldLoopback->setSizePolicy(sizePolicy);

        gridLayout_8->addWidget(PageProfFieldLoopback, 1, 0, 1, 1);

        PageProfSplitterIL_MO->addWidget(PageProfFrameIL);
        PageProfFrameMO = new QFrame(PageProfSplitterIL_MO);
        PageProfFrameMO->setObjectName("PageProfFrameMO");
        sizePolicy.setHeightForWidth(PageProfFrameMO->sizePolicy().hasHeightForWidth());
        PageProfFrameMO->setSizePolicy(sizePolicy);
        PageProfFrameMO->setFrameShape(QFrame::Shape::NoFrame);
        PageProfFrameMO->setFrameShadow(QFrame::Shadow::Plain);
        PageProfFrameMO->setLineWidth(0);
        gridLayout_9 = new QGridLayout(PageProfFrameMO);
        gridLayout_9->setObjectName("gridLayout_9");
        gridLayout_9->setHorizontalSpacing(0);
        gridLayout_9->setVerticalSpacing(6);
        gridLayout_9->setContentsMargins(0, 0, 10, 0);
        PageProfFrameO = new QFrame(PageProfFrameMO);
        PageProfFrameO->setObjectName("PageProfFrameO");
        sizePolicy.setHeightForWidth(PageProfFrameO->sizePolicy().hasHeightForWidth());
        PageProfFrameO->setSizePolicy(sizePolicy);
        PageProfFrameO->setFrameShape(QFrame::Shape::NoFrame);
        PageProfFrameO->setFrameShadow(QFrame::Shadow::Plain);
        PageProfFrameO->setLineWidth(0);
        gridLayout_10 = new QGridLayout(PageProfFrameO);
        gridLayout_10->setObjectName("gridLayout_10");
        gridLayout_10->setHorizontalSpacing(6);
        gridLayout_10->setVerticalSpacing(0);
        gridLayout_10->setContentsMargins(0, 0, 0, 0);
        PageProfFieldOutput = new FieldOutputS1M1(PageProfFrameO);
        PageProfFieldOutput->setObjectName("PageProfFieldOutput");
        sizePolicy.setHeightForWidth(PageProfFieldOutput->sizePolicy().hasHeightForWidth());
        PageProfFieldOutput->setSizePolicy(sizePolicy);

        gridLayout_10->addWidget(PageProfFieldOutput, 0, 0, 1, 1);

        PageProfWidgetConfigMenu = new M62_PrivateWidget5(PageProfFrameO);
        PageProfWidgetConfigMenu->setObjectName("PageProfWidgetConfigMenu");
        sizePolicy.setHeightForWidth(PageProfWidgetConfigMenu->sizePolicy().hasHeightForWidth());
        PageProfWidgetConfigMenu->setSizePolicy(sizePolicy);

        gridLayout_10->addWidget(PageProfWidgetConfigMenu, 0, 1, 1, 1);

        gridLayout_10->setColumnStretch(0, 85);
        gridLayout_10->setColumnStretch(1, 15);

        gridLayout_9->addWidget(PageProfFrameO, 1, 0, 1, 1);

        PageProfFieldMixer = new FieldMixerS1M1(PageProfFrameMO);
        PageProfFieldMixer->setObjectName("PageProfFieldMixer");
        sizePolicy.setHeightForWidth(PageProfFieldMixer->sizePolicy().hasHeightForWidth());
        PageProfFieldMixer->setSizePolicy(sizePolicy);

        gridLayout_9->addWidget(PageProfFieldMixer, 0, 0, 1, 1);

        PageProfSplitterIL_MO->addWidget(PageProfFrameMO);

        gridLayout_11->addWidget(PageProfSplitterIL_MO, 1, 0, 1, 1);

        gridLayout_11->setRowStretch(0, 20);
        gridLayout_11->setRowStretch(1, 625);
        stackedWidget->addWidget(PageProf);

        gridLayout_5->addWidget(stackedWidget, 0, 0, 1, 1);

        MainWindow_M62->setCentralWidget(centralWidget);
        statusBar = new QStatusBar(MainWindow_M62);
        statusBar->setObjectName("statusBar");
        MainWindow_M62->setStatusBar(statusBar);

        retranslateUi(MainWindow_M62);

        stackedWidget->setCurrentIndex(1);


        QMetaObject::connectSlotsByName(MainWindow_M62);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow_M62)
    {
        MainWindow_M62->setWindowTitle(QCoreApplication::translate("MainWindow_M62", "MainWindow", nullptr));
        PageUpgdLabel1->setText(QString());
        PageUpgdLabel2->setText(QString());
        PageFctyLabel->setText(QString());
        PageFctyPushButton2->setText(QString());
        PageFctyPushButton1->setText(QString());
        PageMbleLabel->setText(QString());
        PageMblePushButton->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class MainWindow_M62: public Ui_MainWindow_M62 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_M62_H

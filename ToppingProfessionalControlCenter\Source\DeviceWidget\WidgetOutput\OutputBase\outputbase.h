#ifndef OUTPUTBASE_H
#define OUTPUTBASE_H


#include <QWidget>


class OutputBase : public QWidget
{
    Q_OBJECT
public:
    explicit OutputBase(QWidget* parent=nullptr) : QWidget(parent) { }
    virtual ~OutputBase() = default;
    OutputBase& setChannelName(QString name);
    OutputBase& setWidgetEnable(bool state=true);
    OutputBase& setWidgetEnableWithUpdate(bool state=true);
    OutputBase& setWidgetMovable(bool state=true);
    OutputBase& setWidgetReady(bool state=true);
    OutputBase& setWidgetEmitAction(bool state=true);
    QString getChannelName() { return mChannelName; }
    bool isWidgetEnable() { return mEnable; }
    bool isWidgetMovable() { return mMovable; }
    bool isWidgetReady() { return mReady; }
    bool isWidgetEmitAction() { return mEmitAction; }
protected:
    virtual void updateAttribute() = 0;
private:
    QString mChannelName="";
    bool mEnable=false;
    bool mMovable=false;
    bool mReady=false;
    bool mEmitAction=false;
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // OUTPUTBASE_H


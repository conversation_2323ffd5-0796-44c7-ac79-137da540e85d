/************************************************************************

    Description:
        TLDFU API library
        Represents the dynamically loaded API library

    Author(s):
        <PERSON><PERSON><PERSON>ck
        <PERSON> Eberhardt
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#include "tlusbdfusdk.h"

// If our header file was not included, this file is empty.
#ifdef __TLDfuApi_h__


// The one and only instance.
//static 
TLDfuApi TLDfuApi::mInstance;



TLDfuApi::TLDfuApi()
{
    ClearPointers();
}


TLDfuApi::~TLDfuApi()
{
#if TLDFUAPI_UNLOAD_IN_DESTRUCTOR
    Unload();
#endif
}


void
TLDfuApi::ClearPointers()
{
    memset(&mFunc, 0, sizeof(mFunc));
}


TLSTATUS
TLDfuApi::LoadByName(
    const T_UNICHAR* fileName,
    bool checkApiCompatibility,
    unsigned int majorApiVersion,
    unsigned int minorApiVersion
    )
{
    TLSTATUS st;

    // make sure the current handle is released
    Unload();

    st = mDynlib.LoadDynlibByName(fileName);
    if ( st != TLSTATUS_SUCCESS ) {
        return st;
    }

    st = LoadApiFunctions(checkApiCompatibility, majorApiVersion, minorApiVersion);
    if ( st != TLSTATUS_SUCCESS ) {
        ClearPointers();
        mDynlib.UnloadDynlib();
        return st;
    }

    return TLSTATUS_SUCCESS;
}


void
TLDfuApi::Unload()
{
    mApiVersion = 0;
    ClearPointers();
    mDynlib.UnloadDynlib();
}


bool
TLDfuApi::IsApiVersionEqualOrGreater(
    unsigned int major,
    unsigned int minor
    ) const
{
    if ( MajorApiVersion() > major ) {
        return true;
    }
    if ( MajorApiVersion() == major ) {
        if ( MinorApiVersion() >= minor ) {
            return true;
        }
    }
    return false;
}



// helper macro
#define GET_FUNCTION_ADDRESS( FctName ) \
    mFunc.FctName = reinterpret_cast<decltype(mFunc.FctName)>(mDynlib.ResolveDynlibSymbol("TLDFU_" #FctName)); \
    if ( nullptr == mFunc.FctName ) { \
        return TLSTATUS_OBJECT_NOT_FOUND; \
    }


TLSTATUS
TLDfuApi::LoadApiFunctions(
    bool checkApiCompatibility,
    unsigned int majorApiVersion,
    unsigned int minorApiVersion
    )
{
    GET_FUNCTION_ADDRESS(GetApiVersion);
    GET_FUNCTION_ADDRESS(CheckApiVersion);

    // cache API version so that it is available even if the compatibility check below fails
    mApiVersion = mFunc.GetApiVersion();

    if ( checkApiCompatibility ) {
        if ( 0 == mFunc.CheckApiVersion(majorApiVersion, minorApiVersion) ) {
            return TLSTATUS_VERSION_MISMATCH;
        }
    }

    // load the remaining API functions
    GET_FUNCTION_ADDRESS(GetUniCharSize);
    GET_FUNCTION_ADDRESS(GetErrorText);
    GET_FUNCTION_ADDRESS(StartFileLogging);
    GET_FUNCTION_ADDRESS(EnumerateUsbDfuDevices);
    GET_FUNCTION_ADDRESS(CloseEnumeration);
    GET_FUNCTION_ADDRESS(CompareDeviceInstance);
    GET_FUNCTION_ADDRESS(OpenDevice);
    GET_FUNCTION_ADDRESS(CloseDevice);
    GET_FUNCTION_ADDRESS(CheckDeviceConnection);
    GET_FUNCTION_ADDRESS(GetDevicePropertyUint);
    GET_FUNCTION_ADDRESS(GetDevicePropertyString);
    GET_FUNCTION_ADDRESS(RebootDevice);
    GET_FUNCTION_ADDRESS(StartUpgrade);
    GET_FUNCTION_ADDRESS(GetUpgradeStatus);
    GET_FUNCTION_ADDRESS(FinishUpgrade);
    GET_FUNCTION_ADDRESS(LoadFirmwareImageFromFile);
    GET_FUNCTION_ADDRESS(LoadFirmwareFromBuffer);
    GET_FUNCTION_ADDRESS(UnloadFirmwareImage);
    GET_FUNCTION_ADDRESS(GetImagePropertyUint);
    GET_FUNCTION_ADDRESS(GetImagePropertyUint64);
    GET_FUNCTION_ADDRESS(GetImagePropertyString);

    GET_FUNCTION_ADDRESS(RegisterDeviceChangeCallback);
    GET_FUNCTION_ADDRESS(UnregisterDeviceChangeCallback);

    // get functions which are available in API version 1.14 and above
    if ( IsApiVersionEqualOrGreater(1,14) ) {
        GET_FUNCTION_ADDRESS(InterfaceVendorInRequest);
        GET_FUNCTION_ADDRESS(InterfaceVendorOutRequest);
    }

    // get functions which are available in API version 1.16 and above
    if ( IsApiVersionEqualOrGreater(1,16) ) {
        GET_FUNCTION_ADDRESS(StartReadout);
        GET_FUNCTION_ADDRESS(GetReadoutStatus);
        GET_FUNCTION_ADDRESS(FinishReadout);
        GET_FUNCTION_ADDRESS(StoreFirmwareInBuffer);
    }

    // get functions which are available in API version 1.17 and above
    if ( IsApiVersionEqualOrGreater(1,17) ) {
        GET_FUNCTION_ADDRESS(GetTargetImagePropertyUint);
        GET_FUNCTION_ADDRESS(GetTargetImagePropertyUint64);
        GET_FUNCTION_ADDRESS(GetTargetImagePropertyString);
    }

    return TLSTATUS_SUCCESS;
}



#endif  //#ifdef __TLDfuApi_h__


/*** EOF ***/

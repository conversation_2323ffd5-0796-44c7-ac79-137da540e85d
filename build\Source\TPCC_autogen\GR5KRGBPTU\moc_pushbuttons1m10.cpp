/****************************************************************************
** Meta object code from reading C++ file 'pushbuttons1m10.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10/pushbuttons1m10.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'pushbuttons1m10.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN15PushButtonS1M10E_t {};
} // unnamed namespace

template <> constexpr inline auto PushButtonS1M10::qt_create_metaobjectdata<qt_meta_tag_ZN15PushButtonS1M10E_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "PushButtonS1M10",
        "buttonStateChanged",
        "",
        "ButtonID",
        "button",
        "state",
        "in_mPushButtonMic1_clicked",
        "in_mPushButtonMic35_clicked",
        "in_mPushButtonMicHP_clicked",
        "in_mPushButton48V_clicked",
        "in_mPushButtonAUTO_clicked"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'buttonStateChanged'
        QtMocHelpers::SignalData<void(ButtonID, bool)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 }, { QMetaType::Bool, 5 },
        }}),
        // Slot 'in_mPushButtonMic1_clicked'
        QtMocHelpers::SlotData<void()>(6, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'in_mPushButtonMic35_clicked'
        QtMocHelpers::SlotData<void()>(7, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'in_mPushButtonMicHP_clicked'
        QtMocHelpers::SlotData<void()>(8, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'in_mPushButton48V_clicked'
        QtMocHelpers::SlotData<void()>(9, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'in_mPushButtonAUTO_clicked'
        QtMocHelpers::SlotData<void()>(10, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<PushButtonS1M10, qt_meta_tag_ZN15PushButtonS1M10E_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject PushButtonS1M10::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15PushButtonS1M10E_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15PushButtonS1M10E_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN15PushButtonS1M10E_t>.metaTypes,
    nullptr
} };

void PushButtonS1M10::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<PushButtonS1M10 *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->buttonStateChanged((*reinterpret_cast< std::add_pointer_t<ButtonID>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 1: _t->in_mPushButtonMic1_clicked(); break;
        case 2: _t->in_mPushButtonMic35_clicked(); break;
        case 3: _t->in_mPushButtonMicHP_clicked(); break;
        case 4: _t->in_mPushButton48V_clicked(); break;
        case 5: _t->in_mPushButtonAUTO_clicked(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (PushButtonS1M10::*)(ButtonID , bool )>(_a, &PushButtonS1M10::buttonStateChanged, 0))
            return;
    }
}

const QMetaObject *PushButtonS1M10::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *PushButtonS1M10::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15PushButtonS1M10E_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int PushButtonS1M10::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void PushButtonS1M10::buttonStateChanged(ButtonID _t1, bool _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}
QT_WARNING_POP

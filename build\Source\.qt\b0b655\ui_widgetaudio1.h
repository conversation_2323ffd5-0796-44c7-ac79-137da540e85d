/********************************************************************************
** Form generated from reading UI file 'widgetaudio1.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_WIDGETAUDIO1_H
#define UI_WIDGETAUDIO1_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include <comboboxs1m3.h>

QT_BEGIN_NAMESPACE

class Ui_WidgetAudio1
{
public:
    QGridLayout *gridLayout;
    QSpacerItem *verticalSpacer_7;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *verticalSpacer;
    QVBoxLayout *verticalLayout;
    QWidget *widget1;
    QLabel *icon1;
    QLabel *label1;
    ComboBoxS1M3 *comboBox1;
    QSpacerItem *verticalSpacer_4;
    QWidget *widget2;
    QLabel *icon2;
    QLabel *label2;
    ComboBoxS1M3 *comboBox2;
    QSpacerItem *verticalSpacer_6;
    QWidget *widget3;
    QLabel *icon3;
    QLabel *label3;
    QPushButton *button3;
    QSpacerItem *horizontalSpacer;

    void setupUi(QWidget *WidgetAudio1)
    {
        if (WidgetAudio1->objectName().isEmpty())
            WidgetAudio1->setObjectName("WidgetAudio1");
        WidgetAudio1->resize(968, 554);
        gridLayout = new QGridLayout(WidgetAudio1);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName("gridLayout");
        gridLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer_7 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer_7, 0, 0, 1, 3);

        horizontalSpacer_2 = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer_2, 1, 2, 1, 1);

        verticalSpacer = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        gridLayout->addItem(verticalSpacer, 2, 0, 1, 3);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName("verticalLayout");
        widget1 = new QWidget(WidgetAudio1);
        widget1->setObjectName("widget1");
        icon1 = new QLabel(widget1);
        icon1->setObjectName("icon1");
        icon1->setGeometry(QRect(1, 1, 16, 16));
        icon1->setStyleSheet(QString::fromUtf8("image: url(:/Icon/sample.svg);"));
        label1 = new QLabel(widget1);
        label1->setObjectName("label1");
        label1->setGeometry(QRect(30, 1, 47, 16));
        comboBox1 = new ComboBoxS1M3(widget1);
        comboBox1->setObjectName("comboBox1");
        comboBox1->setGeometry(QRect(470, 1, 72, 23));

        verticalLayout->addWidget(widget1);

        verticalSpacer_4 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_4);

        widget2 = new QWidget(WidgetAudio1);
        widget2->setObjectName("widget2");
        icon2 = new QLabel(widget2);
        icon2->setObjectName("icon2");
        icon2->setGeometry(QRect(1, 1, 16, 16));
        icon2->setStyleSheet(QString::fromUtf8("image: url(:/Icon/bufferSize.svg);"));
        label2 = new QLabel(widget2);
        label2->setObjectName("label2");
        label2->setGeometry(QRect(30, 1, 124, 16));
        comboBox2 = new ComboBoxS1M3(widget2);
        comboBox2->setObjectName("comboBox2");
        comboBox2->setGeometry(QRect(460, 0, 72, 23));

        verticalLayout->addWidget(widget2);

        verticalSpacer_6 = new QSpacerItem(20, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer_6);

        widget3 = new QWidget(WidgetAudio1);
        widget3->setObjectName("widget3");
        icon3 = new QLabel(widget3);
        icon3->setObjectName("icon3");
        icon3->setGeometry(QRect(1, 1, 16, 16));
        icon3->setStyleSheet(QString::fromUtf8("image: url(:/Icon/safe.svg);"));
        label3 = new QLabel(widget3);
        label3->setObjectName("label3");
        label3->setGeometry(QRect(30, 1, 136, 16));
        button3 = new QPushButton(widget3);
        button3->setObjectName("button3");
        button3->setGeometry(QRect(495, 1, 32, 16));
        QSizePolicy sizePolicy(QSizePolicy::Policy::Preferred, QSizePolicy::Policy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(button3->sizePolicy().hasHeightForWidth());
        button3->setSizePolicy(sizePolicy);
        button3->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/Icon/switchOff.svg);\n"
"}\n"
"QPushButton:checked{\n"
"	image: url(:/Icon/switchOn.svg);\n"
"}"));
        button3->setCheckable(true);

        verticalLayout->addWidget(widget3);

        verticalLayout->setStretch(0, 26);
        verticalLayout->setStretch(1, 22);
        verticalLayout->setStretch(2, 26);
        verticalLayout->setStretch(3, 22);
        verticalLayout->setStretch(4, 26);

        gridLayout->addLayout(verticalLayout, 1, 1, 1, 1);

        horizontalSpacer = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        gridLayout->addItem(horizontalSpacer, 1, 0, 1, 1);

        gridLayout->setRowStretch(0, 24);
        gridLayout->setRowStretch(1, 122);
        gridLayout->setRowStretch(2, 227);
        gridLayout->setColumnStretch(0, 60);
        gridLayout->setColumnStretch(1, 480);
        gridLayout->setColumnStretch(2, 60);

        retranslateUi(WidgetAudio1);

        QMetaObject::connectSlotsByName(WidgetAudio1);
    } // setupUi

    void retranslateUi(QWidget *WidgetAudio1)
    {
        WidgetAudio1->setWindowTitle(QCoreApplication::translate("WidgetAudio1", "WidgetSytem1", nullptr));
        icon1->setText(QString());
        label1->setText(QCoreApplication::translate("WidgetAudio1", "BrightnesSample Rates", nullptr));
        icon2->setText(QString());
        label2->setText(QCoreApplication::translate("WidgetAudio1", "Buffer Size", nullptr));
        icon3->setText(QString());
        label3->setText(QCoreApplication::translate("WidgetAudio1", "Safe mode", nullptr));
        button3->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class WidgetAudio1: public Ui_WidgetAudio1 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_WIDGETAUDIO1_H

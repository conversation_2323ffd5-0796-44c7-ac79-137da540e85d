#ifndef ORIGINS1M8_H
#define ORIGINS1M8_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"
#include "pushbuttons1m7.h"


namespace Ui {
class OriginS1M8;
}


class OriginS1M8 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M8(QWidget* parent=nullptr, QString name="");
    ~OriginS1M8();
    OriginS1M8& setName(QString name);
    OriginS1M8& setFont(QFont font);
    OriginS1M8& setVolumeMeterLeft(int value);
    OriginS1M8& setVolumeMeterLeftClear();
    OriginS1M8& setVolumeMeterLeftSlip();
    OriginS1M8& setVolumeMeterRight(int value);
    OriginS1M8& setVolumeMeterRightClear();
    OriginS1M8& setVolumeMeterRightSlip();
    OriginS1M8& setChannelNameEditable(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OriginS1M8* ui;
    QTimer mTimer;
    QFont mFont;
    int mPreGainMixerLeftChannelLeft=-2147483648;
    int mPreGainMixerLeftChannelRight=-2147483648;
    int mPreGainMixerRightChannelLeft=-2147483648;
    int mPreGainMixerRightChannelRight=-2147483648;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(int value);
    void in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M7::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // ORIGINS1M8_H


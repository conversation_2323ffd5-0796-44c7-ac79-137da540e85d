/************************************************************************
 *  The module is a wrapper for properties that describe an UI language.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnUiLanguage_h__
#define __WnUiLanguage_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// Wrapper class for an UI language.
//
class WnUiLanguage
{
//construction/destruction/assignment
public:    
    WnUiLanguage();    
    ~WnUiLanguage();
    WnUiLanguage( const WnUiLanguage& src );
    WnUiLanguage& operator =( const WnUiLanguage& src );

//interface
public:
    //
    // Set the identifying string of the UI language (Examples: en-GB, zh-Hans_HK, zh-Hans, de). The string is parsed for
    // the language designator and the optional region and script designators (see corresponding Getters). Any other information
    // contained is ignored. The identifying string may be, e.g., a language or local ID. Since the conventions for encoding 
    // are different among the platforms and programming languages the parser only extracts the mentioned designators. It 
    // ignores most additional information that could result from the position of the designators and their linkage.
    //
    // returns:
    //      TSTATUS_SUCCESS if successful,
    //      TSTATUS_INVALID_FORMAT if the identifying string has an invalid (or at least unexpected) format,
    //      TSTATUS_INVALID_PARAMETER if the UI language resulting from the identifying string is not supported by the system,
    //      another error code otherwise
    //
    WNERR
    SetIdentifierStr(
        const WString& idStr
    );

    //
    // get the identifier string as passed to the function SetIdentifierStr() 
    //
    const WString& GetIdentifierStr() const
    {
        return mIdentifierStr;
    }

    //
    // Get the language designator of the UI language.
    //
    // This designator is a code that represents a language. It consists of two (see ISO 639-1) or the three letters (see ISO 639-2).
    // Examples: en (English), haw (Hawaiian). 
    //
    const WString& GetLanguageDesignator() const
    {
        return mLanguageDesignator;
    }

    //
    // Get the region designator of the UI language.
    //
    // This designator is a code that represents a country. It consists of a two-letter, capitalized code (see ISO 3166-1).
    // Examples: AU (Australia), GB (United Kingdom).
    // The region designator is optional and may be empty. It represents a dialect and/or cultural conventions in a specific region.
    //
    const WString& GetRegionDesignator() const
    {
        return mRegionDesignator;
    }

    //
    // Get the script designator of the UI language.
    //
    // This designator is a code that represents a script. It consists of four letters with the first letter uppercase and the
    // last three lowercase (see ISO 15924).
    // Examples: Arab (Arabic script), Hant (Traditional Chinese script).
    // The script designator is optional and may be empty. 
    //
    const WString& GetScriptDesignator() const
    {
        return mScriptDesignator;
    }

    //
    // get the description of the UI language, in English, resulting from the information about language, region and script
    //
    const WString& GetEnglischDescription() const
    {
        return mEnglishDescription;
    }

    //
    // get the description of the UI language, in the current UI language, resulting from the information about language, region and script
    //
    const WString& GetLocalizedDescription() const
    {
        return mLocalizedDescription;
    }

    //
    // return true if the UI language is set, false if not  
    //
    bool IsValid() const
    {
        return (mIdentifierStr.size() > 0);
    }

    //
    // clear any information 
    //
    void Clear();

private:
    

    //
    //
    // get the UI language description resulting from the information about language, region and script
    //
    // parameters:
    //      localized   true if the description is returned in the current UI language, false if the description is returned in English
    //      desc        caller-provided parameter that returns the description
    //      
    WNERR
    GetDescription(
        bool localized,
        WString& desc        
    ) const;

//data
private:
    // identifier string as passed to the function SetIdentifierStr()    
    WString mIdentifierStr;

    //Language designator.
    //This designator is a code that represents a language. It consists of two (see ISO 639-1) or the three letters (see ISO 639-2).
    //Examples: en (English), haw (Hawaiian).    
    WString mLanguageDesignator;

    //Region designator.
    //This designator is a code that represents a country. It consists of a two-letter, capitalized code (see ISO 3166-1).
    //Examples: AU (Australia), GB (United Kingdom).
    //The region designator is optional and may be empty. It represents a dialect and/or cultural conventions in a specific region.
    WString mRegionDesignator;    

    //Script designator.
    //This designator is a code that represents a script. It consists of four letters with the first letter uppercase and the
    //last three lowercase (see ISO 15924).
    //Examples: Arab (Arabic script), Hant (Traditional Chinese script).
    //The script designator is optional and may be empty.   
    WString mScriptDesignator;

    //description of the UI language, in English, resulting from the information about language, region and script
    WString mEnglishDescription;

    //description of the UI language, in the current UI language, resulting from the information about language, region and script
    WString mLocalizedDescription;
};

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguage_h__

/*************************** EOF **************************************/

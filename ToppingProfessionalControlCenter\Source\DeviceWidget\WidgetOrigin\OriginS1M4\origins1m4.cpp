#include "origins1m4.h"
#include "ui_origins1m4.h"
#include <QLabel>
#include <QPainter>
#include <QStyleOption>
#include "globalfont.h"
#include <QButtonGroup>
#include <float.h>

OriginS1M4::OriginS1M4(QWidget *parent, const QString &name) : OriginBase(parent), ui(new Ui::OriginS1M4),
    WorkspaceObserver(name), AppSettingsObserver(name), m_titleHeightRatio(8), m_widgetHeightRatio(92), m_buttonGroupNC(nullptr), m_buttonGroupReverb(nullptr)
{
    ui->setupUi(this);
    setStyleSheet("background:transparent");
    setMinimumSize(80, 220);
    QString style = QString("background: rgba(22, 22, 22, 1);border-radius: 8px;");
    ui->frame->setStyleSheet(style);
    ui->frame->setMinimumSize(80, 220);
    ui->labelNC->setMinimumHeight(20);
    ui->labelReverb->setMinimumHeight(20);
    ui->DialReverb->setColorBG(Qt::transparent);
    ui->DialReverb->showCircle(false);
    ui->buttonNC1->setMinimumHeight(15);
    ui->buttonNC2->setMinimumHeight(15);
    ui->buttonNC3->setMinimumHeight(15);
    ui->buttonNCOFF->setMinimumHeight(15);
    ui->buttonHALL->setMinimumHeight(15);
    ui->buttonLive->setMinimumHeight(15);
    ui->buttonReverbOFF->setMinimumHeight(15);
    ui->buttonSTU->setMinimumHeight(15);

    m_buttonGroupNC = new QButtonGroup(this);
    m_buttonGroupNC->addButton(ui->buttonNCOFF);
    m_buttonGroupNC->addButton(ui->buttonNC1);
    m_buttonGroupNC->addButton(ui->buttonNC2);
    m_buttonGroupNC->addButton(ui->buttonNC3);
    m_buttonGroupReverb = new QButtonGroup(this);
    m_buttonGroupReverb->addButton(ui->buttonReverbOFF);
    m_buttonGroupReverb->addButton(ui->buttonSTU);
    m_buttonGroupReverb->addButton(ui->buttonLive);
    m_buttonGroupReverb->addButton(ui->buttonHALL);
    ui->buttonNCOFF->setChecked(true);
    ui->buttonReverbOFF->setChecked(true);

    style = QString("background: transparent; color: rgb(161,161,161)");
    ui->labelDialLeft->setStyleSheet(style);
    ui->labelDialRight->setStyleSheet(style);

    initSigConnect();
    reset();

    ui->buttonNC3->hide();
}

OriginS1M4::~OriginS1M4() 
{

}

OriginS1M4& OriginS1M4::setName(const QString& name) {
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}

OriginS1M4& OriginS1M4::setFont(const QFont& font) {
    setAllChildFont(this, font);
    return *this;
}

OriginS1M4& OriginS1M4::setStretchFactor(int titleHeightRatio, int widgetHeightRatio)
{
    m_titleHeightRatio = titleHeightRatio;
    m_widgetHeightRatio = widgetHeightRatio;
    return  *this;
}

void OriginS1M4::paintEvent(QPaintEvent* e) {
    QStyleOption option;
    option.initFrom(this);
    QPainter painter(this);
    style()->drawPrimitive(QStyle::PE_Widget, &option, &painter, this);
}

void OriginS1M4::resizeEvent(QResizeEvent* e) {
    auto getWHHoldRatio = [](QWidget* parent, QWidget* child, double wRatio=1.0f, double hRatio=1.0f){
        double aspectRatio = static_cast<double>(child->minimumWidth()) / child->minimumHeight();
        double widthScale = static_cast<double>(parent->width()) *wRatio / child->minimumWidth();
        double heightScale = static_cast<double>(parent->height()) *hRatio / child->minimumHeight();
        double scale = std::min(widthScale, heightScale);
        double newWidth = child->minimumWidth() * scale;
        int newHeight = static_cast<int>(newWidth / aspectRatio);
        return std::make_tuple(newWidth, newHeight);
    };
    auto childCenterToParentAndHoldRatio = [=](QWidget* parent, QWidget* child){
        auto wh =  getWHHoldRatio(parent, child);
        int w =std::get<0>(wh);
        int h =std::get<1>(wh);
        child->setGeometry((parent->width()-w)/2,(parent->height()-h)/2,w, h);
    };
    childCenterToParentAndHoldRatio(this, ui->frame);

    {
        double hPixelPerRatio = ui->frame->height() / 100.0;
        double wPixelPerRatio = ui->frame->width() / 100.0;
        ui->labelNC->setGeometry(0*wPixelPerRatio, 0, 100 * wPixelPerRatio, m_titleHeightRatio * hPixelPerRatio);
        ui->widget->setGeometry(0*wPixelPerRatio, m_titleHeightRatio *hPixelPerRatio, 100 * wPixelPerRatio, m_widgetHeightRatio * hPixelPerRatio);
    }
    double hPixelPerRatio = ui->widget->height() / 100.0;
    double wPixelPerRatio = ui->widget->width() / 100.0;
    ui->buttonNCOFF->setGeometry(32*wPixelPerRatio, 3*hPixelPerRatio, 36 * wPixelPerRatio, 6 * hPixelPerRatio);
    ui->buttonNC1->setGeometry(32*wPixelPerRatio, (3+6+3) * hPixelPerRatio, 36 * wPixelPerRatio, 6 * hPixelPerRatio);
    ui->buttonNC2->setGeometry(32*wPixelPerRatio, (3+6+3+6+3) * hPixelPerRatio, 36 * wPixelPerRatio, 6 * hPixelPerRatio);
    ui->buttonNC3->setGeometry(32*wPixelPerRatio, (3+6+3+6+3+6+3) * hPixelPerRatio, 36 * wPixelPerRatio, 6 * hPixelPerRatio);

    ui->labelReverb->setGeometry(0*wPixelPerRatio, (3+6+3+6+3+6+3+6+4) * hPixelPerRatio, 100 * wPixelPerRatio, 8 * hPixelPerRatio);
    ui->DialReverb->setGeometry(31*wPixelPerRatio, (3+6+3+6+3+6+3+6+4+8+4) * hPixelPerRatio, 42 * wPixelPerRatio, 42 * wPixelPerRatio);
    ui->labelDialLeft->setGeometry(0, (3+6+3+6+3+6+3+6+4+8+4) * hPixelPerRatio +42*wPixelPerRatio/100.0*75, 35 * wPixelPerRatio, 7 * hPixelPerRatio);
    ui->labelDialRight->setGeometry(70 * wPixelPerRatio, (3+6+3+6+3+6+3+6+4+8+4) * hPixelPerRatio +42*wPixelPerRatio/100.0*75, 35 * wPixelPerRatio, 7 * hPixelPerRatio);

    ui->buttonReverbOFF->setGeometry(12*wPixelPerRatio, 78*hPixelPerRatio, 35 * wPixelPerRatio, 6 * hPixelPerRatio);
    ui->buttonSTU->setGeometry(12*wPixelPerRatio, 88 * hPixelPerRatio, 35 * wPixelPerRatio, 6 * hPixelPerRatio);
    ui->buttonLive->setGeometry(55*wPixelPerRatio, 78 * hPixelPerRatio, 35 * wPixelPerRatio, 6 * hPixelPerRatio);
    ui->buttonHALL->setGeometry(55*wPixelPerRatio, 88 * hPixelPerRatio, 35 * wPixelPerRatio, 6 * hPixelPerRatio);

    QFont font = ui->labelDialLeft->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, ui->labelDialLeft->height())*90/100);
    ui->labelDialLeft->setFont(font);
    ui->labelDialRight->setFont(font);

    setWidgetStyle(ui->labelNC);
    setButtonStyle(ui->buttonNCOFF);
    setButtonStyle(ui->buttonNC1);
    setButtonStyle(ui->buttonNC2);
    setButtonStyle(ui->buttonNC3);
    setWidgetStyle(ui->labelReverb);
    setButtonStyle(ui->buttonReverbOFF);
    setButtonStyle(ui->buttonSTU);
    setButtonStyle(ui->buttonLive);
    setButtonStyle(ui->buttonHALL);
}

void OriginS1M4::loadSettings() {
    reset();
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("NCType", "OFF");
        WorkspaceObserver::setValue("ReverbType", "OFF");
        WorkspaceObserver::setValue("DryWet", 0);
    }
    auto ncType = WorkspaceObserver::value("NCType").toString();
    auto reverbType = WorkspaceObserver::value("ReverbType").toString();
    if(ncType=="OFF"){
        ui->buttonNCOFF->setChecked(true);
    }else if(ncType=="NC1"){
        ui->buttonNC1->setChecked(true);
    }else if(ncType=="NC2"){
        ui->buttonNC2->setChecked(true);
    }else if(ncType=="NC3"){
        ui->buttonNC3->setChecked(true);
    }
    if(reverbType=="OFF"){
        ui->buttonReverbOFF->setChecked(true);
    }else if(reverbType=="STU"){
        ui->buttonSTU->setChecked(true);
    }else if(reverbType=="LIVE"){
        ui->buttonLive->setChecked(true);
    }else if(reverbType=="HALL"){
        ui->buttonHALL->setChecked(true);
    }
    ui->DialReverb->setValue(WorkspaceObserver::value("DryWet").toFloat());
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        
        double dryWet = WorkspaceObserver::value("DryWet").toFloat();
        QString savedNCType = WorkspaceObserver::value("NCType").toString();
        QString savedReverbType = WorkspaceObserver::value("ReverbType").toString();
        
        emit attributeChanged("Effect_Reverb", "Save_DryWet", QString::number(dryWet));
        emit attributeChanged("Effect_NC", "Save_NCType", savedNCType);
        emit attributeChanged("Effect_Reverb", "Save_ReverbType", savedReverbType);
    }
    updateAttribute();
}

void OriginS1M4::AppSettingsChanged(QString objectName, QString attribute, QString value) {

}

void OriginS1M4::updateAttribute() 
{    
    if(mDryWet != WorkspaceObserver::value("DryWet").toFloat())
    {
        mDryWet = WorkspaceObserver::value("DryWet").toFloat();
        emit attributeChanged("Effect_Reverb", "DryWet", QString::number(mDryWet));
    }
    
    if(mNCType != WorkspaceObserver::value("NCType").toString() || mReverbType != WorkspaceObserver::value("ReverbType").toString()){
        if(mNCType != WorkspaceObserver::value("NCType").toString())
        {
            mNCType = WorkspaceObserver::value("NCType").toString();
            emit attributeChanged("Effect_NC", "NCType", mNCType);
        }
        
        if(mReverbType != WorkspaceObserver::value("ReverbType").toString())
        {
            mReverbType = WorkspaceObserver::value("ReverbType").toString();
            emit attributeChanged("Effect_Reverb", "ReverbType", mReverbType);
        }
        updateAttributeExtend();
    }
}

void OriginS1M4::reset()
{
    mDryWet = DBL_MAX;
    mNCType = "";
    mReverbType = "";
}

void OriginS1M4::setAllChildFont(QWidget* widget, const QFont& font) {
    for (auto child : widget->children()) {
        QWidget *widget = qobject_cast<QWidget *>(child);
        if (widget) {
            widget->setFont(font);
            setAllChildFont(widget, font);
        }
    }
}

void OriginS1M4::saveAttribute(QString attribute) {
    auto saveValue = [this](const QString& objectName, const QString& attribute, const QString& value){
        if(isWidgetEmitAction())
        {
            emit attributeChanged(objectName, QString("Save_")+attribute, value);
        }
    };
    if(attribute == "NCType")
    {
        saveValue("Effect_NC", attribute, mNCType);
    }
    else if(attribute == "ReverbType")
    {
        saveValue("Effect_Reverb", attribute, mReverbType);
    }
    else if(attribute == "DryWet")
    {
        saveValue("Effect_Reverb", attribute, QString::number(mDryWet));
    }
}

void OriginS1M4::in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value) { 
    WorkspaceObserver::setValue(attribute, value);
    updateAttribute();
    saveAttribute(attribute);
}

void OriginS1M4::initSigConnect() {
    connect(m_buttonGroupNC, QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked), [=](QAbstractButton* button){
        if(button == ui->buttonNCOFF)
        {
            in_mWidgetListAll_attributeChanged(this->objectName(), "NCType", "OFF");
        }
        else if(button == ui->buttonNC1)
        {
            in_mWidgetListAll_attributeChanged(this->objectName(), "NCType", "NC1");
        }
        else if(button == ui->buttonNC2)
        {
            in_mWidgetListAll_attributeChanged(this->objectName(), "NCType", "NC2");
        }
        else if(button == ui->buttonNC3)
        {
            in_mWidgetListAll_attributeChanged(this->objectName(), "NCType", "NC3");
        }
    });
    connect(m_buttonGroupReverb, QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked), [=](QAbstractButton* button){
        if(button == ui->buttonReverbOFF)
        {
            in_mWidgetListAll_attributeChanged(this->objectName(), "ReverbType", "OFF");
        }
        else if(button == ui->buttonSTU)
        {
            in_mWidgetListAll_attributeChanged(this->objectName(), "ReverbType", "STU");
        }
        else if(button == ui->buttonLive)
        {
            in_mWidgetListAll_attributeChanged(this->objectName(), "ReverbType", "LIVE");
        }
        else if(button == ui->buttonHALL)
        {
            in_mWidgetListAll_attributeChanged(this->objectName(), "ReverbType", "HALL");
        }
    });
    connect(ui->DialReverb, &DialS1M1::valueChanged, [=](double value){
        in_mWidgetListAll_attributeChanged(this->objectName(), "DryWet", QString::number(value));
    });
}

void OriginS1M4::setWidgetStyle(QWidget* widget) {
    QFont font = widget->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, widget->height())*80/100);
    widget->setFont(font);

    QString style = QString("background: rgb(46,46,46);"
        "color: rgb(161,161,161);"
        "border-radius: 5px;");
    widget->setStyleSheet(style);
}

void OriginS1M4::setButtonStyle(QWidget* widget){
    QFont font = widget->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, widget->height())*85/100);
    widget->setFont(font);

    QString style = QString("QPushButton{background: rgb(60,60,60);"
        "color: rgb(161,161,161);"
        "border-radius: %1px;}"
        "QPushButton:hover {"
        "border: 2px solid gray;"
        "border-radius: 3px;"
        "}"
        "QPushButton:checked{"
        "color: rgb(229, 229, 229);"
        "background-color: rgb(21, 140, 132);"
    "}").arg(3 * (widget->height() / (double)widget->minimumHeight()));
    widget->setStyleSheet(style);
}
void OriginS1M4::updateAttributeExtend()
{
    if(mNCType == "OFF" && mReverbType == "OFF")
    {
        emit attributeChanged("Mixer_IN12", "MixA_GainMLCL", "16777216");
        emit attributeChanged("Mixer_IN12", "MixA_GainMLCR", "16777216");
        emit attributeChanged("Mixer_IN12", "MixA_GainMRCL", "16777216");
        emit attributeChanged("Mixer_IN12", "MixA_GainMRCR", "16777216");
        emit attributeChanged("Mixer_IN12", "MixB_GainMLCL", "16777216");
        emit attributeChanged("Mixer_IN12", "MixB_GainMLCR", "16777216");
        emit attributeChanged("Mixer_IN12", "MixB_GainMRCL", "16777216");
        emit attributeChanged("Mixer_IN12", "MixB_GainMRCR", "16777216");
        emit attributeChanged("Mixer_IN12", "MixC_GainMLCL", "16777216");
        emit attributeChanged("Mixer_IN12", "MixC_GainMLCR", "16777216");
        emit attributeChanged("Mixer_IN12", "MixC_GainMRCL", "16777216");
        emit attributeChanged("Mixer_IN12", "MixC_GainMRCR", "16777216");
        emit attributeChanged("Mixer_FX", "MixA_GainMLCL", "0");
        emit attributeChanged("Mixer_FX", "MixA_GainMRCR", "0");
        emit attributeChanged("Mixer_FX", "MixB_GainMLCL", "0");
        emit attributeChanged("Mixer_FX", "MixB_GainMRCR", "0");
        emit attributeChanged("Mixer_FX", "MixC_GainMLCL", "0");
        emit attributeChanged("Mixer_FX", "MixC_GainMRCR", "0");
    }
    else
    {
        emit attributeChanged("Mixer_IN12", "MixA_GainMLCL", "0");
        emit attributeChanged("Mixer_IN12", "MixA_GainMLCR", "0");
        emit attributeChanged("Mixer_IN12", "MixA_GainMRCL", "0");
        emit attributeChanged("Mixer_IN12", "MixA_GainMRCR", "0");
        emit attributeChanged("Mixer_IN12", "MixB_GainMLCL", "0");
        emit attributeChanged("Mixer_IN12", "MixB_GainMLCR", "0");
        emit attributeChanged("Mixer_IN12", "MixB_GainMRCL", "0");
        emit attributeChanged("Mixer_IN12", "MixB_GainMRCR", "0");
        emit attributeChanged("Mixer_IN12", "MixC_GainMLCL", "0");
        emit attributeChanged("Mixer_IN12", "MixC_GainMLCR", "0");
        emit attributeChanged("Mixer_IN12", "MixC_GainMRCL", "0");
        emit attributeChanged("Mixer_IN12", "MixC_GainMRCR", "0");
        emit attributeChanged("Mixer_FX", "MixA_GainMLCL", "33554432");
        emit attributeChanged("Mixer_FX", "MixA_GainMRCR", "33554432");
        emit attributeChanged("Mixer_FX", "MixB_GainMLCL", "33554432");
        emit attributeChanged("Mixer_FX", "MixB_GainMRCR", "33554432");
        emit attributeChanged("Mixer_FX", "MixC_GainMLCL", "33554432");
        emit attributeChanged("Mixer_FX", "MixC_GainMRCR", "33554432");
    }
}
OriginS1M4& OriginS1M4::setValueNCType(QString type)
{
    mNCType = type;
    if(mNCType == "OFF")
    {
        ui->buttonNCOFF->setChecked(true);
    }
    else if(mNCType == "NC1")
    {
        ui->buttonNC1->setChecked(true);
    }
    else if(mNCType == "NC2")
    {
        ui->buttonNC2->setChecked(true);
    }
    else if(mNCType == "NC3")
    {
        ui->buttonNC3->setChecked(true);
    }
    else
    {
        ui->buttonNCOFF->setChecked(true);
    }
    WorkspaceObserver::setValue("NCType", mNCType);
    updateAttributeExtend();
    return *this;
}
OriginS1M4& OriginS1M4::setValueReverbType(QString type)
{
    mReverbType = type;
    if(mReverbType == "OFF")
    {
        ui->buttonReverbOFF->setChecked(true);
    }
    else if(mReverbType == "STU")
    {
        ui->buttonSTU->setChecked(true);
    }
    else if(mReverbType == "LIVE")
    {
        ui->buttonLive->setChecked(true);
    }
    else if(mReverbType == "HALL")
    {
        ui->buttonHALL->setChecked(true);
    }
    else
    {
        ui->buttonReverbOFF->setChecked(true);
    }
    WorkspaceObserver::setValue("ReverbType", mReverbType);
    updateAttributeExtend();
    return *this;
}

#include <QRegularExpressionValidator>

#include "globalfont.h"
#include "vsliders1m2.h"


VSliderS1M2::VSliderS1M2(QWidget *parent)
    : QWidget(parent)
{
    mLineEdit.setParent(this);
    mLineEdit.setAlignment(Qt::AlignCenter);
    QRegularExpression reg(R"(^([-+]?(?:[0-9]+(?:\.[0-9])?))|[-+]∞|[-+]$)");
    mLineEdit.setValidator(new QRegularExpressionValidator(reg, this));
    mLineEdit.installEventFilter(this);
    mSlider.setParent(this);
    mSlider.setOrientation(Qt::Vertical);
    mSlider.installEventFilter(this);
    connect(&mLineEdit, SIGNAL(textChanged(const QString&)), this, SLOT(in_mLineEdit_textChanged(const QString&)));
    connect(&mLineEdit, SIGNAL(editingFinished()), this, SLOT(in_mLineEdit_editingFinished()));
    connect(&mSlider, SIGNAL(valueChanged(int)), this, SLOT(in_mSlider_valueChanged(int)));
    setRange(0, -10, -20, -30).setDefault(0).setValue(0);
}
VSliderS1M2::~VSliderS1M2()
{
    delete mLineEdit.validator();
}


// override
bool VSliderS1M2::eventFilter(QObject* obj, QEvent* e)
{
    if(obj == &mSlider && mSlider.isEnabled())
    {
        if(e->type() == QEvent::MouseButtonDblClick)
        {
            mLineEdit.setText(QString::number(mValueDefault));
            in_mLineEdit_editingFinished();
            return true;
        }
        else if(e->type() == QEvent::Wheel)
        {
            QWheelEvent *wheelEvent=static_cast<QWheelEvent *>(e);
            int numSteps=wheelEvent->angleDelta().y() / 120;
            float newValue=mValueStart + mSlider.value() * 0.5;
            if(numSteps > 0)
            {
                if(newValue >= mValueEnd05)
                {
                    newValue += 0.5 * qAbs(numSteps);
                }
                else if(newValue >= mValueEnd10)
                {
                    newValue += 1 * qAbs(numSteps);
                }
                else if(newValue >= mValueEnd20)
                {
                    newValue += 2 * qAbs(numSteps);
                }
                newValue = (newValue > mValueStart) ? (mValueStart) : (newValue);
            }
            else
            {
                if(newValue > mValueEnd05)
                {
                    newValue -= 0.5 * qAbs(numSteps);
                }
                else if(newValue > mValueEnd10)
                {
                    newValue -= 1 * qAbs(numSteps);
                }
                else if(newValue > mValueEnd20)
                {
                    newValue -= 2 * qAbs(numSteps);
                }
                newValue = (mValueEnd20 > newValue) ? (mValueEnd20) : (newValue);
            }
            mLineEdit.setText(QString::number(newValue));
            in_mLineEdit_editingFinished();
            return true;
        }
        else if(e->type() == QEvent::KeyPress)
        {
            QKeyEvent* keyEvent=static_cast<QKeyEvent*>(e);
            float newValue=mValueStart + mSlider.value() * 0.5;
            if(keyEvent->key() == Qt::Key_Up || keyEvent->key() == Qt::Key_Right)
            {
                if(newValue >= mValueEnd05)
                {
                    newValue += 0.5;
                }
                else if(newValue >= mValueEnd10)
                {
                    newValue += 1;
                }
                else if(newValue >= mValueEnd20)
                {
                    newValue += 2;
                }
                newValue = (newValue > mValueStart) ? (mValueStart) : (newValue);
                mLineEdit.setText(QString::number(newValue));
                in_mLineEdit_editingFinished();
                return true;
            }
            else if(keyEvent->key() == Qt::Key_Down || keyEvent->key() == Qt::Key_Left)
            {
                if(newValue > mValueEnd05)
                {
                    newValue -= 0.5;
                }
                else if(newValue > mValueEnd10)
                {
                    newValue -= 1;
                }
                else if(newValue > mValueEnd20)
                {
                    newValue -= 2;
                }
                newValue = (mValueEnd20 > newValue) ? (mValueEnd20) : (newValue);
                mLineEdit.setText(QString::number(newValue));
                in_mLineEdit_editingFinished();
                return true;
            }
            else if(keyEvent->key() == Qt::Key_PageUp || keyEvent->key() == Qt::Key_PageDown || keyEvent->key() == Qt::Key_Home || keyEvent->key() == Qt::Key_End)
            {
                return true;
            }
        }
    }
    if(obj == &mLineEdit)
    {
        if(e->type() == QEvent::FocusOut)
        {
            if(mLineEdit.text().isEmpty())
            {
                in_mLineEdit_editingFinished();
            }
            if(mLineEdit.text().back() == ".")
            {
                mLineEdit.setText(mLineEdit.text().chopped(1));
            }
        }
        else if(e->type() == QEvent::KeyPress)
        {
            QKeyEvent* keyEvent=static_cast<QKeyEvent*>(e);
            if(keyEvent->key() == Qt::Key_Return || keyEvent->key() == Qt::Key_Enter)
            {
                if(mLineEdit.text().isEmpty())
                {
                    in_mLineEdit_editingFinished();
                }
                if(mLineEdit.text().back() == ".")
                {
                    mLineEdit.setText(mLineEdit.text().chopped(1));
                }
            }
        }
    }
    return QWidget::eventFilter(obj, e);
}
void VSliderS1M2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    float pixelPerRatio = size().height() / 100.0;
    int hText=pixelPerRatio * mHText;
    int hSpace1=pixelPerRatio * mHSpace;
    int hSlider=pixelPerRatio * mHSlider;
    mLineEdit.setGeometry(0, 0, width(), hText);
    mSlider.setGeometry(width() / 2 - width() / 10.0 * 4, hText + hSpace1, width() / 10.0 * 8, hSlider - pixelPerRatio);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "+9.0", mLineEdit.rect()));
    mLineEdit.setFont(mFont);
    QString style;
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    // "   background-color: rgb(46, 46, 46);"
                    "   background-color: transparent;"
                    "   border-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(width() / 5);
    mLineEdit.setStyleSheet(style);
    style = QString("QSlider {"
                    "   background-color: transparent;"
                    "}"
                    "QSlider::groove:vertical {"
                    "   background: #333333;"
                    "   border-radius: %1px;"
                    "   width: %2px;"
                    "}"
                    "QSlider::add-page:vertical {"
                    "   background: #CCCCCC;"
                    "   border-radius: %1px;"
                    "   width: %2px;"
                    "}"
                    "QSlider::handle:vertical {"
                    "   border-image: url(:/Icon/SliderHandle.png);"
                    "   height: %3px;"
                    "   margin: -0px  -%4px -1px -%4px;"
                    "}").arg(mSlider.width() * 0.1).arg(mSlider.width() * 0.25).arg(mSlider.width() * 0.7).arg(mSlider.width() * 0.225);
    mSlider.setStyleSheet(style);
}


// slot
void VSliderS1M2::in_mLineEdit_textChanged(const QString& text)
{
    QString newText=text;
    bool isNumber=false;
    float newValue=newText.toFloat(&isNumber);
    if(isNumber)
    {
        float newValueMod=std::abs(std::fmod(newValue, 1.0));
        if(mValueEnd20 <= newValue && newValue <= mValueStart)
        {
            if(newValue > mValueEnd05)
            {
                if(newValueMod != 0 && newValueMod != 0.5)
                {
                    mLineEdit.setText(newText.chopped(1));
                }
            }
            else if(newValue > mValueEnd10)
            {
                if(newValueMod != 0)
                {
                    mLineEdit.setText(newText.chopped(1));
                }
                newText.remove('.');
                mLineEdit.setText(newText);
            }
            else if(newValue >= mValueEnd20)
            {
                if(newValueMod != 0)
                {
                    mLineEdit.setText(newText.chopped(1));
                }
                newText.remove('.');
                mLineEdit.setText(newText);
            }
        }
        else
        {
            mLineEdit.setText(newText.chopped(1));
        }
    }
}
void VSliderS1M2::in_mLineEdit_editingFinished()
{
    QString text=mLineEdit.text();
    text.remove("+");
    text.remove("-");
    text.remove("∞");
    if(text.isEmpty())
    {
        if(mLineEdit.text() == "-∞")
        {
            if(mSlider.value() != mSlider.minimum())
            {
                mSlider.setValue(mSlider.minimum());
            }
            else
            {
                in_mSlider_valueChanged(mSlider.minimum());
            }
        }
        else if(mLineEdit.text() == "+∞")
        {
            if(mSlider.value() != mSlider.maximum())
            {
                mSlider.setValue(mSlider.maximum());
            }
            else
            {
                in_mSlider_valueChanged(mSlider.maximum());
            }
        }
        else
        {
            in_mSlider_valueChanged(mSlider.value());
        }
    }
    else
    {
        float newValue=mLineEdit.text().toFloat();
        int sliderValue=0;
        if(newValue >= mValueEnd05)
        {
            sliderValue = (newValue - mValueStart) * 2;
        }
        else if(newValue >= mValueEnd10)
        {
            sliderValue = (newValue - mValueStart) * 2;
        }
        else if(newValue >= mValueEnd20)
        {
            if(((int) newValue) % 2)
            {
                newValue--;
            }
            sliderValue = (newValue - mValueStart) * 2;
        }
        if(mSlider.value() != sliderValue)
        {
            mSlider.setValue(sliderValue);
        }
        else
        {
            in_mSlider_valueChanged(sliderValue);
        }
    }
    mLineEdit.clearFocus();
}
void VSliderS1M2::in_mSlider_valueChanged(int value)
{
    float newValue=mValueStart + value * 0.5;
    float newValueMod=std::abs(std::fmod(newValue, 1.0));
    if(newValue >= mValueEnd05)
    {
    }
    else if(newValue >= mValueEnd10)
    {
        if(newValueMod != 0)
        {
            return;
        }
    }
    else if(newValue >= mValueEnd20)
    {
        if(newValueMod != 0 || ((int) newValue) % 2)
        {
            return;
        }
    }
    if(newValue == 0)
    {
        mLineEdit.setText(QString::number(newValue));
    }
    else if(newValue > 0)
    {
        if(newValue > mValueEnd05)
        {
            mLineEdit.setText("+" + QString::number(newValue, 'f', 1));
        }
        else
        {
            mLineEdit.setText("+" + QString::number(newValue));
        }
    }
    else
    {
        if(newValue > mValueEnd05)
        {
            mLineEdit.setText(QString::number(newValue, 'f', 1));
        }
        else
        {
            mLineEdit.setText(QString::number(newValue));
        }
    }
    if(value == mSlider.minimum() && mShowInfinitesimal)
    {
        mLineEdit.setText("-∞");
    }
    else if(value == mSlider.maximum() && mShowInfinity)
    {
        mLineEdit.setText("+∞");
    }
    if(mValue != newValue)
    {
        mValue = newValue;
        if(mEmitOpen)
        {
            emit valueChanged(mValue);
        }
    }
}


// setter & getter
VSliderS1M2& VSliderS1M2::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
VSliderS1M2& VSliderS1M2::setValue(float value)
{
    mEmitOpen = false;
    mLineEdit.setText(QString::number(value));
    in_mLineEdit_editingFinished();
    mEmitOpen = true;
    return *this;
}
VSliderS1M2& VSliderS1M2::setDefault(float value)
{
    mValueDefault = value;
    return *this;
}
VSliderS1M2& VSliderS1M2::setRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20)
{
    mValueStart = valueStart;
    mValueEnd05 = valueEnd05;
    mValueEnd10 = valueEnd10;
    mValueEnd20 = valueEnd20;
    mEmitOpen = false;
    mSlider.setRange((mValueEnd20 - mValueStart) * 2, 0);
    mEmitOpen = true;
    return *this;
}
VSliderS1M2& VSliderS1M2::setHeightRatio(int text, int space, int Slider)
{
    mHText = text;
    mHSpace = space;
    mHSlider = Slider;
    return *this;
}
VSliderS1M2& VSliderS1M2::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
VSliderS1M2& VSliderS1M2::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}


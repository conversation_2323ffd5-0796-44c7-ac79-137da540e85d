/************************************************************************
 *
 *  Module:       win_targetver.h
 *
 *  Description:  Define Windows target version
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo.E<PERSON><EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __win_targetver_h__
#define __win_targetver_h__


// Set the _WIN32_WINNT macro to the minimum platform you wish to support.

// Note: If _WIN32_WINNT is set to Windows 10 then in project settings
// Target Platform Version must be set to 10.xxx
// see https://msdn.microsoft.com/en-us/library/mt186161.aspx


//#define _WIN32_WINNT    0x0501  //XP
//#define _WIN32_WINNT    0x0600  //Vista
#define _WIN32_WINNT    0x0601  //7
//#define _WIN32_WINNT    0x0602  //8
//#define _WIN32_WINNT    0x0603  //8.1
//#define _WIN32_WINNT    0x0A00  //10


#if (_MSC_VER >= 1900)    //Visual Studio 2015 and above
    #include <WinSDKVer.h>
    #include <SDKDDKVer.h>
#endif


#endif

/********************************* EOF *********************************/

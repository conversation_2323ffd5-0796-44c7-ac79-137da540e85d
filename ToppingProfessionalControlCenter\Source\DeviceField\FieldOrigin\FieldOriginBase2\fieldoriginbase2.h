#ifndef FieldOriginBase2_H
#define FieldOriginBase2_H


#include <QPair>
#include <QFont>
#include <QRect>
#include <QLabel>
#include <QColor>
#include <QWidget>
#include <QVector>
#include <QString>
#include <QPainter>
#include <QScrollArea>
#include <QPaintEvent>
#include <QPushButton>
#include <QResizeEvent>

#include "originbase.h"
#include "buttonboxs1m1.h"


class FieldOriginBase2 : public QWidget
{
    Q_OBJECT
public:
    explicit FieldOriginBase2(QWidget* parent=nullptr);
    ~FieldOriginBase2();
    FieldOriginBase2& modifyWidgetList(QVector<OriginBase*> list);
    FieldOriginBase2& setFont(QFont font);
    FieldOriginBase2& setVisibleList(QVector<QString> list);
    FieldOriginBase2& setFieldColor(QColor color);
    FieldOriginBase2& setWidgetAreaColor(QColor color);
    FieldOriginBase2& setWidgetAreaVisible(bool state=true);
    FieldOriginBase2& setAdditionVisible(bool state=true);
    FieldOriginBase2& setAdditionButtonWeight(int weightWidth, int weightHeight);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    struct MixerWidget
    {
        bool visible;
        OriginBase* widget;
    };
    QFont mFont;
    QColor mColorBG=QColor(0, 0, 0);
    QColor mColorWidgetArea=QColor(128, 128, 128);
    QRect mRectBody;
    QRect mRectWidgetArea;
    QScrollArea mScrollArea;
    int mScrollBarValue=0;
    QWidget mWidget;
    QVector<MixerWidget*> mWidgetList;
    ButtonBoxS1M1 mWidgetAddition;
    bool mWidgetAreaVisible=true;
    bool mAdditionVisible=true;
    void drawBG(QPainter* painter);
private slots:
    void in_mScrollArea_valueChanged(int value);
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);
    void in_mWidgetAddition_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FieldOriginBase2_H

